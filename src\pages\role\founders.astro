---
import Layout from "../../layouts/Layout.astro";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Rocket, DollarSign, TrendingUp, Clock, CheckCircle, ArrowRight, Building } from "lucide-react";



const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Role',"aria-disabled": true ,
  },
  {
    index: "2", text: " Founders & Business Leaders","aria-disabled": true 
  },
];
---

<Layout title="Founders - Build a Trust Layer into Your Stack"
  description="Stop losing enterprise deals to compliance requirements. Get SOC2, HIPAA, and ISO certifications that integrate seamlessly with your development workflow."
  keywords="Founders, developer-friendly compliance, SOC2 for Founders, enterprise sales, compliance automation, tech leadership"
  url="/role/founders/"
  breadcrumbLinks={breadcrumbLinks}>
  {/* Hero Section */}
      <section class="relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5 pt-16 pb-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center">
            <Badge variant="outline" className="mb-4">
              For Founders & Business Leaders
            </Badge>
            <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
              Ship Enterprise Features While Building Product
            </h1>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Don't let compliance slow down your growth. Get SOC2, HIPAA, and enterprise certifications 
              while your team stays focused on building and shipping product.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
              
              <a href="/get-started">
              <Button size="lg" variant="default">
                  Get Startup Compliance Plan
                  <ArrowRight className="ml-2 h-4 w-4" />               
              </Button>
              </a>              
              <a href="/contact">
                <Button variant="outline" size="lg">Talk to a Founder
                </Button>
              </a>
              
            </div>
          </div>
        </div>
      </section>

      {/* Key Benefits */}
      <section class="py-16 sm:py-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl lg:text-center">
            <h2 class="text-base font-semibold leading-7 text-primary">Founder Benefits</h2>
            <p class="mt-2 text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Compliance That Accelerates Growth
            </p>
          </div>
          <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <dl class="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
              <div class="flex flex-col">
                <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                  <Rocket className="h-5 w-5 flex-none text-primary" />
                  Faster Enterprise Sales
                </dt>
                <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p class="flex-auto">
                    Close enterprise deals faster with SOC2 Type II compliance. Remove security 
                    questionnaires and procurement delays from your sales cycle.
                  </p>
                </dd>
              </div>
              <div class="flex flex-col">
                <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                  <DollarSign className="h-5 w-5 flex-none text-primary" />
                  Predictable Compliance Costs
                </dt>
                <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p class="flex-auto">
                    Know exactly what compliance will cost from Series A to IPO. No surprise 
                    consulting fees or hidden costs as you scale.
                  </p>
                </dd>
              </div>
              <div class="flex flex-col">
                <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                  <TrendingUp className="h-5 w-5 flex-none text-primary" />
                  Investor Confidence
                </dt>
                <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p class="flex-auto">
                    Show investors you're building a mature, scalable business with proper 
                    governance and risk management from day one.
                  </p>
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </section>

      {/* Key Outcomes */}
      <section class="py-16 sm:py-20 bg-muted">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Founder Success Stories
            </h2>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Real outcomes from founders & business leaders who chose compliance-first growth strategies.
            </p>
          </div>
          <div class="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-6 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-3 lg:gap-8">
            {[
              {
                icon: Clock,
                title: "3 Months to SOC2",
                description: "Get SOC2 Type II in 3-6 months instead of 12-18 months. Start selling to enterprise customers sooner."
              },
              {
                icon: Building,
                title: "2x Faster Enterprise Sales",
                description: "Eliminate security procurement delays and close enterprise deals in weeks instead of months."
              },
              {
                icon: CheckCircle,
                title: "Zero Engineering Distraction",
                description: "Our experts handle compliance while your team stays focused on product development and user growth."
              }
            ].map((item, index) => (
              <div  class="flex flex-col items-start">
                <div class="rounded-md bg-primary/10 p-2 ring-1 ring-primary/20">
                  <item.icon className="h-6 w-6 text-primary" />
                </div>
                <dt class="mt-4 font-semibold text-foreground">{item.title}</dt>
                <dd class="mt-2 leading-7 text-muted-foreground">{item.description}</dd>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section class="py-16 sm:py-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Ready to Make Compliance Your Competitive Advantage?
            </h2>
            <p class="mx-auto mt-6 max-w-xl text-lg leading-8 text-muted-foreground">
              Join hundreds of founders & business leaders who chose proactive compliance to accelerate enterprise growth.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
              
                <a href="/get-started">
                  <Button size="lg" variant="default">Get Founder Roadmap
                  </Button>
                </a>              
                <a href="/resources">
                  <Button variant="outline" size="lg">Download Startup Guide
                  </Button>
                </a>              
            </div>
          </div>
        </div>
      </section>

</Layout>
