---
import Layout from "../../layouts/Layout.astro";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowRight } from "lucide-react";


const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Industry',"aria-disabled": true ,
  },
  {
    index: "1", text: "SaaS & Tech","aria-disabled": true ,
  },
];
---

<Layout  breadcrumbLinks={breadcrumbLinks}>
      <section class="relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5 pt-16 pb-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center">
            <Badge variant="outline" className="mb-4">SaaS & Technology</Badge>
            <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
              SaaS & Technology Compliance
            </h1>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              SOC2, ISO 27001, and privacy compliance for SaaS platforms and technology companies.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
              <a href="/get-started">
              <Button size="lg" variant="default">
                Get SaaS Assessment<ArrowRight className="ml-2 h-4 w-4" />
              </Button>
              </a>
            </div>
          </div>
        </div>
      </section>
</Layout>
