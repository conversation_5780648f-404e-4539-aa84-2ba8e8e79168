{"resourceType": "Questionnaire", "meta": {"profile": ["http://hl7.org/fhir/4.0/StructureDefinition/Questionnaire"], "tag": [{"system": "InternalAssessment", "code": "AccessControl", "display": "Access Control"}, {"system": "InternalAssessment", "code": "AccessControl-L1", "display": "CMMC Level 1"}]}, "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "displayOrder", "language": "text/fhirpath", "expression": "3", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "3"}]}}], "name": "identification_authentication_verify_identities_of_users_and_processes", "title": "Identification & Authentication", "status": "draft", "date": "2025-08-26", "publisher": "Netspective", "description": "Identification & Authentication (Verify identities of users and processes)", "purpose": "This assessment verifies that all users, devices, and processes accessing the system are properly identified and authenticated. It examines whether unique identifiers are issued, whether identity verification mechanisms (such as passwords, tokens, or certificates) are in place, and whether authentication controls prevent unauthorized access to Federal Contract Information (FCI).", "approvalDate": "2025-08-26", "item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "281929303054", "text": "Notes / Evidence"}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "362061549890", "text": "Implementation Status", "repeats": false, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 100}], "valueCoding": {"display": "Fully Implemented"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 50}], "valueCoding": {"display": "Partially Implemented"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 0}], "valueCoding": {"display": "Not Implemented"}}]}, {"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "243749447566", "text": "Notes / Evidence:"}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "maxScore", "language": "text/fhirpath", "expression": "100", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "100"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "139461602895", "text": "User Identification Standards", "repeats": false, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": " First name + last name (john.smith)"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": " First initial + last name (j<PERSON>)"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": " Employee ID numbers (EMP001234)"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": " Department codes + names (IT-jsmith)"}}]}, {"item": [{"type": "integer", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter number"}], "linkId": "************", "text": "Number of service accounts:"}, {"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "maxScore", "language": "text/fhirpath", "expression": "100", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "100"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "check-box", "display": "Check-box"}]}}], "linkId": "************", "text": "Check all that apply:", "repeats": true, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": " Database services"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": " Web applications"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": " Backup processes"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": " Monitoring/logging services"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Security scanning tools"}}]}, {"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "441172825241", "text": "Notes / Evidence"}, {"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "279054233268", "text": "Notes / Evidence "}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "926744954268", "text": "Do you have a device inventory spreadsheet?", "repeats": false, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 100}], "valueCoding": {"display": "Yes"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 0}], "valueCoding": {"display": "No"}}]}, {"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "maxScore", "language": "text/fhirpath", "expression": "100", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "100"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "check-box", "display": "Check-box"}]}}], "linkId": "297397401977", "text": "Identity Verification Process", "repeats": true, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": "HR verification with employee records"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": "Manager approval with written authorization"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": "Background check completion"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": "Photo identification verification"}}]}, {"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "************", "text": "Notes / Evidence"}, {"linkId": "************_helpText", "type": "display", "text": "Manage service accounts carefully by assigning minimal privileges and regularly reviewing their usage to prevent misuse.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "************", "text": "Service Account Management"}, {"item": [{"type": "integer", "linkId": "************", "text": "Workstations/laptops:"}, {"type": "integer", "linkId": "************", "text": "Servers:"}, {"type": "integer", "linkId": "************", "text": "Mobile devices:"}, {"type": "integer", "linkId": "************", "text": "Network devices:"}, {"linkId": "************_helpText", "type": "display", "text": "Maintain an up-to-date list of all devices connected to the network to track and manage authorized hardware", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "************", "text": "<PERSON><PERSON> Inventory"}, {"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "maxScore", "language": "text/fhirpath", "expression": "99.96", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "99.96"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "check-box", "display": "Check-box"}]}}], "linkId": "359160217347", "text": "Device Identification", "repeats": true, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 16.66}], "valueCoding": {"display": "MAC addresses"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 16.66}], "valueCoding": {"display": "IP addresses (static)"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 16.66}], "valueCoding": {"display": "Computer/device names"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 16.66}], "valueCoding": {"display": "Asset tag numbers"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 16.66}], "valueCoding": {"display": "Serial numbers"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 16.66}], "valueCoding": {"display": "Certificates/digital signatures"}}]}, {"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "157280172274", "text": "Notes / Evidence"}, {"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "438433871645", "text": "Notes / Evidence"}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "210356958517", "text": "Supporting Documentation", "repeats": false, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 100}], "valueCoding": {"display": "Yes"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 0}], "valueCoding": {"display": "No"}}]}, {"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Any additional notes, remediation plans, or implementation challenges..."}], "linkId": "268793244463", "text": "Additional Notes"}, {"linkId": "228228158249_helpText", "type": "display", "text": "Identify information system users, processes acting on behalf of users, or devices.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "228228158249", "text": "IA.L1-B.1.V - Identification"}, {"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "440326396427", "text": "Notes / Evidence"}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "676336695824", "text": "Implementation Status", "repeats": false, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 100}], "valueCoding": {"display": "Fully Implemented"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 50}], "valueCoding": {"display": "Partially Implemented"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 0}], "valueCoding": {"display": "Not Implemented"}}]}, {"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "maxScore", "language": "text/fhirpath", "expression": "99.96", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "99.96"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "check-box", "display": "Check-box"}]}}], "linkId": "901079756471", "text": "User Authentication Methods", "repeats": true, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 16.66}], "valueCoding": {"display": "Username and password"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 16.66}], "valueCoding": {"display": "Multi-factor authentication (MFA)"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 16.66}], "valueCoding": {"display": "Smart cards/PIV cards"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 16.66}], "valueCoding": {"display": "Biometric authentication"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 16.66}], "valueCoding": {"display": " Digital certificates"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 16.66}], "valueCoding": {"display": "Single sign-on (SSO)"}}]}, {"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "115035657570", "text": "Notes / Evidence"}, {"item": [{"type": "integer", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "8"}], "linkId": "444552965098", "text": "Minimum length (characters):"}, {"type": "integer", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "90"}], "linkId": "499668919305", "text": "Password expiration (days):"}, {"type": "integer", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "5"}], "linkId": "190124104069", "text": "Password history (passwords remembered):"}, {"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "maxScore", "language": "text/fhirpath", "expression": "100", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "100"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "check-box", "display": "Check-box"}]}}], "linkId": "404025003688", "text": "Click all that apply:", "repeats": true, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": "Uppercase letters required"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": "Lowercase letters required"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": "Numbers required"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": "Special characters required"}}]}, {"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "149539043632", "text": "Notes / Evidence"}, {"linkId": "459655669415_helpText", "type": "display", "text": "Set and enforce strong password rules to ensure users and processes securely verify their identity before accessing systems.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "459655669415", "text": "Password Requirements"}, {"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "496212946934", "text": "Notes / Evidence"}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "maxScore", "language": "text/fhirpath", "expression": "100", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "100"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "************", "text": "Multi-Factor Authentication", "repeats": false, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Yes, for all users and systems"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Yes, for privileged accounts only"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Yes, for remote access only"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Yes, for critical systems only"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "No, not implemented"}}]}, {"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "************", "text": "Notes / Evidence"}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "maxScore", "language": "text/fhirpath", "expression": "100", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "100"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "830887074055", "text": "Default Credential Management", "repeats": false, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": "Always changed before deployment"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": "Changed during initial configuration"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": "Users required to change on first login"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": "No formal process"}}]}, {"item": [{"type": "integer", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "3"}], "linkId": "************", "text": "Number of failed attempts before lockout:"}, {"type": "integer", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "30"}], "linkId": "************", "text": "Account lockout duration (minutes):"}, {"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "maxScore", "language": "text/fhirpath", "expression": "100", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "100"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "check-box", "display": "Check-box"}]}}], "linkId": "947716241721", "text": "Click all that apply:", "repeats": true, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 50}], "valueCoding": {"display": "Administrator notification sent"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 30}], "valueCoding": {"display": " Security team alerted"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Logged for review"}}]}, {"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "434988961472", "text": "Notes / Evidence"}, {"linkId": "341175611920_helpText", "type": "display", "text": "Implement procedures to detect, respond to, and limit the impact of failed authentication attempts to protect against unauthorized access.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "341175611920", "text": "Authentication Failure Handling"}, {"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "116846260787", "text": "Notes / Evidence"}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "230111377333", "text": "Supporting Documentation", "repeats": false, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 100}], "valueCoding": {"display": "Yes"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 0}], "valueCoding": {"display": "No"}}]}, {"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Any additional notes, remediation plans, or implementation challenges..."}], "linkId": "939036015644", "text": "Additional Notes"}, {"linkId": "865372145224_helpText", "type": "display", "text": "Authenticate (or verify) the identities of those users, processes, or devices, as a prerequisite to allowing access to organizational information systems.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "865372145224", "text": "IA.L1-B.1.VI - Authentication"}]}