-- _layout.sql -- title: Opsfolio Analytics Layout -- menu: _layout --
description: Shared layout helpers — emits header values and preserved-params
links.

-- Shared params CTE used by all pages. SQLPage will call this file to render
layout header values. WITH params AS ( SELECT coalesce(:from::timestamp, now() -
interval '7 days') AS from_ts, coalesce(:to::timestamp, now()) AS to_ts,
NULLIF(trim(coalesce(:env, '')), '')::text AS env,
NULLIF(trim(coalesce(:service, '')), '')::text AS service,
NULLIF(trim(coalesce(:path_like, '')), '')::text AS path_like ) -- Layout header
values (single-row) that SQLPage can read to render site header / preserved
links. SELECT from_ts, to_ts, env, service, path_like, -- human friendly range
label to_char(from_ts, 'YYYY-MM-DD HH24:MI') || ' → ' || to_char(to_ts,
'YYYY-MM-DD HH24:MI') AS range_label, -- preserved query string for link
building (use in templates) '?from=' || urlencode(to_char(from_ts,
'YYYY-MM-DD"T"HH24:MI:SS')) || '&to=' || urlencode(to_char(to_ts,
'YYYY-MM-DD"T"HH24:MI:SS')) || case when env IS NOT NULL AND env <> '' then
'&env='||urlencode(env) else '' end || case when service IS NOT NULL AND service
<> '' then '&service='||urlencode(service) else '' end || case when path_like IS
NOT NULL AND path_like <> '' then '&path_like='||urlencode(path_like) else ''
end FROM params;
