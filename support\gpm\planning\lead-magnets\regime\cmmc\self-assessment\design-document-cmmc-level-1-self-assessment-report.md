# Design Document – CMMC Level 1 Self-Assessment Report

---

## **1. Overview**

The **CMMC Level 1 Self-Assessment Report** is the deliverable generated after a user completes the Opsfolio self-assessment tool. The core value of the report will lie in the Plan of Action & Milestones breakdown and the narrative summary, both based on the assessment results.

Its purpose is twofold:

1. **Independent Value** — Provide a clear, factual, actionable assessment of the organization’s CMMC Level 1 readiness, entirely based on official source material (CMMC Assessment Guide v2, FAR 52.204-21, NIST SP 800-171, NIST SP 800-172).
2. **Lead-in to Paid Services** — Naturally position **Scoped Technical Assistance** and **White Glove Service** offerings by showing exactly where the user needs expert support.

The report is intended to differentiate from competitors by being:

- **Persona-tailored narrative** — The narrative section speaks directly to the decision-maker’s role and priorities.
- **Action-oriented** — Next steps are specific, prioritized, and tied to real compliance requirements.
- **Source-locked** — Every claim is mapped to an authoritative reference.

---

## **2. Report Structure**

**1. Executive Summary (Score + Dashboards)**

- Overall compliance score (%).
- Score breakdown by control family (visual dashboard format).
- “Key risk areas” call-outs (top 3 failing or partial controls).

**2. Per-Persona Narrative**

- Tailored explanation for the persona selected in assessment start (e.g., CEO, CTO, Compliance Officer).
- Business impact framing (risks, cost of inaction, opportunities for competitive advantage).
- Presented in plain English, avoiding heavy technical jargon unless persona = technical role.

**3. Next Steps**

- Tiered path to remediation:
    1. **DIY** — Specific, manageable actions the organization can take internally.
    2. **Scoped Technical Assistance** — How we can guide them through priority areas.
    3. **White Glove Service** — How we can take the burden entirely off their plate.
- Persuasive yet professional copy that frames upsell as the logical next step.

**4. Plan of Action & Milestones Breakdown**

The core of the report will be a Plan of Action & Milestones (POA&M) breakdown based on the test results. 

For each control we will provide the following basic information:

- **Control ID & Name** (e.g., AC.L1-3.1.1 – Limit System Access to Authorized Users)
- **Status**: Pass / Fail
- **Plain English Restatement**
- **Why It Matters** — Business risk/consequence if not met.
- **Citation** — Explicit link to Assessment Guide section and/or FAR clause text.

Together with the components required for a POA&M: 

- Vulnerability Identification and Documentation
- Risk Assessment and Prioritization
- Remediation Planning and Milestones
- Resource Allocation and Responsibility
- Interim Risk Mitigation
- Verification and Validation


---

## **3. Input → Processing → Output Flow**

**Step 1 – Input (Assessment Stage)**

- User provides answers to each question.
- Each question is pre-mapped to a CMMC Level 1 control and its authoritative source text.

**Step 2 – Single-Control Processing (Prompt A)**

- Input: `{question, user_answer, control_text}`.
- Output: One fully formatted control breakdown section.
- All claims must cite Assessment Guide, FAR, or NIST text.

**Step 3 – Narrative Summary and Next Steps Processing (Prompt B)**

- Input: `{all control results, persona}`.
- Output: Narrative summary discussing results and business impact; Next Steps section

---

## **4. Initial User Experience & Formatting Notes**

[More notes to come after Ravi finishes full self-assessment tool evaluation]

- **Visuals:** Executive summary of test results presented in a dashboard-style layout, other sections featuring text
- **Language:** Plain English for non-technical personas, technical phrasing available for CTO/IT/security personas.

---

## **5. Effort Breakdown**

- **Content Engineering**
    - Write and test prompt(s) for single control mapping.
    - Write and test prompt(s) for full report generation.
    - Ensure outputs are source-locked (Assessment Guide + FAR).
- **Engineering**
    - Build tool logic to:
        1. Map assessment answers → relevant control(s).
        2. Pass answer + control text into single-control prompt.
        3. Aggregate all result outputs into full-report prompt.
    - Output final report.

---

## **6. Marketing Messaging**

- The **report itself is positioned as a milestone** — it’s the first tangible, documented proof of the organization’s CMMC Level 1 readiness status.
- Messaging should highlight that:
    - Completing the self-assessment and receiving the report marks a **clear, objective checkpoint** in the compliance journey.
    - It identifies exact gaps and maps them to authoritative sources, making it the **perfect springboard for remediation**.
    - The report provides the **decision-ready insight** needed for leadership to decide next steps with confidence.
- The emotional hook: “You now know exactly where you stand — and exactly what it will take to get there.”
- Each persona’s version of this message will reflect their core motivations (e.g., for a CEO: contract security, for a CTO: technical clarity, for a compliance officer: audit readiness).

---
