# Evidence-Driven Website Feedback Prioritization

This paper helps senior marketing leadership consistently evaluate, organize, and prioritize website feedback for [https://www.opsfolio.com](https://www.opsfolio.com) using first-party data plus authoritative third-party guidance (Nielsen Norman Group, Google Search Essentials, Microsoft SEO guidelines, McKinsey B2B decision-journey research, etc.). The included prompt turns raw feedback in `*.md` files into a ranked backlog with clear, defensible rationale.

Feedback is valuable—but it’s uneven. This workflow:

* Centralizes feedback in Markdown files
* Cross-checks items against first-party evidence (analytics, CRM, A/B tests, session replays)
* Grounds decisions in credible third-party research (no opinions-only changes)
* Scores by Evidence × Impact × Ease and outputs a prioritized table
* Surfaces “Implement Now” vs “Test First / Monitor / Park”

## Repo structure

```
/
├─ README.md                      # This file
├─ /feedback/                     # Raw feedback notes as .md (one item or batch per file)
│  ├─ 2025-08-12\_sales-calls.md
│  ├─ 2025-08-13\_customer-email-thread.md
│  └─ template.feedback.md        # Use this template for new feedback
├─ /evidence/                     # First-party evidence (internal only)
│  ├─ analytics-summary.md
│  ├─ seo-rankings.md
│  ├─ ab-tests.md
│  └─ session-replays-notes.md
├─ /outputs/
│  ├─ prioritized-backlog\_2025-08-13.md
│  └─ changelog.md
└─ /prompt/
└─ prioritization-prompt.md    # The master prompt below (paste into your AI tool)
```

We on only needs to supply first-party evidence. The prompt itself brings NN/g, Google & Microsoft SEO guidelines, McKinsey, and other reputable sources—no third-party placeholders needed.

## How to add feedback (`*.md`)

1. Create a new file in `/feedback/` using the template below (copy as `YYYY-MM-DD_short-title.md`).
2. Keep each feedback item crisp; for larger batches, use bullet lists.
3. Link to any relevant first-party evidence in `/evidence/`.

### template.feedback.md

```md
---
source: "Sales call / Customer email / Internal Slack / Social / Support ticket / Qual research"
icp: "Primary / Secondary / Unknown"
page\-or\-flow: "/, /product, /pricing, /contact, demo form, nav, etc."
severity: "Low / Medium / High (reporter’s view)"
theme: "Messaging / UX / Tech / SEO / GEO / Trust / IA / Forms / Performance"
tags: ["conversion", "trust", "pricing", "navigation"]
---

## Feedback

* \[Write the raw feedback in plain language. If multiple items, bullet them.]

## Context (optional)

* \[Who said it? What were they trying to do? Any objections?]

## Links (optional)

* \[Screenshots, URLs, analytics segments, recordings, test results, etc.]
```

## How to run the prioritization prompt

1. Gather the following either manually or using surveilr AI Contxxt pattern:
   * All feedback files from `/feedback/`
   * All relevant first-party evidence from `/evidence/`

2. Paste them into the **Prompt** in `/prompt/prioritization-prompt.md` or use DSPy.

## `feedback-prioritization.prompt.md`

You are a senior digital marketing strategist specializing in B2B SaaS websites, conversion rate optimization (CRO), ideal customer profile (ICP) targeting, SEO best practices, and generative engine optimization (GEO).

I will provide you with raw website feedback and available first-party evidence. You will use:

* Nielsen Norman Group usability heuristics and UX research
* Google Search Essentials & SEO Starter Guide
* Microsoft SEO guidelines
* McKinsey B2B customer decision journey research
* Other credible third-party studies and frameworks you know of
  to evaluate and prioritize the feedback.

**Website URL:** [https://www.opsfolio.com](https://www.opsfolio.com)
**Feedback Dataset:** \[PASTE\_FEEDBACK\_HERE]
**First-party Evidence Available:** \[PASTE\_FIRST\_PARTY\_EVIDENCE\_HERE]

**Your Task:**

1. Categorize each feedback item into one of these:

   * Website content clarity & messaging
   * UX/UI & navigation
   * Technical performance
   * SEO/GEO optimization
   * ICP alignment & lead conversion potential
2. Cross-check each item against:

   * First-party evidence (our data)
   * Relevant NN/g heuristics and UX research
   * Google & Microsoft SEO guidelines
   * McKinsey or other credible marketing studies
3. Score each feedback item:

   * Evidence Score (0–5): How strongly supported it is by first-party data & authoritative research
   * Impact Score (0–5): Potential to improve qualified leads, conversions, SEO/GEO visibility
   * Ease Score (0–5): Effort/resources required to implement
4. Prioritize from highest to lowest using:

   * High Evidence + High Impact + Reasonable Ease
   * Break ties with ICP alignment and SEO/GEO benefit
5. Output as a table with columns:

   * Feedback Item
   * Category
   * Evidence Summary (cite sources like NN/g, Google SEO guidelines, etc.)
   * Scores (Evidence, Impact, Ease)
   * Priority Rank
   * Recommendation (Implement Now, Test First, Monitor, or Park)
6. Summarize the top 3 “Implement Now” changes and explain:

   * The supporting research & guidelines
   * The expected effect on ICP targeting, credibility, and conversions
