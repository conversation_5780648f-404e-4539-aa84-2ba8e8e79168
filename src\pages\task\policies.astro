---
import Layout from "../../layouts/Layout.astro";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { BookOpen, Edit3, Users, Clock, CheckCircle, ArrowRight, FileText } from "lucide-react";



const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Task',"aria-disabled": true ,
  },
  {
    index: "2", text: "Author Policies","aria-disabled": true 
  },
];
---
<Layout
  breadcrumbLinks={breadcrumbLinks}
>

    {/* Hero Section */}
    <section class="relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5 pt-16 pb-20">
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <div class="mx-auto max-w-2xl text-center">
        <Badge variant="outline" className="mb-4">
            Policy Authoring & Management
        </Badge>
        <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
            Create Compliance-Ready Policies Fast
        </h1>
        <p class="mt-6 text-lg leading-8 text-muted-foreground">
            Generate comprehensive, auditor-approved policies in minutes, not months. Our AI-powered 
            platform creates custom policies tailored to your business and compliance requirements.
        </p>
        <div class="mt-10 flex items-center justify-center gap-x-6">
            <a href="/get-started">
                <Button size="lg">
                    Start Policy Generation
                    <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
            </a>
            <a href="/contact">
                <Button variant="outline" size="lg">
                    See Policy Demo
                </Button>
            </a>
        </div>
        </div>
    </div>
    </section>

    {/* Key Features */}
    <section class="py-16 sm:py-20">
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <div class="mx-auto max-w-2xl lg:text-center">
        <h2 class="text-base font-semibold leading-7 text-primary">Policy Features</h2>
        <p class="mt-2 text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
            Intelligent Policy Authoring
        </p>
        </div>
        <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
        <dl class="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
            <div class="flex flex-col">
            <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                <BookOpen className="h-5 w-5 flex-none text-primary" />
                AI-Generated Content
            </dt>
            <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                <p class="flex-auto">
                Generate complete policy sets using AI trained on thousands of auditor-approved 
                policies. Customize for your specific industry, size, and technology stack.
                </p>
            </dd>
            </div>
            <div class="flex flex-col">
            <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                <Edit3 className="h-5 w-5 flex-none text-primary" />
                Collaborative Editing
            </dt>
            <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                <p class="flex-auto">
                Work with your team to review, edit, and approve policies. Version control, 
                change tracking, and approval workflows built-in.
                </p>
            </dd>
            </div>
            <div class="flex flex-col">
            <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                <Users className="h-5 w-5 flex-none text-primary" />
                Multi-Framework Support
            </dt>
            <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                <p class="flex-auto">
                Create policies that meet multiple compliance frameworks simultaneously. 
                SOC2, HIPAA, ISO 27001, and more from a single policy set.
                </p>
            </dd>
            </div>
        </dl>
        </div>
    </div>
    </section>

    {/* Policy Library */}
    <section class="py-16 sm:py-20 bg-muted">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">

            <!-- Heading Center -->
            <div class="mx-auto max-w-2xl text-center">
            <h2 class="text-base font-semibold leading-7 text-primary">Policy Library</h2>
            <p class="mt-2 text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                Comprehensive Policy Coverage
            </p>
            </div>

            <!-- Grid Center, keep column text LEFT -->
            <div class="mx-auto mt-16 grid max-w-4xl grid-cols-1 gap-12 sm:mt-20 lg:grid-cols-2">

            <!-- Left Column -->
            <div class="space-y-4 text-left md:ml-48 lg:ml-48">
                <h3 class="font-semibold text-foreground">Core Information Security Policies</h3>
                <ul class="space-y-2 text-muted-foreground">
                <li class="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-primary" />
                    Information Security Policy
                </li>
                <li class="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-primary" />
                    Access Control Policy
                </li>
                <li class="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-primary" />
                    Data Classification Policy
                </li>
                <li class="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-primary" />
                    Incident Response Policy
                </li>
                <li class="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-primary" />
                    Vulnerability Management Policy
                </li>
                <li class="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-primary" />
                    Business Continuity Policy
                </li>
                </ul>
            </div>

            <!-- Right Column -->
            <div class="space-y-4 text-left md:ml-48 lg:ml-10">
                <h3 class="font-semibold text-foreground">Specialized Compliance Policies</h3>
                <ul class="space-y-2 text-muted-foreground">
                <li class="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-primary" />
                    HIPAA Privacy & Security Policies
                </li>
                <li class="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-primary" />
                    PCI DSS Data Protection Policy
                </li>
                <li class="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-primary" />
                    GDPR Data Privacy Policy
                </li>
                <li class="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-primary" />
                    CCPA Consumer Privacy Policy
                </li>
                <li class="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-primary" />
                    NIST Cybersecurity Framework Policy
                </li>
                <li class="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-primary" />
                    Remote Work Security Policy
                </li>
                </ul>
            </div>

            </div>
        </div>
    </section>


    {/* Implementation Process */}
    <section class="py-16 sm:py-20">
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <div class="mx-auto max-w-2xl lg:text-center">
        <h2 class="text-base font-semibold leading-7 text-primary">Implementation</h2>
        <p class="mt-2 text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
            Policies Ready in 48 Hours
        </p>
        </div>
        <div class="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-6 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-4 lg:gap-8">
        {[
            {
            step: "Step 1",
            title: "Organization Assessment",
            description: "Complete our questionnaire about your business, technology, and compliance needs."
            },
            {
            step: "Step 2",
            title: "AI Policy Generation",
            description: "Our AI generates a complete policy set customized for your organization."
            },
            {
            step: "Step 3",
            title: "Expert Review",
            description: "Compliance experts review and refine policies to ensure accuracy and completeness."
            },
            {
            step: "Step 4",
            title: "Delivery & Training",
            description: "Receive final policies with implementation guidance and staff training materials."
            }
        ].map((step) => (
            <div class="flex flex-col items-start">
            <div class="rounded-md bg-primary/10 p-2 ring-1 ring-primary/20">
                <FileText className="h-6 w-6 text-primary" />
            </div>
            <dt class="mt-4 font-semibold text-foreground">{step.step}</dt>
            <dd class="mt-2 text-sm font-medium text-primary">{step.title}</dd>
            <dd class="mt-2 leading-7 text-muted-foreground text-sm">{step.description}</dd>
            </div>
        ))}
        </div>
    </div>
    </section>

    {/* CTA Section */}
    <section class="py-16 sm:py-20 bg-muted">
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <div class="mx-auto max-w-2xl text-center">
        <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
            Ready to Automate Your Policy Creation?
        </h2>
        <p class="mx-auto mt-6 max-w-xl text-lg leading-8 text-muted-foreground">
            Skip months of policy writing. Get auditor-approved policies customized for your business in 48 hours.
        </p>
        <div class="mt-10 flex items-center justify-center gap-x-6">
            <a href="/get-started">
                <Button size="lg">Generate My Policies
                </Button>
            </a>
            <a href="/resources">
                <Button variant="outline" size="lg">
                    View Sample Policies
                </Button>
            </a>
        </div>
        </div>
    </div>
    </section>
</Layout>