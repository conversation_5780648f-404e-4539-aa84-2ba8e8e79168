---
import Layout from "../../../../layouts/Layout.astro";
import { Badge } from "@/components/ui/badge";
import HitrustControls from "@/components/HitrustControls";
import ControlsExplorer from "@/components/ControlsExplorer";
import { 
  Shield, 
  FileText, 
  Users, 
  Clock
} from "lucide-react";
import * as IsoHeroContent from "@/content/control-explorer/hitrust.md";
import { getControlListforHitrust } from "../../../../services/auditServices";
import type {
ControlsRegimeType,
TransformedArrayType,
ControlRegimEnvironment,
ControlsType,
} from "../../../../services/auditTypes";


const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Regime',"aria-disabled": true ,
  },
  {
    index: "2", text: "HiTRUST","aria-disabled": true 
  },
];


//Hitrust cntrl dynamic
const auditTypeID = 15;
const regimeId = 6;
let tenantId = '239518031485599747';
tenantId = tenantId ? tenantId : "";
let controlAuditTableData: { title: string; dataVariable: string }[] = [];

// const auditControls = await getControlListByRegimeIdAndAuditTypeId(regimeId, auditTypeID , tenantId,);
let auditControls: ControlsType[] | { [x: string]: string; }[] = [];
try {
  auditControls = await getControlListforHitrust(tenantId);
} catch (err) {
  console.error("Failed to load controls:", err);
  auditControls = []; // fallback to empty array
}
 
let auditControl = auditControls.map(item => ({ ...item }));

const transformedArray: TransformedArrayType[] = [];
let currentCommonCriteria: string = "";
auditControl.forEach((control) => {
  const typedControl = control as ControlsRegimeType;
  if (typedControl["common_criteria"] !== currentCommonCriteria) {
    currentCommonCriteria = typedControl["common_criteria"];
    transformedArray.push({
      common_criteria: currentCommonCriteria,
      controls: [typedControl],
    });
  } else {
    transformedArray[transformedArray.length - 1].controls.push(typedControl);
  }
});
const ControlData: ControlRegimEnvironment[] = [];
for (const item of transformedArray) {
  const title = item["common_criteria"];
  const controlsForTitle = await Promise.all(
    item.controls.map(async (control) => ({
      controlCode: control["control_code"],
      controlQuestion: control["question"],
      controlIdentifier: control["control_identifier"],
      defaultSlug: control["slug"] ? control["slug"] : "",
    })),
  );

  ControlData.push({
    title: title,
    controls: controlsForTitle,
  });
  }
 const hasRealData =
  Array.isArray(ControlData) &&
  ControlData.some(section => Array.isArray(section.controls) && section.controls.length > 0);
// console.log("auditControls", auditControls,hasRealData);
---

<Layout title="HiTrust Made Simple - Healthcare Data Protection"
  description="The HITRUST Common Security Framework (CSF) is a comprehensive, flexible, and certifiable security and privacy framework used by organizations across multiple industries to efficiently approach regulatory compliance and risk management. This standard provides customers with confidence in knowing their data and confidential information are secure."
  url="/regime/hitrust/"
  breadcrumbLinks={breadcrumbLinks}>

       {/* Hero Section */}
      <section class="relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5 pt-16 pb-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-4xl text-center">
            <Badge variant="outline" className="mb-4">
              {IsoHeroContent.frontmatter.badge}
            </Badge>
            <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
              {IsoHeroContent.frontmatter.title}
            </h1>
            <p class="mt-6 text-lg leading-8 text-muted-foreground max-w-3xl mx-auto">
              {IsoHeroContent.frontmatter.description}
            </p>
          </div>
        </div>
      </section>      

       {hasRealData ? (
    <ControlsExplorer 
      controlData={ControlData}
      controlPageUrl=""
      sessionId={Number(regimeId)}
      auditTypeId={Number(auditTypeID)} 
      controlType="hitrust"
      client:only="react" />
  ) : (    
     <HitrustControls client:only="react" /> 
  )
  }
      {/* Last Updated */}
      <section class="py-8 border-t bg-muted/30">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-4xl text-center">
            <div class="flex items-center justify-center gap-2 text-sm text-muted-foreground">
              <Clock className="h-4 w-4" />
              <span>Last Updated: September 3, 2025</span>
            </div>
          </div>
        </div>
      </section>

    </Layout>