{"resourceType": "Questionnaire", "meta": {"profile": ["http://hl7.org/fhir/4.0/StructureDefinition/Questionnaire"]}, "title": "Company Information", "status": "draft", "item": [{"type": "group", "linkId": "158032884208", "text": "Organization Details", "item": [{"linkId": "158032884208_helpText", "type": "display", "text": "Provide essential information about your organization for CMMC Level 2 compliance tracking.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter your organization name"}], "linkId": "715544477968", "text": "Organization Name", "required": true}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Your full name"}], "linkId": "655141523763", "text": "Form Completed By", "required": true}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Your job title"}], "linkId": "761144039651", "text": "Position/Title"}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "<EMAIL>"}], "linkId": "441278853405", "text": "Email Address", "required": true}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "(*************"}], "linkId": "375736159279", "text": "Work Phone", "required": true}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "(*************"}], "linkId": "948589414714", "text": "Mobile Phone", "required": true}, {"type": "date", "linkId": "276403539223", "text": "Assessment Date"}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Defense, Technology, etc."}], "linkId": "789286873476", "text": "Industry"}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "1-10, 11-50, 51-200, etc."}], "linkId": "697235963218", "text": "Employee Count"}, {"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Prime contracts, subcontracts, etc. (comma-separated)"}], "linkId": "863463230823", "text": "Contract Types"}, {"item": [{"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "5-character CAGE code"}], "linkId": "805221373063", "text": "CAGE Code"}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "9-digit DUNS number"}], "linkId": "374784155003", "text": "DUNS Number"}], "type": "group", "linkId": "127163950314", "text": "Organization Identifiers"}, {"type": "display", "linkId": "371062907932", "text": "Save & Continue", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<div style=\"display: flex; justify-content: flex-end; gap: 10px; padding: 10px; background-color: #eaf3ff; border-radius: 6px;\">\n  <button type=\"button\" style=\"\n    background-color: #0052cc;\n    color: white;\n    padding: 10px 20px;\n    font-size: 14px;\n    border: none;\n    border-radius: 6px;\n    cursor: pointer;\n    box-shadow: 0 2px 5px rgba(0,0,0,0.2);\n  \"; onclick=\"goToNextForm()\">\n    ✔ Save & Continue\n  </button>\n\n  <button type=\"button\" style=\"\n    background-color: black;\n    color: white;\n    padding: 10px 20px;\n    font-size: 14px;\n    border: 1px solid #ffffff22;\n    border-radius: 6px;\n    cursor: pointer;\n    box-shadow: 0 2px 5px rgba(0,0,0,0.2);\n  \">\n    ✖ Cancel\n  </button>\n</div>"}]}}]}