# Opsfolio.com

**Compliance outcomes delivered with confidence**

> Expert guidance + AI-powered automation + Compliance-as-Code

This is the marketing and product website for **Opsfolio** – a platform that
helps organizations pass and maintain SOC2, HIPAA, ISO, CMMC, FedRAMP, and
HITRUST certifications quickly and efficiently. Built with
[Astro](https://astro.build), it combines static content performance with
interactive components for dynamic sections.

---

## 📌 What the site offers

- **Expert-guided compliance:** Real compliance engineers, fractional CCO
  services, coaching, audit prep, and policy authoring.
- **AI-driven tools:** Automated evidence collection, real-time monitoring,
  policy generation, and gap analysis.
- **One system of record:** Centralized dashboard for all compliance data,
  policies, controls, and audit readiness scoring.
- **Compliance-as-Code:** Automate compliance from your code, test results,
  workflows, and customer feedback.
- **Evidence warehouse:** Private, secure, SQL-queryable, edge-based data store
  for compliance artifacts.

---

## ⚙️ Technical stack

- **Framework:** [Astro](https://astro.build)
- **Styling:** Likely Tailwind CSS (or similar utility-first framework)
- **Hosting:** Static site hosting (e.g., GitHub Pages, Vercel, Netlify, etc.)
- **Content:** Markdown/MDX pages and dynamic React/JS components as needed

---

## 🚀 How it works (from product perspective)

- Combines real human expertise with AI-powered automation.
- Collects compliance evidence automatically from your existing dev workflows.
- Stores everything securely in an edge-based, local-first, SQL-queryable
  evidence warehouse powered by `surveilr`.
- Focuses on **outcomes** rather than just tools: 100% outcome guarantee, faster
  time to compliance, and support from compliance engineers.

---

## 📦 Project structure (typical Astro)

```
├── public
│   ├── assets
│   ├── favicon.png
│   └── robots.txt
├── src
│   ├── ai-context-engineering
│   ├── components
│   ├── content
│   ├── data
│   ├── hooks
│   ├── layouts
│   ├── lib
│   ├── pages
│   ├── services
│   ├── styles
│   └── content.config.ts
├── support
│   ├── gpm
│   └── ci-cd-prod.sh
├── LICENSE.md
├── README.md
├── astro.config.mjs
├── package.json
├── pnpm-lock.yaml
├── sandbox.config.json
├── tailwind.config.ts
└── tsconfig.json
```

---

## 🛠 Running locally

|                 Step |            Command |                     Description |
| -------------------: | -----------------: | ------------------------------: |
| Install dependencies |     `pnpm install` |  Installs all required packages |
|     Start dev server |     `pnpm run dev` | Starts local development server |
| Build for production |   `pnpm run build` |      Generates production build |
|        Preview build | `pnpm run preview` | Previews the built site locally |

## 📁 File Naming Conventions

To maintain consistency, readability, and compatibility across the Astro-based
project, please follow these naming conventions when adding or updating files:

### ✅ General Rules

- **Avoid spaces** in all file and folder names.
- Use **lowercase letters** only for filenames unless explicitly required
  otherwise (e.g., React components).
- Separate words using **kebab-case** or **PascalCase** depending on the file
  type (see below).

---

### 📄 Markdown Files (`.md` / `.mdx`)

Use **kebab-case** for all Markdown file names (especially content collection
pages).

**Example:**

```bash
correct: getting-started.md
incorrect: GettingStarted.md
incorrect: getting started.md
```

---

### 🧱 Astro Components (`.astro`)

Use **PascalCase** for all component files in the `components/` folder. Use
**kebab-case** for pages in the `pages/` directory.

**Examples:**

```bash
components/
  HeroBanner.astro       ✅
  FooterNav.astro        ✅

pages/
  about-us.astro         ✅
  contact/index.astro    ✅
```

---

### ⚛️ TypeScript Files (`.ts` / `.tsx`)

- Use **PascalCase** for React components.
- Use **kebab-case** for utility and non-component files (hooks, configs,
  services).

**Examples:**

```bash
components/
  NavBar.tsx             ✅
  FeatureCard.tsx        ✅

utils/
  fetch-client.ts        ✅
  use-local-storage.ts   ✅
```

---

### ❌ What to Avoid

- Filenames with **spaces**
- Mixed case or inconsistent separators (`getStarted`, `Get_Started`,
  `getStartedPage`)
- Using special characters in filenames

---

### 🧩 Why It Matters

- Ensures **cross-platform compatibility** (especially in CI/CD and version
  control)
- Makes file structure easier to **navigate and scale**
- Keeps codebase **clean and professional**

---

## ✏️ About

Opsfolio combines software, policy content, AI, and real human guidance to help
organizations meet compliance faster and with more confidence.


## 🧭 Customizing Breadcrumb Labels and Links

To keep certain breadcrumb labels as acronyms (like "API", "CaaS") or make specific breadcrumb items non-clickable (e.g., the current page), use the `customizeLinks` prop of the Astro Breadcrumbs component.

- `index`: Position of the breadcrumb (0 = Home, 1 = next, etc.)
- `text`: The label to display (e.g., preserve acronyms)
- `aria-disabled: true`: Disables the link for that breadcrumb item

If you don’t provide `customizeLinks`, breadcrumb labels are auto-generated from the URL path.

**Example:**

```jsx
<Breadcrumbs
  linkTextFormat="capitalized"
  customizeLinks={[
    { index: 0, text: "Home" },
    { index: 1, text: "CaaS" },
    { index: 2, text: "API Docs", "aria-disabled": true }
  ]}
/>
```

In this example:

- The first breadcrumb is labeled "Home".
- The second preserves the acronym "CaaS".
- The third is shown as "API Docs" and is not clickable.

<!-- Security scan triggered at 2025-09-02 00:51:26 -->

<!-- Security scan triggered at 2025-09-02 01:28:31 -->

<!-- Security scan triggered at 2025-09-02 15:46:50 -->

<!-- Security scan triggered at 2025-09-02 15:47:54 -->

<!-- Security scan triggered at 2025-09-02 15:49:33 -->