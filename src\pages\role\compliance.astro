---
import Layout from "../../layouts/Layout.astro";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { BookOpen, Target, Zap, Clock, CheckCircle, ArrowRight, FileCheck } from "lucide-react";



const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Role',"aria-disabled": true ,
  },
  {
    index: "2", text: "GRC & Compliance Leaders","aria-disabled": true 
  },
];
---

<Layout title="Compliance - Build a Trust Layer into Your Stack"
  description="Stop losing enterprise deals to compliance requirements. Get SOC2, HIPAA, and ISO certifications that integrate seamlessly with your development workflow."
  keywords="Compliance, developer-friendly compliance, SOC2 for Compliance, enterprise sales, compliance automation, tech leadership"
  url="/role/compliance/"
  breadcrumbLinks={breadcrumbLinks}>
  {/* Hero Section */}
      <section class="relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5 pt-16 pb-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center">
            <Badge variant="outline" className="mb-4">
              For GRC & Compliance Leaders
            </Badge>
            <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
              Master Compliance Operations with Expert Systems
            </h1>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Streamline your compliance workflows, automate evidence collection, and maintain 
              continuous audit readiness with our purpose-built platform and expert guidance.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
              
                <a href="/get-started">
                  <Button size="lg" variant="default">
                  Get Compliance Assessment
                  <ArrowRight className="ml-2 h-4 w-4" />                
                  </Button>                
                </a>
                <a href="/contact">
                  <Button variant="outline" size="lg">
                  Talk to GRC Expert
                </Button>
                </a>                
            </div>
          </div>
        </div>
      </section>

      {/* Key Benefits */}
      <section class="py-16 sm:py-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl lg:text-center">
            <h2 class="text-base font-semibold leading-7 text-primary">GRC Benefits</h2>
            <p class="mt-2 text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Compliance Operations Excellence
            </p>
          </div>
          <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <dl class="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
              <div class="flex flex-col">
                <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                  <BookOpen className="h-5 w-5 flex-none text-primary" />
                  Centralized Control Library
                </dt>
                <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p class="flex-auto">
                    Maintain a single source of truth for all compliance controls across multiple 
                    frameworks. Map controls efficiently and eliminate redundant work.
                  </p>
                </dd>
              </div>
              <div class="flex flex-col">
                <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                  <Target className="h-5 w-5 flex-none text-primary" />
                  Automated Evidence Collection
                </dt>
                <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p class="flex-auto">
                    Set up automated evidence collection workflows that continuously gather and 
                    organize proof of control effectiveness without manual intervention.
                  </p>
                </dd>
              </div>
              <div class="flex flex-col">
                <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                  <Zap className="h-5 w-5 flex-none text-primary" />
                  Real-time Compliance Status
                </dt>
                <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p class="flex-auto">
                    Get instant visibility into compliance posture with dashboards that show 
                    control status, gaps, and remediation priorities in real-time.
                  </p>
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </section>

      {/* Key Outcomes */}
      <section class="py-16 sm:py-20 bg-muted">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
           <div class="flex flex-col items-center justify-center text-center">
            <h2 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              GRC Team Success Metrics
            </h2>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Measurable improvements in compliance efficiency and audit readiness.
            </p>
          </div>
          <div class="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-6 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-3 lg:gap-8">
            {[
              {
                icon: Clock,
                title: "80% Less Prep Time",
                description: "Reduce audit preparation from months to weeks with continuous readiness and automated evidence collection."
              },
              {
                icon: FileCheck,
                title: "99% Evidence Accuracy",
                description: "Eliminate missing or outdated evidence with automated collection and validation workflows."
              },
              {
                icon: CheckCircle,
                title: "Multi-Framework Efficiency",
                description: "Manage SOC2, ISO, HIPAA, and other frameworks simultaneously with control mapping and cross-walks."
              }
            ].map((item, index) => (
              <div class="flex flex-col items-start">
                <div class="rounded-md bg-primary/10 p-2 ring-1 ring-primary/20">
                  <item.icon className="h-6 w-6 text-primary" />
                </div>
                <dt class="mt-4 font-semibold text-foreground">{item.title}</dt>
                <dd class="mt-2 leading-7 text-muted-foreground">{item.description}</dd>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section class="py-16 sm:py-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Ready to Transform Your GRC Operations?
            </h2>
            <p class="mx-auto mt-6 max-w-xl text-lg leading-8 text-muted-foreground">
              See how leading compliance teams use Opsfolio to streamline operations and maintain continuous audit readiness.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
              <a href="/get-started">
              <Button size="lg" variant="default">
                Start GRC Assessment
              </Button>
              </a>
              <a href="/resources">
              <Button variant="outline" size="lg">
                Download GRC Templates
              </Button>
              </a>
            </div>
          </div>
        </div>
      </section>
</Layout>
