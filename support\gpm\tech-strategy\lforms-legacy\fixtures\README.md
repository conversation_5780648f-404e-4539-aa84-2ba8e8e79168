# Test Fixture Setup Guide

This guide outlines the process for preparing and testing a CMMC Level Assessment dataset using LFROMS.

## 1. Prepare the Provenance Data

All CMMC Level Assessment LFROMS files are stored in:

```
TENANT_ID-fixture1/USER_ID-fixture1/SESSION_ID-fixture1/provenance
```

Within this folder, the file:

```
Media-Protection-(Protect-information-on-digital-and-non-digital-media).R4.json
```

has been updated to include **scores** and **weights**.

## 2. Add Response Data

The corresponding response data for the above file is located at:

```
TENANT_ID-fixture1/USER_ID-fixture1/SESSION_ID-fixture1/responses/Media-Protection-(Protect-information-on-digital-and-non-digital-media).R4.submit.lform-submission.json
```

## 3. Update SQL Views

In the `lforms.surveilr.sql` file:
- The existing view `uniform_resource_lform_item` has been updated.
- A new view `uniform_resource_lform_item_grouped` has been created to combine submitted information with provenance weights and scores.

## 4. Build the Test Database

Run the following commands to ingest the files and update the database schema:

```bash
cd TENANT_ID-fixture1/USER_ID-fixture1/SESSION_ID-fixture1/generated
rm -f session.sqlite.db
surveilr ingest files -d session.sqlite.db -r ../provenance -r ../responses
cat ../../../../../lforms.surveilr.sql | sqlite3 session.sqlite.db
```

## 5. Inspect the Database

Open `session.sqlite.db` and review the following views:

- **`uniform_resource_lform_item`** – Shows individual submitted items.
- **`uniform_resource_lform_item_grouped`** – Displays grouped data with associated weights and scores.
