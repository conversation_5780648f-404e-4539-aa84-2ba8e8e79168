# Opsfolio.com Website – Multi-Tenancy Options for Lead Magnets

## Background

- **Opsfolio Suite** (the product) is already designed with a robust
  **multi-tenancy model**.
- **Opsfolio.com** (the website) serves as a **lead-generation platform** by
  offering free **sample assessments, reports, and questionnaires** (lead
  magnets and content pillars).
- Today, these sample assessments run under an **anonymous tenant model** — each
  user account/session is isolated.

**New Requirement**:

- If two or more users from the same company sign up on Opsfolio.com, they
  should be able to **share sample assessment forms** and **resume each other’s
  work**.
- This will provide a more realistic preview of Opsfolio Suite’s collaboration
  features, encouraging customers to upgrade.

---

## 🔹 Option 1: Organization Creation During Signup (Direct Approach)

**Flow**

1. User signs up with **company name** in addition to personal details.
2. Website checks Zitadel (IDP) for existing org:

   - If org exists → user is linked to that org (pending admin approval if
     needed).
   - If not → new org created in Zitadel; user becomes Org Admin.
3. Auto-suggest helps avoid duplicate org names.

**Pros**

- Collaboration-ready from the start.
- Clear company-based grouping for leads.
- Strong continuity into Opsfolio Suite.

**Cons**

- Signup flow is longer (may lower conversion).
- Requires deduplication logic for company names.

---

## 🔹 Option 2: Organization Creation During First Assessment

**Flow**

1. User signs up with just personal details (lightweight).
2. During first **Company Information** form in a sample assessment, they
   provide **company name**.
3. Website queries Zitadel:

   - If org exists → user added to that org.
   - If not → org created, user = Org Admin.
4. Other colleagues signing up later can join same org.

**Pros**

- Lower signup friction (good for lead conversion).
- Captures company info at the “engagement point.”

**Cons**

- Possible duplicate orgs if company naming isn’t standardized.
- Requires backend org merge workflow.

---

## 🔹 Option 3: Invitation-Based Collaboration

**Flow**

1. First user signs up → creates a **sample assessment org** (Org Admin).
2. They can invite colleagues via **email links**.
3. Invitees join the same org in Zitadel and can **resume shared sample
   assessments**.

**Pros**

- Prevents tenant enumeration.
- Provides clear admin-led control (mirrors Suite behavior).
- Strong collaboration preview for leads.

**Cons**

- Requires first user to act as Org Admin quickly.
- Slightly more effort for additional users.

---

## 🔹 Option 4: Domain-Based Auto Organization Assignment

**Flow**

1. During signup, capture **email domain** (e.g., `@acme.com`).
2. Website checks Zitadel:

   - If domain mapped → user auto-assigned to org.
   - If not → new org created and mapped to that domain.
3. Exceptions (e.g., Gmail, Outlook) handled via manual org creation.

**Pros**

- Seamless for enterprise users.
- Reduces duplicate org creation.

**Cons**

- Doesn’t work for consultants/freelancers with generic emails.
- May create multiple orgs for same company if multiple domains exist.

---

## 🔹 Option 5: Hybrid (Signup + Later Org Linking)

**Flow**

1. User signs up → personal account only.
2. From dashboard, user can **create or join an org** at any time.

   - Search existing org (invite-only or request-to-join).
   - Create a new org if none found.
3. Assessments always run in an **org context**.

**Pros**

- Maximum flexibility (works for companies and consultants).
- Closely aligned with Opsfolio Suite’s model.
- Allows multiple org memberships.

**Cons**

- More complex UX.
- Requires user training on “org context.”

---

## 🔒 Implementation Safeguards

- **Prevent Tenant Enumeration** → never list orgs publicly.
- **Org Admin First** → first user in org = Org Admin.
- **Org Switching Support** → especially for consultants who test multiple
  clients.
- **Scoped Data** → all sample assessments tied to `organization_id`.

---

## 📌 Recommendation

- For **best lead conversion with realistic preview of Suite features**:

  - Use **Option 2 (Company Info During Assessment)** for low friction.
  - Or **Option 3 (Invitation-Based)** for strong collaboration demo.
- For **enterprise-focused lead funnels**:

  - Consider **Option 1 (Org at Signup)** with fuzzy matching and admin
    approval.
