---
import Layout from "../layouts/Layout.astro";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import VideoPlaceholder from "@/components/sections/VideoPlaceholder";

import { 
  Code, 
  GitBranch, 
  CheckCircle, 
  Users, 
  Zap, 
  Target,
  ArrowRight,
  Shield,
  Cpu,
  Database,
  FileText,
  TestTube,
  MessageSquare,
  AlertTriangle,
  TrendingUp,
  Eye,
  Lock,
  Workflow,
  BarChart3
} from "lucide-react";


const corePrinciples = [
  {
    icon: Code,
    title: "Infrastructure-as-Code for GRC",
    description: "Define policies and controls in code so they can be versioned, tested, and reviewed like any other software artifact. Every requirement is codified in YAML/JSON/Markdown.",
    benefits: [
      "Version control tracks every change to policies and procedures",
      "Ensures transparency and auditability",
      "Avoids brittle Word docs and spreadsheets",
      "Policies are testable and reviewable"
    ]
  },
  {
    icon: TrendingUp,
    title: "Continuous Compliance",
    description: "Move from snapshot audits to 24/7 monitoring. Compliance status is always up-to-date as part of the CI/CD pipeline.",
    benefits: [
      "Automated tests run on every code commit and deployment",
      "Real-time dashboards show compliance posture at a glance",
      "Problems are caught before they reach production or auditors",
      "Eliminates the annual scramble"
    ]
  },
  {
    icon: Workflow,
    title: "Automated Evidence",
    description: "Leverage existing workflows to generate proof automatically. Instead of filling forms, automatically harvest evidence from everyday tools.",
    benefits: [
      "Test results, code coverage reports become audit artifacts",
      "Slashes manual overhead and human error",
      "Ensures consistent, machine-attested evidence",
      "No more spreadsheets or manual documentation"
    ]
  },
  {
    icon: Users,
    title: "Developer-Centric Integration",
    description: "Embed compliance checks into the tools that dev teams use every day. Policy enforcement becomes a natural byproduct of standard development work.",
    benefits: [
      "Tight integration with Git repositories, CI servers, issue trackers",
      "Commit signatures, branch protection settings pulled from Git",
      "Automated test runs feed into compliance record",
      "Compliance as shared responsibility, not just GRC checkbox"
    ]
  }
];

const crisisPoints = [
  {
    icon: AlertTriangle,
    title: "The Compliance Trap",
    description: "Many CISOs and engineering leaders feel trapped in a vicious cycle of audits and blame. Rigid policies, manual attestations, and piles of paperwork dominate most GRC programs."
  },
  {
    icon: Shield,
    title: "Compliant Insecurity",
    description: "Most enterprise data breaches occur in companies with excellent adherence to regulatory provisions - because teams assume security rather than verifying it with code."
  },
  {
    icon: FileText,
    title: "The Blame Game",
    description: "The blame game between IT, security, and compliance stakeholders is no longer acceptable. Auditors are frustrated by inconsistent evidence and outdated controls."
  }
];

const workflowIntegrations = [
  {
    icon: GitBranch,
    title: "Code Repositories",
    description: "Version control systems become living audit trails. Every commit, code review, and branch merge becomes proof of change-management controls.",
    artifacts: ["Commit signatures", "Branch protection rules", "Code review records", "Change management controls"],
    integration: "Surveilr agent integrates with Git to pull in commit signatures, branch protection rules, and code review records automatically."
  },
  {
    icon: TestTube,
    title: "Testing and QA",
    description: "Automated test suites and CI pipelines are compliance goldmines. Each test execution, coverage report, and quality benchmark is recorded.",
    artifacts: ["Test execution logs", "Coverage reports", "Quality benchmarks", "Vulnerability scans"],
    integration: "Integrating Surveilr into Jenkins, GitHub Actions means test results flow into the Evidence Warehouse automatically."
  },
  {
    icon: MessageSquare,
    title: "Customer & Support Systems",
    description: "Operational controls like incident response, training, and customer feedback are tracked through existing support systems.",
    artifacts: ["Incident reports", "Support ticket resolutions", "Training completions", "Customer feedback"],
    integration: "Surveilr can ingest ticketing system logs and customer success artifacts as compliance proof."
  },
  {
    icon: Shield,
    title: "Security Toolchain",
    description: "Existing security tools are integrated seamlessly. Pen tests, access-control logs, and alerts become continuous evidence.",
    artifacts: ["Pen test results", "Access control logs", "Security alerts", "Vulnerability assessments"],
    integration: "Surveilr collects outputs of security tools, demonstrating active risk management without manual report gathering."
  }
];

const teamBenefits = [
  {
    title: "Engineers",
    description: "Focus on building great software while compliance evidence is collected automatically from your existing workflows. No more filling spreadsheets about code changes.",
    icon: Code,
    color: "bg-blue-50 border-blue-200 text-blue-700"
  },
  {
    title: "QA Specialists", 
    description: "Get recognition: every test you run, every bug you catch, feeds the compliance case. Your test suites prove quality controls without additional documentation.",
    icon: TestTube,
    color: "bg-green-50 border-green-200 text-green-700"
  },
  {
    title: "Security Analysts",
    description: "Define scans and checks in code. Scanning and monitoring tools output evidence seamlessly. Security posture is continually validated without side-tracking engineers.",
    icon: Shield,
    color: "bg-purple-50 border-purple-200 text-purple-700"
  },
  {
    title: "Customer Success & Ops",
    description: "Handle incidents and customer data while contributing evidence. Security incident tickets become evidence trails that policies were followed.",
    icon: MessageSquare,
    color: "bg-orange-50 border-orange-200 text-orange-700"
  },
  {
    title: "Tech Leaders & Auditors",
    description: "Get real-time visibility. CISOs can instantly see compliance gaps and trends. Auditors can query the warehouse directly or review machine-generated reports.",
    icon: Cpu,
    color: "bg-indigo-50 border-indigo-200 text-indigo-700"
  }
];

const implementationTips = [
  {
    icon: Code,
    title: "Treat Policies as Code",
    description: "Keep all policies in version-controlled repositories using machine-readable formats (YAML, Markdown) with automated testing."
  },
  {
    icon: ArrowRight,
    title: "Shift Left in SDLC",
    description: "Move compliance checks to the earliest possible stage. Integrate linters, static analysis, and policy validators into pull-request workflows."
  },
  {
    icon: Workflow,
    title: "Leverage Existing Tools",
    description: "Don't invent new manual tasks; piggyback on what teams already do. Configure webhooks or CLI commands that run Surveilr after each build."
  },
  {
    icon: Database,
    title: "Build Evidence Warehouse Early",
    description: "Deploy the Surveilr agent in development or staging as a pilot. The more historical evidence it gathers, the more painless audits become."
  },
  {
    icon: Lock,
    title: "Use Open Standards",
    description: "Store evidence in standard formats (JSON, CSV, SQL) to ensure portability and longevity. Surveilr's data is plain SQL tables."
  },
  {
    icon: BarChart3,
    title: "Automate Reporting",
    description: "Use SQL queries on the warehouse to drive dashboards or automated compliance reports. Keep compliance proactive with nightly summaries."
  }
];

const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Compliance as Code',"aria-disabled": true ,
  }
];
 
---

<Layout breadcrumbLinks={breadcrumbLinks}>
        <main>
             {/* Hero Section */}
        <section class="relative py-24 bg-gradient-to-br from-primary/5 via-background to-secondary/5">
          <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="mx-auto max-w-3xl text-center">
              <Badge variant="secondary" className="mb-4">
                GRC Revolution
              </Badge>
              <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
                Compliance-as-Code Manifesto
              </h1>
              <p class="mt-6 text-xl leading-8 text-muted-foreground">
                Reimagining GRC for Modern Enterprises
              </p>
              <p class="mt-4 text-lg leading-8 text-muted-foreground">
                Break free from endless checklists and transform compliance into a continuous, 
                code-driven process. No more annual scrambles, no more blame games - just 
                machine-attested evidence from your existing workflows.
              </p>
              <div class="mt-10 flex items-center justify-center gap-x-6">
                 <a href="/get-started"><Button  size="lg">
                 Join the Revolution
                </Button></a>
                <a href="/evidence-warehouse"><Button variant="outline"  size="lg">
                  Explore Evidence Warehouse
                </Button></a>
              </div>
            </div>
            
            {/* Manifesto Introduction Video */}
            <!-- <div class="mt-16">
              <VideoPlaceholder
                title="The Compliance-as-Code Revolution"
                description="Watch how leading organizations are breaking free from traditional GRC constraints and achieving continuous compliance"
                duration="4:20"
                variant="demo"
                aspectRatio="16:9"
                class="max-w-5xl mx-auto"
              />
            </div> -->
          </div>
        </section>

        {/* The Compliance Crisis Today */}
        <section class="py-24">
          <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="mx-auto max-w-2xl text-center">
              <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                The Compliance Crisis Today
              </h2>
              <p class="mt-6 text-lg leading-8 text-muted-foreground">
                We need a radical shift: Compliance must become a continuous, code-driven process, not an annual chore.
              </p>
            </div>
            
            <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
              <div class="grid max-w-xl grid-cols-1 gap-8 lg:max-w-none lg:grid-cols-3">
                {crisisPoints.map((point) => (
                  <Card key={point.title} className="border-destructive/20 bg-destructive/5">
                    <CardContent className="p-6">
                      <div class="flex items-start gap-4">
                        <div class="rounded-lg bg-destructive/10 p-2">
                          <point.icon class="h-6 w-6 text-destructive" />
                        </div>
                        <div>
                          <h3 class="text-lg font-semibold text-foreground mb-3">{point.title}</h3>
                          <p class="text-muted-foreground">{point.description}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
            
            <div class="mx-auto mt-16 max-w-4xl">
              <Card className="border-primary/20 bg-primary/5 p-8">
                <div class="text-center">
                  <h3 class="text-2xl font-bold text-foreground mb-4">
                    The Rise of Compliance-as-Code
                  </h3>
                  <p class="text-lg text-muted-foreground mb-6">
                    Leading thinkers describe a "GRC engineering" movement that embeds controls and evidence directly into development workflows. 
                    Instead of treating compliance as a gate at the end of the pipeline, we shift it left into every sprint and release.
                  </p>
                  <blockquote class="text-lg italic text-primary border-l-4 border-primary pl-6 mb-6">
                    "We should leverage the same DevOps channels used for infrastructure and software deployment to ensure that 
                    security and compliance are built into the software development lifecycle (SDLC)."
                  </blockquote>
                  <p class="text-lg text-muted-foreground">
                    <strong>In short:</strong> We must treat policies like code: version-controlled, testable, and continuously enforced.
                  </p>
                </div>
              </Card>
            </div>
          </div>
        </section>

        {/* Core Principles */}
        <section class="py-24 bg-muted">
          <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="mx-auto max-w-2xl text-center">
              <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                Core Principles of Compliance-as-Code
              </h2>
              <p class="mt-6 text-lg leading-8 text-muted-foreground">
                Opsfolio's vision for modern GRC rests on revolutionary principles that transform compliance from overhead into competitive advantage.
              </p>
            </div>
            
            {/* Principles Overview Video */}
            <!-- <div class="mx-auto mt-12 max-w-4xl">
              <VideoPlaceholder
                title="The Four Pillars of Compliance-as-Code"
                description="Deep dive into the foundational principles that make continuous compliance possible"
                duration="6:15"
                aspectRatio="16:9"
              />
            </div> -->
            
            <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
              <div class="grid max-w-xl grid-cols-1 gap-8 lg:max-w-none lg:grid-cols-2">
                {corePrinciples.map((principle) => (
                  <Card key={principle.title} className="border-border/50">
                    <CardHeader>
                      <div class="flex items-center gap-3">
                        <div class="rounded-lg bg-primary/10 p-3">
                          <principle.icon class="h-6 w-6 text-primary" />
                        </div>
                        <CardTitle className="text-xl">{principle.title}</CardTitle>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p class="text-muted-foreground mb-6">{principle.description}</p>
                      <ul class="space-y-2">
                        {principle.benefits.map((benefit) => (
                          <li  class="flex items-start gap-2">
                            <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                            <span class="text-sm text-muted-foreground">{benefit}</span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Embedding Compliance in Dev Workflows */}
        <section class="py-24">
          <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="mx-auto max-w-2xl text-center">
              <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                Embedding Compliance in Dev Workflows
              </h2>
              <p class="mt-6 text-lg leading-8 text-muted-foreground">
                Every stage of development and operations yields evidence for auditors. Surveilr acts as the machine-attested collector, 
                eliminating the need to fill out forms or take part in lengthy meetings.
              </p>
            </div>
            
            {/* Workflow Integration Demo */}
            <!-- <div class="mx-auto mt-12 max-w-4xl">
              <VideoPlaceholder
                title="Surveilr in Action: Workflow Integrations"
                description="See how Surveilr automatically collects evidence from Git, CI/CD, support systems, and security tools"
                duration="5:30"
                aspectRatio="16:9"
              />
            </div> -->
            
            <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
              <div class="grid max-w-xl grid-cols-1 gap-8 lg:max-w-none lg:grid-cols-2">
                {workflowIntegrations.map((workflow) => (
                  <Card key={workflow.title} className="border-border/50 bg-card">
                    <CardHeader>
                      <div class="flex items-center gap-3">
                        <div class="rounded-lg bg-primary/10 p-3">
                          <workflow.icon className="h-6 w-6 text-primary" />
                        </div>
                        <CardTitle className="text-xl">{workflow.title}</CardTitle>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p class="text-muted-foreground mb-4">{workflow.description}</p>
                      
                      <div class="bg-muted/50 p-4 rounded-lg mb-6">
                        <h4 class="font-medium text-foreground mb-2">How it works:</h4>
                        <p class="text-sm text-muted-foreground italic">{workflow.integration}</p>
                      </div>
                      
                      <div class="space-y-2">
                        <h4 class="font-medium text-foreground">Evidence Generated:</h4>
                        <div class="flex flex-wrap gap-2">
                          {workflow.artifacts.map((artifact) => (
                            <Badge key={artifact} variant="secondary" class="text-xs">
                              {artifact}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* The Evidence Warehouse */}
        <section class="py-24 bg-muted">
          <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="mx-auto max-w-2xl text-center">
              <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                The Opsfolio Evidence Warehouse
              </h2>
              <p class="mt-6 text-lg leading-8 text-muted-foreground">
                At the heart of this system is the Evidence Warehouse – Opsfolio's private, SQL-backed repository of compliance data.
              </p>
            </div>
            
            <!-- <div class="mx-auto mt-12 max-w-4xl">
              <VideoPlaceholder
                title="Inside the Evidence Warehouse"
                description="Explore the SQL-queryable compliance data store that makes machine-attested evidence possible"
                duration="4:45"
                aspectRatio="16:9"
              />
            </div> -->
            
            <div class="mx-auto mt-16 max-w-6xl">
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <Card className="border-primary/20">
                  <CardHeader>
                    <div class="flex items-center gap-3">
                      <Lock class="h-6 w-6 text-primary" />
                      <CardTitle>Local-First & Private</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p class="text-muted-foreground">
                      The warehouse runs entirely under your control. Surveilr has no external dependencies; 
                      your sensitive data never leaves your premises. This edge-based model ensures data stays secure.
                    </p>
                  </CardContent>
                </Card>
                
                <Card className="border-primary/20">
                  <CardHeader>
                    <div class="flex items-center gap-3">
                      <TrendingUp class="h-6 w-6 text-primary" />
                      <CardTitle>Continuous Collection</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p class="text-muted-foreground">
                      Rather than one-off audits, Surveilr automatically collects and updates evidence nonstop. 
                      Whether it's a nightly build or a security alert, the warehouse stays up-to-date.
                    </p>
                  </CardContent>
                </Card>
                
                <Card className="border-primary/20">
                  <CardHeader>
                    <div class="flex items-center gap-3">
                      <Database className="h-6 w-6 text-primary" />
                      <CardTitle>SQL Queryable Store</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p class="text-muted-foreground">
                      Because everything is in SQL, you can query it like any database. Audit questions become simple queries, 
                      not manual searches. This queryability makes compliance machine-attestable.
                    </p>
                  </CardContent>
                </Card>
                
                <Card className="border-primary/20">
                  <CardHeader>
                    <div class="flex items-center gap-3">
                      <FileText className="h-6 w-6 text-primary" />
                      <CardTitle>Audit-Ready Reporting</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p class="text-muted-foreground">
                      Generate compliance reports on demand. With evidence already collected, auditors get comprehensive 
                      reports that meet standards out-of-the-box. No more scrambling for yearly audit folders.
                    </p>
                  </CardContent>
                </Card>
              </div>
              
              <div class="mt-12 text-center">
                <Card className="bg-gradient-to-r from-primary/10 to-secondary/10 border-primary/20 p-8">
                  <h3 class="text-2xl font-bold text-foreground mb-4">DRY Compliance</h3>
                  <p class="text-lg text-muted-foreground">
                    "Don't Repeat Yourself" - each piece of evidence is generated once and reused for all controls. 
                    A single log entry can satisfy multiple policies, dramatically cutting overhead and avoiding duplicate work.
                  </p>
                </Card>
              </div>
            </div>
          </div>
        </section>

        {/* Empowering Every Team Member */}
        <section class="py-24">
          <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="mx-auto max-w-2xl text-center">
              <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                Empowering Every Team Member
              </h2>
              <p class="mt-6 text-lg leading-8 text-muted-foreground">
                This approach transforms compliance into a team effort without extra burdens. Individual contributors 
                become the generators of compliance proof by simply doing their jobs.
              </p>
            </div>
            
            <!-- <div class="mx-auto mt-12 max-w-4xl">
              <VideoPlaceholder
                title="Team Success Stories"
                description="Hear from engineers, QA specialists, and security teams about how Compliance-as-Code transformed their daily work"
                duration="3:40"
                aspectRatio="16:9"
              />
            </div> -->
            
            <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
              <div class="grid max-w-xl grid-cols-1 gap-6 lg:max-w-none lg:grid-cols-2 xl:grid-cols-3">
                {teamBenefits.map((benefit) => (
                  <Card key={benefit.title} className={`border-2 ${benefit.color}`}>
                    <CardContent className="p-6">
                      <div class="flex items-start gap-4">
                        <div class="rounded-lg bg-background p-3 border-2 border-current">
                          <benefit.icon className="h-6 w-6" />
                        </div>
                        <div class="flex-1">
                          <h3 class="text-lg font-semibold mb-3">{benefit.title}</h3>
                          <p class="text-sm opacity-80">{benefit.description}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Implementation Strategies */}
        <section class="py-24 bg-muted">
          <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="mx-auto max-w-2xl text-center">
              <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                Implementation Strategies and Tips
              </h2>
              <p class="mt-6 text-lg leading-8 text-muted-foreground">
                This vision is fully attainable with practical steps. Here's how to put Compliance-as-Code into action.
              </p>
            </div>
            
            <!-- <div class="mx-auto mt-12 max-w-4xl">
              <VideoPlaceholder
                title="Getting Started with Compliance-as-Code"
                description="Step-by-step implementation guide from proof-of-concept to full production deployment"
                duration="7:20"
                aspectRatio="16:9"
              />
            </div> -->
            
            <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
              <div class="grid max-w-xl grid-cols-1 gap-8 lg:max-w-none lg:grid-cols-2">
                {implementationTips.map((tip) => (
                  <Card key={tip.title} className="border-border/50">
                    <CardContent className="p-6">
                      <div class="flex items-start gap-4">
                        <div class="rounded-lg bg-primary/10 p-2">
                          <tip.icon class="h-6 w-6 text-primary" />
                        </div>
                        <div class="flex-1">
                          <h3 class="text-lg font-semibold text-foreground mb-3">{tip.title}</h3>
                          <p class="text-muted-foreground">{tip.description}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* A New Era of Continuous Assurance */}
        <section class="py-24">
          <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="mx-auto max-w-4xl text-center">
              <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl mb-8">
                A New Era of Continuous Assurance
              </h2>
              
              <div class="bg-card p-8 rounded-2xl border shadow-lg mb-12">
                <p class="text-lg text-muted-foreground mb-6">
                  Compliance-as-code is more than a set of tools – it's a mindset shift. Opsfolio's manifesto is that 
                  compliance should empower, not impede. By treating GRC like software, we transform it into a source of competitive advantage.
                </p>
                
                <div class="grid md:grid-cols-3 gap-6 mb-8">
                  <div class="text-center">
                    <Code className="h-8 w-8 text-primary mx-auto mb-3" />
                    <h4 class="font-semibold text-foreground mb-2">For Engineers</h4>
                    <p class="text-sm text-muted-foreground">Build without constant audit interruptions</p>
                  </div>
                  <div class="text-center">
                    <Eye className="h-8 w-8 text-primary mx-auto mb-3" />
                    <h4 class="font-semibold text-foreground mb-2">For CISOs</h4>
                    <p class="text-sm text-muted-foreground">Gain confidence from hard data and real-time dashboards</p>
                  </div>
                  <div class="text-center">
                    <Target className="h-8 w-8 text-primary mx-auto mb-3" />
                    <h4 class="font-semibold text-foreground mb-2">For Business</h4>
                    <p class="text-sm text-muted-foreground">Security is baked into every release</p>
                  </div>
                </div>
                
                <blockquote class="text-xl italic text-primary mb-6 border-l-4 border-primary pl-6">
                  "By adopting Opsfolio's approach – machine-attested evidence, a private data warehouse, and policy-as-code – 
                  organizations can finally break free from endless checklists and start demonstrating true, continuously-verified compliance."
                </blockquote>
              </div>
              
              <!-- <div class="mb-12">
                <VideoPlaceholder
                  title="The Future of GRC is Here"
                  description="Join the Compliance-as-Code revolution where the future of GRC is written in code, powered by Surveilr, and executed with rigor and speed"
                  duration="2:50"
                  aspectRatio="16:9"
                  class="max-w-3xl mx-auto"
                />
              </div> -->

              <div class="bg-card text-card-foreground shadow-sm bg-gradient-to-r from-primary/10 to-secondary/10 border-primary/20 p-8 rounded-2xl">
                <h3 class="text-2xl font-bold mb-4 text-foreground">Join the Compliance-as-Code Movement</h3>
                <p class="text-lg mb-8 opacity-90">
                  It's practical, it's achievable today, and it promises to make compliance auditable, transparent, and even agile.
                </p>
                <div class="flex flex-col sm:flex-row items-center justify-center gap-4">
                  <a href="/get-started"><Button  size="lg" variant="secondary">
                      Start Your Revolution
                      <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                  </a>
                  <a href="/evidence-warehouse">
                  <Button  size="lg" variant="default">
                    Explore Surveilr
                  </Button>
                  </a>
                  <a href="/contact">
                  <Button  size="lg" variant="outline">
                    Schedule Demo
                  </Button>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

</Layout>