---
import Layout from "../../layouts/Layout.astro";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Shield, 
  Users, 
  Database, 
  FileText, 
  AlertTriangle, 
  CheckCircle,
  Globe,
  Lock,
  UserCheck
} from "lucide-react";

const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Regime',"aria-disabled": true ,
  },
  {
    index: "2", text: "GDPR","aria-disabled": true 
  },
];
---
<Layout title="Opsfolio - Compliance as a Service | SOC2, HIPAA, ISO Certification"
  description="Get SOC2, HIPAA, ISO certified fast with expert-guided compliance. 98% audit pass rate, 60% faster than DIY. Your Compliance System of Record."
  keywords="GDPR, compliance, SOC2, HIPAA, ISO 27001, cybersecurity, audit, certification"
  url="/regime/pgdpr/"
  breadcrumbLinks={breadcrumbLinks}>

        {/* Hero Section */}
      <section class="py-24 sm:py-32 bg-gradient-to-b from-primary/5 to-background">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-3xl text-center">
            <Badge variant="outline" className="mb-4">
              <Globe className="w-4 h-4 mr-2" />
              EU Data Protection
            </Badge>
            <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
              GDPR Compliance
            </h1>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Navigate EU data protection requirements with confidence. Ensure privacy by design, 
              implement data subject rights, and maintain continuous compliance monitoring.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
              
                <a href="/get-started">
                  <Button size="lg">Start GDPR Assessment
                  </Button>
                </a>
                <a href="/contact">
                  <Button variant="outline" size="lg">
                    Book Consultation
                  </Button>
                </a>
            </div>
          </div>
        </div>
      </section>

      {/* Key Requirements */}
      <section class="py-24 sm:py-32">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center mb-16">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              GDPR Key Requirements
            </h2>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Essential principles and obligations for EU data protection compliance
            </p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="border-border/50">
              <CardHeader>
                <Shield className="h-8 w-8 text-primary mb-2" />
                <CardTitle>Data Protection by Design</CardTitle>
              </CardHeader>
              <CardContent>
                <p class="text-muted-foreground">
                  Implement privacy safeguards from system inception, ensuring data protection 
                  is built into every process and technology decision.
                </p>
              </CardContent>
            </Card>

            <Card className="border-border/50">
              <CardHeader>
                <UserCheck className="h-8 w-8 text-primary mb-2" />
                <CardTitle>Data Subject Rights</CardTitle>
              </CardHeader>
              <CardContent>
                <p class="text-muted-foreground">
                  Enable individuals to access, correct, delete, and port their data. 
                  Implement automated systems for handling rights requests efficiently.
                </p>
              </CardContent>
            </Card>

            <Card className="border-border/50">
              <CardHeader>
                <FileText className="h-8 w-8 text-primary mb-2" />
                <CardTitle>Legal Basis & Consent</CardTitle>
              </CardHeader>
              <CardContent>
                <p class="text-muted-foreground">
                  Establish clear legal grounds for processing. Implement granular consent 
                  management with easy withdrawal mechanisms.
                </p>
              </CardContent>
            </Card>

            <Card className="border-border/50">
              <CardHeader>
                <Database className="h-8 w-8 text-primary mb-2" />
                <CardTitle>Data Minimization</CardTitle>
              </CardHeader>
              <CardContent>
                <p class="text-muted-foreground">
                  Process only necessary data for specified purposes. Implement automated 
                  data retention and deletion policies.
                </p>
              </CardContent>
            </Card>

            <Card className="border-border/50">
              <CardHeader>
                <AlertTriangle className="h-8 w-8 text-primary mb-2" />
                <CardTitle>Breach Notification</CardTitle>
              </CardHeader>
              <CardContent>
                <p class="text-muted-foreground">
                  Detect and report breaches within 72 hours. Implement automated 
                  incident response and notification workflows.
                </p>
              </CardContent>
            </Card>

            <Card className="border-border/50">
              <CardHeader>
                <Users className="h-8 w-8 text-primary mb-2" />
                <CardTitle>DPO & Governance</CardTitle>
              </CardHeader>
              <CardContent>
                <p class="text-muted-foreground">
                  Appoint Data Protection Officers where required. Establish privacy 
                  governance frameworks and regular compliance audits.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Implementation Approach */}
      <section class="py-24 sm:py-32 bg-muted">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center mb-16">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Smart GDPR Implementation
            </h2>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Our AI-powered platform automates compliance monitoring and evidence collection
            </p>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div class="space-y-8">
                <div class="flex items-start space-x-4">
                  <CheckCircle className="h-6 w-6 text-primary mt-1 flex-shrink-0" />
                  <div>
                    <h3 class="font-semibold text-foreground">Privacy Impact Assessments</h3>
                    <p class="text-muted-foreground mt-1">
                      Automated DPIA workflows with AI-powered risk scoring and mitigation recommendations.
                    </p>
                  </div>
                </div>

                <div class="flex items-start space-x-4">
                  <CheckCircle className="h-6 w-6 text-primary mt-1 flex-shrink-0" />
                  <div>
                    <h3 class="font-semibold text-foreground">Consent Management</h3>
                    <p class="text-muted-foreground mt-1">
                      Real-time consent tracking with granular controls and withdrawal automation.
                    </p>
                  </div>
                </div>

                <div class="flex items-start space-x-4">
                  <CheckCircle className="h-6 w-6 text-primary mt-1 flex-shrink-0" />
                  <div>
                    <h3 class="font-semibold text-foreground">Data Mapping & Lineage</h3>
                    <p class="text-muted-foreground mt-1">
                      Automated discovery and mapping of personal data flows across your infrastructure.
                    </p>
                  </div>
                </div>

                <div class="flex items-start space-x-4">
                  <CheckCircle className="h-6 w-6 text-primary mt-1 flex-shrink-0" />
                  <div>
                    <h3 class="font-semibold text-foreground">Cross-Border Transfer Controls</h3>
                    <p class="text-muted-foreground mt-1">
                      Monitor international data transfers with automated adequacy decision checking.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div class="lg:text-center">
              <Card className="p-8 bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20">
                <div class="space-y-4">
                  <Lock className="h-12 w-12 text-primary mx-auto" />
                  <h3 class="text-xl font-semibold text-foreground">Privacy by Design</h3>
                  <p class="text-muted-foreground">
                    Our platform embeds privacy controls directly into your development workflows, 
                    ensuring GDPR compliance is maintained automatically as your systems evolve.
                  </p>
                  <div class="grid grid-cols-2 gap-4 mt-6">
                    <div class="text-center">
                      <div class="text-2xl font-bold text-primary">99.9%</div>
                      <div class="text-sm text-muted-foreground">Uptime SLA</div>
                    </div>
                    <div class="text-center">
                      <div class="text-2xl font-bold text-primary">&lt;1min</div>
                      <div class="text-sm text-muted-foreground">Response Time</div>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section class="py-24 sm:py-32">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Ready for GDPR Compliance?
            </h2>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Start your GDPR journey with our AI-powered compliance platform. 
              Get audit-ready in weeks, not months.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
              
                <a href="/get-started">
                  <Button size="lg">Get Started Today
                  </Button>
                </a>  
                <a href="/resources">
                  <Button variant="outline" size="lg">View Resources
                  </Button>
                </a>    
            </div>
          </div>
        </div>
      </section>

</Layout>