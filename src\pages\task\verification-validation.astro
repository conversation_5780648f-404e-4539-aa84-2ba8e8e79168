---
import Layout from "../../layouts/Layout.astro";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Activity, Microscope, Shield, Zap, GitBranch, Workflow, ArrowRight, Cpu, Network, Eye, Code, Lock, Target, CheckCircle, FileCode, Layers } from "lucide-react";
  const capabilities = [
    {
      icon: Code,
      title: "Deterministic Software V&V",
      description: "Classical software verification ensuring correctness, reliability, and security through formal methods and systematic testing approaches.",
      metric: "Formal Proof"
    },
    {
      icon: Cpu,
      title: "Probabilistic Software V&V", 
      description: "Modern AI/ML system validation accounting for uncertainty, model drift, and probabilistic behaviors in intelligent systems.",
      metric: "AI/ML Ready"
    },
    {
      icon: Shield,
      title: "Security V&V Testing",
      description: "Penetration testing and security validation throughout the development lifecycle to identify vulnerabilities early.",
      metric: "Continuous Testing"
    },
    {
      icon: Lock,
      title: "SDL Security Integration",
      description: "Deep integration of security practices into every SDL phase, creating a culture where security by design is the default approach.",
      metric: "By Design"
    },
    {
      icon: Target,
      title: "Customer-Driven V&V",
      description: "Real verification and validation based on your specific customer requirements and use cases, not generic compliance templates.",
      metric: "Custom Fit"
    },
    {
      icon: CheckCircle,
      title: "Embedded Compliance Evidence",
      description: "Compliance artifacts automatically generated from secure development practices, making compliance a natural outcome of secure design.",
      metric: "Auto-Evidence"
    },
    {
      icon: Layers,
      title: "Uncertainty-Aware Security",
      description: "Advanced security assurance for modern probabilistic systems that accounts for both traditional correctness and inherent uncertainty.",
      metric: "Future-Ready"
    }
  ];

  const validationTypes = [
    {
      type: "Artifact/Specification Verification",
      description: "Ensures each development stage correctly implements its input specifications and requirements",
      examples: ["Requirements traceability", "Design validation", "Code review compliance", "Test case verification"]
    },
    {
      type: "External Validation", 
      description: "Stakeholder feedback integration to confirm software meets real-world needs and security requirements",
      examples: ["User acceptance testing", "Security validation", "Performance benchmarking", "Compliance verification"]
    },
    {
      type: "Security Lifecycle Validation",
      description: "Continuous validation of security practices throughout the software development lifecycle",
      examples: ["Threat modeling", "Secure coding practices", "Penetration testing", "Security architecture review"]
    }
  ];


const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Task',"aria-disabled": true ,
  },
  {
    index: "2", text: "Verification & Validation (V&V)","aria-disabled": true 
  },
];
---
<Layout breadcrumbLinks={breadcrumbLinks}>

     {/* Hero Section */}
      <section class="relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5 pt-16 pb-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-3xl text-center">
            <Badge variant="outline" className="mb-4">Software & Security V&V Services</Badge>
            <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
              Software & Security Verification & Validation
            </h1>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              We're not your typical compliance paperwork company. Opsfolio CaaS is a security-first compliance platform that proves 
              secure design and implementation through real software verification and validation, embedding security by design directly 
              into your development lifecycle—making security and compliance natural outcomes, not afterthoughts.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
              <a href="/get-started">
              <Button size="lg" >
                Start Security-First V&V<ArrowRight className="ml-2 h-4 w-4" />
              </Button>
              </a>
            </div>
            
            {/* Key Differentiators */}
            <div class="mt-16 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 text-center">
              <div class="p-4">
                <div class="text-2xl font-bold text-primary">Security-First</div>
                <div class="text-sm text-muted-foreground">Not paperwork-first</div>
              </div>
              <div class="p-4">
                <div class="text-2xl font-bold text-primary">Secure by Design</div>
                <div class="text-sm text-muted-foreground">Embedded in SDL</div>
              </div>
              <div class="p-4">
                <div class="text-2xl font-bold text-primary">Real V&V</div>
                <div class="text-sm text-muted-foreground">Customer-driven validation</div>
              </div>
              <div class="p-4">
                <div class="text-2xl font-bold text-primary">AI/ML Ready</div>
                <div class="text-sm text-muted-foreground">Probabilistic systems</div>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* Security-First Positioning Section */}
      <section class="py-20 ">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-4xl text-center mb-16">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl mb-6">
              Redefining Compliance: Security-First, Not Paperwork-First
            </h2>
            <p class="text-lg text-muted-foreground mb-8">
              Traditional compliance companies focus on documentation and checkboxes. Opsfolio CaaS is fundamentally different—we're 
              a security-first compliance platform that proves secure design and implementation through real verification and validation.
            </p>
          </div>
          
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h3 class="text-2xl font-semibold mb-6">What Makes Us Different</h3>
              <div class="space-y-4">
                <div class="flex items-start gap-3">
                  <Shield className="h-6 w-6 text-primary mt-1 flex-shrink-0" />
                  <div>
                    <div class="font-semibold">Security by Design Integration</div>
                    <div class="text-muted-foreground">We embed security and compliance evidence directly into your Secure Development Lifecycle (SDL), making them natural outcomes rather than afterthoughts.</div>
                  </div>
                </div>
                <div class="flex items-start gap-3">
                  <CheckCircle className="h-6 w-6 text-primary mt-1 flex-shrink-0" />
                  <div>
                    <div class="font-semibold">Real Software V&V</div>
                    <div class="text-muted-foreground">We perform genuine verification and validation based on your specific customer needs and use cases, not generic compliance templates.</div>
                  </div>
                </div>
                <div class="flex items-start gap-3">
                  <Code className="h-6 w-6 text-primary mt-1 flex-shrink-0" />
                  <div>
                    <div class="font-semibold">Modern Software Reality</div>
                    <div class="text-muted-foreground">We handle both classic deterministic software and modern probabilistic software (AI/ML systems) where security assurance must account for uncertainty.</div>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="bg-card border rounded-lg p-8">
              <h4 class="text-xl font-semibold mb-4">Traditional vs. Security-First Approach</h4>
              <div class="space-y-4">
                <div>
                  <div class="text-sm font-medium text-muted-foreground">Traditional Compliance</div>
                  <div class="text-red-500">→ Documentation-heavy, checkbox mentality</div>
                </div>
                <div>
                  <div class="text-sm font-medium text-muted-foreground">Opsfolio CaaS</div>
                  <div class="text-green-500">→ Security-first, evidence-based validation</div>
                </div>
                <div class="mt-6 p-4 bg-primary/10 rounded-lg">
                  <div class="text-sm font-medium">Customer Perception Shift</div>
                  <div class="text-sm text-muted-foreground mt-1">
                    See us as the platform that proves your software is secure by design—not just compliant on paper.
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Secure Development Lifecycle Integration */}
      <section class="py-20 bg-muted">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center mb-16">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Secure Development Lifecycle (SDL) Integration
            </h2>
            <p class="mt-4 text-lg text-muted-foreground">
              Opsfolio CaaS is known for improving software security through comprehensive SDL integration, 
              making security and compliance natural parts of your development process.
            </p>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="text-center">
              <CardHeader>
                <FileCode className="h-8 w-8 text-primary mx-auto mb-2" />
                <CardTitle className="text-lg">Requirements Phase</CardTitle>
              </CardHeader>
              <CardContent>
                <p class="text-sm text-muted-foreground">
                  Security requirements embedded from day one, with automated compliance mapping and threat modeling integration.
                </p>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardHeader>
                <Code className="h-8 w-8 text-primary mx-auto mb-2" />
                <CardTitle className="text-lg">Design & Development</CardTitle>
              </CardHeader>
              <CardContent>
                <p class="text-sm text-muted-foreground">
                  Secure coding practices with real-time security validation and automated evidence collection during development.
                </p>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardHeader>
                <Shield className="h-8 w-8 text-primary mx-auto mb-2" />
                <CardTitle className="text-lg">Testing & Validation</CardTitle>
              </CardHeader>
              <CardContent>
                <p class="text-sm text-muted-foreground">
                  Comprehensive security testing including penetration testing and formal verification methods for critical systems.
                </p>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardHeader>
                <CheckCircle className="h-8 w-8 text-primary mx-auto mb-2" />
                <CardTitle className="text-lg">Deployment & Monitoring</CardTitle>
              </CardHeader>
              <CardContent>
                <p class="text-sm text-muted-foreground">
                  Continuous security monitoring with automated compliance reporting and evidence generation.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
      {/* Capabilities Grid */}
      <section class="py-20 ">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center mb-16">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Comprehensive V&V Services
            </h2>
            <p class="mt-4 text-lg text-muted-foreground">
              Software and security verification services that build secure development culture and compliance readiness
            </p>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {capabilities.map((capability, index) => (
              <Card key={index} className="border-border/50 hover:border-primary/50 transition-colors">
                <CardHeader>
                  <div class="flex items-center justify-between">
                    <capability.icon className="h-8 w-8 text-primary" />
                    <Badge variant="secondary" className="text-xs">{capability.metric}</Badge>
                  </div>
                  <CardTitle className="text-xl">{capability.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">
                    {capability.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Validation Types */}
      <section class="py-20 bg-muted">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center mb-16">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Core V&V Concepts
            </h2>
            <p class="mt-4 text-lg text-muted-foreground">
              Understanding the fundamental difference: Verification asks "Are we building the product right?" 
              while Validation asks "Are we building the right product?" Both are essential for secure, compliant software.
            </p>
          </div>
          
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {validationTypes.map((validation, index) => (
              <Card key={index} className="h-full">
                <CardHeader>
                  <CardTitle className="text-xl text-center">{validation.type}</CardTitle>
                  <CardDescription className="text-center">
                    {validation.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul class="space-y-2">
                    {validation.examples.map((example, exIndex) => (
                      <li  class="flex items-center gap-2 text-sm">
                        <Shield className="h-4 w-4 text-primary flex-shrink-0" />
                        {example}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Real-time Dashboard Preview */}
      <section class="py-20 ">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center mb-16">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Security-First Development Culture
            </h2>
            <p class="mt-4 text-lg text-muted-foreground">
              Transform your organization into a security-conscious development environment that naturally produces compliance artifacts
            </p>
          </div>
          
          <div class="bg-card border rounded-lg p-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div class="text-center">
                <div class="text-3xl font-bold text-green-500">100%</div>
                <div class="text-sm text-muted-foreground">Automated Compliance Artifacts</div>
              </div>
              <div class="text-center">
                <div class="text-3xl font-bold text-blue-500">Security</div>
                <div class="text-sm text-muted-foreground">by Design Culture</div>
              </div>
              <div class="text-center">
                <div class="text-3xl font-bold text-primary">Continuous</div>
                <div class="text-sm text-muted-foreground">V&V Integration</div>
              </div>
            </div>
            
            <div class="flex justify-center">
              <a href="/get-started">
              <Button size="lg" >
                Start V&V Assessment<ArrowRight className="ml-2 h-4 w-4" />
              </Button>
              </a>
            </div>
          </div>
        </div>
      </section>

</Layout>
      