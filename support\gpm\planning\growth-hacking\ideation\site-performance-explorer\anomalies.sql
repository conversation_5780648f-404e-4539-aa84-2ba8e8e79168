-- anomalies.sql
-- title: Anomalies
-- menu: Anomalies
-- description: Spike finder and bot heuristics.

WITH params AS (
  SELECT
    coalesce(:from::timestamp, now() - interval '14 days') AS from_ts,
    coalesce(:to::timestamp, now()) AS to_ts,
    NULLIF(trim(coalesce(:env, '')), '')::text       AS env,
    NULLIF(trim(coalesce(:service, '')), '')::text   AS service,
    NULLIF(trim(coalesce(:path_like, '')), '')::text AS path_like
),
range_meta AS (
  SELECT *,
    CASE WHEN (to_ts - from_ts) > interval '21 days' THEN 'day' ELSE 'hour' END AS trunc_unit
  FROM params
)

-- Time buckets with requests and unique sessions
, bucketed AS (
  SELECT
    CASE WHEN rm.trunc_unit = 'day' THEN date_trunc('day', wt.ts) ELSE date_trunc('hour', wt.ts) END AS bucket,
    COUNT(*) AS requests,
    COUNT(DISTINCT wt.session_id) AS unique_sessions
  FROM opsfolio.web_traffic wt, range_meta rm
  WHERE wt.ts >= rm.from_ts
    AND wt.ts < rm.to_ts
    AND (rm.env IS NULL OR rm.env = wt.env)
    AND (rm.service IS NULL OR rm.service = wt.service)
    AND (rm.path_like IS NULL OR wt.request_path ILIKE '%' || rm.path_like || '%')
  GROUP BY bucket
  ORDER BY bucket
)

-- Compute mean/stddev to find z-scores
, stats AS (
  SELECT avg(requests)::numeric AS mean_requests, stddev_pop(requests)::numeric AS stddev_requests FROM bucketed
)

-- Spike finder: z-score > 3 (configurable)
SELECT
  b.bucket,
  b.requests,
  b.unique_sessions,
  CASE WHEN s.stddev_requests IS NULL OR s.stddev_requests = 0 THEN NULL
       ELSE (b.requests - s.mean_requests) / s.stddev_requests END AS z_score,
  CASE WHEN (s.stddev_requests IS NOT NULL AND s.stddev_requests > 0 AND (b.requests - s.mean_requests) / s.stddev_requests) > 3 THEN true ELSE false END AS is_spike
FROM bucketed b, stats s
ORDER BY b.bucket DESC;

-- Bot heuristics 1: UA bot/crawler ratio by bucket
, ua_flags AS (
  SELECT
    CASE WHEN rm.trunc_unit = 'day' THEN date_trunc('day', wt.ts) ELSE date_trunc('hour', wt.ts) END AS bucket,
    SUM(CASE WHEN wt.user_agent ILIKE '%bot%' OR wt.user_agent ILIKE '%crawl%' OR wt.user_agent ILIKE '%spider%' THEN 1 ELSE 0 END) AS bot_hits,
    COUNT(*) AS total_hits
  FROM opsfolio.web_traffic wt, range_meta rm
  WHERE wt.ts >= rm.from_ts
    AND wt.ts < rm.to_ts
    AND (rm.env IS NULL OR rm.env = wt.env)
    AND (rm.service IS NULL OR rm.service = wt.service)
    AND (rm.path_like IS NULL OR wt.request_path ILIKE '%' || rm.path_like || '%')
  GROUP BY bucket
  ORDER BY bucket DESC
)
SELECT bucket, bot_hits, total_hits, ROUND(100.0 * bot_hits::numeric / NULLIF(total_hits,0)::numeric,2) AS pct_bot_hits
FROM ua_flags
ORDER BY bucket DESC
LIMIT 200;

-- Bot heuristics 2: sessions that only ever hit '/'
WITH single_page_sessions AS (
  SELECT session_id,
         COUNT(DISTINCT request_path) AS distinct_paths,
         MIN(ts) AS first_ts,
         MAX(ts) AS last_ts
  FROM opsfolio.web_traffic wt
  JOIN range_meta rm ON true
  WHERE wt.ts >= rm.from_ts
    AND wt.ts < rm.to_ts
    AND (rm.env IS NULL OR rm.env = wt.env)
    AND (rm.service IS NULL OR rm.service = wt.service)
    AND (rm.path_like IS NULL OR wt.request_path ILIKE '%' || rm.path_like || '%')
  GROUP BY session_id
  HAVING COUNT(DISTINCT request_path) = 1
)
SELECT s.session_id, s.first_ts, s.last_ts
FROM single_page_sessions s
ORDER BY s.first_ts DESC
LIMIT 200;

-- Bot heuristics 3: high-frequency IPs in short windows (e.g., hits > 500 in any 1-hour bucket)
, ip_hourly AS (
  SELECT remote_addr,
         date_trunc('hour', ts) AS hour_bucket,
         COUNT(*) AS hits
  FROM opsfolio.web_traffic wt
  JOIN range_meta rm ON true
  WHERE wt.ts >= rm.from_ts
    AND wt.ts < rm.to_ts
    AND (rm.env IS NULL OR rm.env = wt.env)
    AND (rm.service IS NULL OR rm.service = wt.service)
    AND (rm.path_like IS NULL OR wt.request_path ILIKE '%' || rm.path_like || '%')
  GROUP BY remote_addr, hour_bucket
)
SELECT remote_addr, hour_bucket, hits
FROM ip_hourly
WHERE hits > 500
ORDER BY hits DESC
LIMIT 200;

-- Trend chart data for bots vs normal: bucketed bot_pct + total requests
SELECT
  b.bucket,
  b.requests,
  COALESCE(ROUND(100.0 * uf.bot_hits::numeric / NULLIF(b.requests,0)::numeric,2),0) AS pct_bot_hits
FROM bucketed b
LEFT JOIN (
  SELECT
    CASE WHEN rm.trunc_unit = 'day' THEN date_trunc('day', wt.ts) ELSE date_trunc('hour', wt.ts) END AS bucket,
    SUM(CASE WHEN wt.user_agent ILIKE '%bot%' OR wt.user_agent ILIKE '%crawl%' OR wt.user_agent ILIKE '%spider%' THEN 1 ELSE 0 END) AS bot_hits
  FROM opsfolio.web_traffic wt, range_meta rm
  WHERE wt.ts >= rm.from_ts
    AND wt.ts < rm.to_ts
    AND (rm.env IS NULL OR rm.env = wt.env)
    AND (rm.service IS NULL OR rm.service = wt.service)
    AND (rm.path_like IS NULL OR wt.request_path ILIKE '%' || rm.path_like || '%')
  GROUP BY bucket
) uf ON uf.bucket = b.bucket
ORDER BY b.bucket;
