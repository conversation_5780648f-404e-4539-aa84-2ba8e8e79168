We’re moving to a **server-side analytics baseline** (like <PERSON><PERSON>’s TEM flow) so
our funnel reporting doesn’t hinge on GA4’s gaps. Below is the end-to-end
plan—**where logs live, how to ingest with Surveilr**, how to make them
**SQL-queryable**, and how to expose them via a **SQLPage** web UI.

> IMPORTANT: this spec was AI generated by <PERSON><PERSON> on September 10, 2025 and
> meant as a starting point for Geo and Subhash to coordinate work. All the
> technical work mentioned should be considered pseudocode and not actual code
> but should edited and corrected where there are hallucination errors.

## 1) Log sources (where things are now)

**Nginx**

- **Paths (typical production):**

  - `/var/log/nginx/access.log`
  - `/var/log/nginx/error.log`
- **Format:** default/“combined” for access (remote\_addr, time\_local, request,
  status, body\_bytes\_sent, http\_referer, http\_user\_agent).
- **Why we care:** canonical source of **real traffic** (IPs, request paths,
  status codes, referrers, UAs) to diagnose GA4 undercounting, bot spikes, and
  attribution.

**Astro app/server telemetry**

- **Paths (typical app host):**

  - `/var/log/astro/app.log` (application events)
  - If we already emit JSON logs (preferred), keep JSON. Otherwise, we’ll
    normalize line logs on ingest.
- **Events of interest:** authenticated vs. anonymous session IDs, route hits
  (`/`, `/get-started`, `/contact`, `/caas`, etc.), API timings, conversion
  markers (form posts, CTA clicks), internal feature events.

> If any of these locations differ on our boxes/containers, please confirm and
> substitute the real paths. Core idea: **point Surveilr at the exact files
> we’re rotating today**.

## 2) Ingest into `surveilr` (same spirit as EAA and TEM)

We’ll use `surveilr`’s file ingestion to get both sources into a single,
queryable lake. Start with **raw** tables, then add **normalized**/materialized
views for analytics.

- Nginx access: parse “combined” JSON or JSONL into structured fields
  (timestamp, method, path, status, bytes, referer, user_agent, remote_addr).
- Astro app: treat as JSON or JSONL if available (timestamp, user\_id,
  session\_id, event, route, duration\_ms, http\_status, metadata).
- Tag metadata: `env=prod|staging`, `service=nginx|astro`, `app=opsfolio`.

(`surveilr`’s CLI supports capturable executables so use them for complex
requirements. The goal is to land two SQL Views that read from
`uniform_resource`: `raw_nginx_access`, `raw_astro_events`.)

## 3) Normalize into analytics-friendly tables

Create views (or materialized tables) aligned with the queries we’ll run most:

```sql
-- Nginx normalized view
CREATE OR REPLACE VIEW opsfolio.web_traffic AS
SELECT
  to_timestamp(ts) AS ts,
  remote_addr,
  http_method,
  request_path,
  http_status AS status_code,
  body_bytes_sent,
  http_referer AS referer,
  user_agent,
  coalesce(session_id, derived_session_id(remote_addr, user_agent, ts)) AS session_id,
  'nginx' AS source
FROM raw_nginx_access;

-- Astro normalized view
CREATE OR REPLACE VIEW opsfolio.astro_events AS
SELECT
  to_timestamp(ts) AS ts,
  user_id,
  session_id,
  event,
  route AS request_path,
  duration_ms,
  http_status AS status_code,
  metadata,
  'astro' AS source
FROM raw_astro_events;
```

> `derived_session_id(...)` is a placeholder for whatever session heuristic we
> decide (e.g., IP+UA+time bucketing) if Nginx lacks a cookie or x-session
> header. If we already log session/cid in headers, parse and use that.

**Indexing/partitioning hints (configure in Surveilr):**

- Time-based partitioning by `ts`.
- Secondary indexes on `request_path`, `status_code`, `session_id`, `user_id`.

**Retention:** 90 days hot → 400 days warm (tune as needed).

## 4) Canonical queries we’ll rely on (SQL in `surveilr`)

**A. Top landing pages (unique sessions, last 7d):**

```sql
SELECT request_path,
       COUNT(DISTINCT session_id) AS unique_sessions
FROM opsfolio.web_traffic
WHERE ts >= now() - INTERVAL '7 days'
GROUP BY request_path
ORDER BY unique_sessions DESC;
```

**B. Referrer quality & bounce proxy (2xx requests only):**

```sql
WITH first_hits AS (
  SELECT session_id,
         min(ts) AS first_ts
  FROM opsfolio.web_traffic
  WHERE status_code BETWEEN 200 AND 299
  GROUP BY session_id
)
SELECT wt.referer,
       COUNT(DISTINCT wt.session_id) AS sessions,
       AVG(CASE WHEN next_hit.session_id IS NULL THEN 1 ELSE 0 END)::float AS bounce_rate_est
FROM opsfolio.web_traffic wt
LEFT JOIN LATERAL (
  SELECT 1
  FROM opsfolio.web_traffic w2
  WHERE w2.session_id = wt.session_id
    AND w2.ts > wt.ts
  LIMIT 1
) next_hit ON TRUE
JOIN first_hits fh ON fh.session_id = wt.session_id AND fh.first_ts = wt.ts
GROUP BY wt.referer
ORDER BY sessions DESC;
```

**C. Funnel shape across key routes (/, /get-started, /contact):**

```sql
WITH stage_hits AS (
  SELECT session_id,
         MAX(CASE WHEN request_path='/' THEN 1 ELSE 0 END) AS hit_home,
         MAX(CASE WHEN request_path='/get-started' THEN 1 ELSE 0 END) AS hit_get_started,
         MAX(CASE WHEN request_path='/contact' THEN 1 ELSE 0 END) AS hit_contact
  FROM opsfolio.web_traffic
  WHERE ts >= now() - INTERVAL '14 days'
  GROUP BY session_id
)
SELECT
  COUNT(*) AS sessions_total,
  SUM(hit_home) AS reached_home,
  SUM(hit_get_started) AS reached_get_started,
  SUM(hit_contact) AS reached_contact
FROM stage_hits;
```

**D. Sept 5 anomaly deep-dive (example date window):**

```sql
SELECT date_trunc('hour', ts) AS hr,
       COUNT(*) AS hits,
       COUNT(DISTINCT session_id) AS uniq_sessions,
       AVG(CASE WHEN user_agent ILIKE '%bot%' OR user_agent ILIKE '%crawler%' THEN 1 ELSE 0 END) AS bot_ua_ratio
FROM opsfolio.web_traffic
WHERE ts >= TIMESTAMP '2025-09-05 00:00:00'
  AND ts <  TIMESTAMP '2025-09-06 00:00:00'
  AND request_path = '/'
GROUP BY hr
ORDER BY hr;
```

## 5) Web UI with **SQLPage** (simple, fast, SQL-first)

Turn this into a new pattern or merge it with another pattern. If it's a new
pattern, call it "Site Performance Explorer" and be able to merge multiple
tenants and sites later. If you want to try new `spry` package Shahid is writing
instead of a `surveilr` pattern, you can also try that:

```
/srv/sqlpage/
  index.sql           -- overview dashboard
  traffic.sql         -- traffic & top pages
  funnel.sql          -- funnel metrics
  anomalies.sql       -- spikes & bot heuristics
  _layout.sql         -- shared header/nav (optional)
```

**A. Example `index.sql` (overview cards + quick tables):**

```sql
-- title: Opsfolio Analytics Overview
-- menu: Overview

SELECT 'KPI' AS component, 'Sessions (7d)' AS title,
       (SELECT COUNT(DISTINCT session_id)
        FROM opsfolio.web_traffic
        WHERE ts >= now() - interval '7 days') AS value;

SELECT 'KPI' AS component, 'Unique Users (Astro, 7d)' AS title,
       (SELECT COUNT(DISTINCT COALESCE(user_id, session_id))
        FROM opsfolio.astro_events
        WHERE ts >= now() - interval '7 days') AS value;

SELECT 'table' AS component, 'Top Pages (7d)' AS title,
       request_path AS "Path",
       unique_sessions AS "Unique Sessions"
FROM (
  SELECT request_path, COUNT(DISTINCT session_id) AS unique_sessions
  FROM opsfolio.web_traffic
  WHERE ts >= now() - interval '7 days'
  GROUP BY request_path
  ORDER BY unique_sessions DESC
  LIMIT 20
) t;
```

**B. Example `funnel.sql` (route funnel):**

```sql
-- title: Funnel (/ -> /get-started -> /contact)
-- menu: Funnel

SELECT 'table' AS component, 'Funnel (last 14 days)' AS title,
       sessions_total AS "Sessions",
       reached_home AS "Reached /",
       reached_get_started AS "Reached /get-started",
       reached_contact AS "Reached /contact"
FROM (
  WITH stage_hits AS (
    SELECT session_id,
           MAX((request_path='/')::int) AS home,
           MAX((request_path='/get-started')::int) AS gs,
           MAX((request_path='/contact')::int) AS c
    FROM opsfolio.web_traffic
    WHERE ts >= now() - interval '14 days'
    GROUP BY session_id
  )
  SELECT COUNT(*) AS sessions_total,
         SUM(home) AS reached_home,
         SUM(gs) AS reached_get_started,
         SUM(c) AS reached_contact
  FROM stage_hits
) s;
```

> SQLPage renders components from result sets; the comments (`-- title`,
> `-- menu`) help define navigation and presentation. We can add charts later
> using SQLPage-supported components.

**Deploying SQLPage (example):**

- Install SQLPage on the analytics host.
- Configure DB connection to Surveilr’s SQL endpoint.
- Serve on internal URL (behind Opsfolio SSO/Reverse Proxy via Nginx).
- Add a simple Nginx site: `/analytics` → SQLPage service.

## 6) Operational notes

- **Consistency with TEM:** mirror Joby’s ingestion pipeline pattern: **raw →
  parsed → normalized → SQL views → dashboards**.
- **Data quality:** Prefer JSON logging in Astro; for Nginx, add a request ID /
  session cookie to link page hits with app sessions (if not already).
- **Bot heuristics:** user agent checks + rate thresholds, plus “homepage-only”
  session detection to flag likely inorganic spikes.
- **Governance:** tag data with env/service; default retention 90d hot; access
  controlled via SSO groups (Marketing ReadOnly, Eng ReadWrite).

## 7) Who does what

- **Geo** → Surveilr ingestion (file targets, parsing, indexes, retention), plus
  Nginx reverse proxy for SQLPage.
- **Arun** → SQL schema/views, canonical queries, and SQLPage pages (index,
  traffic, funnel, anomalies).

### Flow of the SSPE

- Use Surveilr Binary to load data to SQLite DB or Bash script to collect
- Talk to system owners to use JSON or jsonl > ingest
- If it is log/txt format Use Captureable executable to rad and parse

![text](./sspe.png)
