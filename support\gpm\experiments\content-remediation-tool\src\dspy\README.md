# Website Content Remediation Tool

## Overview
The Website Content Remediation Tool is an AI-driven application designed to evaluate website pages across multiple dimensions, including:  

- Value Proposition  
- Compliance & Positioning  
- SEO  
- Copywriting (Focus, Clarity, Scannability, Persuasiveness)  
- Calls-to-Action (CTAs) & Conversion Opportunities  
- UX & Design  
- Technical Factors  

The tool produces structured audit reports that include strengths, weaknesses, recommendations, and remediated content suggestions for each page. 

---

## Features
- Run multiple audits on a single page using different prompts (e.g., compliance, SEO, UX).  
- Output structured results including:
  - Executive Summary  
  - Strengths (with evidence citations)  
  - Weaknesses (with evidence citations)  
  - Recommendations (linked to evidence)  
  - Observation vs Hypothesis Table  
  - Original content and Remediated content per weakness  
- Page-wise summaries. 

---

## Prerequisites
- **Linux** (recommended)
- **Windows**

### 1. Install Python
- **Linux**: Python 3.11+ recommended
- **Windows**: Python 3.12+ required (due to asyncio event loop handling) 
- Download and install from [Python.org](https://www.python.org/downloads/).  
- During installation, ensure **“Add Python to PATH”** is checked.

### 2. Verify Python Installation
Open a terminal/command prompt and run:

```bash
python --version
# or
python3 --version
```

You should see something like:
```bash
Python 3.13.7
```

---

## Installation

### 1. Clone the Repository
```bash
git clone https://github.com/opsfolio/www.opsfolio.com.git
```

### 2. Navigate to the Project Directory
```bash
cd support/gpm/experiments/content-remediation-tool/src/dspy
```

### 3. Create a Virtual Environment (Optional but Recommended)
```bash
python -m venv venv
```

### 4. Activate the Virtual Environment
```bash
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate
```

### 5. Install Dependencies
```bash
pip install -r requirements.txt
```
To complete Playwright, run the following command:
```bash
playwright install
```

## Usage

### 1. Configure the Environment Variables
Create a `.env` file in the project root and add your model name and API key:

```bash
LLM_NAME=your_llm_name_here
LLM_API_KEY=your_api_key_here
```
Example
```bash
LLM_NAME='gemini/gemini-2.5-flash'
LLM_API_KEY=your_api_key_here
```

### 2. Run the Audit Tool
Note: You can use CLI or Streamlit app to run the audit tool, see below.

```bash
python main.py
```
Provide inputs when prompted:
- Page URL
- Page type (Landing, Case Study, Product Page, etc.)


### Example:
```
Enter the page URL: https://opsfolio.com/
Enter the page type (Landing, Case Study, Product Page): Homepage

```
### Output:
A structured audit report will be saved in the audits_reports/ folder.
Page-level executive summary included in the report.

### Running the typer CLI
We have a typer CLI that can be used to run the audit tool.  To run the CLI, execute the following command:
```bash
python audit-cli.py "https://opsfolio.com/" "Homepage"
```

### Running the Streamlit app
We have a Streamlit app that can be used to run the audit tool.  To run the app, execute the following command:
```bash
streamlit run streamlit-app.py
```
In the UI there is an option to edit the prompts and run it for testing.
Note: The default prompts will not be changed but can be used for testing.



