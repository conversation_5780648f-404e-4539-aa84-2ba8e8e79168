---
import Layout from "../../layouts/Layout.astro";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { getEntry,getCollection } from 'astro:content';
import { 
  Clock, 
  Users, 
  MapPin, 
  Building,  
} from "lucide-react";
import { marked } from 'marked';

const companyStats = [
  { icon: Users, label: "Employees", value: "50-100" },
  { icon: MapPin, label: "Location", value: "Washington, D.C." },
  { icon: Building, label: "Industry", value: "Sustainability & Supply Chain" },
  { icon: Clock, label: "Customer Since", value: "2025" }
];
const clockIcon = Clock;
const IndustryIcon = Building;
const LocationIcon = MapPin;
const Userscon = Users;

const slug = Astro.params.slug;
const entry = await getEntry("resources", slug);
if (!entry) {
  // Handle Error, for example:
  throw new Error("Could not find data");
}


const htmlString = entry.rendered?.html;
const content = marked.parse(htmlString);
const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Resources',"aria-disabled": false ,
  },
  {
    index: "2", text: "Case Studies","aria-disabled": true ,
  },
  {
    index: "3", text: entry.data.title,"aria-disabled": true ,
  },
];
 
---

<Layout title={entry.data.title} description={entry.data.shortdescription} breadcrumbLinks={breadcrumbLinks}>
   <main>
        {/* Hero Section */}
        <div class="relative isolate px-6 pt-14 lg:px-8 overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5">
          <div class="mx-auto max-w-4xl py-24 sm:py-32">
            <div class="text-center">
              <Badge variant="secondary" className="mb-4">{entry.data.category}</Badge>
              <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl mb-6">
                {entry.data.title}
              </h1>
              <p class="text-lg leading-8 text-muted-foreground max-w-3xl mx-auto">
                {entry.data.shortdescription}
              </p>
            </div>
          </div>
        </div>
        {entry.data.company?.stats && (
        <div class="py-16 border-y border-border">
            <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                {entry.data.company.stats.map((stat, index) => (
                <div class="text-center">
                    <img src={stat.image} class="h-8 w-8 text-primary mx-auto mb-2" />
                    <div class="text-sm text-muted-foreground">{stat.label}</div>
                    <div class="font-semibold text-foreground">{stat.value}</div>
                </div>
                ))}
            </div>
            </div>
        </div>
        )}
        

        {/* Company Overview */}
        <div class="pt-24 sm:pt-32 -mb-5">
         <article class="prose prose-lg max-w-none markdown-content resource-card">
            <div class="mx-auto max-w-4xl px-6 lg:px-8">
                <Fragment set:html={content} />
            </div>

            {/* CTA Section */}
            <div class="py-24 sm:py-32 bg-muted text-foreground">
              <div class="mx-auto max-w-4xl px-6 lg:px-8 text-center">
                <h2 class="text-3xl font-bold tracking-tight sm:text-4xl mb-6">
                  Get Compliant and Build Trust—Fast
                </h2>
                <p class="text-lg opacity-90 mb-8">
                  Explore how Opsfolio CaaS can help your organization achieve SOC 2 certification, streamline compliance, and ensure data security.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                  <a href="/get-started">
                    <Button size="lg" variant="default">
                      Request a Demo
                    </Button>
                  </a>
                  <a href="/contact">
                    <Button size="lg" variant="outline">
                      Talk to an Expert
                    </Button>
                  </a>
                </div>
              </div>
            </div>
      </article>    
    </div>
  </main>
</Layout>