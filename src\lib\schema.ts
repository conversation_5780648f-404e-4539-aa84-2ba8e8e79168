import { z } from "zod";

export const contactSchema = z.object({
    firstName: z.string().min(1, "First name is required").max(50),
    lastName: z.string().min(1, "Last name is required").max(50),
    email: z.string().email("Invalid email address"),
    company: z.string().min(1, "Company is required").max(500),
    role: z.string().optional(),
    inquiryType: z.string().min(1, "Please select an inquiry type"),
    message: z.string().min(10, "Message must be at least 10 characters").max(10000),
})
const GenericPayloadSchema = z.object({
    name: z.string().optional(),
    pageTitle: z.string().optional(),
    commentersName: z.string().optional(),
    commentUrl: z.string().optional(),
    commentContent: z.string().optional(),
    tenantName: z.string().optional(),
}).catchall(z.union([z.string(), z.number(), z.undefined(), z.boolean()]));

// Infer TypeScript type from Zod schema

export const getStartedSchema = z.object({
    firstName: z.string().min(1, "First name is required"),
    lastName: z.string().min(1, "Last name is required"),
    email: z.string().email("Enter a valid email"),
    company: z.string().min(1, "Company name is required"),
    role: z.string().min(1, "Role is required"),
    companyStage: z.string().min(1, "Company stage is required"),
    compliance: z.string().min(1, "Compliance framework is required"),
    timeline: z.string().min(1, "Timeline is required"),
    currentState: z.string().optional(),
    challenges: z.string().optional(),
    newsletter: z.boolean().optional(),
});

export const soc2PlanSchema = z.object({
    company: z.string().min(1, "Company name is required"),
    email: z.string().email("Invalid email address"),
    role: z.string().min(1, "Select your role"),
    employees: z.string().min(1, "Select company size"),
    industry: z.string().min(1, "Select industry"),
    infrastructure: z.string().min(1, "Select infrastructure"),
    timeline: z.string().min(1, "Select desired timeline"),
    details: z.string().optional(),
});

export type ContactFormData = z.infer<typeof contactSchema>;
export type GetStartedFormData = z.infer<typeof getStartedSchema>;
export type SOC2PlanFormData = z.infer<typeof soc2PlanSchema>;
export type GenericPayload = z.infer<typeof GenericPayloadSchema>;