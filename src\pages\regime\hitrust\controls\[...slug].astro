---
import Layout from "../../../../layouts/Layout.astro";
import ControlDetail from "@/components/ControlDetail.tsx";
import { getControlByIdentifierHitrust } from "../../../../services/auditServices";
import type { ControlsType } from "../../../../services/auditTypes";
import { ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";

//const auditTypeID = 15;
//const regimeId = 6;
let tenantId = '239518031485599747';

const { slug } = Astro.params;

let currentControl: ControlsType | null = null;

try {
  currentControl = await getControlByIdentifierHitrust(   
    tenantId,
    slug,
  );
} catch (err) {
  console.error("Failed to fetch control:", err);
}

const breadcrumbLinks = [
  { index: "0", text: "Home" },
  { index: "1", text: "Regime", "aria-disabled": true },
  { index: "2", text: "HITRUST", "aria-disabled": true },
];
---

<Layout title="HiTrust Made Simple - Healthcare Data Protection"
  description="The HITRUST Common Security Framework (CSF) is a comprehensive, flexible, and certifiable security and privacy framework used by organizations across multiple industries to efficiently approach regulatory compliance and risk management. This standard provides customers with confidence in knowing their data and confidential information are secure."
  keywords="HITRUST compliance, healthcare data protection, PHI security, healthcare compliance, HITRUST training, healthcare cybersecurity"
  url="/regime/hitrust/"
  breadcrumbLinks={breadcrumbLinks}>
  <div class="min-h-screen bg-background">
    {
      currentControl ? (

    <section
    class="relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5 pt-16 pb-20"
  >
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
      <div class="mx-auto max-w-4xl text-center">
        <h1
          class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl"
        >
          HITRUST Control {currentControl.control_identifier}
        </h1>
        <p
          class="mt-6 text-lg leading-8 text-muted-foreground max-w-3xl mx-auto"
        >
        {currentControl.question}        
        </p>
      </div>

       <div class="mt-10 flex items-center justify-center gap-x-6">
            <a href="/regime/hitrust/controls">
              <Button size="lg" variant="default">
                View All Controls
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </a>
          </div>
    </div>
    </section>
    <section class="py-20">
      <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <ControlDetail client:only="react" controlData={currentControl} name="HITRUST" />
      </div>
    </section>
      ) : (
        <p class="text-center py-20 text-muted-foreground">
          No control found for this slug.
        </p>
      )
    }
  </div>
</Layout>
