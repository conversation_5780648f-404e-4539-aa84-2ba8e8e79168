---
import Layout from "../../layouts/Layout.astro";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Database, ArrowRight, Brain, Layers, GitMerge, Gauge, Globe, Lock } from "lucide-react";
import IntegratedComplianceTabs from "@/components/task/IntegratedComplianceTabs";

  const platformFeatures = [
    {
      icon: Brain,
      title: "Unified Intelligence Layer",
      description: "AI-powered compliance intelligence that connects policies, controls, evidence, and risks across all frameworks.",
      capabilities: ["Cross-framework mapping", "Intelligent recommendations", "Predictive analytics"]
    },
    {
      icon: Layers,
      title: "Multi-Tenant Architecture",
      description: "Enterprise-grade platform designed for complex organizational structures and multiple compliance programs.",
      capabilities: ["Role-based access", "Hierarchical governance", "Custom workflows"]
    },
    {
      icon: GitMerge,
      title: "Integration Ecosystem",
      description: "Native integrations with 200+ security tools, cloud platforms, and enterprise systems.",
      capabilities: ["API-first design", "Real-time sync", "No-code connectors"]
    },
    {
      icon: Gauge,
      title: "Real-Time Dashboards",
      description: "Executive and operational dashboards with live compliance metrics and risk indicators.",
      capabilities: ["Customizable views", "Automated reporting", "Trend analysis"]
    },
    {
      icon: Globe,
      title: "Global Compliance Hub",
      description: "Centralized management of compliance across multiple regions, subsidiaries, and regulatory environments.",
      capabilities: ["Multi-jurisdiction support", "Localized frameworks", "Cross-border reporting"]
    },
    {
      icon: Lock,
      title: "Zero-Trust Security",
      description: "Platform built with zero-trust principles, ensuring the highest security for your compliance data.",
      capabilities: ["End-to-end encryption", "Immutable audit logs", "SOC 2 Type II certified"]
    }
  ];

  const modules = [
    {
      name: "Policy Engine",
      description: "Intelligent policy management with version control, approval workflows, and automated distribution.",
      features: ["Policy templating", "Approval workflows", "Distribution tracking", "Version control"]
    },
    {
      name: "Evidence Vault",
      description: "Secure, searchable repository for all compliance evidence with automated collection and validation.",
      features: ["Automated collection", "Evidence validation", "Secure storage", "Smart search"]
    },
    {
      name: "Control Observatory",
      description: "Comprehensive control monitoring with real-time effectiveness measurement and optimization.",
      features: ["Control mapping", "Effectiveness testing", "Performance metrics", "Optimization insights"]
    },
    {
      name: "Risk Radar",
      description: "Continuous risk assessment and monitoring with predictive risk modeling and mitigation tracking.",
      features: ["Risk assessment", "Predictive modeling", "Mitigation tracking", "Risk reporting"]
    },
    {
      name: "Audit Command",
      description: "Complete audit lifecycle management from planning through remediation with AI-powered insights.",
      features: ["Audit planning", "Finding management", "Remediation tracking", "Audit analytics"]
    },
    {
      name: "Compliance Copilot",
      description: "AI-powered assistant that provides intelligent guidance, answers compliance questions, automates routine tasks, and offers real-time recommendations based on your specific regulatory context and organizational needs.",
      features: ["Natural language queries", "Intelligent guidance", "Task automation", "Knowledge base"]
    }
  ];

  const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Task',"aria-disabled": true ,
  },
  {
    index: "2", text: "Centralize Everything","aria-disabled": true 
  },
];
---

<Layout  breadcrumbLinks={breadcrumbLinks}>
    {/* Hero Section */}
    <section class="relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5 pt-16 pb-20">
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <div class="mx-auto max-w-3xl text-center">
        <Badge variant="outline" className="mb-4">Enterprise Compliance Platform</Badge>
        <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
            The Operating System for Compliance
        </h1>
        <p class="mt-6 text-lg leading-8 text-muted-foreground">
            Revolutionary platform that unifies all compliance activities into a single, 
            AI-powered system of record. Built for the complexity of modern enterprise compliance.
        </p>
        <div class="mt-10 flex items-center justify-center gap-x-6">
            <a href="/get-started/">
              <Button size="lg" variant="default">Experience Platform<ArrowRight className="ml-2 h-4 w-4" /></Button>
            </a>
        </div>
        </div>
    </div>
    </section>

    {/* Platform Features */}
    <section class="py-20">
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <div class="mx-auto max-w-2xl text-center mb-16">
        <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
            Enterprise-Scale Architecture
        </h2>
        <p class="mt-4 text-lg text-muted-foreground">
            Built for the most demanding compliance environments
        </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {platformFeatures.map((feature, index) => (
            <Card key={index} className="border-border/50 hover:border-primary/50 transition-colors h-full">
            <CardHeader>
                <feature.icon className="h-8 w-8 text-primary mb-2" />
                <CardTitle className="text-xl">{feature.title}</CardTitle>
                <CardDescription className="text-base">
                {feature.description}
                </CardDescription>
            </CardHeader>
            <CardContent>
                <ul class="space-y-1 text-sm text-muted-foreground">
                {feature.capabilities.map((capability) => (
                    <li class="flex items-center gap-2">
                    <div class="w-1.5 h-1.5 bg-primary rounded-full flex-shrink-0" />
                    {capability}
                    </li>
                ))}
                </ul>
            </CardContent>
            </Card>
        ))}
        </div>
    </div>
    </section>

    {/* Platform Modules */}
    <section class="py-20 bg-muted">
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <div class="mx-auto max-w-2xl text-center mb-16">
        <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
            Integrated Compliance Modules
        </h2>
        <p class="mt-4 text-lg text-muted-foreground">
            Six core modules that work seamlessly together
        </p>
        </div>

        <IntegratedComplianceTabs client:load modules={modules} />
    </div>
    </section>

    {/* Platform Stats */}
    <section class="py-20">
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <div class="mx-auto max-w-2xl text-center mb-16">
        <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
            Platform by the Numbers
        </h2>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <div class="text-center">
            <div class="text-4xl font-bold text-primary">99.99%</div>
            <div class="text-muted-foreground">Platform Uptime</div>
        </div>
        <div class="text-center">
            <div class="text-4xl font-bold text-primary">200+</div>
            <div class="text-muted-foreground">Native Integrations</div>
        </div>
        <div class="text-center">
            <div class="text-4xl font-bold text-primary">50+</div>
            <div class="text-muted-foreground">Compliance Frameworks</div>
        </div>
        <div class="text-center">
            <div class="text-4xl font-bold text-primary">10x</div>
            <div class="text-muted-foreground">Faster Implementation</div>
        </div>
        </div>
    </div>
    </section>

    {/* CTA Section */}
    <section class="py-20 bg-muted">
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <div class="mx-auto max-w-2xl text-center">
        <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
            Ready to Transform Compliance?
        </h2>
        <p class="mt-4 text-lg text-muted-foreground">
            See how the platform can revolutionize your compliance operations
        </p>
        <div class="mt-8">
            <a href="/get-started">
            <Button size="lg" variant="default"> Request Platform Demo<ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </a>
        </div>
        </div>
    </div>
    </section>
</Layout>
