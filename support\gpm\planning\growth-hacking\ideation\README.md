# Content Engineering Ideation Guidelines

> A formal process for content ideation and evidence-backed proposals

This guide is for content engineering leaders with strong B2B content creation
skills who want to introduce new ideas for lead magnets, blog posts, white
papers, case studies, or other collateral. It establishes a formal ideation
process for presenting content ideas to Netspective executives or ODC
engineering leaders.

The core principle: Every piece of content in the B2B lifecycle must connect to
revenue. Content is never created in isolation — it always serves one or more
purposes in the customer journey:

- Acquire a new customer (top of funnel)
- Convert interest into a contract (middle or bottom of funnel)
- Renew an existing contract (customer retention)
- Expand client relationships (upsell or cross-sell)

Because of this, all content ideas must be tied to measurable outcomes,
supported by research or testing strategies, and positioned within customer
journeys that <PERSON><PERSON> has already mapped or is in the process of designing.

> See [Ideation AI Prompt](ideation.prompt.md) to get AI to help with generating
> your business case.

## Current Ideas to Discuss

- [Pen Test (Security Assessment) Lead Magnet](idea-pen-test-lead-magnet.md)
- [Compliance as Code E-Book](idea-compliance-as-code-ebook.md)

## Foundations of Content Ideation

1. Revenue Orientation

   - Ask: _How will this content help generate or protect revenue?_
   - Map the idea to a revenue purpose: acquisition, conversion, renewal,
     retention, or expansion.

2. Evidence-First Thinking

   - Use first-party research sources (e.g., Nielsen Norman Group (NN/g),
     Gartner, <PERSON>er, McKinsey) when possible.
   - When first-party research isn’t available, present the idea as a hypothesis
     to be validated with A/B testing, pilots, or other data-driven experiments.

3. Customer Journey Alignment

   - Every idea should explicitly state where it fits in the Opsfolio Customer
     Journey Map (CJM).
   - This is different from funnel mapping:

     - Funnel Mapping = marketing/sales conversion mechanics (ToFu, MoFu, BoFu,
       Retention).
     - Customer Journey Mapping (CJM) = the real-world sequence of interactions
       a buyer has with Opsfolio content, campaigns, or tools across email,
       website, LinkedIn, demos, lead magnets, etc.
   - A good idea proposal shows how the content plugs into an existing CJM or
     argues why a new journey step should be added.

## The Idea Submission Framework

Every new content idea should be submitted in a short, structured format:

1. Title of Idea

   - A clear, outcomes-based title (e.g., “CMMC Strategy AI Prompt Generator for
     Top-of-Funnel Engagement”).

2. Revenue Connection

   - Explicitly tie the idea to one or more revenue goals: acquisition,
     conversion, renewal, retention, expansion.

3. Evidence Base

   - Provide relevant references to NN/g, B2B sales research, or competitor lead
     magnets.
   - If no direct evidence exists, explicitly state the hypothesis and why you
     believe it has merit.

4. Funnel Mapping

   - Top of Funnel (ToFu): How this content will capture attention or generate
     new leads.
   - Middle of Funnel (MoFu): How it nurtures trust and provides value.
   - Bottom of Funnel (BoFu): How it accelerates decisions and supports
     conversion.
   - Retention/Expansion: How it supports renewals or deeper client engagement.

5. Customer Journey Placement

   - Show where in the Opsfolio CJM this content belongs.
   - Examples:

     - Pre-awareness → Cold LinkedIn outreach → Lead Magnet → Email nurturing →
       Demo request.
     - Or: Existing client → Renewal touchpoint → White paper or report →
       Customer success check-in.
   - Explicitly state: “This idea will replace/strengthen the touchpoint at \[CJ
     step], or it creates a new step between \[A] and \[B].”

6. Hypothesis and Test Strategy

   - Write a testable hypothesis: _“If we provide X lead magnet, then we expect
     Y% increase in conversions at the \[CJ step] because of Z.”_
   - Outline testing methods: A/B tests, gated vs ungated, pilot with a subset
     of LinkedIn ads, etc.

7. Competitive/Comparative Insight

   - Briefly note what competitors are doing and how our approach will be
     similar, better, or differentiated.

8. Execution Requirements

   - Identify what’s needed: research, design, AI assistance, subject matter
     experts, distribution channel strategy.

## Examples of Applying the Framework

Example 1: Lead Magnet

- Title: “SOC 2 Self-Assessment Checklist (Opsfolio-Branded)”
- Revenue Connection: Acquisition of compliance-minded startups exploring CaaS.
- Evidence: SecureFrame and Drata both offer checklists; NN/g research shows
  checklists reduce cognitive friction and improve lead capture.
- Funnel Mapping:

  - ToFu: Gated checklist captures email leads.
  - MoFu: Nurture emails reinforce Opsfolio’s compliance expertise.
  - BoFu: Checklist links to demo scheduling.
- Customer Journey Placement: This lead magnet becomes a new step after “Cold
  LinkedIn Outreach” and before “First Nurture Email.”
- Hypothesis: “Adding this lead magnet to the CJM will increase landing page
  signups by 20%.”
- Test Strategy: Run A/B LinkedIn ads with and without checklist.
- Execution: Content design, gated landing page by ODC engineering.

Example 2: White Paper

- Title: “Reducing Compliance Surface Area with Virtual Desktops: A CMMC Level 1
  Strategy”
- Revenue Connection: Middle- and bottom-funnel trust builder for firms
  evaluating CMMC readiness.
- Evidence: DoD guidance + Gartner reports on VDI adoption.
- Funnel Mapping:

  - ToFu: SEO and ad campaigns targeting “compliance surface area.”
  - MoFu: Shared in nurture campaigns.
  - BoFu: Used in proposals to demonstrate depth.
- Customer Journey Placement: Fits into the “Evaluation” stage between “Email
  Nurture Series” and “Demo Scheduling Call.”
- Hypothesis: “Including this paper in the CJM will increase proposal acceptance
  rates by 10%.”
- Test Strategy: Track proposals that reference the white paper vs. those that
  don’t.
- Execution: SME input, copywriting, PDF design, sales enablement distribution.

## Governance and Review

- All new ideas must include both funnel mapping and CJ placement before being
  reviewed by executives.
- Submissions without clear revenue ties, evidence, or CJ mapping will be sent
  back for revision.
- Approved ideas will be prioritized in the content roadmap according to their
  impact on CJ stages and revenue influence.

## Key Takeaways

- Every idea must be tied to revenue impact.
- All content must be explicitly mapped to Opsfolio customer journeys so
  executives know where it plugs in.
- Support with evidence or testable hypotheses (NN/g, competitor analysis, A/B
  testing).
- Structure proposals using the eight-part submission framework.
- Content is not “just creative” — it is an engineered asset with measurable
  business outcomes in the customer journey.

> See [Ideation AI Prompt](ideation.prompt.md) to get AI to help with generating
> your business case.
