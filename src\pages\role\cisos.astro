---
import Layout from "../../layouts/Layout.astro";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Shield, Users, TrendingUp, Clock, CheckCircle, ArrowRight } from "lucide-react";


const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Role',"aria-disabled": true ,
  },
  {
    index: "2", text: "CISOs & Security Engineers","aria-disabled": true 
  },
];
---

<Layout title="For CISOs - Build Resilient Security Programs That Scale"
  description="Transform your security posture from reactive to proactive. Get audit-ready frameworks, continuous monitoring, and expert guidance for CISOs and security leaders."
  keywords="CISO solutions, security leadership, security program management, audit readiness, continuous monitoring, security frameworks"
  url="/role/cisos/"
  breadcrumbLinks={breadcrumbLinks}
  >
  <section class="relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5 pt-16 pb-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center">
            <Badge variant="outline" className="mb-4">
              For Chief Information Security Officers
            </Badge>
            <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
              Build Resilient Security Programs That Scale
            </h1>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Transform your security posture from reactive to proactive. Get audit-ready frameworks,
              continuous monitoring, and expert guidance to protect your organization at scale.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
              <a href="/get-started">
                <Button size="lg" variant="default">                
                  Get Your Security Assessment
                  <ArrowRight className="ml-2 h-4 w-4" />                
                </Button>
              </a>
              <a href="/contact">
                <Button variant="outline" size="lg">Talk to a CISO
                </Button>
              </a>
              
            </div>
          </div>
        </div>
      </section>

      {/* Key Benefits */}
      <section class="py-16 sm:py-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl lg:text-center">
            <h2 class="text-base font-semibold leading-7 text-primary">CISO Benefits</h2>
            <p class="mt-2 text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Security Leadership Made Strategic
            </p>
          </div>
          <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <dl class="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
              <div class="flex flex-col">
                <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                  <Shield className="h-5 w-5 flex-none text-primary" />
                  Board-Ready Reporting
                </dt>
                <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p class="flex-auto">
                    Get executive dashboards and risk metrics that communicate security posture
                    in business terms. Automated reporting for board meetings and stakeholder updates.
                  </p>
                </dd>
              </div>
              <div class="flex flex-col">
                <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                  <Users className="h-5 w-5 flex-none text-primary" />
                  Team Enablement
                </dt>
                <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p class="flex-auto">
                    Empower your security team with standardized processes, playbooks, and
                    automation tools. Reduce manual work and focus on strategic initiatives.
                  </p>
                </dd>
              </div>
              <div class="flex flex-col">
                <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                  <TrendingUp className="h-5 w-5 flex-none text-primary" />
                  Continuous Improvement
                </dt>
                <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p class="flex-auto">
                    Build a culture of continuous security improvement with metrics, KPIs,
                    and maturity assessments that drive measurable progress.
                  </p>
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </section>

      {/* Key Outcomes */}
      <section class="py-16 sm:py-20 bg-muted">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              CISO Success Outcomes
            </h2>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Strategic security leadership that drives business value and reduces organizational risk.
            </p>
          </div>
          <div class="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-6 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-3 lg:gap-8">
            {[
              {
                icon: Clock,
                title: "90% Faster Audit Prep",
                description: "Continuous audit readiness means no more scrambling for evidence or documentation."
              },
              {
                icon: Shield,
                title: "Enterprise-Grade Controls",
                description: "Implement controls that scale with your organization and meet multiple compliance frameworks."
              },
              {
                icon: CheckCircle,
                title: "Zero Audit Findings",
                description: "Proactive monitoring and remediation to ensure clean audit results every time."
              }
            ].map((item, index) => (
              <div class="flex flex-col items-start">
                <div class="rounded-md bg-primary/10 p-2 ring-1 ring-primary/20">
                  <item.icon className="h-6 w-6 text-primary" />
                </div>
                <dt class="mt-4 font-semibold text-foreground">{item.title}</dt>
                <dd class="mt-2 leading-7 text-muted-foreground">{item.description}</dd>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section class="py-16 sm:py-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Ready to Elevate Your Security Program?
            </h2>
            <p class="mx-auto mt-6 max-w-xl text-lg leading-8 text-muted-foreground">
              Schedule a strategic consultation with our CISO experts to discuss your security roadmap.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
              
                <a href="/get-started">
                  <Button size="lg" variant="default">Schedule CISO Consultation
                 </Button>
                </a>                 
                <a href="/resources">
                  <Button variant="outline" size="lg">View Security Resources
                  </Button>
                </a>
            </div>
          </div>
        </div>
      </section>
</Layout>
