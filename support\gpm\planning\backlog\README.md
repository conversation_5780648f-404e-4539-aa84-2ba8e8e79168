# Backlog.md Task Management

This project uses **[Backlog.md](https://github.com/MrLesk/Backlog.md)** to
manage tasks directly inside our Git repository.\
Backlog.md makes every task a simple Markdown file (`.md`) that can be viewed,
edited, and tracked using either a **command-line board** or a **web browser
interface**.

This README explains how to get started.

---

## Installation

Backlog.md is distributed via **npm** (Node.js package manager).\
If you don’t have Node.js installed, please install it first:

- [Download Node.js (LTS version)](https://nodejs.org/)

Once Node.js is ready, install Backlog.md globally:

```bash
npm install -g backlog.md
```

To verify installation:

```bash
backlog --version
```

### Setting Up the Backlog (Skip this step because set up is already done)

If you are starting fresh, initialize the backlog (this will create the .backlog
directory and configuration):

```bash
backlog init "www.opsfolio.com"
```

---

## Viewing Tasks

You have two options:

### Option A: Terminal Board

Run:

```bash
backlog board view
```

This opens an interactive **Kanban-style board** in your terminal.

### Option B: Web Browser Board

Run:

```bash
backlog browser
```

This launches a modern **drag-and-drop task board** in your default web browser.
(If it doesn’t open automatically, check the terminal for the local URL, e.g.
`http://localhost:3000`)

---

## Creating Tasks

To add a new task:

```bash
backlog task create "Write project documentation"
```

This will create a new Markdown file (e.g.,
`task-1 - write-project-documentation.md`) in the backlog folder.

You can open and edit this task file in any text editor (e.g., Notepad, VS
Code).

---
##  Completing & Archiving Tasks

When a task is finished, mark it as done and archive it:

```bash
backlog task archive <task-id>
```

This keeps the backlog clean while retaining task history.
---

## Tips for Non-Technical Users

- Use the **web browser board** (`backlog browser`) for a simple, visual
  interface.
- Each task is just a `.md` file, so you can also open it like a document.
- Don’t worry about Git commands — as long as you can run `backlog browser`, you
  can view and manage tasks.

---

## Tips for Technical Users

- You can script task creation and reporting in CI/CD pipelines.
- All task data is plain Markdown — versioned with Git.
- Use `backlog list` and `backlog filter` to query tasks quickly.
- Tasks can be linked to commits or PRs for full traceability.

---

## Summary

- **Install** Backlog.md: `npm install -g backlog.md`
- **View tasks**:

  - Terminal board → `backlog board view`
  - Browser board → `backlog browser`
- **Create tasks**: `backlog task create "My Task"`
- **Archive tasks**: `backlog task archive <id>`

This way, both technical and non-technical team members can **track project
progress in a transparent, Git-native, Markdown-first way**.

```
```
