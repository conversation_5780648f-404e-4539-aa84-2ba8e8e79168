---
import Layout from "../../../layouts/Layout.astro";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Globe, Shield, Building, Clock, CheckCircle, ArrowRight } from "lucide-react";


const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Regime',"aria-disabled": true ,
  },
  {
    index: "2", text: "ISO 27001","aria-disabled": true 
  },
];
---

<Layout title="Opsfolio - Compliance as a Service | SOC2, HIPAA, ISO Certification"
        description="Get SOC2, HIPAA, ISO certified fast with expert-guided compliance. 98% audit pass rate, 60% faster than DIY. Your Compliance System of Record."
        keywords="international security recognition, ISO 27001 certification, information security management, global market access, risk management framework, regulatory compliance"
        url="/regime/iso-27001/"
        breadcrumbLinks={breadcrumbLinks}>
      {/* Hero Section */}
      <section class="relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5 pt-16 pb-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center">
            <Badge variant="outline" className="mb-4">
              ISO 27001 Certification
            </Badge>
            <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
              Achieve Global Security Standard
            </h1>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Get ISO 27001 certified with comprehensive Information Security Management System (ISMS) 
              implementation. Meet international standards and enter global markets with confidence.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
              
                <a href="/get-started">
                  <Button size="lg" >
                    Start ISO Assessment
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </a>             
              
                <a href="/contact">
                  <Button variant="outline" size="lg" >
                    Talk to ISO Expert
                  </Button>
                </a>

                <a href="/regime/iso-27001/controls">
                  <Button variant="outline" size="lg" >
                    Control Explorer
                  </Button>
                </a>
              
            </div>
          </div>
        </div>
      </section>

      {/* ISO Benefits */}
      <section class="py-16 sm:py-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl lg:text-center">
            <h2 class="text-base font-semibold leading-7 text-primary">ISO 27001 Benefits</h2>
            <p class="mt-2 text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              International Security Recognition
            </p>
          </div>
          <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <dl class="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
              <div class="flex flex-col">
                <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                  <Globe className="h-5 w-5 flex-none text-primary" />
                  Global Market Access
                </dt>
                <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p class="flex-auto">
                    Open doors to international business opportunities. Many global organizations 
                    require ISO 27001 certification from their vendors and partners.
                  </p>
                </dd>
              </div>
              <div class="flex flex-col">
                <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                  <Shield className="h-5 w-5 flex-none text-primary" />
                  Risk Management Framework
                </dt>
                <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p class="flex-auto">
                    Implement a systematic approach to managing information security risks with 
                    continuous monitoring and improvement processes.
                  </p>
                </dd>
              </div>
              <div class="flex flex-col">
                <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                  <Building className="h-5 w-5 flex-none text-primary" />
                  Regulatory Compliance
                </dt>
                <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p class="flex-auto">
                    Meet GDPR, NIS2, and other regulatory requirements. ISO 27001 provides 
                    the foundation for many privacy and security regulations.
                  </p>
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </section>

      {/* Implementation Process */}
      <section class="py-16 sm:py-20 bg-muted">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl lg:text-center">
            <h2 class="text-base font-semibold leading-7 text-primary">Implementation</h2>
            <p class="mt-2 text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              ISO 27001 in 6-12 Months
            </p>
          </div>
          <div class="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-6 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-4 lg:gap-8">
            {[
              {
                phase: "Phase 1",
                title: "Gap Analysis",
                description: "Assess current state against ISO 27001 requirements and create implementation roadmap."
              },
              {
                phase: "Phase 2",
                title: "ISMS Design",
                description: "Design and implement Information Security Management System with policies and procedures."
              },
              {
                phase: "Phase 3",
                title: "Risk Assessment",
                description: "Conduct comprehensive risk assessment and implement appropriate security controls."
              },
              {
                phase: "Phase 4",
                title: "Certification Audit",
                description: "Prepare for and undergo Stage 1 and Stage 2 certification audits with accredited body."
              }
            ].map((phase) => (
              <div class="flex flex-col items-start">
                <div class="rounded-md bg-primary/10 p-2 ring-1 ring-primary/20">
                  <Clock className="h-6 w-6 text-primary" />
                </div>
                <dt class="mt-4 font-semibold text-foreground">{phase.phase}</dt>
                <dd class="mt-2 text-sm font-medium text-primary">{phase.title}</dd>
                <dd class="mt-2 leading-7 text-muted-foreground text-sm">{phase.description}</dd>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section class="py-16 sm:py-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Ready for ISO 27001 Certification?
            </h2>
            <p class="mx-auto mt-6 max-w-xl text-lg leading-8 text-muted-foreground">
              Get started with our ISO 27001 readiness assessment and certification roadmap.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
              
                <a href="/get-started">
                  <Button size="lg" >Start ISO Assessment
                  </Button>
                </a>
                <a href="/resources">
                 <Button variant="outline" size="lg" >Download ISO Guide
                 </Button>
                </a>      
              
            </div>
          </div>
        </div>
      </section>

</Layout>
