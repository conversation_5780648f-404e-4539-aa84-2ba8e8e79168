{"lformsVersion": "29.0.0", "PATH_DELIMITER": "/", "code": "all-in-one", "codeList": [{"code": "all-in-one", "display": "Full-Featured Demo", "system": "OTHER"}], "identifier": null, "codeSystem": "OTHER", "name": "Full-Featured Demo", "type": "LOINC", "template": "table", "copyrightNotice": "A Copyright notice of the form", "items": [{"questionCode": "q_lg", "question": "'lg' view mode", "dataType": "QTY", "displayControl": {"viewMode": "lg", "css": [{"name": "color", "value": "red"}]}, "units": [{"name": "lbs", "default": true}, {"name": "kgs"}], "linkId": "/q_lg", "codeList": [{"code": "q_lg", "display": "'lg' view mode"}], "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "unit": {"name": "kgs"}, "value": null}, {"questionCode": "q_md", "question": "'md' view mode", "dataType": "QTY", "codingInstructions": "The 1st line\n   The 2nd line with spaces before and afer and an empty line after.   \n\nThe 3rd line.", "displayControl": {"viewMode": "md"}, "units": [{"name": "lbs", "default": true}, {"name": "kgs"}], "linkId": "/q_md", "codeList": [{"code": "q_md", "display": "'md' view mode"}], "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "unit": {"name": "lbs", "default": true}, "value": 100}, {"questionCode": "q_sm", "question": "'sm' view mode", "dataType": "QTY", "displayControl": {"viewMode": "sm"}, "units": [{"name": "lbs", "default": true}, {"name": "kgs"}], "linkId": "/q_sm", "codeList": [{"code": "q_sm", "display": "'sm' view mode"}], "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "unit": {"name": "lbs", "default": true}, "value": 200}, {"questionCode": "q_auto", "question": "'auto' view mode", "dataType": "QTY", "displayControl": {"viewMode": "auto"}, "units": [{"name": "lbs", "default": true}, {"name": "kgs"}], "linkId": "/q_auto", "codeList": [{"code": "q_auto", "display": "'auto' view mode"}], "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "unit": {"name": "lbs", "default": true}, "value": 45}, {"questionCode": "type0", "questionCodeSystem": "LOINC", "dataType": "ST", "header": false, "units": null, "codingInstructions": "simple text instructions", "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "With empty data type", "answers": null, "skipLogic": null, "editable": null, "defaultAnswer": null, "displayControl": null, "calculationMethod": null, "items": null, "linkId": "/type0", "codeList": [{"code": "type0", "display": "With empty data type", "system": "http://loinc.org"}], "legal": "A Copyright notice of the item", "value": "empty data type response"}, {"questionCode": "type1", "dataType": "BL", "header": false, "units": null, "codingInstructions": "<code>HTML</code> instructions, with a <button class='testButton'>button</button>LForms Demo 1", "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "With data type BL, #1", "answers": null, "questionCodeSystem": "NON_LOINC", "skipLogic": null, "editable": null, "defaultAnswer": null, "displayControl": null, "calculationMethod": null, "items": null, "linkId": "/type1", "responseAEI": {"evaluation": {"criteria": {"nature": "binary", "weight": 1, "yes": 100, "no": 0, "maxScore": 100}}}, "codeList": [{"code": "type1", "display": "With data type BL, #1", "system": "NON_LOINC"}], "value": false}, {"questionCode": "type1b", "dataType": "BL", "question": "With data type BL, #2", "answers": null, "questionCodeSystem": "NON_LOINC", "linkId": "/type1b", "codeList": [{"code": "type1b", "display": "With data type BL, #2", "system": "NON_LOINC"}], "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}}, {"questionCode": "type2", "dataType": "INT", "header": false, "units": null, "codingInstructions": "<code>HTML</code> instructions, with a <button class='testButton'>button</button>LForms Demo 2", "codingInstructionsFormat": "text", "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "With data type INT", "answers": null, "skipLogic": null, "editable": null, "defaultAnswer": null, "displayControl": null, "calculationMethod": null, "items": null, "linkId": "/type2", "codeList": [{"code": "type2", "display": "With data type INT"}], "legal": "not copyrighted"}, {"questionCode": "type3", "dataType": "REAL", "header": false, "units": null, "codingInstructions": "<code>HTML</code> instructions, with a <button class='testButton'>button</button>LForms Demo 3", "codingInstructionsFormat": "html", "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "With data type REAL", "answers": null, "skipLogic": null, "editable": null, "defaultAnswer": null, "displayControl": null, "calculationMethod": null, "items": null, "linkId": "/type3", "codeList": [{"code": "type3", "display": "With data type REAL"}], "legal": "not copyrighted"}, {"questionCode": "type4", "dataType": "ST", "header": false, "units": null, "codingInstructions": "Extra long text for showing a vertical scroll bar: Gave read use way make spot how nor. In daughter goodness an likewise oh consider at procured wandered. Songs words wrong by me hills heard timed. Happy eat may doors songs. Be ignorant so of suitable dissuade weddings together. Least whole timed we is. An smallness deficient discourse do newspaper be an eagerness continued. Mr my ready guest ye after short at. \nAs collected deficient objection by it discovery sincerity curiosity. Quiet decay who round three world whole has mrs man. Built the china there tried jokes which gay why. Assure in adieus wicket it is. But spoke round point and one joy. Offending her moonlight men sweetness see unwilling. Often of it tears whole oh balls share an. \nDeath weeks early had their and folly timed put. Hearted forbade on an village ye in fifteen. Age attended betrayed her man raptures laughter. Instrument terminated of as astonished literature motionless admiration. The affection are determine how performed intention discourse but. On merits on so valley indeed assure of. Has add particular boisterous uncommonly are. Early wrong as so manor match. Him necessary shameless discovery consulted one but. \nUp unpacked friendly ecstatic so possible humoured do. Ample end might folly quiet one set spoke her. We no am former valley assure. Four need spot ye said we find mile. Are commanded him convinced dashwoods did estimable forfeited. Shy celebrated met sentiments she reasonably but. Proposal its disposed eat advanced marriage sociable. Drawings led greatest add subjects endeavor gay remember. Principles one yet assistance you met impossible. \nIt real sent your at. Amounted all shy set why followed declared. Repeated of endeavor mr position kindness offering ignorant so up. Simplicity are melancholy preference considered saw companions. Disposal on outweigh do speedily in on. Him ham although thoughts entirely drawings. Acceptance unreserved old admiration projection nay yet him. Lasted am so before on esteem vanity oh. \nShe suspicion dejection saw instantly. Well deny may real one told yet saw hard dear. Bed chief house rapid right the. Set noisy one state tears which. No girl oh part must fact high my he. Simplicity in excellence melancholy as remarkably discovered. Own partiality motionless was old excellence she inquietude contrasted. Sister giving so wicket cousin of an he rather marked. Of on game part body rich. Adapted mr savings venture it or comfort affixed friends. \nNo in he real went find mr. Wandered or strictly raillery stanhill as. Jennings appetite disposed me an at subjects an. To no indulgence diminution so discovered mr apartments. Are off under folly death wrote cause her way spite. Plan upon yet way get cold spot its week. Almost do am or limits hearts. Resolve parties but why she shewing. She sang know now how nay cold real case. \nAt ourselves direction believing do he departure. Celebrated her had sentiments understood are projection set. Possession ye no mr unaffected remarkably at. Wrote house in never fruit up. Pasture imagine my garrets an he. However distant she request behaved see nothing. Talking settled at pleased an of me brother weather. \nDo play they miss give so up. Words to up style of since world. We leaf to snug on no need. Way own uncommonly travelling now acceptance bed compliment solicitude. Dissimilar admiration so terminated no in contrasted it. Advantages entreaties mr he apartments do. Limits far yet turned highly repair parish talked six. Draw fond rank form nor the day eat. \nLiterature admiration frequently indulgence announcing are who you her. Was least quick after six. So it yourself repeated together cheerful. Neither it cordial so painful picture studied if. Sex him position doubtful resolved boy expenses. Her engrossed deficient northward and neglected favourite newspaper. But use peculiar produced concerns ten. \n", "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "With data type ST", "answers": null, "skipLogic": null, "editable": null, "defaultAnswer": "default value", "displayControl": null, "calculationMethod": null, "items": null, "linkId": "/type4", "codeList": [{"code": "type4", "display": "With data type ST"}], "value": "default value"}, {"questionCode": "type5", "dataType": "BIN", "header": false, "units": null, "codingInstructions": null, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "With data type BIN", "answers": null, "skipLogic": null, "editable": null, "defaultAnswer": null, "displayControl": null, "calculationMethod": null, "items": null, "linkId": "/type5", "codeList": [{"code": "type5", "display": "With data type BIN"}]}, {"questionCode": "type6", "dataType": "DT", "header": false, "units": null, "codingInstructions": null, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "With data type DT", "answers": null, "skipLogic": null, "editable": null, "defaultAnswer": null, "displayControl": null, "calculationMethod": null, "items": null, "linkId": "/type6", "codeList": [{"code": "type6", "display": "With data type DT"}]}, {"questionCode": "type7", "dataType": "DTM", "header": false, "units": null, "codingInstructions": null, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "With data type DTM", "answers": null, "skipLogic": null, "editable": null, "defaultAnswer": "2019-11-11T11:11:11Z", "displayControl": null, "calculationMethod": null, "items": null, "linkId": "/type7", "codeList": [{"code": "type7", "display": "With data type DTM"}], "value": "2019-11-11T11:11:11.000Z"}, {"questionCode": "type8", "dataType": "TM", "header": false, "units": null, "codingInstructions": null, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "With data type TM", "answers": null, "skipLogic": null, "editable": null, "defaultAnswer": null, "displayControl": null, "calculationMethod": null, "items": null, "linkId": "/type8", "codeList": [{"code": "type8", "display": "With data type TM"}]}, {"questionCode": "type9", "dataType": "CODING", "header": false, "units": null, "codingInstructions": null, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "With data type CNE", "answers": [{"code": "c1", "text": "Answer 1", "other": null}, {"code": "c2", "text": "Answer 2", "other": null}, {"code": "c3", "text": "Answer 3", "other": null}, {"code": "c4", "text": "Other:", "other": true}], "skipLogic": null, "editable": null, "defaultAnswer": {"text": "Answer 2"}, "displayControl": {"answerLayout": {"type": "COMBO_BOX", "columns": "0"}}, "calculationMethod": null, "items": null, "linkId": "/type9", "codeList": [{"code": "type9", "display": "With data type CNE"}], "value": {"code": "c2", "text": "Answer 2", "other": null}}, {"questionCode": "type10", "dataType": "CODING", "header": false, "units": null, "codingInstructions": null, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "With data type CWE", "answers": [{"label": "1", "code": "c01", "text": "With a label 1", "score": 1, "other": null}, {"label": "2", "code": "c02", "text": "With a label 2", "score": 2, "other": null}, {"label": "3", "code": "c03", "text": "With a label 3", "score": 3, "other": null}], "skipLogic": null, "editable": null, "defaultAnswer": null, "displayControl": {"answerLayout": {"type": "COMBO_BOX", "columns": "0"}}, "calculationMethod": null, "items": null, "linkId": "/type10", "answerConstraint": "optionsOrString", "codeList": [{"code": "type10", "display": "With data type CWE"}]}, {"questionCode": "type11", "dataType": "RTO", "header": false, "units": null, "codingInstructions": null, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "With data type RTO", "answers": null, "skipLogic": null, "editable": null, "defaultAnswer": null, "displayControl": null, "calculationMethod": null, "items": null, "linkId": "/type11", "codeList": [{"code": "type11", "display": "With data type RTO"}]}, {"questionCode": "type12", "dataType": "QTY", "header": false, "units": null, "codingInstructions": null, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "With data type QTY", "answers": null, "skipLogic": null, "editable": null, "defaultAnswer": null, "displayControl": null, "calculationMethod": null, "items": null, "linkId": "/type12", "codeList": [{"code": "type12", "display": "With data type QTY"}]}, {"questionCode": "type13", "dataType": "YEAR", "header": false, "units": null, "codingInstructions": null, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "With data type YEAR", "answers": null, "skipLogic": null, "editable": null, "defaultAnswer": null, "displayControl": null, "calculationMethod": null, "items": null, "linkId": "/type13", "codeList": [{"code": "type13", "display": "With data type YEAR"}]}, {"questionCode": "type14", "dataType": "MONTH", "header": false, "units": null, "codingInstructions": null, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "With data type MONTH", "answers": null, "skipLogic": null, "editable": null, "defaultAnswer": null, "displayControl": null, "calculationMethod": null, "items": null, "linkId": "/type14", "codeList": [{"code": "type14", "display": "With data type MONTH"}]}, {"questionCode": "type15", "dataType": "DAY", "header": false, "units": null, "codingInstructions": null, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "With data type DAY", "answers": null, "skipLogic": null, "editable": null, "defaultAnswer": null, "displayControl": null, "calculationMethod": null, "items": null, "linkId": "/type15", "codeList": [{"code": "type15", "display": "With data type DAY"}]}, {"questionCode": "type16", "dataType": "URL", "header": false, "units": null, "codingInstructions": null, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "With data type URL", "answers": null, "skipLogic": null, "editable": null, "defaultAnswer": null, "displayControl": null, "calculationMethod": null, "items": null, "linkId": "/type16", "codeList": [{"code": "type16", "display": "With data type URL"}]}, {"questionCode": "type17", "dataType": "EMAIL", "header": false, "units": null, "codingInstructions": null, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "With data type EMAIL", "answers": null, "skipLogic": null, "editable": null, "defaultAnswer": null, "displayControl": null, "calculationMethod": null, "items": null, "linkId": "/type17", "codeList": [{"code": "type17", "display": "With data type EMAIL"}]}, {"questionCode": "type18", "dataType": "PHONE", "header": false, "units": null, "codingInstructions": null, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "With data type PHONE", "answers": null, "skipLogic": null, "editable": null, "defaultAnswer": null, "displayControl": null, "calculationMethod": null, "items": null, "linkId": "/type18", "codeList": [{"code": "type18", "display": "With data type PHONE"}]}, {"questionCode": "type19", "dataType": "TX", "header": false, "units": null, "codingInstructions": null, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "With data type TX", "answers": null, "skipLogic": null, "editable": null, "defaultAnswer": null, "displayControl": null, "calculationMethod": null, "items": null, "linkId": "/type19", "codeList": [{"code": "type19", "display": "With data type TX"}]}, {"questionCode": "multiSelectCNE", "dataType": "CODING", "header": false, "units": null, "codingInstructions": null, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "*"}, "question": "Multi Selection on CNE", "answers": [{"code": "c1", "text": "Answer 1"}, {"code": "c2", "text": "Answer 2"}, {"code": "c3", "text": "Answer 3"}, {"code": "c4", "text": "Answer 4"}], "skipLogic": null, "editable": null, "defaultAnswer": null, "displayControl": {"answerLayout": {"type": "COMBO_BOX", "columns": "0"}}, "calculationMethod": null, "items": null, "linkId": "/multiSelectCNE", "codeList": [{"code": "multiSelectCNE", "display": "Multi Selection on CNE"}]}, {"questionCode": "multiSelectCWE", "dataType": "CODING", "header": false, "units": null, "codingInstructions": null, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "*"}, "question": "Multi Selection on CWE", "answers": [{"code": "c1", "text": "Answer 1"}, {"code": "c2", "text": "Answer 2"}, {"code": "c3", "text": "Answer 3"}, {"code": "c4", "text": "Answer 4"}], "skipLogic": null, "editable": null, "defaultAnswer": null, "displayControl": {"answerLayout": {"type": "COMBO_BOX", "columns": "0"}}, "calculationMethod": null, "items": null, "linkId": "/multiSelectCWE", "answerConstraint": "optionsOrString", "codeList": [{"code": "multiSelectCWE", "display": "Multi Selection on CWE"}]}, {"questionCode": "slSource1", "dataType": "INT", "header": false, "units": null, "codingInstructions": "1 to show T1; >=2 to show T2; <=5 to show header T3, and its subitmes T4 and T5.", "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "Skip Logic Source #1", "answers": null, "editable": null, "defaultAnswer": null, "displayControl": null, "calculationMethod": null, "items": null, "linkId": "/slSource1", "codeList": [{"code": "slSource1", "display": "Skip Logic Source #1"}]}, {"questionCode": "slTargetItem1", "dataType": "INT", "header": false, "units": null, "codingInstructions": null, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "T1: Shown when 'Skip Logic Source #1' == 1", "answers": null, "skipLogic": {"conditions": [{"source": "/slSource1", "trigger": {"value": 1}}], "action": "show"}, "editable": null, "defaultAnswer": null, "displayControl": null, "calculationMethod": null, "items": null, "linkId": "/slTargetItem1", "codeList": [{"code": "slTargetItem1", "display": "T1: Shown when 'Skip Logic Source #1' == 1"}]}, {"questionCode": "slTargetItem2", "dataType": "INT", "header": false, "units": null, "codingInstructions": null, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "T2: Shown when 'Skip Logic Source #1' > 1", "answers": null, "skipLogic": {"conditions": [{"source": "/slSource1", "trigger": {"minInclusive": 2}}], "action": "show"}, "editable": null, "defaultAnswer": null, "displayControl": null, "calculationMethod": null, "items": null, "linkId": "/slTargetItem2", "codeList": [{"code": "slTargetItem2", "display": "T2: Shown when 'Skip Logic Source #1' > 1"}]}, {"questionCode": "slTargetHeader1", "dataType": "SECTION", "header": true, "units": null, "codingInstructions": null, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "T3: Shown when 'Skip Logic Source #1' <= 5", "answers": null, "skipLogic": {"conditions": [{"source": "/slSource1", "trigger": {"maxInclusive": 5}}], "action": "show"}, "editable": null, "defaultAnswer": null, "displayControl": {"questionLayout": "vertical"}, "calculationMethod": null, "items": [{"questionCode": "slTargetSubItem1", "dataType": "INT", "header": false, "units": null, "codingInstructions": null, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "T4: Shown when my section header is shown", "answers": null, "skipLogic": null, "editable": null, "defaultAnswer": null, "displayControl": null, "calculationMethod": null, "items": null, "linkId": "/slTargetHeader1/slTargetSubItem1", "codeList": [{"code": "slTargetSubItem1", "display": "T4: Shown when my section header is shown"}]}, {"questionCode": "slTargetSubItem2", "dataType": "INT", "header": false, "units": null, "codingInstructions": null, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "T5: Shown when my section header is shown", "answers": null, "skipLogic": null, "editable": null, "defaultAnswer": null, "displayControl": null, "calculationMethod": null, "items": null, "linkId": "/slTargetHeader1/slTargetSubItem2", "codeList": [{"code": "slTargetSubItem2", "display": "T5: Shown when my section header is shown"}]}], "linkId": "/slTargetHeader1", "codeList": [{"code": "slTargetHeader1", "display": "T3: Shown when 'Skip Logic Source #1' <= 5"}]}, {"questionCode": "slTargetItem6", "dataType": "INT", "question": "T6: Shown when 'Skip Logic Source #1' != 2", "skipLogic": {"conditions": [{"source": "/slSource1", "trigger": {"notEqual": 2}}], "action": "show"}, "linkId": "/slTargetItem6", "codeList": [{"code": "slTargetItem6", "display": "T6: Shown when 'Skip Logic Source #1' != 2"}], "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}}, {"questionCode": "54139-1-cnesrc-1", "answerCodeSystem": "http://loinc.org", "questionCardinality": {"min": "1", "max": "1"}, "question": "Living?", "answers": [{"text": "Yes", "code": "LA33-6", "system": "http://loinc.org"}, {"text": "No", "code": "LA32-8", "system": "http://loinc.org"}, {"text": "Unknown", "code": "LA4489-6", "system": "http://loinc.org"}], "dataType": "CODING", "units": "", "header": false, "items": [{"questionCode": "54124-3", "questionCardinality": {"min": "1", "max": "1"}, "question": "Date of Birth (show if Living is YES)", "answers": "", "dataType": "DT", "units": "", "header": false, "skipLogic": {"conditions": [{"source": "/54139-1-cnesrc-1", "trigger": {"value": {"code": "LA33-6", "system": "http://loinc.org"}}}], "action": "show"}, "linkId": "/54139-1-cnesrc-1/54124-3", "codeList": [{"code": "54124-3", "display": "Date of Birth (show if Living is YES)"}], "answerCardinality": {"min": "0", "max": "1"}}, {"questionCode": "54124-3b", "questionCardinality": {"min": "1", "max": "1"}, "question": "Date of Birth (show if Living is YES) -- not working without 'system' in trigger value", "answers": "", "dataType": "DT", "units": "", "header": false, "skipLogic": {"conditions": [{"source": "/54139-1-cnesrc-1", "trigger": {"value": {"code": "LA33-6"}}}], "action": "show"}, "linkId": "/54139-1-cnesrc-1/54124-3b", "codeList": [{"code": "54124-3b", "display": "Date of Birth (show if Living is YES) -- not working without 'system' in trigger value"}], "answerCardinality": {"min": "0", "max": "1"}}, {"questionCode": "54141-7", "questionCardinality": {"min": "1", "max": "1"}, "question": "Age (show if Living answered)", "answers": "", "dataType": "REAL", "units": "", "header": false, "skipLogic": {"conditions": [{"source": "/54139-1-cnesrc-1", "trigger": {"exists": true}}], "action": "show"}, "linkId": "/54139-1-cnesrc-1/54141-7", "codeList": [{"code": "54141-7", "display": "Age (show if Living answered)"}], "answerCardinality": {"min": "0", "max": "1"}}, {"questionCode": "54112-8", "questionCardinality": {"min": "1", "max": "1"}, "question": "Cause of Death (show if Living is NO)", "answers": [{"text": "Blood Clots", "code": "LA10533-0"}, {"text": "-- Blood Clot in Leg", "code": "LA10572-8"}, {"text": "-- Blood Clot in Lungs", "code": "LA10573-6"}], "dataType": "CODING", "units": "", "header": false, "skipLogic": {"conditions": [{"source": "/54139-1-cnesrc-1", "trigger": {"value": {"code": "LA32-8", "system": "http://loinc.org"}}}], "action": "show"}, "linkId": "/54139-1-cnesrc-1/54112-8", "codeList": [{"code": "54112-8", "display": "Cause of Death (show if Living is NO)"}], "displayControl": {"answerLayout": {"type": "COMBO_BOX", "columns": "0"}}, "answerCardinality": {"min": "0", "max": "1"}}, {"questionCode": "54113-6", "answerCodeSystem": "http://loinc.org", "questionCardinality": {"min": "1", "max": "1"}, "question": "Age at Death (show if Living not answered)", "answers": [{"text": "Pre-Birth", "code": "LA10402-8", "system": "http://loinc.org"}, {"text": "Newborn", "code": "LA10403-6", "system": "http://loinc.org"}, {"text": "Infancy", "code": "LA10394-7", "system": "http://loinc.org"}], "dataType": "CODING", "units": "", "header": false, "skipLogic": {"conditions": [{"source": "/54139-1-cnesrc-1", "trigger": {"exists": false}}], "action": "show"}, "linkId": "/54139-1-cnesrc-1/54113-6", "codeList": [{"code": "54113-6", "display": "Age at Death (show if Living not answered)"}], "displayControl": {"answerLayout": {"type": "COMBO_BOX", "columns": "0"}}, "answerCardinality": {"min": "0", "max": "1"}}, {"questionCode": "skiplogic_not", "question": "Cause of Death #2 (show if Living is _not_ YES)", "answers": [{"text": "Blood Clots", "code": "LA10533-0"}, {"text": "-- Blood Clot in Leg", "code": "LA10572-8"}, {"text": "-- Blood Clot in Lungs", "code": "LA10573-6"}], "dataType": "CODING", "skipLogic": {"conditions": [{"source": "/54139-1-cnesrc-1", "trigger": {"notEqual": {"code": "LA33-6", "system": "http://loinc.org"}}}], "action": "show"}, "linkId": "/54139-1-cnesrc-1/skiplogic_not", "codeList": [{"code": "skiplogic_not", "display": "Cause of Death #2 (show if Living is _not_ YES)"}], "displayControl": {"answerLayout": {"type": "COMBO_BOX", "columns": "0"}}, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}}], "linkId": "/54139-1-cnesrc-1", "codeList": [{"code": "54139-1-cnesrc-1", "display": "Living?"}], "displayControl": {"answerLayout": {"type": "COMBO_BOX", "columns": "0"}}, "answerCardinality": {"min": "0", "max": "1"}}, {"questionCode": "54139-1-c<PERSON>rc-2", "questionCardinality": {"min": "1", "max": "1"}, "question": "Living too?", "answers": [{"text": "Yes", "code": "LA33-6"}, {"text": "No", "code": "LA32-8", "system": "http://loinc.org"}, {"text": "Unknown", "code": "LA4489-6"}], "dataType": "CODING", "units": "", "header": false, "items": [{"questionCode": "54124-3c", "questionCardinality": {"min": "1", "max": "1"}, "question": "Date of Birth (show if Living is YES) -- not working with 'system' in trigger value", "answers": "", "dataType": "DT", "units": "", "header": false, "skipLogic": {"conditions": [{"source": "/54139-1-cnesrc-2", "trigger": {"value": {"code": "LA33-6", "system": "http://loinc.org"}}}], "action": "show"}, "linkId": "/54139-1-cnesrc-2/54124-3c", "codeList": [{"code": "54124-3c", "display": "Date of Birth (show if Living is YES) -- not working with 'system' in trigger value"}], "answerCardinality": {"min": "0", "max": "1"}}, {"questionCode": "54124-3d", "questionCardinality": {"min": "1", "max": "1"}, "question": "Date of Birth (show if Living is YES) ", "answers": "", "dataType": "DT", "units": "", "header": false, "skipLogic": {"conditions": [{"source": "/54139-1-cnesrc-2", "trigger": {"value": {"code": "LA33-6"}}}], "action": "show"}, "linkId": "/54139-1-cnesrc-2/54124-3d", "codeList": [{"code": "54124-3d", "display": "Date of Birth (show if Living is YES) "}], "answerCardinality": {"min": "0", "max": "1"}}, {"questionCode": "54112-8b", "questionCardinality": {"min": "1", "max": "1"}, "question": "Cause of Death (show if Living is NO) ", "answers": "", "dataType": "DT", "units": "", "header": false, "skipLogic": {"conditions": [{"source": "/54139-1-cnesrc-2", "trigger": {"value": {"code": "LA32-8", "system": "http://loinc.org"}}}], "action": "show"}, "linkId": "/54139-1-cnesrc-2/54112-8b", "codeList": [{"code": "54112-8b", "display": "Cause of Death (show if Living is NO) "}], "answerCardinality": {"min": "0", "max": "1"}}], "linkId": "/54139-1-cnesrc-2", "codeList": [{"code": "54139-1-c<PERSON>rc-2", "display": "Living too?"}], "displayControl": {"answerLayout": {"type": "COMBO_BOX", "columns": "0"}}, "answerCardinality": {"min": "0", "max": "1"}}, {"questionCode": "slALLSource1", "dataType": "INT", "header": false, "units": null, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "Source #1 (ALL)", "answers": null, "editable": null, "defaultAnswer": null, "displayControl": null, "calculationMethod": null, "items": null, "linkId": "/slALLSource1", "codeList": [{"code": "slALLSource1", "display": "Source #1 (ALL)"}]}, {"questionCode": "slALLSource2", "dataType": "INT", "header": false, "units": null, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "Source #2 (ALL)", "answers": null, "editable": null, "defaultAnswer": null, "displayControl": null, "calculationMethod": null, "items": null, "linkId": "/slALLSource2", "codeList": [{"code": "slALLSource2", "display": "Source #2 (ALL)"}]}, {"questionCode": "slALLTargetItem", "dataType": "INT", "header": false, "units": null, "codingInstructions": null, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "Shown when 'Source #1 (ALL)' == 1 AND Shown when 'Source #2 (ALL)' == 2 ", "answers": null, "skipLogic": {"conditions": [{"source": "/slALLSource1", "trigger": {"value": 1}}, {"source": "/slALLSource2", "trigger": {"value": 2}}], "action": "show", "logic": "ALL"}, "editable": null, "defaultAnswer": null, "displayControl": null, "calculationMethod": null, "items": null, "linkId": "/slALLTargetItem", "codeList": [{"code": "slALLTargetItem", "display": "Shown when 'Source #1 (ALL)' == 1 AND Shown when 'Source #2 (ALL)' == 2 "}]}, {"questionCode": "slANYSource1", "dataType": "INT", "header": false, "units": null, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "Source #1 (ANY)", "answers": null, "editable": null, "defaultAnswer": null, "displayControl": null, "calculationMethod": null, "items": null, "linkId": "/slANYSource1", "codeList": [{"code": "slANYSource1", "display": "Source #1 (ANY)"}]}, {"questionCode": "slANYSource2", "dataType": "INT", "header": false, "units": null, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "Source #2 (ANY)", "answers": null, "editable": null, "defaultAnswer": null, "displayControl": null, "calculationMethod": null, "items": null, "linkId": "/slANYSource2", "codeList": [{"code": "slANYSource2", "display": "Source #2 (ANY)"}]}, {"questionCode": "slANYTargetItem", "dataType": "INT", "header": false, "units": null, "codingInstructions": null, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "Shown when 'Source #1 (ANY)' == 1 OR Shown when 'Source #2 (ANY)' == 2 ", "answers": null, "skipLogic": {"conditions": [{"source": "/slANYSource1", "trigger": {"value": 1}}, {"source": "/slANYSource2", "trigger": {"value": 2}}], "action": "show", "logic": "ANY"}, "editable": null, "defaultAnswer": null, "displayControl": null, "calculationMethod": null, "items": null, "linkId": "/slANYTargetItem", "codeList": [{"code": "slANYTargetItem", "display": "Shown when 'Source #1 (ANY)' == 1 OR Shown when 'Source #2 (ANY)' == 2 "}]}, {"questionCode": "rpSource2", "dataType": "INT", "header": false, "units": null, "codingInstructions": "2 to show T2", "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "Skip Logic Source (repeating) #2", "answers": null, "editable": null, "defaultAnswer": null, "displayControl": null, "calculationMethod": null, "items": null, "linkId": "/rpSource2", "codeList": [{"code": "rpSource2", "display": "Skip Logic Source (repeating) #2"}]}, {"questionCode": "repeatingSection1", "header": true, "question": "A Repeating Section", "questionCardinality": {"max": "*", "min": "1"}, "items": [{"questionCode": "rpSource1", "dataType": "INT", "header": false, "units": null, "codingInstructions": "1 to show T1; <=5 to show header T3, and its subitem T4.", "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "Skip Logic Source (repeating) #1", "answers": null, "editable": null, "defaultAnswer": null, "displayControl": null, "calculationMethod": null, "items": null, "linkId": "/repeatingSection1/rpSource1", "codeList": [{"code": "rpSource1", "display": "Skip Logic Source (repeating) #1"}]}, {"questionCode": "rpTargetItem1", "dataType": "INT", "header": false, "units": null, "codingInstructions": null, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "T1: Shown when 'Skip Logic Source (repeating) #1' == 1", "answers": null, "skipLogic": {"conditions": [{"source": "/repeatingSection1/rpSource1", "trigger": {"value": 1}}], "action": "show"}, "editable": null, "defaultAnswer": null, "displayControl": null, "calculationMethod": null, "items": null, "linkId": "/repeatingSection1/rpTargetItem1", "codeList": [{"code": "rpTargetItem1", "display": "T1: Shown when 'Skip Logic Source (repeating) #1' == 1"}]}, {"questionCode": "rpTargetItem2", "dataType": "INT", "header": false, "units": null, "codingInstructions": null, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "T2: Shown when 'Skip Logic Source (repeating) #2' == 2", "answers": null, "skipLogic": {"conditions": [{"source": "/rpSource2", "trigger": {"value": 2}}], "action": "show"}, "editable": null, "defaultAnswer": null, "displayControl": null, "calculationMethod": null, "items": null, "linkId": "/repeatingSection1/rpTargetItem2", "codeList": [{"code": "rpTargetItem2", "display": "T2: Shown when 'Skip Logic Source (repeating) #2' == 2"}]}, {"questionCode": "rpTargetHeader1", "dataType": "SECTION", "header": true, "units": null, "codingInstructions": null, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "T3: Shown when 'Skip Logic Source (repeating) #1' <= 5", "answers": null, "skipLogic": {"conditions": [{"source": "/repeatingSection1/rpSource1", "trigger": {"maxInclusive": 5}}], "action": "show"}, "editable": null, "defaultAnswer": null, "displayControl": {"questionLayout": "vertical"}, "calculationMethod": null, "items": [{"questionCode": "rpTargetSubItem1", "dataType": "INT", "header": false, "units": null, "codingInstructions": null, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "question": "T4: Shown when my section header is shown", "answers": null, "skipLogic": null, "editable": null, "defaultAnswer": null, "displayControl": null, "calculationMethod": null, "items": null, "linkId": "/repeatingSection1/rpTargetHeader1/rpTargetSubItem1", "codeList": [{"code": "rpTargetSubItem1", "display": "T4: Shown when my section header is shown"}]}], "linkId": "/repeatingSection1/rpTargetHeader1", "codeList": [{"code": "rpTargetHeader1", "display": "T3: Shown when 'Skip Logic Source (repeating) #1' <= 5"}]}], "linkId": "/repeatingSection1", "codeList": [{"code": "repeatingSection1", "display": "A Repeating Section"}], "dataType": "SECTION", "displayControl": {"questionLayout": "vertical"}, "answerCardinality": {"min": "0", "max": "1"}}, {"questionCode": "dataControlExamples", "header": true, "question": "'dataControl' examples", "items": [{"questionCode": "itemWithExtraData", "dataType": "CODING", "header": false, "question": "Drug (with extra data of strengths and forms)", "externallyDefined": "https://clin-table-search.lhc.nlm.nih.gov/api/rxterms/v3/search?ef=STRENGTHS_AND_FORMS,RXCUIS&autocomp=1", "linkId": "/dataControlExamples/itemWithExtraData", "codeList": [{"code": "itemWithExtraData", "display": "Drug (with extra data of strengths and forms)"}], "displayControl": {"answerLayout": {"type": "COMBO_BOX", "columns": "0"}}, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}}, {"questionCode": "controlledItem_LIST", "dataType": "CODING", "header": false, "question": "Strengths and Forms (from 'Drug')", "dataControl": [{"source": {"sourceType": "INTERNAL", "sourceLinkId": "/dataControlExamples/itemWithExtraData"}, "construction": "ARRAY", "dataFormat": {"code": "value.data.RXCUIS", "text": "value.data.STRENGTHS_AND_FORMS"}, "onAttribute": "answers"}], "linkId": "/dataControlExamples/controlledItem_LIST", "codeList": [{"code": "controlledItem_LIST", "display": "Strengths and Forms (from 'Drug')"}], "displayControl": {"answerLayout": {"type": "COMBO_BOX", "columns": "0"}}, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}, "answers": []}, {"questionCode": "controlledItem_TEXT", "dataType": "ST", "header": false, "question": "The First Strength (from 'Drugs')", "dataControl": [{"source": {"sourceLinkId": "/dataControlExamples/itemWithExtraData"}, "construction": "SIMPLE", "dataFormat": "value.data.STRENGTHS_AND_FORMS[0]", "onAttribute": "value"}], "linkId": "/dataControlExamples/controlledItem_TEXT", "codeList": [{"code": "controlledItem_TEXT", "display": "The First Strength (from 'Drugs')"}], "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}}], "linkId": "/dataControlExamples", "codeList": [{"code": "dataControlExamples", "display": "'dataControl' examples"}], "dataType": "SECTION", "displayControl": {"questionLayout": "vertical"}, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}}, {"questionCode": "<PERSON><PERSON><PERSON>er", "question": "This is a TITLE. It looks like a section header but has no children", "dataType": "TITLE", "header": false, "linkId": "/titleHeader", "codeList": [{"code": "<PERSON><PERSON><PERSON>er", "display": "This is a TITLE. It looks like a section header but has no children"}], "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}}, {"questionCode": "cardinalityControl", "question": "This controls the initial number of rows in the horizontal table below", "dataType": "CODING", "answers": [{"code": "c1", "text": "1 row, no repeating", "questionCardinality": {"min": "1", "max": "1"}}, {"code": "c2", "text": "1 row, repeating", "questionCardinality": {"min": "1", "max": "*"}}, {"code": "c3", "text": "2 rows, no repeating", "questionCardinality": {"min": "2", "max": "2"}}, {"code": "c4", "text": "2 rows, repeating", "questionCardinality": {"min": "2", "max": "*"}}, {"code": "c5", "text": "2 rows, repeating, 5 rows max", "questionCardinality": {"min": "2", "max": "5"}}], "linkId": "/cardinalityControl", "codeList": [{"code": "cardinalityControl", "display": "This controls the initial number of rows in the horizontal table below"}], "displayControl": {"answerLayout": {"type": "COMBO_BOX", "columns": "0"}}, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}}, {"questionCode": "horizontalTable", "questionCardinality": {"min": "1", "max": "1"}, "question": "A non-repeating horizontal table", "header": true, "dataControl": [{"source": {"sourceType": "INTERNAL", "sourceLinkId": "/cardinalityControl"}, "construction": "SIMPLE", "dataFormat": "value.questionCardinality", "onAttribute": "questionCardinality"}], "items": [{"questionCode": "colA", "question": "A ST", "dataType": "ST", "prefix": "Pre. A:", "displayControl": {"colCSS": [{"name": "width", "value": "25%"}, {"name": "min-width", "value": "10%"}]}, "linkId": "/horizontalTable/colA", "codeList": [{"code": "colA", "display": "A ST"}], "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}}, {"questionCode": "colB", "question": "A TX", "dataType": "TX", "displayControl": {"colCSS": [{"name": "width", "value": "25%"}, {"name": "min-width", "value": "15%"}]}, "linkId": "/horizontalTable/colB", "codeList": [{"code": "colB", "display": "A TX"}], "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}}, {"questionCode": "colC", "question": "A CNE", "dataType": "CODING", "answers": [{"code": "c1", "text": "Answer 1", "other": null}, {"code": "c2", "text": "Answer 2", "other": null}, {"code": "c3", "text": "Answer 3", "other": null}, {"code": "c4", "text": "Other:", "other": true}], "displayControl": {"colCSS": [{"name": "width", "value": "50%"}], "answerLayout": {"type": "COMBO_BOX", "columns": "0"}}, "linkId": "/horizontalTable/colC", "codeList": [{"code": "colC", "display": "A CNE"}], "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}}], "linkId": "/horizontalTable", "codeList": [{"code": "horizontalTable", "display": "A non-repeating horizontal table"}], "dataType": "SECTION", "displayControl": {"questionLayout": "horizontal"}, "answerCardinality": {"min": "0", "max": "1"}}, {"questionCode": "listWHeaders", "question": "A list with headers", "dataType": "CODING", "answers": [{"code": "food", "text": "Food allergies"}, {"code": "FOOD-2", "text": "Chocolate", "parentAnswerCode": "food"}, {"code": "FOOD-22", "text": "<PERSON><PERSON>", "parentAnswerCode": "food"}, {"code": "environmental", "text": "Environmental allergies"}, {"code": "OTHR-18", "text": "Cat", "parentAnswerCode": "environmental"}, {"code": "OTHR-5", "text": "Cold Weather", "parentAnswerCode": "environmental"}], "linkId": "/listWHeaders", "answerConstraint": "optionsOrString", "codeList": [{"code": "listWHeaders", "display": "A list with headers"}], "displayControl": {"answerLayout": {"type": "COMBO_BOX", "columns": "0"}}, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}}, {"questionCode": "readonlyST", "dataType": "ST", "question": "editable='0' data type ST", "editable": "0", "linkId": "/readonlyST", "codeList": [{"code": "readonlyST", "display": "editable='0' data type ST"}], "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}}, {"questionCode": "readonlyCNE-s", "dataType": "CODING", "question": "editable='0' data type CNE, single selection", "editable": "0", "answerCardinality": {"min": "0", "max": "1"}, "value": {"code": "c1", "text": "Answer 1", "other": null}, "answers": [{"code": "c1", "text": "Answer 1", "other": null}, {"code": "c2", "text": "Answer 2", "other": null}, {"code": "c3", "text": "Answer 3", "other": null}, {"code": "c4", "text": "Other:", "other": true}], "linkId": "/readonlyCNE-s", "codeList": [{"code": "readonlyCNE-s", "display": "editable='0' data type CNE, single selection"}], "displayControl": {"answerLayout": {"type": "COMBO_BOX", "columns": "0"}}, "questionCardinality": {"min": "1", "max": "1"}}, {"questionCode": "readonlyCWE-m", "dataType": "CODING", "question": "editable='0' data type CWE, multiple selections", "editable": "0", "answerCardinality": {"min": "0", "max": "*"}, "answers": [{"code": "c1", "text": "Answer 1", "other": null}, {"code": "c2", "text": "Answer 2", "other": null}, {"code": "c3", "text": "Answer 3", "other": null}, {"code": "c4", "text": "Other:", "other": true}], "linkId": "/readonlyCWE-m", "codeList": [{"code": "readonlyCWE-m", "display": "editable='0' data type CWE, multiple selections"}], "displayControl": {"answerLayout": {"type": "COMBO_BOX", "columns": "0"}}, "questionCardinality": {"min": "1", "max": "1"}}, {"questionCode": "readonlyCNE-sb", "dataType": "CODING", "question": "editable='0' data type CNE, single selection, radio button", "editable": "0", "answerCardinality": {"min": "0", "max": "1"}, "value": {"code": "c2", "text": "Answer 2", "other": null}, "displayControl": {"answerLayout": {"type": "RADIO_CHECKBOX", "columns": "1"}}, "answers": [{"code": "c1", "text": "Answer 1", "other": null}, {"code": "c2", "text": "Answer 2", "other": null}, {"code": "c3", "text": "Answer 3", "other": null}, {"code": "c4", "text": "Other:", "other": true}], "linkId": "/readonlyCNE-sb", "codeList": [{"code": "readonlyCNE-sb", "display": "editable='0' data type CNE, single selection, radio button"}], "questionCardinality": {"min": "1", "max": "1"}}, {"questionCode": "readonlyCWE-mb", "dataType": "CODING", "question": "editable='0' data type CWE, multiple selections, checkboxes", "editable": "0", "answerCardinality": {"min": "0", "max": "*"}, "displayControl": {"answerLayout": {"type": "RADIO_CHECKBOX", "columns": "1"}}, "answers": [{"code": "c1", "text": "Answer 1", "other": null}, {"code": "c2", "text": "Answer 2", "other": null}, {"code": "c3", "text": "Answer 3", "other": null}, {"code": "c4", "text": "Other:", "other": true}], "linkId": "/readonlyCWE-mb", "codeList": [{"code": "readonlyCWE-mb", "display": "editable='0' data type CWE, multiple selections, checkboxes"}], "questionCardinality": {"min": "1", "max": "1"}}, {"questionCode": "with_prefix", "dataType": "ST", "header": false, "prefix": "Prefix A:", "question": "Question display text", "linkId": "/with_prefix", "codeList": [{"code": "with_prefix", "display": "Question display text"}], "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}}, {"questionCode": "numeric_answer", "dataType": "CODING", "header": false, "question": "One answer is numeric, no seq num displayed", "answers": [{"code": "c1", "text": "1", "other": null}, {"code": "c2", "text": "Answer 2", "other": null}, {"code": "c3", "text": "Answer 3", "other": null}], "linkId": "/numeric_answer", "codeList": [{"code": "numeric_answer", "display": "One answer is numeric, no seq num displayed"}], "displayControl": {"answerLayout": {"type": "COMBO_BOX", "columns": "0"}}, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}}, {"questionCode": "answer_item", "dataType": "ST", "header": false, "question": "item.answer.item test main item", "items": [{"questionCode": "answer_item_name", "dataType": "ST", "header": false, "question": "item.answer.item name", "linkId": "/answer_item/answer_item_name", "codeList": [{"code": "answer_item_name", "display": "item.answer.item name"}], "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}}, {"questionCode": "answer_item_age", "dataType": "INT", "header": false, "question": "item.answer.item age", "linkId": "/answer_item/answer_item_age", "codeList": [{"code": "answer_item_age", "display": "item.answer.item age"}], "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}}], "linkId": "/answer_item", "codeList": [{"code": "answer_item", "display": "item.answer.item test main item"}], "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}}, {"questionCode": "required_dt", "question": "Required DT field", "dataType": "DT", "linkId": "/required_dt", "answerCardinality": {"min": "1", "max": "1"}, "codeList": [{"code": "required_dt", "display": "Required DT field"}], "questionCardinality": {"min": "1", "max": "1"}}, {"questionCode": "required_dtm", "question": "Required DTM field", "dataType": "DTM", "linkId": "/required_dtm", "answerCardinality": {"min": "1", "max": "1"}, "codeList": [{"code": "required_dtm", "display": "Required DTM field"}], "questionCardinality": {"min": "1", "max": "1"}}, {"questionCode": "required_tx", "question": "Required TX field", "dataType": "TX", "linkId": "/required_tx", "answerCardinality": {"min": "1", "max": "1"}, "codeList": [{"code": "required_tx", "display": "Required TX field"}], "questionCardinality": {"min": "1", "max": "1"}}, {"questionCode": "required_st", "question": "Required ST field", "dataType": "ST", "linkId": "/required_st", "answerCardinality": {"min": "1", "max": "1"}, "codeList": [{"code": "required_st", "display": "Required ST field"}], "questionCardinality": {"min": "1", "max": "1"}}, {"questionCode": "sl_source_to_test_required", "dataType": "INT", "codingInstructions": "1 to show RT1; >=2 to show RT2; <=5 to show header RT3, and its subitmes RT4", "question": "Skip Logic Source to test Required items", "linkId": "/sl_source_to_test_required", "codeList": [{"code": "sl_source_to_test_required", "display": "Skip Logic Source to test Required items"}], "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}}, {"questionCode": "sl_target_to_test_required1", "dataType": "ST", "question": "Required RT1: Shown when 'Skip Logic Required Source' == 1;", "answerCardinality": {"min": "1", "max": "1"}, "skipLogic": {"conditions": [{"source": "/sl_source_to_test_required", "trigger": {"value": 1}}], "action": "show"}, "linkId": "/sl_target_to_test_required1", "codeList": [{"code": "sl_target_to_test_required1", "display": "Required RT1: Shown when 'Skip Logic Required Source' == 1;"}], "questionCardinality": {"min": "1", "max": "1"}}, {"questionCode": "sl_target_to_test_required2", "dataType": "ST", "question": "Required RT2: Shown when 'Skip Logic Required Source' > 1", "answerCardinality": {"min": "1", "max": "1"}, "skipLogic": {"conditions": [{"source": "/sl_source_to_test_required", "trigger": {"minInclusive": 2}}], "action": "show"}, "linkId": "/sl_target_to_test_required2", "codeList": [{"code": "sl_target_to_test_required2", "display": "Required RT2: Shown when 'Skip Logic Required Source' > 1"}], "questionCardinality": {"min": "1", "max": "1"}}, {"questionCode": "sl_target_header", "dataType": "SECTION", "header": true, "question": "RT3: Shown when 'Skip Logic Required Source' <= 5", "skipLogic": {"conditions": [{"source": "/sl_source_to_test_required", "trigger": {"maxInclusive": 5}}], "action": "show"}, "items": [{"questionCode": "sl_target_to_test_required", "dataType": "ST", "question": "RT4: Shown when my section header is shown;", "linkId": "/sl_target_header/sl_target_to_test_required", "answerCardinality": {"min": "1", "max": "1"}, "codeList": [{"code": "sl_target_to_test_required", "display": "RT4: Shown when my section header is shown;"}], "questionCardinality": {"min": "1", "max": "1"}}], "linkId": "/sl_target_header", "codeList": [{"code": "sl_target_header", "display": "RT3: Shown when 'Skip Logic Required Source' <= 5"}], "displayControl": {"questionLayout": "vertical"}, "questionCardinality": {"min": "1", "max": "1"}, "answerCardinality": {"min": "0", "max": "1"}}], "templateOptions": {"showQuestionCode": false, "showCodingInstruction": false, "allowMultipleEmptyRepeatingItems": false, "allowHTML": true, "displayControl": {"questionLayout": "vertical"}, "viewMode": "auto", "defaultAnswerLayout": {"answerLayout": {"type": "COMBO_BOX", "columns": "0"}}, "hideTreeLine": false, "hideIndentation": false, "hideRepetitionNumber": false, "displayScoreWithAnswerText": true, "displayInvalidHTML": false, "messageLevel": "error", "hideFormControls": false, "showFormHeader": true}, "hasSavedData": true}