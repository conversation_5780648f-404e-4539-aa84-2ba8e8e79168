---
import Layout from "../../layouts/Layout.astro";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Calendar, Clock, ArrowLeft, User } from "lucide-react";
import { render,getEntry,getCollection } from 'astro:content';
import { marked } from 'marked';



const slug = Astro.params.slug;
const entry = await getEntry("blog", slug);
if (!entry) {
  // Handle Error, for example:
  throw new Error("Could not find data");
}

const {
  title,
  description,
  metaTitle,
  metaDescription,
  keywords = [],
} = entry.data;

const url = `/blog/${slug}/`;

const htmlString = entry.rendered?.html;
const content = marked.parse(htmlString);
const dateObj = new Date(entry.data.date);
const formattedDate = dateObj.toLocaleDateString('en-US', {
  year: 'numeric',
  month: 'long',  
});
const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Blog',"aria-disabled": false ,
  },
  {
    index: "2", text: entry.data.title,"aria-disabled": true ,
  },
];

---

<Layout
  title={metaTitle || title}
  description={metaDescription || description}
  keywords={keywords.join(', ')}
  url={url}
  breadcrumbLinks= {breadcrumbLinks}
>
 <main class="pt-20">
        <div class="container mx-auto px-4 py-8 max-w-4xl">
          <div class="mb-8">            
            <a href="/blog">
               <Button variant="ghost" className="mb-4">
                <ArrowLeft className="w-4 h-4 mr-2" />Back to Blog                   
               </Button>
            </a>                       
            <Badge variant="secondary" className="mb-4">{entry.data.category}</Badge>
            <h1 class="text-4xl font-bold mb-4">
              {entry.data.title}
            </h1>
            
            <div class="flex items-center gap-6 text-muted-foreground mb-8">
              <div class="flex items-center gap-2">
                <User className="w-4 h-4" />
                <span>{entry.data.author}</span>
              </div>
              <div class="flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                <span>{formattedDate}</span>
              </div>
              <div class="flex items-center gap-2">
                <Clock className="w-4 h-4" />
                <span>{entry.data.readingTime}</span>
              </div>
            </div>
          </div>
          <article class="prose prose-lg max-w-none markdown-content">
            <div>
              <Fragment set:html={content} />
            </div>
          </article>
          </div>
        </div>
      </main>
</Layout>