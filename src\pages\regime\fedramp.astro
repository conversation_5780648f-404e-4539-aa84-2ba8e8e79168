---
import Layout from "../../layouts/Layout.astro";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Cloud, Flag, Building, Clock, CheckCircle, ArrowRight } from "lucide-react";

const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Regime',"aria-disabled": true ,
  },
  {
    index: "2", text: "FedRAMP","aria-disabled": true 
  },
];
---

<Layout title="Opsfolio - Compliance as a Service | SOC2, HIPAA, ISO Certification"
        description="Get SOC2, HIPAA, ISO certified fast with expert-guided compliance. 98% audit pass rate, 60% faster than DIY. Your Compliance System of Record."
        keywords="agency authorization (ATO), JAB provisional authorization, CSP supplied self-assessment, FedRAMP impact levels, cloud security compliance, federal market access"
        url="/regime/fedramp/"
        breadcrumbLinks={breadcrumbLinks}>
      {/* Hero Section */}
      <section class="relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5 pt-16 pb-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center">
            <Badge variant="outline" className="mb-4">
              FedRAMP Authorization
            </Badge>
            <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
              Authorize Your Cloud for Government
            </h1>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Achieve FedRAMP authorization to sell cloud services to federal agencies. Navigate the complex 
              authorization process with expert guidance and proven methodologies.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
              
                <a href="/get-started">
                  <Button size="lg" >
                  Start FedRAMP Assessment
                  <ArrowRight className="ml-2 h-4 w-4" />
                 </Button>
                </a>

                <a href="/contact">
                  <Button variant="outline" size="lg" >Talk to FedRAMP Expert
                  </Button>
                </a>
            </div>
          </div>
        </div>
      </section>

      {/* FedRAMP Paths */}
      <section class="py-16 sm:py-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl lg:text-center">
            <h2 class="text-base font-semibold leading-7 text-primary">Authorization Paths</h2>
            <p class="mt-2 text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Choose Your FedRAMP Path
            </p>
          </div>
          <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <dl class="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
              <div class="flex flex-col">
                <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                  <Flag className="h-5 w-5 flex-none text-primary" />
                  Agency Authorization (ATO)
                </dt>
                <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p class="flex-auto">
                    Work directly with a federal agency sponsor. Faster path to market with 
                    agency-specific authorization that can be leveraged by other agencies.
                  </p>
                </dd>
              </div>
              <div class="flex flex-col">
                <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                  <Cloud className="h-5 w-5 flex-none text-primary" />
                  JAB Provisional Authorization
                </dt>
                <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p class="flex-auto">
                    Joint Authorization Board review for broad government adoption. 
                    Most comprehensive but provides widest market access.
                  </p>
                </dd>
              </div>
              <div class="flex flex-col">
                <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                  <Building className="h-5 w-5 flex-none text-primary" />
                  CSP Supplied (Self-Assessment)
                </dt>
                <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p class="flex-auto">
                    Independent security assessment to demonstrate FedRAMP readiness. 
                    Prepares for agency or JAB authorization paths.
                  </p>
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </section>

      {/* Impact Levels */}
      <section class="py-16 sm:py-20 bg-muted/50">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl lg:text-center">
            <h2 class="text-base font-semibold leading-7 text-primary">Security Impact Levels</h2>
            <p class="mt-2 text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              FISMA Impact Levels
            </p>
          </div>
          <div class="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-6 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-3 lg:gap-8">
            {[
              {
                level: "Low Impact",
                controls: "325+ Controls",
                description: "Information systems with Low impact level for confidentiality, integrity, and availability."
              },
              {
                level: "Moderate Impact", 
                controls: "400+ Controls",
                description: "Information systems with Moderate impact level requiring enhanced security controls."
              },
              {
                level: "High Impact",
                controls: "450+ Controls", 
                description: "Information systems with High impact level requiring the most stringent security controls."
              }
            ].map((item) => (
              <div class="flex flex-col items-start">
                <div class="rounded-md bg-primary/10 p-2 ring-1 ring-primary/20">
                  <CheckCircle className="h-6 w-6 text-primary" />
                </div>
                <dt class="mt-4 font-semibold text-foreground">{item.level}</dt>
                <dd class="mt-2 text-sm font-medium text-primary">{item.controls}</dd>
                <dd class="mt-2 leading-7 text-muted-foreground text-sm">{item.description}</dd>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Implementation Timeline */}
      <section class="py-16 sm:py-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl lg:text-center">
            <h2 class="text-base font-semibold leading-7 text-primary">Implementation</h2>
            <p class="mt-2 text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              FedRAMP Authorization in 12-18 Months
            </p>
          </div>
          <div class="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-6 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-4 lg:gap-8">
            {[
              {
                phase: "Months 1-3",
                title: "Readiness Assessment",
                description: "Gap analysis, architecture review, and FedRAMP path determination."
              },
              {
                phase: "Months 4-9",
                title: "Control Implementation",
                description: "Deploy security controls, create SSP, and implement continuous monitoring."
              },
              {
                phase: "Months 10-15",
                title: "Assessment & Testing",
                description: "Independent assessment, penetration testing, and vulnerability scanning."
              },
              {
                phase: "Months 16-18",
                title: "Authorization Process",
                description: "Agency or JAB review, POA&M remediation, and final authorization."
              }
            ].map((phase) => (
              <div class="flex flex-col items-start">
                <div class="rounded-md bg-primary/10 p-2 ring-1 ring-primary/20">
                  <Clock className="h-6 w-6 text-primary" />
                </div>
                <dt class="mt-4 font-semibold text-foreground">{phase.phase}</dt>
                <dd class="mt-2 text-sm font-medium text-primary">{phase.title}</dd>
                <dd class="mt-2 leading-7 text-muted-foreground text-sm">{phase.description}</dd>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section class="py-16 sm:py-20 bg-muted">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Ready to Enter the Federal Market?
            </h2>
            <p class="mx-auto mt-6 max-w-xl text-lg leading-8 text-muted-foreground">
              Get FedRAMP authorized and unlock billion-dollar federal cloud opportunities.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
              
              <a href="/get-started">
                <Button size="lg" >Start FedRAMP Assessment
                </Button>
              </a>
              <a href="/resources">
                <Button variant="outline" size="lg" >Download FedRAMP Guide
                </Button>
              </a>
            </div>
          </div>
        </div>
      </section>

</Layout>
