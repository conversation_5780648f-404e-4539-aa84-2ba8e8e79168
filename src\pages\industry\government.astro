---
import Layout from "../../layouts/Layout.astro";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Flag, ArrowRight, Shield, Building } from "lucide-react";

 const programs = [
    {
      icon: Flag,
      title: "Defense Industrial Base",
      description: "CMMC 2.0 compliance for defense contractors handling Controlled Unclassified Information (CUI).",
      requirements: ["CMMC Level 1-3", "NIST SP 800-171", "DFARS", "CUI Protection"]
    },
    {
      icon: Shield,
      title: "Federal Cloud Services",
      description: "FedRAMP authorization for cloud service providers serving federal agencies.",
      requirements: ["FedRAMP Low/Moderate/High", "NIST SP 800-53", "Continuous Monitoring", "3PAO Assessment"]
    },
    {
      icon: Building,
      title: "Federal Information Systems", 
      description: "FISMA compliance for federal agencies and information systems.",
      requirements: ["NIST RMF", "SP 800-53 Controls", "ATO Process", "Security Categorization"]
    }
  ];

const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Industry',"aria-disabled": true ,
  },
  {
    index: "1", text: "Government Contractors","aria-disabled": true ,
  },
];
---

<Layout  breadcrumbLinks={breadcrumbLinks}>
      <section class="relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5 pt-16 pb-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-3xl text-center">
            <Badge variant="outline" className="mb-4">Government Sector Compliance</Badge>
            <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
              Mission-Critical Compliance
            </h1>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Advanced compliance platform for government contractors and federal agencies. 
              Navigate complex security requirements while supporting national security missions.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
              <a href="/get-started">
              <Button size="lg" variant="default">
                Get Government Readiness Assessment<ArrowRight className="ml-2 h-4 w-4" />
              </Button>
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Programs Section */}
      <section class="py-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center mb-16">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Government Compliance Programs
            </h2>
            <p class="mt-4 text-lg text-muted-foreground">
              Specialized expertise across all major government compliance frameworks
            </p>
          </div>
          
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {programs.map((program, index) => (
              <Card key={index} className="border-border/50 hover:border-primary/50 transition-colors h-full">
                <CardHeader>
                  <program.icon className="h-8 w-8 text-primary mb-2" />
                  <CardTitle className="text-xl">{program.title}</CardTitle>
                  <CardDescription className="text-base">
                    {program.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul class="space-y-2">
                    {program.requirements.map((req) => (
                      <li class="flex items-center gap-2 text-sm">
                        <div class="w-1.5 h-1.5 bg-primary rounded-full flex-shrink-0" />
                        {req}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
</Layout>
