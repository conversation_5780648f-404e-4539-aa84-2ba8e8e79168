# 📑 Spec: Opsfolio Content Audit Tool (v1)

## **Purpose**

A lightweight AI-powered audit tool that evaluates website content at the **page** and **site** levels.

* **Page audit:** Runs a set of modular prompts (value prop, SEO, UX, etc.), synthesizes results into an executive summary.
* **Site audit:** Aggregates all page-level summaries into a sitewide executive summary.
* **Configurable:** Allows insertion of additional audit prompts (e.g., Trust Audit, Legal SEO tilt).
* **Flexible Input:** Can run on single page or entire site directory.

---

## **Inputs**

* **Scope**:

  * `Single Page` (audits only that page, outputs page report).
  * `Site Directory / Sitemap` (discovers all pages, runs per-page audits, then generates sitewide summary).

* **Page Type**: Required parameter for each page (`Home`, `Landing Page`, `Blog`, `Documentation`, etc.).

* **Audit Prompt Modules**:

  * Core set (always run):

    * `value-proposition.prompt`
    * `compliance.prompt`
    * `seo-audit.prompt`
    * `copywriting.prompt`
    * `ctas-conversions.prompt`
    * `ux.prompt`
    * `technical.prompt`
  * Configurable extensions (engineers must allow arbitrary plug-in prompts, e.g. `trust-audit.prompt`, `legal-seo.prompt`).

---

## **Process Workflow**

1. **Content Capture**

   * **Input = Site directory or single page.**
   * **UX requirement**: Capture must preserve page appearance (layout, visuals). Engineers can decide best method (e.g., PDF export, screenshot + OCR, rendered DOM capture). This ensures the UX prompt has meaningful context.

2. **Page-Level Audit**

   * For each page: run all audit prompt modules (core + configurable).
   * Pass `[Page Type]` parameter into every prompt.

3. **Page-Level Executive Summary**

   * Feed outputs of all page-level audits into `executive-summary-page.prompt`.
   * Produces synthesis: strengths, weaknesses, risks, recommendations, narrative.

4. **Site-Level Executive Summary**

   * If scope = site directory: collect all page summaries.
   * Feed into `executive-summary-site.prompt`.
   * Produces top-level synthesis: recurring strengths, recurring weaknesses, strategic risks, sitewide recommendations, executive narrative.

---

## **Outputs**

* **Single Page Audit**

  * All audit prompt outputs.
  * One executive summary report.

* **Sitewide Audit**

  * All audit prompt outputs per page.
  * Executive summary per page.
  * One sitewide executive summary report.

* **Output Formats**

  * Default: Markdown (easy human-readable, versionable).
  * Optional: JSON (structured data for downstream use).
  * Optional: PDF/HTML for presentation.

---

## **Configurable Parameters**

* **Scope**: Single Page vs Site Directory.
* **Page Type**: Must be supplied.
* **Audit Modules**: Core set + ability to add/remove configurable modules.
* **Output Format**: Markdown vs JSON vs PDF/HTML.

---

## **Implementation Notes**

* **Pipeline:**

  * `Input (site directory/page)` → `Content capture (appearance + text)` → `Run audit modules` → `Page exec summary` → `Site exec summary (if applicable)` → `Final report`.

* **Content Capture:**

  * Must allow ChatGPT to “see” visual UX, not only raw text. Engineers should decide between DOM-based capture, screenshot/PDF, or hybrid.

* **Extensibility:**

  * Any new audit prompt must fit into the same I/O format.
  * System should be designed modularly (prompt registry).

* **Lightweight:**

  * Minimal required input: `site directory` + `page type(s)`.
  * Output: Human-readable report.

* **Error Handling:**

  * If a page fails capture, skip and flag in site summary.