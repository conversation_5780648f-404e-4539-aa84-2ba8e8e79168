import * as rt from "./r4q-runtime.ts";
/**
 * @file media-protection-protect-information-on-digital-and-non-digital-media.auto.ts
 * @generated This file was auto-generated from the FHIR R4 Questionnaire "Media Protection (Protect information on digital and non-digital media)".
 * Do not edit this file manually; re-run the generator if the source Questionnaire changes.
 * Profiles: http://hl7.org/fhir/4.0/StructureDefinition/Questionnaire
 *
 * Normalizes LHC JSON and FHIR QuestionnaireResponse into the type-safe `MediaProtectionProtectInformationOnDigitalAndNonDigitalMedia` interface.
 */

// this is the module signature, used by importers to identify the module
// using r4q-runtime.ts `moduleSignature` function
export const mediaProtectionProtectInformationOnDigitalAndNonDigitalMediaModuleSignature: rt.ModuleSignature = {
  title: "Media Protection (Protect information on digital and non-digital media)",
  filename: "media-protection-protect-information-on-digital-and-non-digital-media.auto.ts",
  titleCamel: "`mediaProtectionProtectInformationOnDigitalAndNonDigitalMedia`",
  titlePascal: "`MediaProtectionProtectInformationOnDigitalAndNonDigitalMedia`",
  titleKebab: "`media-protection-protect-information-on-digital-and-non-digital-media`",
  lhcFormResponseAdapterFnName: "mediaProtectionProtectInformationOnDigitalAndNonDigitalMediaLhcFormResponseAdapter",
  fhirQuestionnaireResponseAdapterFnName: "mediaProtectionProtectInformationOnDigitalAndNonDigitalMediaFhirQuestionnaireResponseAdapter",
  sourceTextConstName: "mediaProtectionProtectInformationOnDigitalAndNonDigitalMediaSource",
}

// deno-lint-ignore no-explicit-any
type Any = any;
/**
 * Form Help (from display/help controls):
 * - Define and document policies for handling, storing, and disposing of media to prevent unauthorized access and data loss.
 * - Practice: Sanitize or destroy information system media containing Federal Contract Information before disposal or release for reuse
 */
/** Map of normalized property names to their source `linkId`. */
export const mediaProtectionProtectInformationOnDigitalAndNonDigitalMediaLinkIds = {
  doYouHaveAMediaDisposalPolicy: "957584520694",
  notesEvidence: "256250807567",
  implementationStatus: "272642906092",
  notesEvidence2: "686200078306",
  confirmThatYourMediaDisposalPolicyIncludesTheFollowingElementsClickAllThatApply: "698818405059",
  notesEvidence3: "806966265807"
} as const;

/** Normalized view of "Media Protection (Protect information on digital and non-digital media)" answers. */
export interface MediaProtectionProtectInformationOnDigitalAndNonDigitalMedia {
  /**
   * Do you have a Media Disposal Policy?
   * linkId: 957584520694
   * FHIR type: choice
   * Section: MP.L1-B.1.VII - MEDIA PROTECTION (MP) - 1 PRACTICE
   * Options: "Yes", "No"
   * Required: no
   */
  doYouHaveAMediaDisposalPolicy?: "Yes" | "No";

  /**
   * Notes / Evidence
   * linkId: 256250807567
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: MP.L1-B.1.VII - MEDIA PROTECTION (MP) - 1 PRACTICE
   * Required: no
   */
  notesEvidence?: string;

  /**
   * Implementation Status
   * linkId: 272642906092
   * FHIR type: choice
   * Section: MP.L1-B.1.VII - MEDIA PROTECTION (MP) - 1 PRACTICE
   * Options: "Fully Implemented", "Partially Implemented", " Not Implemented"
   * Required: no
   */
  implementationStatus?: "Fully Implemented" | "Partially Implemented" | " Not Implemented";

  /**
   * Notes / Evidence
   * linkId: 686200078306
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: MP.L1-B.1.VII - MEDIA PROTECTION (MP) - 1 PRACTICE
   * Required: no
   */
  notesEvidence2?: string;

  /**
   * Confirm that your media disposal policy includes the following elements (click all that apply):
   * linkId: 698818405059
   * FHIR type: choice
   * Section: MP.L1-B.1.VII - MEDIA PROTECTION (MP) - 1 PRACTICE
   * Options: "Types of media covered by policy (Policy defines all types of media that may contain FCI (hard drives, SSDs, USB drives, etc.))", "Identification methods for FCI-containing media (Procedures for identifying media that contains or may contain FCI)", "Sanitization methods by media type (Specific sanitization methods appropriate for each media type)", "Destruction methods by media type (Specific destruction methods appropriate for each media type)", "Verification requirements (Procedures to verify sanitization or destruction was successful)", "Documentation requirements (Required records of sanitization and destruction activities)", "Roles and responsibilities (Designation of who is responsible for each aspect of media disposal)", "Compliance with relevant standards (References to NIST SP 800-88 or other applicable standards)"
   * Required: no
   */
  confirmThatYourMediaDisposalPolicyIncludesTheFollowingElementsClickAllThatApply?: ("Types of media covered by policy (Policy defines all types of media that may contain FCI (hard drives, SSDs, USB drives, etc.))" | "Identification methods for FCI-containing media (Procedures for identifying media that contains or may contain FCI)" | "Sanitization methods by media type (Specific sanitization methods appropriate for each media type)" | "Destruction methods by media type (Specific destruction methods appropriate for each media type)" | "Verification requirements (Procedures to verify sanitization or destruction was successful)" | "Documentation requirements (Required records of sanitization and destruction activities)" | "Roles and responsibilities (Designation of who is responsible for each aspect of media disposal)" | "Compliance with relevant standards (References to NIST SP 800-88 or other applicable standards)")[];

  /**
   * Notes / Evidence
   * linkId: 806966265807
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: MP.L1-B.1.VII - MEDIA PROTECTION (MP) - 1 PRACTICE
   * Required: no
   */
  notesEvidence3?: string;
}

/** Convert an LHC JSON response into a normalized MediaProtectionProtectInformationOnDigitalAndNonDigitalMedia object. */
export function mediaProtectionProtectInformationOnDigitalAndNonDigitalMediaLhcFormResponseAdapter(input: Any): MediaProtectionProtectInformationOnDigitalAndNonDigitalMedia {
  return {
    doYouHaveAMediaDisposalPolicy: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "957584520694")) as MediaProtectionProtectInformationOnDigitalAndNonDigitalMedia["doYouHaveAMediaDisposalPolicy"],
    notesEvidence: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "256250807567")),
    implementationStatus: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "272642906092")) as MediaProtectionProtectInformationOnDigitalAndNonDigitalMedia["implementationStatus"],
    notesEvidence2: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "686200078306")),
    confirmThatYourMediaDisposalPolicyIncludesTheFollowingElementsClickAllThatApply: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "698818405059")) as MediaProtectionProtectInformationOnDigitalAndNonDigitalMedia["confirmThatYourMediaDisposalPolicyIncludesTheFollowingElementsClickAllThatApply"],
    notesEvidence3: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "806966265807")),
  };
}

/** Convert a FHIR QuestionnaireResponse into a normalized MediaProtectionProtectInformationOnDigitalAndNonDigitalMedia object. */
export function mediaProtectionProtectInformationOnDigitalAndNonDigitalMediaFhirQuestionnaireResponseAdapter(qr: Any): MediaProtectionProtectInformationOnDigitalAndNonDigitalMedia {
  return {
    doYouHaveAMediaDisposalPolicy: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "957584520694")) as MediaProtectionProtectInformationOnDigitalAndNonDigitalMedia["doYouHaveAMediaDisposalPolicy"],
    notesEvidence: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "256250807567")),
    implementationStatus: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "272642906092")) as MediaProtectionProtectInformationOnDigitalAndNonDigitalMedia["implementationStatus"],
    notesEvidence2: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "686200078306")),
    confirmThatYourMediaDisposalPolicyIncludesTheFollowingElementsClickAllThatApply: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "698818405059")) as MediaProtectionProtectInformationOnDigitalAndNonDigitalMedia["confirmThatYourMediaDisposalPolicyIncludesTheFollowingElementsClickAllThatApply"],
    notesEvidence3: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "806966265807")),
  };
}

/**
 * NOTE TO DEVELOPERS:
 * -------------------
 * This Interpreter class is provided only as an EXAMPLE scaffold to demonstrate
 * how to consume the normalized type-safe interface generated for this
 * Questionnaire. It shows minimal factories (`fromLhc`, `fromQuestionnaireResponse`)
 * and convenience methods (`validateRequiredFields`, `assessReadiness`) but it is
 * NOT intended for production use.
 *
 * In real applications:
 * - Treat this class as SAMPLE CODE only.
 * - Replace or extend it with proper business logic, rules engines, or validation
 *   frameworks appropriate to your domain.
 * - Do not rely on the simplistic readiness scoring or validation in production
 *   scenarios; they are illustrative, not authoritative.
 *
 * Best practice: use the generated TypeScript interface (`MediaProtectionProtectInformationOnDigitalAndNonDigitalMedia`) as your
 * contract for normalized data, then integrate with your own rules processors,
 * compliance engines, or plain TypeScript/JavaScript functions as needed.
 */
export class MediaProtectionProtectInformationOnDigitalAndNonDigitalMediaInterpreter {
  constructor(readonly value: MediaProtectionProtectInformationOnDigitalAndNonDigitalMedia) { }

  /** Factory: build from LHC JSON. */
  static fromLhcFormResponse(input: Any): MediaProtectionProtectInformationOnDigitalAndNonDigitalMediaInterpreter {
    return new MediaProtectionProtectInformationOnDigitalAndNonDigitalMediaInterpreter(mediaProtectionProtectInformationOnDigitalAndNonDigitalMediaLhcFormResponseAdapter(input));
  }

  /** Factory: build from FHIR QuestionnaireResponse. */
  static fromQuestionnaireResponse(qr: Any): MediaProtectionProtectInformationOnDigitalAndNonDigitalMediaInterpreter {
    return new MediaProtectionProtectInformationOnDigitalAndNonDigitalMediaInterpreter(mediaProtectionProtectInformationOnDigitalAndNonDigitalMediaFhirQuestionnaireResponseAdapter(qr));
  }

  /** Check required fields and report any missing or blank. */
  validateRequiredFields(): { ok: boolean; missing: Array<keyof MediaProtectionProtectInformationOnDigitalAndNonDigitalMedia> } {
    const missing: Array<keyof MediaProtectionProtectInformationOnDigitalAndNonDigitalMedia> = [];
    const req: Array<keyof MediaProtectionProtectInformationOnDigitalAndNonDigitalMedia> = [];
    for (const k of req) {
      const v = (this.value as Any)[k];
      if (rt.isBlank(v)) missing.push(k);
    }
    return { ok: missing.length === 0, missing };
  }

  /**
   * Assess readiness with a simple completeness score. This is meant to be
   * used to help understand how complete the types are and serves as an
   * example of how to use the generated code.
   * - requiredCovered: percentage of required fields that are non-blank
   * - overallFilled: count of non-blank fields among all known properties
   */
  assessReadiness(): {
    formTitle: string;
    requiredCovered: number; // 0..1
    totalRequired: number;
    totalFilled: number;
    totalFields: number;
    missingRequired: Array<keyof MediaProtectionProtectInformationOnDigitalAndNonDigitalMedia>;
  } {
    const req: Array<keyof MediaProtectionProtectInformationOnDigitalAndNonDigitalMedia> = [];
    const all: Array<keyof MediaProtectionProtectInformationOnDigitalAndNonDigitalMedia> = ["doYouHaveAMediaDisposalPolicy", "notesEvidence", "implementationStatus", "notesEvidence2", "confirmThatYourMediaDisposalPolicyIncludesTheFollowingElementsClickAllThatApply", "notesEvidence3"];

    let reqFilled = 0;
    const missingReq: Array<keyof MediaProtectionProtectInformationOnDigitalAndNonDigitalMedia> = [];
    for (const k of req) {
      const v = (this.value as Any)[k];
      if (!rt.isBlank(v)) reqFilled++;
      else missingReq.push(k);
    }

    let totalFilled = 0;
    for (const k of all) {
      if (!rt.isBlank((this.value as Any)[k])) totalFilled++;
    }

    return {
      formTitle: "Media Protection (Protect information on digital and non-digital media)",
      requiredCovered: req.length ? reqFilled / req.length : 1,
      totalRequired: req.length,
      totalFilled,
      totalFields: all.length,
      missingRequired: missingReq,
    };
  }
}

/** The original source */
export const mediaProtectionProtectInformationOnDigitalAndNonDigitalMediaSource = `{
  "resourceType": "Questionnaire",
  "meta": {
    "profile": [
      "http://hl7.org/fhir/4.0/StructureDefinition/Questionnaire"
    ],
    "tag": [
      {
        "display": "Media Security"
      },
      {
        "display": "Data Protection"
      },
      {
        "display": "Digital Media"
      },
      {
        "display": "Physical Media"
      },
      {
        "display": "Information Security"
      },
      {
        "display": "Confidentiality"
      },
      {
        "display": "Compliance"
      },
      {
        "display": "Risk Management"
      }
    ]
  },
  "extension": [
    {
      "url": "http://hl7.org/fhir/StructureDefinition/variable",
      "valueExpression": {
        "name": "displayOrder",
        "language": "text/fhirpath",
        "expression": "4",
        "extension": [
          {
            "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
            "valueString": "simple"
          },
          {
            "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
            "valueString": "4"
          }
        ]
      }
    }
  ],
  "name": "media_protection",
  "title": "Media Protection (Protect information on digital and non-digital media)",
  "status": "draft",
  "date": "2025-08-26",
  "publisher": "Netspective",
  "description": "Protect information on digital and non-digital media",
  "purpose": "This assessment covers the protection, sanitization, and disposal of information system media. It ensures that any physical or digital media containing FCI is either destroyed or sanitized prior to disposal or reuse. The goal is to prevent unauthorized recovery of sensitive data from decommissioned hardware, removable media, or printed materials.",
  "approvalDate": "2025-08-26",
  "lastReviewDate": "2025-08-26",
  "item": [
    {
      "item": [
        {
          "type": "choice",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/variable",
              "valueExpression": {
                "name": "weight",
                "language": "text/fhirpath",
                "expression": "1",
                "extension": [
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                    "valueString": "simple"
                  },
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                    "valueString": "1"
                  }
                ]
              }
            },
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "radio-button",
                    "display": "Radio Button"
                  }
                ]
              }
            }
          ],
          "linkId": "957584520694",
          "text": "Do you have a Media Disposal Policy?",
          "repeats": false,
          "answerOption": [
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 100
                }
              ],
              "valueCoding": {
                "display": "Yes"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 0
                }
              ],
              "valueCoding": {
                "display": "No"
              }
            }
          ]
        },
        {
          "type": "string",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
              "valueString": "Type your comments here..."
            }
          ],
          "linkId": "256250807567",
          "text": "Notes / Evidence"
        },
        {
          "type": "choice",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/variable",
              "valueExpression": {
                "name": "weight",
                "language": "text/fhirpath",
                "expression": "1",
                "extension": [
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                    "valueString": "simple"
                  },
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                    "valueString": "1"
                  }
                ]
              }
            },
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "radio-button",
                    "display": "Radio Button"
                  }
                ]
              }
            }
          ],
          "linkId": "272642906092",
          "text": "Implementation Status",
          "repeats": false,
          "answerOption": [
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 100
                }
              ],
              "valueCoding": {
                "display": "Fully Implemented"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 50
                }
              ],
              "valueCoding": {
                "display": "Partially Implemented"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 0
                }
              ],
              "valueCoding": {
                "display": " Not Implemented"
              }
            }
          ]
        },
        {
          "type": "string",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
              "valueString": "Type your comments here..."
            }
          ],
          "linkId": "686200078306",
          "text": "Notes / Evidence"
        },
        {
          "type": "group",
          "linkId": "393852162334",
          "prefix": "1.",
          "text": "Policy Elements",
          "item": [
            {
              "linkId": "393852162334_helpText",
              "type": "display",
              "text": "Define and document policies for handling, storing, and disposing of media to prevent unauthorized access and data loss.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ]
        },
        {
          "type": "choice",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/variable",
              "valueExpression": {
                "name": "weight",
                "language": "text/fhirpath",
                "expression": "1",
                "extension": [
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                    "valueString": "simple"
                  },
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                    "valueString": "1"
                  }
                ]
              }
            },
            {
              "url": "http://hl7.org/fhir/StructureDefinition/variable",
              "valueExpression": {
                "name": "maxScore",
                "language": "text/fhirpath",
                "expression": "100",
                "extension": [
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                    "valueString": "simple"
                  },
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                    "valueString": "100"
                  }
                ]
              }
            },
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "check-box",
                    "display": "Check-box"
                  }
                ]
              }
            }
          ],
          "linkId": "698818405059",
          "text": "Confirm that your media disposal policy includes the following elements (click all that apply):",
          "repeats": true,
          "answerOption": [
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 12.5
                }
              ],
              "valueCoding": {
                "display": "Types of media covered by policy (Policy defines all types of media that may contain FCI (hard drives, SSDs, USB drives, etc.))"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 12.5
                }
              ],
              "valueCoding": {
                "display": "Identification methods for FCI-containing media (Procedures for identifying media that contains or may contain FCI)"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 12.5
                }
              ],
              "valueCoding": {
                "display": "Sanitization methods by media type (Specific sanitization methods appropriate for each media type)"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 12.5
                }
              ],
              "valueCoding": {
                "display": "Destruction methods by media type (Specific destruction methods appropriate for each media type)"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 12.5
                }
              ],
              "valueCoding": {
                "display": "Verification requirements (Procedures to verify sanitization or destruction was successful)"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 12.5
                }
              ],
              "valueCoding": {
                "display": "Documentation requirements (Required records of sanitization and destruction activities)"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 12.5
                }
              ],
              "valueCoding": {
                "display": "Roles and responsibilities (Designation of who is responsible for each aspect of media disposal)"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 12.5
                }
              ],
              "valueCoding": {
                "display": "Compliance with relevant standards (References to NIST SP 800-88 or other applicable standards)"
              }
            }
          ]
        },
        {
          "type": "string",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
              "valueString": "Type your comments here..."
            }
          ],
          "linkId": "806966265807",
          "text": "Notes / Evidence"
        },
        {
          "linkId": "609511072752_helpText",
          "type": "display",
          "text": "Practice: Sanitize or destroy information system media containing Federal Contract Information before disposal or release for reuse",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "help",
                    "display": "Help-Button"
                  }
                ],
                "text": "Help-Button"
              }
            }
          ]
        }
      ],
      "type": "group",
      "linkId": "609511072752",
      "text": "MP.L1-B.1.VII - MEDIA PROTECTION (MP) - 1 PRACTICE"
    }
  ]
}`;