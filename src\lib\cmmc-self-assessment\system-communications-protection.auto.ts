import * as rt from "./r4q-runtime.ts";
/**
 * @file system-communications-protection.auto.ts
 * @generated This file was auto-generated from the FHIR R4 Questionnaire "System & Communications Protection".
 * Do not edit this file manually; re-run the generator if the source Questionnaire changes.
 * Profiles: http://hl7.org/fhir/4.0/StructureDefinition/Questionnaire
 *
 * Normalizes LHC JSON and FHIR QuestionnaireResponse into the type-safe `SystemCommunicationsProtection` interface.
 */

// this is the module signature, used by importers to identify the module
// using r4q-runtime.ts `moduleSignature` function
export const systemCommunicationsProtectionModuleSignature: rt.ModuleSignature = {
  title: "System & Communications Protection",
  filename: "system-communications-protection.auto.ts",
  titleCamel: "`systemCommunicationsProtection`",
  titlePascal: "`SystemCommunicationsProtection`",
  titleKebab: "`system-communications-protection`",
  lhcFormResponseAdapterFnName: "systemCommunicationsProtectionLhcFormResponseAdapter",
  fhirQuestionnaireResponseAdapterFnName: "systemCommunicationsProtectionFhirQuestionnaireResponseAdapter",
  sourceTextConstName: "systemCommunicationsProtectionSource",
}

// deno-lint-ignore no-explicit-any
type Any = any;
/**
 * Form Help (from display/help controls):
 * - Clearly define the limits of your information systems to identify what needs protection and control.
 * - Set up and manage firewalls to control and monitor incoming and outgoing network traffic based on security rules.
 * - Establish continuous monitoring to detect and respond to security events within systems and communications.
 * - Monitor, control, and protect organizational communications (i.e., information transmitted or received by organizational information systems) at the external boundaries and key internal boundaries of the information systems.
 * - Identify and manage system components like web servers, email servers, and public applications that are accessible to external users
 * - Implement measures to separate and segment networks to limit unauthorized access and contain potential security breaches.
 * - Control and restrict communication between networks to prevent unauthorized access and data transfer.
 * - Continuously monitor demilitarized zones (DMZ) and public networks to detect and respond to potential security threats.
 * - Implement subnetworks for publicly accessible system components that are physically or logically separated from internal organizational networks.
 */
/** Map of normalized property names to their source `linkId`. */
export const systemCommunicationsProtectionLinkIds = {
  requirementsImplementNetworkMonitoringAndBoundaryProtectionIncludingFirewallsIntrusionDetectionAndCommunicationControls: "998940326541",
  implementationStatus: "954433842901",
  notesEvidence: "494412529555",
  doYouHaveANetworkDiagramShowingSystemBoundariesKeyComponentsAndDataFlows: "979372224491",
  notesEvidence2: "353406656082",
  externalBoundaryComponents: "591770672887",
  keyInternalBoundaryComponents: "930792706809",
  firewallManufacturerModel: "843201435494",
  firewallSoftwareFirmwareVersion: "706452201694",
  defaultDenyPolicyIsImplementedTrafficIsDeniedByDefaultUnlessExplicitlyPermitted: "949755108024",
  typeYourCommentsHere: "156526970162",
  explicitlyAllowedServicesProtocols: "963088071424",
  explicitlyDeniedServicesProtocols: "122305830447",
  howDoYouMonitorCommunicationsAtSystemBoundaries: "847131102373",
  notesEvidence3: "305967020301",
  supportingDocumentation: "794317413983",
  additionalNotes: "782731881405",
  requirementsCreateDmzOrSeparatedNetworkSegmentsForPublicFacingSystemsToIsolateThemFromInternalNetworks: "556770566326",
  implementationStatus2: "274150359667",
  notesEvidence4: "496638290461",
  whatPubliclyAccessibleSystemComponentsDoesYourOrganizationOperate: "956471776047",
  notesEvidence5: "272791116387",
  howArePubliclyAccessibleSystemsSeparatedFromInternalNetworks: "517448335213",
  notesEvidence6: "299978179191",
  whatControlsPreventUnauthorizedAccessFromPublicNetworksToInternalNetworks: "634425708590",
  notesEvidence7: "845777456178",
  howDoYouMonitorActivityInYourPublicFacingNetworkSegments: "536378863536",
  notesEvidence8: "122899280845",
  supportingDocumentation2: "980001173858",
  additionalNotes2: "597392284230"
} as const;

/** Normalized view of "System & Communications Protection" answers. */
export interface SystemCommunicationsProtection {
  /**
   * Requirements: Implement network monitoring and boundary protection including firewalls, intrusion detection, and communication controls.
   * linkId: 998940326541
   * FHIR type: display
   * Section: SC.L1-B.1.X – Boundary Protection
   * Required: no
   */
  requirementsImplementNetworkMonitoringAndBoundaryProtectionIncludingFirewallsIntrusionDetectionAndCommunicationControls?: string;

  /**
   * Implementation Status 
   * linkId: 954433842901
   * FHIR type: string
   * Section: SC.L1-B.1.X – Boundary Protection
   * Required: yes
   */
  implementationStatus: string;

  /**
   * Notes / Evidence 
   * linkId: 494412529555
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: SC.L1-B.1.X – Boundary Protection
   * Required: no
   */
  notesEvidence?: string;

  /**
   * Do you have a network diagram showing system boundaries, key components, and data flows?
   * linkId: 979372224491
   * FHIR type: string
   * Section: SC.L1-B.1.X – Boundary Protection
   * Required: no
   */
  doYouHaveANetworkDiagramShowingSystemBoundariesKeyComponentsAndDataFlows?: string;

  /**
   * Notes / Evidence 
   * linkId: 353406656082
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: SC.L1-B.1.X – Boundary Protection
   * Required: no
   */
  notesEvidence2?: string;

  /**
   * External Boundary Components
   * linkId: 591770672887
   * FHIR type: text
   * Entry format: Include hardware models, firmware versions and their primary functions.  e.g., Cisco ASA 5515-X v9.12.3 - Primary perimeter firewall, Palo Alto PA-220 v10.1.2 - Branch office firewall.
   * Section: SC.L1-B.1.X – Boundary Protection > 1. System Boundary Definition
   * Required: no
   */
  externalBoundaryComponents?: string;

  /**
   * Key Internal Boundary Components
   * linkId: 930792706809
   * FHIR type: text
   * Entry format: Include components that separate development, test and production environments or create internal segmentation. eg., Internal VLAN switches, host-based firewalls,  Network ACLs.
   * Section: SC.L1-B.1.X – Boundary Protection > 1. System Boundary Definition
   * Required: no
   */
  keyInternalBoundaryComponents?: string;

  /**
   * Firewall Manufacturer/Model
   * linkId: 843201435494
   * FHIR type: text
   * Entry format: e.g., Cisco ASA 5500, Palo Alto PA-220
   * Section: SC.L1-B.1.X – Boundary Protection > 2. Firewall Configuration
   * Required: no
   */
  firewallManufacturerModel?: string;

  /**
   * Firewall Software/Firmware Version
   * linkId: 706452201694
   * FHIR type: text
   * Entry format: e.g., v9.12.3
   * Section: SC.L1-B.1.X – Boundary Protection > 2. Firewall Configuration
   * Required: no
   */
  firewallSoftwareFirmwareVersion?: string;

  /**
   * Default deny policy is implemented (traffic is denied by default unless explicitly permitted)
   * linkId: 949755108024
   * FHIR type: choice
   * Section: SC.L1-B.1.X – Boundary Protection > 2. Firewall Configuration
   * Options: "Yes", "No"
   * Required: no
   */
  defaultDenyPolicyIsImplementedTrafficIsDeniedByDefaultUnlessExplicitlyPermitted?: "Yes" | "No";

  /**
   * Type your comments here...
   * linkId: 156526970162
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: SC.L1-B.1.X – Boundary Protection > 2. Firewall Configuration
   * Required: no
   */
  typeYourCommentsHere?: string;

  /**
   * Explicitly Allowed Services/Protocols
   * linkId: 963088071424
   * FHIR type: text
   * Entry format: e.g., HTTPS (TCP/443), SSH (TCP/22), DNS (UDP/53)
   * Section: SC.L1-B.1.X – Boundary Protection > 2. Firewall Configuration
   * Required: no
   */
  explicitlyAllowedServicesProtocols?: string;

  /**
   * Explicitly Denied Services/Protocols
   * linkId: 122305830447
   * FHIR type: text
   * Entry format: e.g., Telnet (TCP/23), FTP (TCP/21), HTTP (TCP/80)
   * Section: SC.L1-B.1.X – Boundary Protection > 2. Firewall Configuration
   * Required: no
   */
  explicitlyDeniedServicesProtocols?: string;

  /**
   * How do you monitor communications at system boundaries?
   * linkId: 847131102373
   * FHIR type: choice
   * Section: SC.L1-B.1.X – Boundary Protection > 3. Monitoring Implementation
   * Options: "Firewall logs and analysis", "Intrusion detection/prevention systems", "Network monitoring tools", "SIEM system integration", "Manual log review"
   * Required: no
   */
  howDoYouMonitorCommunicationsAtSystemBoundaries?: ("Firewall logs and analysis" | "Intrusion detection/prevention systems" | "Network monitoring tools" | "SIEM system integration" | "Manual log review")[];

  /**
   * Notes / Evidence 
   * linkId: 305967020301
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: SC.L1-B.1.X – Boundary Protection > 3. Monitoring Implementation
   * Required: no
   */
  notesEvidence3?: string;

  /**
   * Supporting Documentation
   * linkId: 794317413983
   * FHIR type: text
   * Entry format: eg., Network Security Policy, v2.1, Firewall configuration documentation, Security monitoring procedures. 
   * Section: SC.L1-B.1.X – Boundary Protection
   * Required: no
   */
  supportingDocumentation?: string;

  /**
   * Additional Notes
   * linkId: 782731881405
   * FHIR type: text
   * Entry format: Any additional information, challenges or implementation notes
   * Section: SC.L1-B.1.X – Boundary Protection
   * Required: no
   */
  additionalNotes?: string;

  /**
   * Requirements: Create DMZ or separated network segments for public-facing systems to isolate them from internal networks.
   * linkId: 556770566326
   * FHIR type: display
   * Section: SC.L1-B.1.X – Implement subnetworks for publicly accessible components
   * Required: no
   */
  requirementsCreateDmzOrSeparatedNetworkSegmentsForPublicFacingSystemsToIsolateThemFromInternalNetworks?: string;

  /**
   * Implementation Status 
   * linkId: 274150359667
   * FHIR type: string
   * Section: SC.L1-B.1.X – Implement subnetworks for publicly accessible components
   * Required: yes
   */
  implementationStatus2: string;

  /**
   * Notes / Evidence 
   * linkId: 496638290461
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: SC.L1-B.1.X – Implement subnetworks for publicly accessible components
   * Required: no
   */
  notesEvidence4?: string;

  /**
   * What publicly accessible system components does your organization operate?
   * linkId: 956471776047
   * FHIR type: string
   * Section: SC.L1-B.1.X – Implement subnetworks for publicly accessible components > 1. Publicly Accessible System Components
   * Required: no
   */
  whatPubliclyAccessibleSystemComponentsDoesYourOrganizationOperate?: string[];

  /**
   * Notes / Evidence 
   * linkId: 272791116387
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: SC.L1-B.1.X – Implement subnetworks for publicly accessible components > 1. Publicly Accessible System Components
   * Required: no
   */
  notesEvidence5?: string;

  /**
   * How are publicly accessible systems separated from internal networks?
   * linkId: 517448335213
   * FHIR type: string
   * Section: SC.L1-B.1.X – Implement subnetworks for publicly accessible components > 2. Network Separation Implementation
   * Required: no
   */
  howArePubliclyAccessibleSystemsSeparatedFromInternalNetworks?: string[];

  /**
   * Notes / Evidence 
   * linkId: 299978179191
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: SC.L1-B.1.X – Implement subnetworks for publicly accessible components > 2. Network Separation Implementation
   * Required: no
   */
  notesEvidence6?: string;

  /**
   * What controls prevent unauthorized access from public networks to internal networks?
   * linkId: 634425708590
   * FHIR type: string
   * Section: SC.L1-B.1.X – Implement subnetworks for publicly accessible components > 3. Access Control Between Networks
   * Required: no
   */
  whatControlsPreventUnauthorizedAccessFromPublicNetworksToInternalNetworks?: string[];

  /**
   * Notes / Evidence 
   * linkId: 845777456178
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: SC.L1-B.1.X – Implement subnetworks for publicly accessible components > 3. Access Control Between Networks
   * Required: no
   */
  notesEvidence7?: string;

  /**
   * How do you monitor activity in your public-facing network segments?
   * linkId: 536378863536
   * FHIR type: string
   * Section: SC.L1-B.1.X – Implement subnetworks for publicly accessible components > 4. DMZ/Public Network Monitoring
   * Required: no
   */
  howDoYouMonitorActivityInYourPublicFacingNetworkSegments?: string[];

  /**
   * Notes / Evidence 
   * linkId: 122899280845
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: SC.L1-B.1.X – Implement subnetworks for publicly accessible components > 4. DMZ/Public Network Monitoring
   * Required: no
   */
  notesEvidence8?: string;

  /**
   * Supporting Documentation
   * linkId: 980001173858
   * FHIR type: text
   * Entry format: eg., Network Security Policy, v2.1, Firewall configuration documentation, Security monitoring procedures. 
   * Section: SC.L1-B.1.X – Implement subnetworks for publicly accessible components
   * Required: no
   */
  supportingDocumentation2?: string;

  /**
   * Additional Notes
   * linkId: 597392284230
   * FHIR type: text
   * Entry format: Any additional information, challenges or implementation notes
   * Section: SC.L1-B.1.X – Implement subnetworks for publicly accessible components
   * Required: no
   */
  additionalNotes2?: string;
}

/** Convert an LHC JSON response into a normalized SystemCommunicationsProtection object. */
export function systemCommunicationsProtectionLhcFormResponseAdapter(input: Any): SystemCommunicationsProtection {
  return {
    requirementsImplementNetworkMonitoringAndBoundaryProtectionIncludingFirewallsIntrusionDetectionAndCommunicationControls: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "998940326541")),
    implementationStatus: rt.coerceString(rt.findLhcValueByLinkId(input, "954433842901")),
    notesEvidence: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "494412529555")),
    doYouHaveANetworkDiagramShowingSystemBoundariesKeyComponentsAndDataFlows: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "979372224491")),
    notesEvidence2: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "353406656082")),
    externalBoundaryComponents: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "591770672887")),
    keyInternalBoundaryComponents: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "930792706809")),
    firewallManufacturerModel: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "843201435494")),
    firewallSoftwareFirmwareVersion: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "706452201694")),
    defaultDenyPolicyIsImplementedTrafficIsDeniedByDefaultUnlessExplicitlyPermitted: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "949755108024")) as SystemCommunicationsProtection["defaultDenyPolicyIsImplementedTrafficIsDeniedByDefaultUnlessExplicitlyPermitted"],
    typeYourCommentsHere: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "156526970162")),
    explicitlyAllowedServicesProtocols: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "963088071424")),
    explicitlyDeniedServicesProtocols: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "122305830447")),
    howDoYouMonitorCommunicationsAtSystemBoundaries: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "847131102373")) as SystemCommunicationsProtection["howDoYouMonitorCommunicationsAtSystemBoundaries"],
    notesEvidence3: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "305967020301")),
    supportingDocumentation: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "794317413983")),
    additionalNotes: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "782731881405")),
    requirementsCreateDmzOrSeparatedNetworkSegmentsForPublicFacingSystemsToIsolateThemFromInternalNetworks: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "556770566326")),
    implementationStatus2: rt.coerceString(rt.findLhcValueByLinkId(input, "274150359667")),
    notesEvidence4: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "496638290461")),
    whatPubliclyAccessibleSystemComponentsDoesYourOrganizationOperate: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "956471776047")) as SystemCommunicationsProtection["whatPubliclyAccessibleSystemComponentsDoesYourOrganizationOperate"],
    notesEvidence5: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "272791116387")),
    howArePubliclyAccessibleSystemsSeparatedFromInternalNetworks: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "517448335213")) as SystemCommunicationsProtection["howArePubliclyAccessibleSystemsSeparatedFromInternalNetworks"],
    notesEvidence6: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "299978179191")),
    whatControlsPreventUnauthorizedAccessFromPublicNetworksToInternalNetworks: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "634425708590")) as SystemCommunicationsProtection["whatControlsPreventUnauthorizedAccessFromPublicNetworksToInternalNetworks"],
    notesEvidence7: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "845777456178")),
    howDoYouMonitorActivityInYourPublicFacingNetworkSegments: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "536378863536")) as SystemCommunicationsProtection["howDoYouMonitorActivityInYourPublicFacingNetworkSegments"],
    notesEvidence8: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "122899280845")),
    supportingDocumentation2: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "980001173858")),
    additionalNotes2: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "597392284230")),
  };
}

/** Convert a FHIR QuestionnaireResponse into a normalized SystemCommunicationsProtection object. */
export function systemCommunicationsProtectionFhirQuestionnaireResponseAdapter(qr: Any): SystemCommunicationsProtection {
  return {
    requirementsImplementNetworkMonitoringAndBoundaryProtectionIncludingFirewallsIntrusionDetectionAndCommunicationControls: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "998940326541")),
    implementationStatus: rt.coerceString(rt.findQrAnswerByLinkId(qr, "954433842901")),
    notesEvidence: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "494412529555")),
    doYouHaveANetworkDiagramShowingSystemBoundariesKeyComponentsAndDataFlows: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "979372224491")),
    notesEvidence2: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "353406656082")),
    externalBoundaryComponents: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "591770672887")),
    keyInternalBoundaryComponents: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "930792706809")),
    firewallManufacturerModel: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "843201435494")),
    firewallSoftwareFirmwareVersion: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "706452201694")),
    defaultDenyPolicyIsImplementedTrafficIsDeniedByDefaultUnlessExplicitlyPermitted: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "949755108024")) as SystemCommunicationsProtection["defaultDenyPolicyIsImplementedTrafficIsDeniedByDefaultUnlessExplicitlyPermitted"],
    typeYourCommentsHere: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "156526970162")),
    explicitlyAllowedServicesProtocols: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "963088071424")),
    explicitlyDeniedServicesProtocols: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "122305830447")),
    howDoYouMonitorCommunicationsAtSystemBoundaries: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "847131102373")) as SystemCommunicationsProtection["howDoYouMonitorCommunicationsAtSystemBoundaries"],
    notesEvidence3: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "305967020301")),
    supportingDocumentation: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "794317413983")),
    additionalNotes: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "782731881405")),
    requirementsCreateDmzOrSeparatedNetworkSegmentsForPublicFacingSystemsToIsolateThemFromInternalNetworks: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "556770566326")),
    implementationStatus2: rt.coerceString(rt.findQrAnswerByLinkId(qr, "274150359667")),
    notesEvidence4: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "496638290461")),
    whatPubliclyAccessibleSystemComponentsDoesYourOrganizationOperate: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "956471776047")) as SystemCommunicationsProtection["whatPubliclyAccessibleSystemComponentsDoesYourOrganizationOperate"],
    notesEvidence5: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "272791116387")),
    howArePubliclyAccessibleSystemsSeparatedFromInternalNetworks: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "517448335213")) as SystemCommunicationsProtection["howArePubliclyAccessibleSystemsSeparatedFromInternalNetworks"],
    notesEvidence6: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "299978179191")),
    whatControlsPreventUnauthorizedAccessFromPublicNetworksToInternalNetworks: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "634425708590")) as SystemCommunicationsProtection["whatControlsPreventUnauthorizedAccessFromPublicNetworksToInternalNetworks"],
    notesEvidence7: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "845777456178")),
    howDoYouMonitorActivityInYourPublicFacingNetworkSegments: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "536378863536")) as SystemCommunicationsProtection["howDoYouMonitorActivityInYourPublicFacingNetworkSegments"],
    notesEvidence8: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "122899280845")),
    supportingDocumentation2: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "980001173858")),
    additionalNotes2: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "597392284230")),
  };
}

/**
 * NOTE TO DEVELOPERS:
 * -------------------
 * This Interpreter class is provided only as an EXAMPLE scaffold to demonstrate
 * how to consume the normalized type-safe interface generated for this
 * Questionnaire. It shows minimal factories (`fromLhc`, `fromQuestionnaireResponse`)
 * and convenience methods (`validateRequiredFields`, `assessReadiness`) but it is
 * NOT intended for production use.
 *
 * In real applications:
 * - Treat this class as SAMPLE CODE only.
 * - Replace or extend it with proper business logic, rules engines, or validation
 *   frameworks appropriate to your domain.
 * - Do not rely on the simplistic readiness scoring or validation in production
 *   scenarios; they are illustrative, not authoritative.
 *
 * Best practice: use the generated TypeScript interface (`SystemCommunicationsProtection`) as your
 * contract for normalized data, then integrate with your own rules processors,
 * compliance engines, or plain TypeScript/JavaScript functions as needed.
 */
export class SystemCommunicationsProtectionInterpreter {
  constructor(readonly value: SystemCommunicationsProtection) { }

  /** Factory: build from LHC JSON. */
  static fromLhcFormResponse(input: Any): SystemCommunicationsProtectionInterpreter {
    return new SystemCommunicationsProtectionInterpreter(systemCommunicationsProtectionLhcFormResponseAdapter(input));
  }

  /** Factory: build from FHIR QuestionnaireResponse. */
  static fromQuestionnaireResponse(qr: Any): SystemCommunicationsProtectionInterpreter {
    return new SystemCommunicationsProtectionInterpreter(systemCommunicationsProtectionFhirQuestionnaireResponseAdapter(qr));
  }

  /** Check required fields and report any missing or blank. */
  validateRequiredFields(): { ok: boolean; missing: Array<keyof SystemCommunicationsProtection> } {
    const missing: Array<keyof SystemCommunicationsProtection> = [];
    const req: Array<keyof SystemCommunicationsProtection> = ["implementationStatus", "implementationStatus2"];
    for (const k of req) {
      const v = (this.value as Any)[k];
      if (rt.isBlank(v)) missing.push(k);
    }
    return { ok: missing.length === 0, missing };
  }

  /**
   * Assess readiness with a simple completeness score. This is meant to be
   * used to help understand how complete the types are and serves as an
   * example of how to use the generated code.
   * - requiredCovered: percentage of required fields that are non-blank
   * - overallFilled: count of non-blank fields among all known properties
   */
  assessReadiness(): {
    formTitle: string;
    requiredCovered: number; // 0..1
    totalRequired: number;
    totalFilled: number;
    totalFields: number;
    missingRequired: Array<keyof SystemCommunicationsProtection>;
  } {
    const req: Array<keyof SystemCommunicationsProtection> = ["implementationStatus", "implementationStatus2"];
    const all: Array<keyof SystemCommunicationsProtection> = ["requirementsImplementNetworkMonitoringAndBoundaryProtectionIncludingFirewallsIntrusionDetectionAndCommunicationControls", "implementationStatus", "notesEvidence", "doYouHaveANetworkDiagramShowingSystemBoundariesKeyComponentsAndDataFlows", "notesEvidence2", "externalBoundaryComponents", "keyInternalBoundaryComponents", "firewallManufacturerModel", "firewallSoftwareFirmwareVersion", "defaultDenyPolicyIsImplementedTrafficIsDeniedByDefaultUnlessExplicitlyPermitted", "typeYourCommentsHere", "explicitlyAllowedServicesProtocols", "explicitlyDeniedServicesProtocols", "howDoYouMonitorCommunicationsAtSystemBoundaries", "notesEvidence3", "supportingDocumentation", "additionalNotes", "requirementsCreateDmzOrSeparatedNetworkSegmentsForPublicFacingSystemsToIsolateThemFromInternalNetworks", "implementationStatus2", "notesEvidence4", "whatPubliclyAccessibleSystemComponentsDoesYourOrganizationOperate", "notesEvidence5", "howArePubliclyAccessibleSystemsSeparatedFromInternalNetworks", "notesEvidence6", "whatControlsPreventUnauthorizedAccessFromPublicNetworksToInternalNetworks", "notesEvidence7", "howDoYouMonitorActivityInYourPublicFacingNetworkSegments", "notesEvidence8", "supportingDocumentation2", "additionalNotes2"];

    let reqFilled = 0;
    const missingReq: Array<keyof SystemCommunicationsProtection> = [];
    for (const k of req) {
      const v = (this.value as Any)[k];
      if (!rt.isBlank(v)) reqFilled++;
      else missingReq.push(k);
    }

    let totalFilled = 0;
    for (const k of all) {
      if (!rt.isBlank((this.value as Any)[k])) totalFilled++;
    }

    return {
      formTitle: "System & Communications Protection",
      requiredCovered: req.length ? reqFilled / req.length : 1,
      totalRequired: req.length,
      totalFilled,
      totalFields: all.length,
      missingRequired: missingReq,
    };
  }
}

/** The original source */
export const systemCommunicationsProtectionSource = `{
  "resourceType": "Questionnaire",
  "meta": {
    "profile": [
      "http://hl7.org/fhir/4.0/StructureDefinition/Questionnaire"
    ],
    "tag": [
      {
        "display": "Network Security"
      },
      {
        "display": "Communication Protection"
      },
      {
        "display": "System Monitoring"
      }
    ]
  },
  "extension": [
    {
      "url": "http://hl7.org/fhir/StructureDefinition/variable",
      "valueExpression": {
        "name": "displayOrder",
        "language": "text/fhirpath",
        "expression": "6",
        "extension": [
          {
            "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
            "valueString": "simple"
          },
          {
            "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
            "valueString": "6"
          }
        ]
      }
    }
  ],
  "version": "1.0",
  "name": "system_communications_protection",
  "title": "System & Communications Protection",
  "status": "draft",
  "date": "2025-08-26",
  "publisher": "Netspective",
  "description": "System & Communications Protection (Monitor, control, and protect organizational communications)",
  "purpose": "This assessment evaluates how the organization monitors and secures its communications at system boundaries. It includes protections for network traffic, use of boundary defenses, and separation of publicly accessible system components from internal networks. The goal is to prevent unauthorized disclosure or alteration of FCI during transmission and to maintain secure system segmentation.",
  "approvalDate": "2025-08-26",
  "lastReviewDate": "2025-08-26",
  "item": [
    {
      "item": [
        {
          "type": "display",
          "linkId": "998940326541",
          "text": "Requirements: Implement network monitoring and boundary protection including firewalls, intrusion detection, and communication controls."
        },
        {
          "type": "string",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "radio-button",
                    "display": "Radio Button"
                  }
                ]
              }
            }
          ],
          "linkId": "954433842901",
          "text": "Implementation Status ",
          "required": true,
          "repeats": false,
          "answerOption": [
            {
              "valueString": "Fully Implemented - All boundary protection controls are in place and operational"
            },
            {
              "valueString": "Partially Implemented - Some boundary protection controls exist but gaps remain"
            },
            {
              "valueString": " Not Implemented - No boundary protection controls currently in place"
            }
          ]
        },
        {
          "type": "string",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
              "valueString": "Type your comments here..."
            }
          ],
          "linkId": "494412529555",
          "text": "Notes / Evidence "
        },
        {
          "type": "string",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "radio-button",
                    "display": "Radio Button"
                  }
                ]
              }
            }
          ],
          "linkId": "979372224491",
          "text": "Do you have a network diagram showing system boundaries, key components, and data flows?",
          "repeats": false,
          "answerOption": [
            {
              "valueString": "Yes"
            },
            {
              "valueString": "No"
            }
          ]
        },
        {
          "type": "string",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
              "valueString": "Type your comments here..."
            }
          ],
          "linkId": "353406656082",
          "text": "Notes / Evidence "
        },
        {
          "item": [
            {
              "type": "text",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Include hardware models, firmware versions and their primary functions.  e.g., Cisco ASA 5515-X v9.12.3 - Primary perimeter firewall, Palo Alto PA-220 v10.1.2 - Branch office firewall."
                }
              ],
              "linkId": "591770672887",
              "text": "External Boundary Components",
              "item": [
                {
                  "linkId": "591770672887_helpText",
                  "type": "display",
                  "text": "List the components that make up your external system boundaries (e.g., firewalls, routers, gateways); Include hardware models, firmware versions, and their primary functions",
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                      "valueCodeableConcept": {
                        "coding": [
                          {
                            "system": "http://hl7.org/fhir/questionnaire-item-control",
                            "code": "help",
                            "display": "Help-Button"
                          }
                        ],
                        "text": "Help-Button"
                      }
                    }
                  ]
                }
              ]
            },
            {
              "type": "text",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Include components that separate development, test and production environments or create internal segmentation. eg., Internal VLAN switches, host-based firewalls,  Network ACLs."
                }
              ],
              "linkId": "930792706809",
              "text": "Key Internal Boundary Components",
              "item": [
                {
                  "linkId": "930792706809_helpText",
                  "type": "display",
                  "text": "List any key internal boundaries that separate parts of your network; Include components that separate development, test, and production environments or create internal segmentation",
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                      "valueCodeableConcept": {
                        "coding": [
                          {
                            "system": "http://hl7.org/fhir/questionnaire-item-control",
                            "code": "help",
                            "display": "Help-Button"
                          }
                        ],
                        "text": "Help-Button"
                      }
                    }
                  ]
                }
              ]
            },
            {
              "linkId": "861774438513_helpText",
              "type": "display",
              "text": "Clearly define the limits of your information systems to identify what needs protection and control.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "861774438513",
          "text": "1. System Boundary Definition",
          "repeats": false
        },
        {
          "item": [
            {
              "type": "text",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "e.g., Cisco ASA 5500, Palo Alto PA-220"
                }
              ],
              "linkId": "843201435494",
              "text": "Firewall Manufacturer/Model",
              "repeats": false,
              "item": [
                {
                  "linkId": "843201435494_helpText",
                  "type": "display",
                  "text": "e.g., Cisco ASA 5500, Palo Alto PA-220",
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                      "valueCodeableConcept": {
                        "coding": [
                          {
                            "system": "http://hl7.org/fhir/questionnaire-item-control",
                            "code": "help",
                            "display": "Help-Button"
                          }
                        ],
                        "text": "Help-Button"
                      }
                    }
                  ]
                }
              ]
            },
            {
              "type": "text",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "e.g., v9.12.3"
                }
              ],
              "linkId": "706452201694",
              "text": "Firewall Software/Firmware Version",
              "repeats": false,
              "item": [
                {
                  "linkId": "706452201694_helpText",
                  "type": "display",
                  "text": "e.g., v9.12.3",
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                      "valueCodeableConcept": {
                        "coding": [
                          {
                            "system": "http://hl7.org/fhir/questionnaire-item-control",
                            "code": "help",
                            "display": "Help-Button"
                          }
                        ],
                        "text": "Help-Button"
                      }
                    }
                  ]
                }
              ]
            },
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "radio-button",
                        "display": "Radio Button"
                      }
                    ]
                  }
                }
              ],
              "linkId": "949755108024",
              "text": "Default deny policy is implemented (traffic is denied by default unless explicitly permitted)",
              "repeats": false,
              "answerOption": [
                {
                  "valueCoding": {
                    "display": "Yes"
                  }
                },
                {
                  "valueCoding": {
                    "display": "No"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "156526970162",
              "text": "Type your comments here..."
            },
            {
              "type": "text",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "e.g., HTTPS (TCP/443), SSH (TCP/22), DNS (UDP/53)"
                }
              ],
              "linkId": "963088071424",
              "text": "Explicitly Allowed Services/Protocols",
              "repeats": false,
              "item": [
                {
                  "linkId": "963088071424_helpText",
                  "type": "display",
                  "text": "e.g., HTTPS (TCP/443), SSH (TCP/22), DNS (UDP/53)",
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                      "valueCodeableConcept": {
                        "coding": [
                          {
                            "system": "http://hl7.org/fhir/questionnaire-item-control",
                            "code": "help",
                            "display": "Help-Button"
                          }
                        ],
                        "text": "Help-Button"
                      }
                    }
                  ]
                }
              ]
            },
            {
              "type": "text",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "e.g., Telnet (TCP/23), FTP (TCP/21), HTTP (TCP/80)"
                }
              ],
              "linkId": "122305830447",
              "text": "Explicitly Denied Services/Protocols",
              "repeats": false,
              "item": [
                {
                  "linkId": "122305830447_helpText",
                  "type": "display",
                  "text": "e.g., Telnet (TCP/23), FTP (TCP/21), HTTP (TCP/80)",
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                      "valueCodeableConcept": {
                        "coding": [
                          {
                            "system": "http://hl7.org/fhir/questionnaire-item-control",
                            "code": "help",
                            "display": "Help-Button"
                          }
                        ],
                        "text": "Help-Button"
                      }
                    }
                  ]
                }
              ]
            },
            {
              "linkId": "835757897200_helpText",
              "type": "display",
              "text": "Set up and manage firewalls to control and monitor incoming and outgoing network traffic based on security rules.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "835757897200",
          "text": "2. Firewall Configuration"
        },
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "100",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "100"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "847131102373",
              "text": "How do you monitor communications at system boundaries?",
              "repeats": true,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Firewall logs and analysis"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Intrusion detection/prevention systems"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Network monitoring tools"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "SIEM system integration"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Manual log review"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "305967020301",
              "text": "Notes / Evidence "
            },
            {
              "linkId": "434121826556_helpText",
              "type": "display",
              "text": "Establish continuous monitoring to detect and respond to security events within systems and communications.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "434121826556",
          "text": "3. Monitoring Implementation"
        },
        {
          "item": [
            {
              "type": "display",
              "linkId": "375737094397",
              "text": "Note: Ensure these documents are readily available with your other compliance documentation for review"
            },
            {
              "linkId": "794317413983_helpText",
              "type": "display",
              "text": "List your supporting documentation and ensure these documents are available with your other compliance documentation",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "text",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
              "valueString": "eg., Network Security Policy, v2.1, Firewall configuration documentation, Security monitoring procedures. "
            }
          ],
          "linkId": "794317413983",
          "text": "Supporting Documentation"
        },
        {
          "type": "text",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
              "valueString": "Any additional information, challenges or implementation notes"
            }
          ],
          "linkId": "782731881405",
          "text": "Additional Notes"
        },
        {
          "linkId": "617514452468_helpText",
          "type": "display",
          "text": "Monitor, control, and protect organizational communications (i.e., information transmitted or received by organizational information systems) at the external boundaries and key internal boundaries of the information systems.",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "help",
                    "display": "Help-Button"
                  }
                ],
                "text": "Help-Button"
              }
            }
          ]
        }
      ],
      "type": "group",
      "linkId": "617514452468",
      "text": "SC.L1-B.1.X – Boundary Protection"
    },
    {
      "item": [
        {
          "type": "display",
          "linkId": "556770566326",
          "text": "Requirements: Create DMZ or separated network segments for public-facing systems to isolate them from internal networks."
        },
        {
          "type": "string",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "radio-button",
                    "display": "Radio Button"
                  }
                ]
              }
            }
          ],
          "linkId": "274150359667",
          "text": "Implementation Status ",
          "required": true,
          "repeats": false,
          "answerOption": [
            {
              "valueString": "Fully Implemented - DMZ/subnetworks properly isolate public systems"
            },
            {
              "valueString": "Partially Implemented - Some separation exists but not comprehensive"
            },
            {
              "valueString": " Not Implemented - No network separation for public systems"
            }
          ]
        },
        {
          "type": "string",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
              "valueString": "Type your comments here..."
            }
          ],
          "linkId": "496638290461",
          "text": "Notes / Evidence "
        },
        {
          "item": [
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "956471776047",
              "text": "What publicly accessible system components does your organization operate?",
              "repeats": true,
              "answerOption": [
                {
                  "valueString": "Web servers"
                },
                {
                  "valueString": "Email servers (public-facing)"
                },
                {
                  "valueString": "DNS servers"
                },
                {
                  "valueString": " FTP servers"
                },
                {
                  "valueString": "VPN gateways"
                },
                {
                  "valueString": "Remote access servers"
                },
                {
                  "valueString": "API gateways"
                },
                {
                  "valueString": "No publicly accessible components"
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "272791116387",
              "text": "Notes / Evidence "
            },
            {
              "linkId": "194546217130_helpText",
              "type": "display",
              "text": "Identify and manage system components like web servers, email servers, and public applications that are accessible to external users",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "194546217130",
          "text": "1. Publicly Accessible System Components"
        },
        {
          "item": [
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "517448335213",
              "text": "How are publicly accessible systems separated from internal networks?",
              "repeats": true,
              "answerOption": [
                {
                  "valueString": "Demilitarized Zone (DMZ) implementation"
                },
                {
                  "valueString": "Virtual LAN (VLAN) segmentation"
                },
                {
                  "valueString": "Physical network separation"
                },
                {
                  "valueString": "Firewall rules and access control lists"
                },
                {
                  "valueString": "Cloud-based isolation and separation"
                },
                {
                  "valueString": "Proxy servers and reverse proxies"
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "299978179191",
              "text": "Notes / Evidence "
            },
            {
              "linkId": "560463506575_helpText",
              "type": "display",
              "text": "Implement measures to separate and segment networks to limit unauthorized access and contain potential security breaches.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "560463506575",
          "text": "2. Network Separation Implementation"
        },
        {
          "item": [
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "634425708590",
              "text": "What controls prevent unauthorized access from public networks to internal networks?",
              "repeats": true,
              "answerOption": [
                {
                  "valueString": " Default deny policies (all traffic blocked unless explicitly allowed)"
                },
                {
                  "valueString": "Stateful firewall inspection"
                },
                {
                  "valueString": "Application-level proxy filtering"
                },
                {
                  "valueString": " Intrusion detection and prevention systems"
                },
                {
                  "valueString": "Continuous network monitoring and logging"
                },
                {
                  "valueString": "Strong authentication for any allowed connections"
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "845777456178",
              "text": "Notes / Evidence "
            },
            {
              "linkId": "126262667735_helpText",
              "type": "display",
              "text": "Control and restrict communication between networks to prevent unauthorized access and data transfer.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "126262667735",
          "text": "3. Access Control Between Networks"
        },
        {
          "item": [
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "536378863536",
              "text": "How do you monitor activity in your public-facing network segments?",
              "repeats": true,
              "answerOption": [
                {
                  "valueString": "Security Information and Event Management (SIEM) system"
                },
                {
                  "valueString": "Network monitoring tools and dashboards"
                },
                {
                  "valueString": " Automated log analysis and alerting"
                },
                {
                  "valueString": " Regular vulnerability scanning"
                },
                {
                  "valueString": "Periodic penetration testing"
                },
                {
                  "valueString": "Manual log review and analysis"
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "122899280845",
              "text": "Notes / Evidence "
            },
            {
              "linkId": "414442892901_helpText",
              "type": "display",
              "text": "Continuously monitor demilitarized zones (DMZ) and public networks to detect and respond to potential security threats.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "414442892901",
          "text": "4. DMZ/Public Network Monitoring"
        },
        {
          "item": [
            {
              "type": "display",
              "linkId": "561948412525",
              "text": "Note: Ensure these documents are readily available with your other compliance documentation for review"
            },
            {
              "linkId": "980001173858_helpText",
              "type": "display",
              "text": "List your supporting documentation and ensure these documents are available with your other compliance documentation",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "text",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
              "valueString": "eg., Network Security Policy, v2.1, Firewall configuration documentation, Security monitoring procedures. "
            }
          ],
          "linkId": "980001173858",
          "text": "Supporting Documentation"
        },
        {
          "type": "text",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
              "valueString": "Any additional information, challenges or implementation notes"
            }
          ],
          "linkId": "597392284230",
          "text": "Additional Notes"
        },
        {
          "linkId": "587208645662_helpText",
          "type": "display",
          "text": "Implement subnetworks for publicly accessible system components that are physically or logically separated from internal organizational networks.",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "help",
                    "display": "Help-Button"
                  }
                ],
                "text": "Help-Button"
              }
            }
          ]
        }
      ],
      "type": "group",
      "linkId": "587208645662",
      "text": "SC.L1-B.1.X – Implement subnetworks for publicly accessible components"
    }
  ]
}`;