-- traffic.sql
-- title: Traffic
-- menu: Traffic
-- description: Requests breakdown, top pages/referrers, user agents, and time series.

WITH params AS (
  SELECT
    coalesce(:from::timestamp, now() - interval '7 days') AS from_ts,
    coalesce(:to::timestamp, now()) AS to_ts,
    NULLIF(trim(coalesce(:env, '')), '')::text       AS env,
    NULLIF(trim(coalesce(:service, '')), '')::text   AS service,
    NULLIF(trim(coalesce(:path_like, '')), '')::text AS path_like,
    coalesce(:page::int, 1) AS page,
    coalesce(:pagesize::int, 200) AS pagesize
),
range_meta AS (
  SELECT *,
    CASE WHEN (to_ts - from_ts) > interval '21 days' THEN 'day' ELSE 'hour' END AS trunc_unit
  FROM params
)

-- Traffic summary: requests by status class, unique sessions, data transferred
SELECT
  SUM(CASE WHEN wt.status_code BETWEEN 200 AND 299 THEN 1 ELSE 0 END) AS requests_2xx,
  SUM(CASE WHEN wt.status_code BETWEEN 300 AND 399 THEN 1 ELSE 0 END) AS requests_3xx,
  SUM(CASE WHEN wt.status_code BETWEEN 400 AND 499 THEN 1 ELSE 0 END) AS requests_4xx,
  SUM(CASE WHEN wt.status_code >= 500 THEN 1 ELSE 0 END) AS requests_5xx,
  COUNT(DISTINCT wt.session_id) AS unique_sessions,
  SUM(COALESCE(wt.body_bytes_sent,0)) AS bytes_transferred,
  COUNT(*) AS total_requests
FROM opsfolio.web_traffic wt
JOIN range_meta rm ON true
WHERE wt.ts >= rm.from_ts
  AND wt.ts < rm.to_ts
  AND (rm.env IS NULL OR rm.env = wt.env)
  AND (rm.service IS NULL OR rm.service = wt.service)
  AND (rm.path_like IS NULL OR wt.request_path ILIKE '%' || rm.path_like || '%');

-- Top Pages (by unique sessions) for the selected window (limit 50)
SELECT
  wt.request_path,
  COUNT(DISTINCT wt.session_id) AS unique_sessions,
  COUNT(*) AS requests,
  COUNT(*) FILTER (WHERE wt.status_code BETWEEN 200 AND 299)::numeric / NULLIF(COUNT(*),0)::numeric AS pct_2xx
FROM opsfolio.web_traffic wt
JOIN range_meta rm ON true
WHERE wt.ts >= rm.from_ts
  AND wt.ts < rm.to_ts
  AND (rm.env IS NULL OR rm.env = wt.env)
  AND (rm.service IS NULL OR rm.service = wt.service)
  AND (rm.path_like IS NULL OR wt.request_path ILIKE '%' || rm.path_like || '%')
GROUP BY wt.request_path
ORDER BY unique_sessions DESC
LIMIT 50;

-- Top Referrers
WITH first_hits AS (
  SELECT wt.session_id, MIN(wt.ts) AS first_ts
  FROM opsfolio.web_traffic wt
  JOIN range_meta rm ON true
  WHERE wt.ts >= rm.from_ts
    AND wt.ts < rm.to_ts
    AND (rm.env IS NULL OR rm.env = wt.env)
    AND (rm.service IS NULL OR rm.service = wt.service)
    AND (rm.path_like IS NULL OR wt.request_path ILIKE '%' || rm.path_like || '%')
  GROUP BY wt.session_id
),
first_hit_with_referer AS (
  SELECT fh.session_id, wt.referer
  FROM first_hits fh
  LEFT JOIN opsfolio.web_traffic wt
    ON wt.session_id = fh.session_id AND wt.ts = fh.first_ts
)
SELECT
  COALESCE(referer, '(direct)') AS referer,
  COUNT(DISTINCT session_id) AS sessions
FROM first_hit_with_referer
GROUP BY referer
ORDER BY sessions DESC
LIMIT 50;

-- User Agents: naive buckets by pattern (desktop/mobile/bot) else top raw UA
SELECT
  CASE
    WHEN wt.user_agent ILIKE '%bot%' OR wt.user_agent ILIKE '%crawl%' OR wt.user_agent ILIKE '%spider%' THEN 'bot/crawler'
    WHEN wt.user_agent ILIKE '%mobile%' OR wt.user_agent ILIKE '%iphone%' OR wt.user_agent ILIKE '%android%' THEN 'mobile'
    WHEN wt.user_agent ILIKE '%tablet%' OR wt.user_agent ILIKE '%ipad%' THEN 'tablet'
    ELSE 'desktop/other'
  END AS ua_bucket,
  COUNT(*) AS requests,
  COUNT(DISTINCT wt.session_id) AS unique_sessions
FROM opsfolio.web_traffic wt
JOIN range_meta rm ON true
WHERE wt.ts >= rm.from_ts
  AND wt.ts < rm.to_ts
  AND (rm.env IS NULL OR rm.env = wt.env)
  AND (rm.service IS NULL OR rm.service = wt.service)
  AND (rm.path_like IS NULL OR wt.request_path ILIKE '%' || rm.path_like || '%')
GROUP BY ua_bucket
ORDER BY requests DESC
LIMIT 50;

-- Time series: by hour/day unique sessions + total requests
WITH bounds AS (SELECT * FROM range_meta)
SELECT
  CASE WHEN bounds.trunc_unit = 'day' THEN date_trunc('day', wt.ts) ELSE date_trunc('hour', wt.ts) END AS bucket,
  COUNT(*) AS requests,
  COUNT(DISTINCT wt.session_id) AS unique_sessions
FROM opsfolio.web_traffic wt, bounds
WHERE wt.ts >= bounds.from_ts
  AND wt.ts < bounds.to_ts
  AND (bounds.env IS NULL OR bounds.env = wt.env)
  AND (bounds.service IS NULL OR bounds.service = wt.service)
  AND (bounds.path_like IS NULL OR wt.request_path ILIKE '%' || bounds.path_like || '%')
GROUP BY bucket
ORDER BY bucket;

-- Optional: Path filter helper (shows matching paths top 50 to help set path_like)
SELECT request_path, COUNT(*) AS hits
FROM opsfolio.web_traffic wt
JOIN range_meta rm ON true
WHERE wt.ts >= rm.from_ts
  AND wt.ts < rm.to_ts
  AND (rm.env IS NULL OR rm.env = wt.env)
  AND (rm.service IS NULL OR rm.service = wt.service)
  AND (rm.path_like IS NULL OR wt.request_path ILIKE '%' || rm.path_like || '%')
GROUP BY request_path
ORDER BY hits DESC
LIMIT 50;
