{"resourceType": "Questionnaire", "meta": {"profile": ["http://hl7.org/fhir/4.0/StructureDefinition/Questionnaire"], "tag": [{"display": "Vulnerability Management"}, {"display": "System Integrity Monitoring"}]}, "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "displayOrder", "language": "text/fhirpath", "expression": "7", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "7"}]}}], "name": "system_information_integrity", "title": "System & Information Integrity", "status": "draft", "date": "2025-08-26", "publisher": "Netspective", "description": "System & Information Integrity (Identify, report, and correct information system flaws)", "purpose": "This assessment ensures that systems are protected against malicious code and vulnerabilities. It includes timely identification and correction of flaws, deployment of security updates, monitoring for threats, and maintaining updated anti-malware capabilities. It also reviews the organization's ability to conduct scans and inspections on external files to prevent the introduction of malicious software.", "approvalDate": "2025-08-26", "lastReviewDate": "2025-08-26", "item": [{"item": [{"item": [{"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "maxScore", "language": "text/fhirpath", "expression": "100", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "100"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "check-box", "display": "Check-box"}]}}], "linkId": "758011605310", "text": "How does your organization identify system flaws and vulnerabilities?", "repeats": true, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Automated vulnerability scanning"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Vendor security notifications and bulletins"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 10}], "valueCoding": {"display": "Penetration testing"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 10}], "valueCoding": {"display": "Regular security assessments"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Threat intelligence feeds"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Incident response and forensics"}}]}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "135467801033", "text": "Notes / Evidence "}], "type": "group", "linkId": "544004255685", "prefix": "1.", "text": "Flaw Identification Process"}, {"item": [{"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "maxScore", "language": "text/fhirpath", "expression": "100", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "100"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "check-box", "display": "Check-box"}]}}], "linkId": "854540559647", "text": "How are identified flaws reported and tracked?", "repeats": true, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": " Formal tracking system or database"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Automatic management notification"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Risk assessment and prioritization"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Communication to affected stakeholders"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Detailed documentation of findings"}}]}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "924286782806", "text": "Notes / Evidence "}, {"linkId": "603452357063_helpText", "type": "display", "text": "Establish processes to identify, report, and track system flaws and vulnerabilities until they are resolved.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "603452357063", "prefix": "2.", "text": "Flaw Reporting and Tracking"}, {"item": [{"item": [{"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "885354230428", "text": "Critical Severity Flaws:", "repeats": false, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 100}], "valueCoding": {"display": "Immediate (within hours)"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 75}], "valueCoding": {"display": "Within 24 hours"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 50}], "valueCoding": {"display": "Within 72 hours"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": " Within 1 week"}}]}, {"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "149460684671", "text": "High Severity Flaws:", "repeats": false, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 100}], "valueCoding": {"display": "Within 1 week"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 50}], "valueCoding": {"display": "Within 2 weeks"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": "Within 1 month"}}]}, {"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "119144494365", "text": "Medium/Low Severity Flaws:", "repeats": false, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 100}], "valueCoding": {"display": "Within 1 month"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 50}], "valueCoding": {"display": "Within 1 quarter"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": "Next scheduled maintenance window"}}]}], "type": "group", "linkId": "802989461197", "text": "What are your target timeframes for correcting identified flaws?"}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "478326704189", "text": "Notes / Evidence "}, {"linkId": "702845194175_helpText", "type": "display", "text": "Define and follow timelines to promptly address and fix identified system vulnerabilities to reduce security risks.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "702845194175", "prefix": "3.", "text": "Flaw Correction Timeline"}, {"item": [{"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "maxScore", "language": "text/fhirpath", "expression": "100", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "100"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "check-box", "display": "Check-box"}]}}], "linkId": "896010001522", "text": "How are security patches and updates managed?", "repeats": true, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Testing in non-production environment before deployment"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Formal change control process"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 10}], "valueCoding": {"display": "Rollback procedures in case of issues"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 10}], "valueCoding": {"display": "Automated patch deployment capabilities"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Emergency patching procedures for critical flaws"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Documentation of all patches applied"}}]}, {"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Any additional context, challenges, or implementation details..."}], "linkId": "731360730463", "text": "Additional Notes or Comments"}, {"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "List or describe the supporting documentation you have available (policies, procedures, scan reports, etc.)..."}], "linkId": "231346071278", "text": "Supporting Documentation", "item": [{"linkId": "231346071278_helpText", "type": "display", "text": "Note: Have documentation available that demonstrates your flaw remediation processes, vulnerability scanning procedures, and patch management policies.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"linkId": "535096646220_helpText", "type": "display", "text": "Implement procedures to regularly apply updates and patches to systems to protect against known vulnerabilities.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "535096646220", "prefix": "4.", "text": "Patch Management Process"}, {"linkId": "350961856234_helpText", "type": "display", "text": "Identify, report, and correct information and information system flaws in a timely manner", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "350961856234", "text": "SI.L1-B.1.XII – Flaw Remediation"}, {"item": [{"item": [{"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "514312894888", "text": "Notes / Evidence "}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "892692932760", "text": "Do you have a malicious code protection policy document?", "repeats": false, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 100}], "valueCoding": {"display": "Yes"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 0}], "valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "342802754726", "text": "Notes / Evidence "}], "type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "942706169228", "text": "Do you need assistance?", "repeats": false, "answerOption": [{"valueString": "Yes"}, {"valueString": "No"}]}, {"item": [{"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "maxScore", "language": "text/fhirpath", "expression": "100", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "100"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "check-box", "display": "Check-box"}]}}], "linkId": "457010911238", "text": "Select all locations where malicious code protection is implemented:", "repeats": true, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Email Gateway"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 10}], "valueCoding": {"display": "Web Proxy/Gateway"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Perimeter Firewall"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 10}], "valueCoding": {"display": "VPN Gateway"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 10}], "valueCoding": {"display": "Endpoints (Workstations, Laptops)"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Servers"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 10}], "valueCoding": {"display": "Mobile Devices"}}]}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "388699038922", "text": "Notes / Evidence "}, {"linkId": "120577885697_helpText", "type": "display", "text": "Identify and secure critical points in systems and networks where integrity controls must be applied to prevent unauthorized changes.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "120577885697", "prefix": "1.", "text": "Protection Locations"}, {"item": [{"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter primary anti-malware solution"}], "linkId": "149423997720", "text": "Primary Anti-Malware Product/Solution: e.g., Microsoft Defender, McAfee, Symantec"}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter version or release identifier"}], "linkId": "343942743605", "text": "Anti-Malware Version/Release: Version number or release identifier"}, {"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Describe your anti-malware implementation scope..."}], "linkId": "581419297519", "text": "Implementation Scope: Describe the scope of your anti-malware implementation (e.g., all company endpoints, specific servers)"}, {"item": [{"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "466807023155", "text": "Notes / Evidence "}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "394557514652", "text": "Real-Time Protection Enabled:", "repeats": false, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 100}], "valueCoding": {"display": "Yes"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 0}], "valueCoding": {"display": "No"}}]}, {"item": [{"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "266083093634", "text": "Notes / Evidence "}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "137330973781", "text": "Centrally Managed:", "repeats": false, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 100}], "valueCoding": {"display": "Yes"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 0}], "valueCoding": {"display": "No"}}]}, {"linkId": "123297792461_helpText", "type": "display", "text": "Deploy and maintain tools and processes to detect, prevent, and respond to malware infections in your systems.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter primary anti-malware solution"}], "linkId": "123297792461", "prefix": "2.", "text": "Implementation Details"}, {"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Any additional context, challenges, or implementation details..."}], "linkId": "************", "text": "Additional Notes or Comments"}, {"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "List or describe the supporting documentation you have available(policies, configuration guides, deployment records, etc.)..."}], "linkId": "************", "text": "Supporting Documentation", "item": [{"linkId": "************_helpText", "type": "display", "text": "Note: Have documentation available that demonstrates your malicious code protection policies, anti-malware configurations, and deployment procedures.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"linkId": "340771388729_helpText", "type": "display", "text": "Provide protection from malicious code at appropriate locations within organizational information systems", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "340771388729", "text": "SI.L1-B.1.XIII – Malicious Code Protection"}, {"item": [{"item": [{"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "830996907328", "text": "How frequently are malicious code protection mechanisms updated?", "repeats": false, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 100}], "valueCoding": {"display": "Real-time updates (as available)"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 75}], "valueCoding": {"display": "Hourly"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 50}], "valueCoding": {"display": "Daily"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": "Weekly"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 15}], "valueCoding": {"display": "Manual updates only"}}]}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "583208753437", "text": "Notes / Evidence "}, {"linkId": "370529733824_helpText", "type": "display", "text": "Regularly update malware protection tools and definitions to ensure defense against the latest threats.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "370529733824", "prefix": "1.", "text": "Update Frequency"}, {"item": [{"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "maxScore", "language": "text/fhirpath", "expression": "100", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "100"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "check-box", "display": "Check-box"}]}}], "linkId": "733457774453", "text": "How are malicious code protection updates managed?", "repeats": true, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Automatic updates enabled"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Centralized update management system"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 10}], "valueCoding": {"display": "Verification of successful updates"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 10}], "valueCoding": {"display": "Rollback capability for problematic updates"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Testing of updates before deployment"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Notification of update status and failures"}}]}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "222629834244", "text": "Notes / Evidence "}, {"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "maxScore", "language": "text/fhirpath", "expression": "100", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "100"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "check-box", "display": "Check-box"}]}}], "linkId": "146442608630", "text": "Which external sources are scanned?", "repeats": true, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 10}], "valueCoding": {"display": "Internet Downloads"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "<PERSON><PERSON>"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 10}], "valueCoding": {"display": "Removable Media"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Cloud Storage"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Network Shares"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Other External Sources"}}]}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "692565504391", "text": "Notes / Evidence "}, {"linkId": "400782620614_helpText", "type": "display", "text": "Establish procedures to manage and verify timely updates to malicious code protection systems.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "400782620614", "prefix": "2.", "text": "Update Management Process"}, {"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Any additional context challenges, or implementation details..."}], "linkId": "660268414578", "text": "Additional Notes or Comments"}, {"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "List o describe the supporting documentation you have available (update procedures, verification logs, rollback plans, etc.)..."}], "linkId": "717091491475", "text": "Supporting Documentation", "item": [{"linkId": "717091491475_helpText", "type": "display", "text": "Note: Have documentation available that demonstrates your update management procedures, verification processes, and rollback capabilities.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"linkId": "363972470334_helpText", "type": "display", "text": "Update malicious code protection mechanisms when new releases are available", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "363972470334", "text": "SI.L1-B.1.XIV – Update Malicious Code Protection"}, {"item": [{"item": [{"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "646475656187", "text": "Notes / Evidence "}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "470606272303", "text": "Do you have a system scanning policy documentation, file scanning policy, and scanning procedure documentation?", "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "189466095401", "text": "Does your organization have antivirus/anti-malware software installed on all systems?", "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}, {"valueCoding": {"display": "Partially (some systems only)"}}], "item": [{"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "720788097516", "text": "Notes / Evidence "}]}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter your current antivirus/anti-malware solution"}], "linkId": "694425083943", "text": "What antivirus/anti-malware solution is currently deployed? e.g., Microsoft Defender, Norton, McAfee, etc."}, {"linkId": "359679551926_helpText", "type": "display", "text": "Deploy anti-malware solutions to regularly scan systems and files for malicious software and remove threats promptly.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "359679551926", "prefix": "1.", "text": "Anti-Malware Implementation"}, {"item": [{"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "-- Select Frequency --"}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "508929065591", "text": "How frequently are full system scans conducted?", "repeats": false, "answerOption": [{"valueCoding": {"display": "Daily"}}, {"valueCoding": {"display": "Weekily"}}, {"valueCoding": {"display": "Bi-weekly"}}, {"valueCoding": {"display": "Monthly"}}, {"valueCoding": {"display": "Quarterly"}}, {"valueCoding": {"display": "Custom Schedule"}}], "item": [{"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "112297593264", "text": "Notes / Evidence "}]}, {"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "-- Select <PERSON><PERSON> --"}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "889472415570", "text": "What level of thoroughness is used for periodic scans?", "repeats": false, "answerOption": [{"valueCoding": {"display": "<PERSON>an (critical files only)"}}, {"valueCoding": {"display": "Standard Scan (system files and common user directories)"}}, {"valueCoding": {"display": "Full Scan (entire file system)"}}, {"valueCoding": {"display": "Custom Scan (specific directories)"}}], "item": [{"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "119870353096", "text": "Notes / Evidence "}]}, {"linkId": "558460360931_helpText", "type": "display", "text": "Schedule regular scans of systems and files to detect and address malware or security issues consistently.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "558460360931", "prefix": "2.", "text": "Periodic Scanning Implementation"}, {"item": [{"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "740865411316", "text": "Are files from external sources scanned in real-time?", "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}, {"valueCoding": {"display": "Partially (some sources only)"}}], "item": [{"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "499524923589", "text": "Notes / Evidence "}]}, {"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "842602142275", "text": "Do you employ file integrity monitoring for critical system files?", "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}, {"valueCoding": {"display": "Planned"}}], "item": [{"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "658767359751", "text": "Notes / Evidence "}]}, {"linkId": "527252274149_helpText", "type": "display", "text": "Use real-time scanning to detect threats immediately and monitor file integrity to prevent unauthorized changes.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "527252274149", "prefix": "3.", "text": "Real-time Scanning & File Integrity"}, {"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Describe your process for reviewing and documenting scan results..."}], "linkId": "707425868010", "text": "How are scan results reviewed and documented? Describe your process for reviewing and documenting scan results..."}, {"type": "open-choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "-- Select Response Timeframe --"}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "986030389075", "text": "What is your response timeframe when malware or vulnerabilities are detected?", "repeats": false, "answerOption": [{"valueCoding": {"display": "Immediate (within hours)"}}, {"valueCoding": {"display": "Within 24 hours"}}, {"valueCoding": {"display": "Within 48 hours"}}, {"valueCoding": {"display": "Within a week"}}], "item": [{"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "936289816171", "text": "Notes / Evidence "}]}, {"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Describe your process for remediating issues detected during scanning.."}], "linkId": "164191875680", "text": "Describe your remediation process for identified issues: Describe your process for remediating issues detected during scanning..."}, {"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "967054991522", "text": "Has scanning effectiveness been tested?", "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}], "item": [{"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "145622130157", "text": "Notes / Evidence "}]}, {"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "List or describe the supporting documentation you have available(scanning policies , procedures , scan logs , remediation records, etc.)..."}], "linkId": "901609884580", "text": "Supporting Documentation", "item": [{"linkId": "901609884580_helpText", "type": "display", "text": "Note: Have documentation available that demonstrates your scanning policies, procedures, scan results, and remediation processes.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"linkId": "123247885877_helpText", "type": "display", "text": "Establish procedures to review scan results, respond to detected threats, and test scanning tools for effectiveness.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "123247885877", "prefix": "4.", "text": "Results Handling & Testing"}, {"linkId": "237888898879_helpText", "type": "display", "text": "Perform periodic scans of the information system and real-time scans of files from external sources", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "237888898879", "text": "SI.L1-B.1.XV – System & File Scanning"}]}