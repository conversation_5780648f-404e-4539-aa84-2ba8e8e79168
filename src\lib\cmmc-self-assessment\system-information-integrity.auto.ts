import * as rt from "./r4q-runtime.ts";
/**
 * @file system-information-integrity.auto.ts
 * @generated This file was auto-generated from the FHIR R4 Questionnaire "System & Information Integrity".
 * Do not edit this file manually; re-run the generator if the source Questionnaire changes.
 * Profiles: http://hl7.org/fhir/4.0/StructureDefinition/Questionnaire
 *
 * Normalizes LHC JSON and FHIR QuestionnaireResponse into the type-safe `SystemInformationIntegrity` interface.
 */

// this is the module signature, used by importers to identify the module
// using r4q-runtime.ts `moduleSignature` function
export const systemInformationIntegrityModuleSignature: rt.ModuleSignature = {
  title: "System & Information Integrity",
  filename: "system-information-integrity.auto.ts",
  titleCamel: "`systemInformationIntegrity`",
  titlePascal: "`SystemInformationIntegrity`",
  titleKebab: "`system-information-integrity`",
  lhcFormResponseAdapterFnName: "systemInformationIntegrityLhcFormResponseAdapter",
  fhirQuestionnaireResponseAdapterFnName: "systemInformationIntegrityFhirQuestionnaireResponseAdapter",
  sourceTextConstName: "systemInformationIntegritySource",
}

// deno-lint-ignore no-explicit-any
type Any = any;
/**
 * Form Help (from display/help controls):
 * - Establish processes to identify, report, and track system flaws and vulnerabilities until they are resolved.
 * - Define and follow timelines to promptly address and fix identified system vulnerabilities to reduce security risks.
 * - Implement procedures to regularly apply updates and patches to systems to protect against known vulnerabilities.
 * - Identify, report, and correct information and information system flaws in a timely manner
 * - Identify and secure critical points in systems and networks where integrity controls must be applied to prevent unauthorized changes.
 * - Deploy and maintain tools and processes to detect, prevent, and respond to malware infections in your systems.
 * - Provide protection from malicious code at appropriate locations within organizational information systems
 * - Regularly update malware protection tools and definitions to ensure defense against the latest threats.
 * - Establish procedures to manage and verify timely updates to malicious code protection systems.
 * - Update malicious code protection mechanisms when new releases are available
 * - Deploy anti-malware solutions to regularly scan systems and files for malicious software and remove threats promptly.
 * - Schedule regular scans of systems and files to detect and address malware or security issues consistently.
 * - Use real-time scanning to detect threats immediately and monitor file integrity to prevent unauthorized changes.
 * - Establish procedures to review scan results, respond to detected threats, and test scanning tools for effectiveness.
 * - Perform periodic scans of the information system and real-time scans of files from external sources
 */
/** Map of normalized property names to their source `linkId`. */
export const systemInformationIntegrityLinkIds = {
  howDoesYourOrganizationIdentifySystemFlawsAndVulnerabilities: "758011605310",
  notesEvidence: "135467801033",
  howAreIdentifiedFlawsReportedAndTracked: "854540559647",
  notesEvidence2: "924286782806",
  criticalSeverityFlaws: "885354230428",
  highSeverityFlaws: "149460684671",
  mediumLowSeverityFlaws: "119144494365",
  notesEvidence3: "478326704189",
  howAreSecurityPatchesAndUpdatesManaged: "896010001522",
  additionalNotesOrComments: "731360730463",
  supportingDocumentation: "231346071278",
  doYouHaveAMaliciousCodeProtectionPolicyDocument: "892692932760",
  doYouNeedAssistance: "942706169228",
  selectAllLocationsWhereMaliciousCodeProtectionIsImplemented: "457010911238",
  notesEvidence4: "388699038922",
  primaryAntiMalwareProductSolutionEGMicrosoftDefenderMcAfeeSymantec: "149423997720",
  antiMalwareVersionReleaseVersionNumberOrReleaseIdentifier: "343942743605",
  implementationScopeDescribeTheScopeOfYourAntiMalwareImplementationEGAllCompanyEndpointsSpecificServers: "581419297519",
  realTimeProtectionEnabled: "394557514652",
  centrallyManaged: "137330973781",
  additionalNotesOrComments2: "************",
  supportingDocumentation2: "************",
  howFrequentlyAreMaliciousCodeProtectionMechanismsUpdated: "************",
  notesEvidence5: "************",
  howAreMaliciousCodeProtectionUpdatesManaged: "************",
  notesEvidence6: "222629834244",
  whichExternalSourcesAreScanned: "146442608630",
  notesEvidence7: "692565504391",
  additionalNotesOrComments3: "660268414578",
  supportingDocumentation3: "717091491475",
  doYouHaveASystemScanningPolicyDocumentationFileScanningPolicyAndScanningProcedureDocumentation: "470606272303",
  doesYourOrganizationHaveAntivirusAntiMalwareSoftwareInstalledOnAllSystems: "189466095401",
  whatAntivirusAntiMalwareSolutionIsCurrentlyDeployedEGMicrosoftDefenderNortonMcAfeeEtc: "694425083943",
  howFrequentlyAreFullSystemScansConducted: "508929065591",
  whatLevelOfThoroughnessIsUsedForPeriodicScans: "889472415570",
  areFilesFromExternalSourcesScannedInRealTime: "740865411316",
  doYouEmployFileIntegrityMonitoringForCriticalSystemFiles: "842602142275",
  howAreScanResultsReviewedAndDocumentedDescribeYourProcessForReviewingAndDocumentingScanResults: "707425868010",
  whatIsYourResponseTimeframeWhenMalwareOrVulnerabilitiesAreDetected: "986030389075",
  describeYourRemediationProcessForIdentifiedIssuesDescribeYourProcessForRemediatingIssuesDetectedDuringScanning: "164191875680",
  hasScanningEffectivenessBeenTested: "967054991522",
  supportingDocumentation4: "901609884580"
} as const;

/** Normalized view of "System & Information Integrity" answers. */
export interface SystemInformationIntegrity {
  /**
   * How does your organization identify system flaws and vulnerabilities?
   * linkId: 758011605310
   * FHIR type: choice
   * Section: SI.L1-B.1.XII – Flaw Remediation > Flaw Identification Process
   * Options: "Automated vulnerability scanning", "Vendor security notifications and bulletins", "Penetration testing", "Regular security assessments", "Threat intelligence feeds", "Incident response and forensics"
   * Required: no
   */
  howDoesYourOrganizationIdentifySystemFlawsAndVulnerabilities?: ("Automated vulnerability scanning" | "Vendor security notifications and bulletins" | "Penetration testing" | "Regular security assessments" | "Threat intelligence feeds" | "Incident response and forensics")[];

  /**
   * Notes / Evidence 
   * linkId: 135467801033
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: SI.L1-B.1.XII – Flaw Remediation > Flaw Identification Process
   * Required: no
   */
  notesEvidence?: string;

  /**
   * How are identified flaws reported and tracked?
   * linkId: 854540559647
   * FHIR type: choice
   * Section: SI.L1-B.1.XII – Flaw Remediation > Flaw Reporting and Tracking
   * Options: " Formal tracking system or database", "Automatic management notification", "Risk assessment and prioritization", "Communication to affected stakeholders", "Detailed documentation of findings"
   * Required: no
   */
  howAreIdentifiedFlawsReportedAndTracked?: (" Formal tracking system or database" | "Automatic management notification" | "Risk assessment and prioritization" | "Communication to affected stakeholders" | "Detailed documentation of findings")[];

  /**
   * Notes / Evidence 
   * linkId: 924286782806
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: SI.L1-B.1.XII – Flaw Remediation > Flaw Reporting and Tracking
   * Required: no
   */
  notesEvidence2?: string;

  /**
   * Critical Severity Flaws:
   * linkId: 885354230428
   * FHIR type: choice
   * Section: SI.L1-B.1.XII – Flaw Remediation > Flaw Correction Timeline > What are your target timeframes for correcting identified flaws?
   * Options: "Immediate (within hours)", "Within 24 hours", "Within 72 hours", " Within 1 week"
   * Required: no
   */
  criticalSeverityFlaws?: "Immediate (within hours)" | "Within 24 hours" | "Within 72 hours" | " Within 1 week";

  /**
   * High Severity Flaws:
   * linkId: 149460684671
   * FHIR type: choice
   * Section: SI.L1-B.1.XII – Flaw Remediation > Flaw Correction Timeline > What are your target timeframes for correcting identified flaws?
   * Options: "Within 1 week", "Within 2 weeks", "Within 1 month"
   * Required: no
   */
  highSeverityFlaws?: "Within 1 week" | "Within 2 weeks" | "Within 1 month";

  /**
   * Medium/Low Severity Flaws:
   * linkId: 119144494365
   * FHIR type: choice
   * Section: SI.L1-B.1.XII – Flaw Remediation > Flaw Correction Timeline > What are your target timeframes for correcting identified flaws?
   * Options: "Within 1 month", "Within 1 quarter", "Next scheduled maintenance window"
   * Required: no
   */
  mediumLowSeverityFlaws?: "Within 1 month" | "Within 1 quarter" | "Next scheduled maintenance window";

  /**
   * Notes / Evidence 
   * linkId: 478326704189
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: SI.L1-B.1.XII – Flaw Remediation > Flaw Correction Timeline
   * Required: no
   */
  notesEvidence3?: string;

  /**
   * How are security patches and updates managed?
   * linkId: 896010001522
   * FHIR type: choice
   * Section: SI.L1-B.1.XII – Flaw Remediation > Patch Management Process
   * Options: "Testing in non-production environment before deployment", "Formal change control process", "Rollback procedures in case of issues", "Automated patch deployment capabilities", "Emergency patching procedures for critical flaws", "Documentation of all patches applied"
   * Required: no
   */
  howAreSecurityPatchesAndUpdatesManaged?: ("Testing in non-production environment before deployment" | "Formal change control process" | "Rollback procedures in case of issues" | "Automated patch deployment capabilities" | "Emergency patching procedures for critical flaws" | "Documentation of all patches applied")[];

  /**
   * Additional Notes or Comments
   * linkId: 731360730463
   * FHIR type: text
   * Entry format: Any additional context, challenges, or implementation details...
   * Section: SI.L1-B.1.XII – Flaw Remediation > Patch Management Process
   * Required: no
   */
  additionalNotesOrComments?: string;

  /**
   * Supporting Documentation
   * linkId: 231346071278
   * FHIR type: text
   * Entry format: List or describe the supporting documentation you have available (policies, procedures, scan reports, etc.)...
   * Section: SI.L1-B.1.XII – Flaw Remediation > Patch Management Process
   * Required: no
   */
  supportingDocumentation?: string;

  /**
   * Do you have a malicious code protection policy document?
   * linkId: 892692932760
   * FHIR type: choice
   * Section: SI.L1-B.1.XIII – Malicious Code Protection
   * Options: "Yes", "No"
   * Required: no
   */
  doYouHaveAMaliciousCodeProtectionPolicyDocument?: "Yes" | "No";

  /**
   * Do you need assistance?
   * linkId: 942706169228
   * FHIR type: string
   * Section: SI.L1-B.1.XIII – Malicious Code Protection
   * Required: no
   */
  doYouNeedAssistance?: string;

  /**
   * Select all locations where malicious code protection is implemented:
   * linkId: 457010911238
   * FHIR type: choice
   * Section: SI.L1-B.1.XIII – Malicious Code Protection > Protection Locations
   * Options: "Email Gateway", "Web Proxy/Gateway", "Perimeter Firewall", "VPN Gateway", "Endpoints (Workstations, Laptops)", "Servers", "Mobile Devices"
   * Required: no
   */
  selectAllLocationsWhereMaliciousCodeProtectionIsImplemented?: ("Email Gateway" | "Web Proxy/Gateway" | "Perimeter Firewall" | "VPN Gateway" | "Endpoints (Workstations, Laptops)" | "Servers" | "Mobile Devices")[];

  /**
   * Notes / Evidence 
   * linkId: 388699038922
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: SI.L1-B.1.XIII – Malicious Code Protection > Protection Locations
   * Required: no
   */
  notesEvidence4?: string;

  /**
   * Primary Anti-Malware Product/Solution: e.g., Microsoft Defender, McAfee, Symantec
   * linkId: 149423997720
   * FHIR type: string
   * Entry format: Enter primary anti-malware solution
   * Section: SI.L1-B.1.XIII – Malicious Code Protection > Implementation Details
   * Required: no
   */
  primaryAntiMalwareProductSolutionEGMicrosoftDefenderMcAfeeSymantec?: string;

  /**
   * Anti-Malware Version/Release: Version number or release identifier
   * linkId: 343942743605
   * FHIR type: string
   * Entry format: Enter version or release identifier
   * Section: SI.L1-B.1.XIII – Malicious Code Protection > Implementation Details
   * Required: no
   */
  antiMalwareVersionReleaseVersionNumberOrReleaseIdentifier?: string;

  /**
   * Implementation Scope: Describe the scope of your anti-malware implementation (e.g., all company endpoints, specific servers)
   * linkId: 581419297519
   * FHIR type: text
   * Entry format: Describe your anti-malware implementation scope...
   * Section: SI.L1-B.1.XIII – Malicious Code Protection > Implementation Details
   * Required: no
   */
  implementationScopeDescribeTheScopeOfYourAntiMalwareImplementationEGAllCompanyEndpointsSpecificServers?: string;

  /**
   * Real-Time Protection Enabled:
   * linkId: 394557514652
   * FHIR type: choice
   * Section: SI.L1-B.1.XIII – Malicious Code Protection > Implementation Details
   * Options: "Yes", "No"
   * Required: no
   */
  realTimeProtectionEnabled?: "Yes" | "No";

  /**
   * Centrally Managed:
   * linkId: 137330973781
   * FHIR type: choice
   * Section: SI.L1-B.1.XIII – Malicious Code Protection > Implementation Details
   * Options: "Yes", "No"
   * Required: no
   */
  centrallyManaged?: "Yes" | "No";

  /**
   * Additional Notes or Comments
   * linkId: ************
   * FHIR type: text
   * Entry format: Any additional context, challenges, or implementation details...
   * Section: SI.L1-B.1.XIII – Malicious Code Protection
   * Required: no
   */
  additionalNotesOrComments2?: string;

  /**
   * Supporting Documentation
   * linkId: ************
   * FHIR type: text
   * Entry format: List or describe the supporting documentation you have available(policies, configuration guides, deployment records, etc.)...
   * Section: SI.L1-B.1.XIII – Malicious Code Protection
   * Required: no
   */
  supportingDocumentation2?: string;

  /**
   * How frequently are malicious code protection mechanisms updated?
   * linkId: ************
   * FHIR type: choice
   * Section: SI.L1-B.1.XIV – Update Malicious Code Protection > Update Frequency
   * Options: "Real-time updates (as available)", "Hourly", "Daily", "Weekly", "Manual updates only"
   * Required: no
   */
  howFrequentlyAreMaliciousCodeProtectionMechanismsUpdated?: "Real-time updates (as available)" | "Hourly" | "Daily" | "Weekly" | "Manual updates only";

  /**
   * Notes / Evidence 
   * linkId: ************
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: SI.L1-B.1.XIV – Update Malicious Code Protection > Update Frequency
   * Required: no
   */
  notesEvidence5?: string;

  /**
   * How are malicious code protection updates managed?
   * linkId: ************
   * FHIR type: choice
   * Section: SI.L1-B.1.XIV – Update Malicious Code Protection > Update Management Process
   * Options: "Automatic updates enabled", "Centralized update management system", "Verification of successful updates", "Rollback capability for problematic updates", "Testing of updates before deployment", "Notification of update status and failures"
   * Required: no
   */
  howAreMaliciousCodeProtectionUpdatesManaged?: ("Automatic updates enabled" | "Centralized update management system" | "Verification of successful updates" | "Rollback capability for problematic updates" | "Testing of updates before deployment" | "Notification of update status and failures")[];

  /**
   * Notes / Evidence 
   * linkId: 222629834244
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: SI.L1-B.1.XIV – Update Malicious Code Protection > Update Management Process
   * Required: no
   */
  notesEvidence6?: string;

  /**
   * Which external sources are scanned?
   * linkId: 146442608630
   * FHIR type: choice
   * Section: SI.L1-B.1.XIV – Update Malicious Code Protection > Update Management Process
   * Options: "Internet Downloads", "Email Attachments", "Removable Media", "Cloud Storage", "Network Shares", "Other External Sources"
   * Required: no
   */
  whichExternalSourcesAreScanned?: ("Internet Downloads" | "Email Attachments" | "Removable Media" | "Cloud Storage" | "Network Shares" | "Other External Sources")[];

  /**
   * Notes / Evidence 
   * linkId: 692565504391
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: SI.L1-B.1.XIV – Update Malicious Code Protection > Update Management Process
   * Required: no
   */
  notesEvidence7?: string;

  /**
   * Additional Notes or Comments
   * linkId: 660268414578
   * FHIR type: text
   * Entry format: Any additional context challenges, or implementation details...
   * Section: SI.L1-B.1.XIV – Update Malicious Code Protection
   * Required: no
   */
  additionalNotesOrComments3?: string;

  /**
   * Supporting Documentation
   * linkId: 717091491475
   * FHIR type: text
   * Entry format: List o describe the supporting documentation you have available (update procedures, verification logs, rollback plans, etc.)...
   * Section: SI.L1-B.1.XIV – Update Malicious Code Protection
   * Required: no
   */
  supportingDocumentation3?: string;

  /**
   * Do you have a system scanning policy documentation, file scanning policy, and scanning procedure documentation?
   * linkId: 470606272303
   * FHIR type: choice
   * Section: SI.L1-B.1.XV – System & File Scanning
   * Options: "Yes", "No"
   * Required: no
   */
  doYouHaveASystemScanningPolicyDocumentationFileScanningPolicyAndScanningProcedureDocumentation?: "Yes" | "No";

  /**
   * Does your organization have antivirus/anti-malware software installed on all systems?
   * linkId: 189466095401
   * FHIR type: choice
   * Section: SI.L1-B.1.XV – System & File Scanning > Anti-Malware Implementation
   * Options: "Yes", "No", "Partially (some systems only)"
   * Required: no
   */
  doesYourOrganizationHaveAntivirusAntiMalwareSoftwareInstalledOnAllSystems?: "Yes" | "No" | "Partially (some systems only)";

  /**
   * What antivirus/anti-malware solution is currently deployed? e.g., Microsoft Defender, Norton, McAfee, etc.
   * linkId: 694425083943
   * FHIR type: string
   * Entry format: Enter your current antivirus/anti-malware solution
   * Section: SI.L1-B.1.XV – System & File Scanning > Anti-Malware Implementation
   * Required: no
   */
  whatAntivirusAntiMalwareSolutionIsCurrentlyDeployedEGMicrosoftDefenderNortonMcAfeeEtc?: string;

  /**
   * How frequently are full system scans conducted?
   * linkId: 508929065591
   * FHIR type: choice
   * Entry format: -- Select Frequency --
   * Section: SI.L1-B.1.XV – System & File Scanning > Periodic Scanning Implementation
   * Options: "Daily", "Weekily", "Bi-weekly", "Monthly", "Quarterly", "Custom Schedule"
   * Required: no
   */
  howFrequentlyAreFullSystemScansConducted?: "Daily" | "Weekily" | "Bi-weekly" | "Monthly" | "Quarterly" | "Custom Schedule";

  /**
   * What level of thoroughness is used for periodic scans?
   * linkId: 889472415570
   * FHIR type: choice
   * Entry format: -- Select Scan Depth --
   * Section: SI.L1-B.1.XV – System & File Scanning > Periodic Scanning Implementation
   * Options: "Quick Scan (critical files only)", "Standard Scan (system files and common user directories)", "Full Scan (entire file system)", "Custom Scan (specific directories)"
   * Required: no
   */
  whatLevelOfThoroughnessIsUsedForPeriodicScans?: "Quick Scan (critical files only)" | "Standard Scan (system files and common user directories)" | "Full Scan (entire file system)" | "Custom Scan (specific directories)";

  /**
   * Are files from external sources scanned in real-time?
   * linkId: 740865411316
   * FHIR type: choice
   * Section: SI.L1-B.1.XV – System & File Scanning > Real-time Scanning & File Integrity
   * Options: "Yes", "No", "Partially (some sources only)"
   * Required: no
   */
  areFilesFromExternalSourcesScannedInRealTime?: "Yes" | "No" | "Partially (some sources only)";

  /**
   * Do you employ file integrity monitoring for critical system files?
   * linkId: 842602142275
   * FHIR type: choice
   * Section: SI.L1-B.1.XV – System & File Scanning > Real-time Scanning & File Integrity
   * Options: "Yes", "No", "Planned"
   * Required: no
   */
  doYouEmployFileIntegrityMonitoringForCriticalSystemFiles?: "Yes" | "No" | "Planned";

  /**
   * How are scan results reviewed and documented? Describe your process for reviewing and documenting scan results...
   * linkId: 707425868010
   * FHIR type: text
   * Entry format: Describe your process for reviewing and documenting scan results...
   * Section: SI.L1-B.1.XV – System & File Scanning > Results Handling & Testing
   * Required: no
   */
  howAreScanResultsReviewedAndDocumentedDescribeYourProcessForReviewingAndDocumentingScanResults?: string;

  /**
   * What is your response timeframe when malware or vulnerabilities are detected?
   * linkId: 986030389075
   * FHIR type: open-choice
   * Entry format: -- Select Response Timeframe --
   * Section: SI.L1-B.1.XV – System & File Scanning > Results Handling & Testing
   * Required: no
   */
  whatIsYourResponseTimeframeWhenMalwareOrVulnerabilitiesAreDetected?: string;

  /**
   * Describe your remediation process for identified issues: Describe your process for remediating issues detected during scanning...
   * linkId: 164191875680
   * FHIR type: text
   * Entry format: Describe your process for remediating issues detected during scanning..
   * Section: SI.L1-B.1.XV – System & File Scanning > Results Handling & Testing
   * Required: no
   */
  describeYourRemediationProcessForIdentifiedIssuesDescribeYourProcessForRemediatingIssuesDetectedDuringScanning?: string;

  /**
   * Has scanning effectiveness been tested?
   * linkId: 967054991522
   * FHIR type: choice
   * Section: SI.L1-B.1.XV – System & File Scanning > Results Handling & Testing
   * Options: "Yes", "No"
   * Required: no
   */
  hasScanningEffectivenessBeenTested?: "Yes" | "No";

  /**
   * Supporting Documentation
   * linkId: 901609884580
   * FHIR type: text
   * Entry format: List or describe the supporting documentation you have available(scanning policies , procedures , scan logs , remediation records, etc.)...
   * Section: SI.L1-B.1.XV – System & File Scanning > Results Handling & Testing
   * Required: no
   */
  supportingDocumentation4?: string;
}

/** Convert an LHC JSON response into a normalized SystemInformationIntegrity object. */
export function systemInformationIntegrityLhcFormResponseAdapter(input: Any): SystemInformationIntegrity {
  return {
    howDoesYourOrganizationIdentifySystemFlawsAndVulnerabilities: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "758011605310")) as SystemInformationIntegrity["howDoesYourOrganizationIdentifySystemFlawsAndVulnerabilities"],
    notesEvidence: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "135467801033")),
    howAreIdentifiedFlawsReportedAndTracked: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "854540559647")) as SystemInformationIntegrity["howAreIdentifiedFlawsReportedAndTracked"],
    notesEvidence2: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "924286782806")),
    criticalSeverityFlaws: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "885354230428")) as SystemInformationIntegrity["criticalSeverityFlaws"],
    highSeverityFlaws: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "149460684671")) as SystemInformationIntegrity["highSeverityFlaws"],
    mediumLowSeverityFlaws: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "119144494365")) as SystemInformationIntegrity["mediumLowSeverityFlaws"],
    notesEvidence3: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "478326704189")),
    howAreSecurityPatchesAndUpdatesManaged: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "896010001522")) as SystemInformationIntegrity["howAreSecurityPatchesAndUpdatesManaged"],
    additionalNotesOrComments: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "731360730463")),
    supportingDocumentation: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "231346071278")),
    doYouHaveAMaliciousCodeProtectionPolicyDocument: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "892692932760")) as SystemInformationIntegrity["doYouHaveAMaliciousCodeProtectionPolicyDocument"],
    doYouNeedAssistance: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "942706169228")),
    selectAllLocationsWhereMaliciousCodeProtectionIsImplemented: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "457010911238")) as SystemInformationIntegrity["selectAllLocationsWhereMaliciousCodeProtectionIsImplemented"],
    notesEvidence4: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "388699038922")),
    primaryAntiMalwareProductSolutionEGMicrosoftDefenderMcAfeeSymantec: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "149423997720")),
    antiMalwareVersionReleaseVersionNumberOrReleaseIdentifier: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "343942743605")),
    implementationScopeDescribeTheScopeOfYourAntiMalwareImplementationEGAllCompanyEndpointsSpecificServers: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "581419297519")),
    realTimeProtectionEnabled: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "394557514652")) as SystemInformationIntegrity["realTimeProtectionEnabled"],
    centrallyManaged: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "137330973781")) as SystemInformationIntegrity["centrallyManaged"],
    additionalNotesOrComments2: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")),
    supportingDocumentation2: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")),
    howFrequentlyAreMaliciousCodeProtectionMechanismsUpdated: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")) as SystemInformationIntegrity["howFrequentlyAreMaliciousCodeProtectionMechanismsUpdated"],
    notesEvidence5: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")),
    howAreMaliciousCodeProtectionUpdatesManaged: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "************")) as SystemInformationIntegrity["howAreMaliciousCodeProtectionUpdatesManaged"],
    notesEvidence6: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "222629834244")),
    whichExternalSourcesAreScanned: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "146442608630")) as SystemInformationIntegrity["whichExternalSourcesAreScanned"],
    notesEvidence7: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "692565504391")),
    additionalNotesOrComments3: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "660268414578")),
    supportingDocumentation3: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "717091491475")),
    doYouHaveASystemScanningPolicyDocumentationFileScanningPolicyAndScanningProcedureDocumentation: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "470606272303")) as SystemInformationIntegrity["doYouHaveASystemScanningPolicyDocumentationFileScanningPolicyAndScanningProcedureDocumentation"],
    doesYourOrganizationHaveAntivirusAntiMalwareSoftwareInstalledOnAllSystems: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "189466095401")) as SystemInformationIntegrity["doesYourOrganizationHaveAntivirusAntiMalwareSoftwareInstalledOnAllSystems"],
    whatAntivirusAntiMalwareSolutionIsCurrentlyDeployedEGMicrosoftDefenderNortonMcAfeeEtc: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "694425083943")),
    howFrequentlyAreFullSystemScansConducted: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "508929065591")) as SystemInformationIntegrity["howFrequentlyAreFullSystemScansConducted"],
    whatLevelOfThoroughnessIsUsedForPeriodicScans: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "889472415570")) as SystemInformationIntegrity["whatLevelOfThoroughnessIsUsedForPeriodicScans"],
    areFilesFromExternalSourcesScannedInRealTime: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "740865411316")) as SystemInformationIntegrity["areFilesFromExternalSourcesScannedInRealTime"],
    doYouEmployFileIntegrityMonitoringForCriticalSystemFiles: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "842602142275")) as SystemInformationIntegrity["doYouEmployFileIntegrityMonitoringForCriticalSystemFiles"],
    howAreScanResultsReviewedAndDocumentedDescribeYourProcessForReviewingAndDocumentingScanResults: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "707425868010")),
    whatIsYourResponseTimeframeWhenMalwareOrVulnerabilitiesAreDetected: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "986030389075")),
    describeYourRemediationProcessForIdentifiedIssuesDescribeYourProcessForRemediatingIssuesDetectedDuringScanning: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "164191875680")),
    hasScanningEffectivenessBeenTested: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "967054991522")) as SystemInformationIntegrity["hasScanningEffectivenessBeenTested"],
    supportingDocumentation4: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "901609884580")),
  };
}

/** Convert a FHIR QuestionnaireResponse into a normalized SystemInformationIntegrity object. */
export function systemInformationIntegrityFhirQuestionnaireResponseAdapter(qr: Any): SystemInformationIntegrity {
  return {
    howDoesYourOrganizationIdentifySystemFlawsAndVulnerabilities: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "758011605310")) as SystemInformationIntegrity["howDoesYourOrganizationIdentifySystemFlawsAndVulnerabilities"],
    notesEvidence: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "135467801033")),
    howAreIdentifiedFlawsReportedAndTracked: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "854540559647")) as SystemInformationIntegrity["howAreIdentifiedFlawsReportedAndTracked"],
    notesEvidence2: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "924286782806")),
    criticalSeverityFlaws: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "885354230428")) as SystemInformationIntegrity["criticalSeverityFlaws"],
    highSeverityFlaws: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "149460684671")) as SystemInformationIntegrity["highSeverityFlaws"],
    mediumLowSeverityFlaws: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "119144494365")) as SystemInformationIntegrity["mediumLowSeverityFlaws"],
    notesEvidence3: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "478326704189")),
    howAreSecurityPatchesAndUpdatesManaged: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "896010001522")) as SystemInformationIntegrity["howAreSecurityPatchesAndUpdatesManaged"],
    additionalNotesOrComments: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "731360730463")),
    supportingDocumentation: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "231346071278")),
    doYouHaveAMaliciousCodeProtectionPolicyDocument: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "892692932760")) as SystemInformationIntegrity["doYouHaveAMaliciousCodeProtectionPolicyDocument"],
    doYouNeedAssistance: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "942706169228")),
    selectAllLocationsWhereMaliciousCodeProtectionIsImplemented: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "457010911238")) as SystemInformationIntegrity["selectAllLocationsWhereMaliciousCodeProtectionIsImplemented"],
    notesEvidence4: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "388699038922")),
    primaryAntiMalwareProductSolutionEGMicrosoftDefenderMcAfeeSymantec: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "149423997720")),
    antiMalwareVersionReleaseVersionNumberOrReleaseIdentifier: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "343942743605")),
    implementationScopeDescribeTheScopeOfYourAntiMalwareImplementationEGAllCompanyEndpointsSpecificServers: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "581419297519")),
    realTimeProtectionEnabled: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "394557514652")) as SystemInformationIntegrity["realTimeProtectionEnabled"],
    centrallyManaged: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "137330973781")) as SystemInformationIntegrity["centrallyManaged"],
    additionalNotesOrComments2: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")),
    supportingDocumentation2: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")),
    howFrequentlyAreMaliciousCodeProtectionMechanismsUpdated: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")) as SystemInformationIntegrity["howFrequentlyAreMaliciousCodeProtectionMechanismsUpdated"],
    notesEvidence5: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")),
    howAreMaliciousCodeProtectionUpdatesManaged: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "************")) as SystemInformationIntegrity["howAreMaliciousCodeProtectionUpdatesManaged"],
    notesEvidence6: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "222629834244")),
    whichExternalSourcesAreScanned: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "146442608630")) as SystemInformationIntegrity["whichExternalSourcesAreScanned"],
    notesEvidence7: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "692565504391")),
    additionalNotesOrComments3: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "660268414578")),
    supportingDocumentation3: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "717091491475")),
    doYouHaveASystemScanningPolicyDocumentationFileScanningPolicyAndScanningProcedureDocumentation: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "470606272303")) as SystemInformationIntegrity["doYouHaveASystemScanningPolicyDocumentationFileScanningPolicyAndScanningProcedureDocumentation"],
    doesYourOrganizationHaveAntivirusAntiMalwareSoftwareInstalledOnAllSystems: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "189466095401")) as SystemInformationIntegrity["doesYourOrganizationHaveAntivirusAntiMalwareSoftwareInstalledOnAllSystems"],
    whatAntivirusAntiMalwareSolutionIsCurrentlyDeployedEGMicrosoftDefenderNortonMcAfeeEtc: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "694425083943")),
    howFrequentlyAreFullSystemScansConducted: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "508929065591")) as SystemInformationIntegrity["howFrequentlyAreFullSystemScansConducted"],
    whatLevelOfThoroughnessIsUsedForPeriodicScans: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "889472415570")) as SystemInformationIntegrity["whatLevelOfThoroughnessIsUsedForPeriodicScans"],
    areFilesFromExternalSourcesScannedInRealTime: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "740865411316")) as SystemInformationIntegrity["areFilesFromExternalSourcesScannedInRealTime"],
    doYouEmployFileIntegrityMonitoringForCriticalSystemFiles: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "842602142275")) as SystemInformationIntegrity["doYouEmployFileIntegrityMonitoringForCriticalSystemFiles"],
    howAreScanResultsReviewedAndDocumentedDescribeYourProcessForReviewingAndDocumentingScanResults: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "707425868010")),
    whatIsYourResponseTimeframeWhenMalwareOrVulnerabilitiesAreDetected: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "986030389075")),
    describeYourRemediationProcessForIdentifiedIssuesDescribeYourProcessForRemediatingIssuesDetectedDuringScanning: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "164191875680")),
    hasScanningEffectivenessBeenTested: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "967054991522")) as SystemInformationIntegrity["hasScanningEffectivenessBeenTested"],
    supportingDocumentation4: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "901609884580")),
  };
}

/**
 * NOTE TO DEVELOPERS:
 * -------------------
 * This Interpreter class is provided only as an EXAMPLE scaffold to demonstrate
 * how to consume the normalized type-safe interface generated for this
 * Questionnaire. It shows minimal factories (`fromLhc`, `fromQuestionnaireResponse`)
 * and convenience methods (`validateRequiredFields`, `assessReadiness`) but it is
 * NOT intended for production use.
 *
 * In real applications:
 * - Treat this class as SAMPLE CODE only.
 * - Replace or extend it with proper business logic, rules engines, or validation
 *   frameworks appropriate to your domain.
 * - Do not rely on the simplistic readiness scoring or validation in production
 *   scenarios; they are illustrative, not authoritative.
 *
 * Best practice: use the generated TypeScript interface (`SystemInformationIntegrity`) as your
 * contract for normalized data, then integrate with your own rules processors,
 * compliance engines, or plain TypeScript/JavaScript functions as needed.
 */
export class SystemInformationIntegrityInterpreter {
  constructor(readonly value: SystemInformationIntegrity) { }

  /** Factory: build from LHC JSON. */
  static fromLhcFormResponse(input: Any): SystemInformationIntegrityInterpreter {
    return new SystemInformationIntegrityInterpreter(systemInformationIntegrityLhcFormResponseAdapter(input));
  }

  /** Factory: build from FHIR QuestionnaireResponse. */
  static fromQuestionnaireResponse(qr: Any): SystemInformationIntegrityInterpreter {
    return new SystemInformationIntegrityInterpreter(systemInformationIntegrityFhirQuestionnaireResponseAdapter(qr));
  }

  /** Check required fields and report any missing or blank. */
  validateRequiredFields(): { ok: boolean; missing: Array<keyof SystemInformationIntegrity> } {
    const missing: Array<keyof SystemInformationIntegrity> = [];
    const req: Array<keyof SystemInformationIntegrity> = [];
    for (const k of req) {
      const v = (this.value as Any)[k];
      if (rt.isBlank(v)) missing.push(k);
    }
    return { ok: missing.length === 0, missing };
  }

  /**
   * Assess readiness with a simple completeness score. This is meant to be
   * used to help understand how complete the types are and serves as an
   * example of how to use the generated code.
   * - requiredCovered: percentage of required fields that are non-blank
   * - overallFilled: count of non-blank fields among all known properties
   */
  assessReadiness(): {
    formTitle: string;
    requiredCovered: number; // 0..1
    totalRequired: number;
    totalFilled: number;
    totalFields: number;
    missingRequired: Array<keyof SystemInformationIntegrity>;
  } {
    const req: Array<keyof SystemInformationIntegrity> = [];
    const all: Array<keyof SystemInformationIntegrity> = ["howDoesYourOrganizationIdentifySystemFlawsAndVulnerabilities", "notesEvidence", "howAreIdentifiedFlawsReportedAndTracked", "notesEvidence2", "criticalSeverityFlaws", "highSeverityFlaws", "mediumLowSeverityFlaws", "notesEvidence3", "howAreSecurityPatchesAndUpdatesManaged", "additionalNotesOrComments", "supportingDocumentation", "doYouHaveAMaliciousCodeProtectionPolicyDocument", "doYouNeedAssistance", "selectAllLocationsWhereMaliciousCodeProtectionIsImplemented", "notesEvidence4", "primaryAntiMalwareProductSolutionEGMicrosoftDefenderMcAfeeSymantec", "antiMalwareVersionReleaseVersionNumberOrReleaseIdentifier", "implementationScopeDescribeTheScopeOfYourAntiMalwareImplementationEGAllCompanyEndpointsSpecificServers", "realTimeProtectionEnabled", "centrallyManaged", "additionalNotesOrComments2", "supportingDocumentation2", "howFrequentlyAreMaliciousCodeProtectionMechanismsUpdated", "notesEvidence5", "howAreMaliciousCodeProtectionUpdatesManaged", "notesEvidence6", "whichExternalSourcesAreScanned", "notesEvidence7", "additionalNotesOrComments3", "supportingDocumentation3", "doYouHaveASystemScanningPolicyDocumentationFileScanningPolicyAndScanningProcedureDocumentation", "doesYourOrganizationHaveAntivirusAntiMalwareSoftwareInstalledOnAllSystems", "whatAntivirusAntiMalwareSolutionIsCurrentlyDeployedEGMicrosoftDefenderNortonMcAfeeEtc", "howFrequentlyAreFullSystemScansConducted", "whatLevelOfThoroughnessIsUsedForPeriodicScans", "areFilesFromExternalSourcesScannedInRealTime", "doYouEmployFileIntegrityMonitoringForCriticalSystemFiles", "howAreScanResultsReviewedAndDocumentedDescribeYourProcessForReviewingAndDocumentingScanResults", "whatIsYourResponseTimeframeWhenMalwareOrVulnerabilitiesAreDetected", "describeYourRemediationProcessForIdentifiedIssuesDescribeYourProcessForRemediatingIssuesDetectedDuringScanning", "hasScanningEffectivenessBeenTested", "supportingDocumentation4"];

    let reqFilled = 0;
    const missingReq: Array<keyof SystemInformationIntegrity> = [];
    for (const k of req) {
      const v = (this.value as Any)[k];
      if (!rt.isBlank(v)) reqFilled++;
      else missingReq.push(k);
    }

    let totalFilled = 0;
    for (const k of all) {
      if (!rt.isBlank((this.value as Any)[k])) totalFilled++;
    }

    return {
      formTitle: "System & Information Integrity",
      requiredCovered: req.length ? reqFilled / req.length : 1,
      totalRequired: req.length,
      totalFilled,
      totalFields: all.length,
      missingRequired: missingReq,
    };
  }
}

/** The original source */
export const systemInformationIntegritySource = `{
  "resourceType": "Questionnaire",
  "meta": {
    "profile": [
      "http://hl7.org/fhir/4.0/StructureDefinition/Questionnaire"
    ],
    "tag": [
      {
        "display": "Vulnerability Management"
      },
      {
        "display": "System Integrity Monitoring"
      }
    ]
  },
  "extension": [
    {
      "url": "http://hl7.org/fhir/StructureDefinition/variable",
      "valueExpression": {
        "name": "displayOrder",
        "language": "text/fhirpath",
        "expression": "7",
        "extension": [
          {
            "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
            "valueString": "simple"
          },
          {
            "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
            "valueString": "7"
          }
        ]
      }
    }
  ],
  "name": "system_information_integrity",
  "title": "System & Information Integrity",
  "status": "draft",
  "date": "2025-08-26",
  "publisher": "Netspective",
  "description": "System & Information Integrity (Identify, report, and correct information system flaws)",
  "purpose": "This assessment ensures that systems are protected against malicious code and vulnerabilities. It includes timely identification and correction of flaws, deployment of security updates, monitoring for threats, and maintaining updated anti-malware capabilities. It also reviews the organization's ability to conduct scans and inspections on external files to prevent the introduction of malicious software.",
  "approvalDate": "2025-08-26",
  "lastReviewDate": "2025-08-26",
  "item": [
    {
      "item": [
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "100",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "100"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "758011605310",
              "text": "How does your organization identify system flaws and vulnerabilities?",
              "repeats": true,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Automated vulnerability scanning"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Vendor security notifications and bulletins"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 10
                    }
                  ],
                  "valueCoding": {
                    "display": "Penetration testing"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 10
                    }
                  ],
                  "valueCoding": {
                    "display": "Regular security assessments"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Threat intelligence feeds"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Incident response and forensics"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "135467801033",
              "text": "Notes / Evidence "
            }
          ],
          "type": "group",
          "linkId": "544004255685",
          "prefix": "1.",
          "text": "Flaw Identification Process"
        },
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "100",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "100"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "854540559647",
              "text": "How are identified flaws reported and tracked?",
              "repeats": true,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": " Formal tracking system or database"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Automatic management notification"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Risk assessment and prioritization"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Communication to affected stakeholders"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Detailed documentation of findings"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "924286782806",
              "text": "Notes / Evidence "
            },
            {
              "linkId": "603452357063_helpText",
              "type": "display",
              "text": "Establish processes to identify, report, and track system flaws and vulnerabilities until they are resolved.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "603452357063",
          "prefix": "2.",
          "text": "Flaw Reporting and Tracking"
        },
        {
          "item": [
            {
              "item": [
                {
                  "type": "choice",
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/variable",
                      "valueExpression": {
                        "name": "weight",
                        "language": "text/fhirpath",
                        "expression": "1",
                        "extension": [
                          {
                            "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                            "valueString": "simple"
                          },
                          {
                            "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                            "valueString": "1"
                          }
                        ]
                      }
                    },
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                      "valueCodeableConcept": {
                        "coding": [
                          {
                            "system": "http://hl7.org/fhir/questionnaire-item-control",
                            "code": "radio-button",
                            "display": "Radio Button"
                          }
                        ]
                      }
                    }
                  ],
                  "linkId": "885354230428",
                  "text": "Critical Severity Flaws:",
                  "repeats": false,
                  "answerOption": [
                    {
                      "extension": [
                        {
                          "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                          "valueDecimal": 100
                        }
                      ],
                      "valueCoding": {
                        "display": "Immediate (within hours)"
                      }
                    },
                    {
                      "extension": [
                        {
                          "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                          "valueDecimal": 75
                        }
                      ],
                      "valueCoding": {
                        "display": "Within 24 hours"
                      }
                    },
                    {
                      "extension": [
                        {
                          "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                          "valueDecimal": 50
                        }
                      ],
                      "valueCoding": {
                        "display": "Within 72 hours"
                      }
                    },
                    {
                      "extension": [
                        {
                          "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                          "valueDecimal": 25
                        }
                      ],
                      "valueCoding": {
                        "display": " Within 1 week"
                      }
                    }
                  ]
                },
                {
                  "type": "choice",
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/variable",
                      "valueExpression": {
                        "name": "weight",
                        "language": "text/fhirpath",
                        "expression": "1",
                        "extension": [
                          {
                            "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                            "valueString": "simple"
                          },
                          {
                            "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                            "valueString": "1"
                          }
                        ]
                      }
                    },
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                      "valueCodeableConcept": {
                        "coding": [
                          {
                            "system": "http://hl7.org/fhir/questionnaire-item-control",
                            "code": "radio-button",
                            "display": "Radio Button"
                          }
                        ]
                      }
                    }
                  ],
                  "linkId": "149460684671",
                  "text": "High Severity Flaws:",
                  "repeats": false,
                  "answerOption": [
                    {
                      "extension": [
                        {
                          "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                          "valueDecimal": 100
                        }
                      ],
                      "valueCoding": {
                        "display": "Within 1 week"
                      }
                    },
                    {
                      "extension": [
                        {
                          "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                          "valueDecimal": 50
                        }
                      ],
                      "valueCoding": {
                        "display": "Within 2 weeks"
                      }
                    },
                    {
                      "extension": [
                        {
                          "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                          "valueDecimal": 25
                        }
                      ],
                      "valueCoding": {
                        "display": "Within 1 month"
                      }
                    }
                  ]
                },
                {
                  "type": "choice",
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/variable",
                      "valueExpression": {
                        "name": "weight",
                        "language": "text/fhirpath",
                        "expression": "1",
                        "extension": [
                          {
                            "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                            "valueString": "simple"
                          },
                          {
                            "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                            "valueString": "1"
                          }
                        ]
                      }
                    },
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                      "valueCodeableConcept": {
                        "coding": [
                          {
                            "system": "http://hl7.org/fhir/questionnaire-item-control",
                            "code": "radio-button",
                            "display": "Radio Button"
                          }
                        ]
                      }
                    }
                  ],
                  "linkId": "119144494365",
                  "text": "Medium/Low Severity Flaws:",
                  "repeats": false,
                  "answerOption": [
                    {
                      "extension": [
                        {
                          "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                          "valueDecimal": 100
                        }
                      ],
                      "valueCoding": {
                        "display": "Within 1 month"
                      }
                    },
                    {
                      "extension": [
                        {
                          "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                          "valueDecimal": 50
                        }
                      ],
                      "valueCoding": {
                        "display": "Within 1 quarter"
                      }
                    },
                    {
                      "extension": [
                        {
                          "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                          "valueDecimal": 25
                        }
                      ],
                      "valueCoding": {
                        "display": "Next scheduled maintenance window"
                      }
                    }
                  ]
                }
              ],
              "type": "group",
              "linkId": "802989461197",
              "text": "What are your target timeframes for correcting identified flaws?"
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "478326704189",
              "text": "Notes / Evidence "
            },
            {
              "linkId": "702845194175_helpText",
              "type": "display",
              "text": "Define and follow timelines to promptly address and fix identified system vulnerabilities to reduce security risks.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "702845194175",
          "prefix": "3.",
          "text": "Flaw Correction Timeline"
        },
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "100",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "100"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "896010001522",
              "text": "How are security patches and updates managed?",
              "repeats": true,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Testing in non-production environment before deployment"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Formal change control process"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 10
                    }
                  ],
                  "valueCoding": {
                    "display": "Rollback procedures in case of issues"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 10
                    }
                  ],
                  "valueCoding": {
                    "display": "Automated patch deployment capabilities"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Emergency patching procedures for critical flaws"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Documentation of all patches applied"
                  }
                }
              ]
            },
            {
              "type": "text",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Any additional context, challenges, or implementation details..."
                }
              ],
              "linkId": "731360730463",
              "text": "Additional Notes or Comments"
            },
            {
              "type": "text",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "List or describe the supporting documentation you have available (policies, procedures, scan reports, etc.)..."
                }
              ],
              "linkId": "231346071278",
              "text": "Supporting Documentation",
              "item": [
                {
                  "linkId": "231346071278_helpText",
                  "type": "display",
                  "text": "Note: Have documentation available that demonstrates your flaw remediation processes, vulnerability scanning procedures, and patch management policies.",
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                      "valueCodeableConcept": {
                        "coding": [
                          {
                            "system": "http://hl7.org/fhir/questionnaire-item-control",
                            "code": "help",
                            "display": "Help-Button"
                          }
                        ],
                        "text": "Help-Button"
                      }
                    }
                  ]
                }
              ]
            },
            {
              "linkId": "535096646220_helpText",
              "type": "display",
              "text": "Implement procedures to regularly apply updates and patches to systems to protect against known vulnerabilities.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "535096646220",
          "prefix": "4.",
          "text": "Patch Management Process"
        },
        {
          "linkId": "350961856234_helpText",
          "type": "display",
          "text": "Identify, report, and correct information and information system flaws in a timely manner",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "help",
                    "display": "Help-Button"
                  }
                ],
                "text": "Help-Button"
              }
            }
          ]
        }
      ],
      "type": "group",
      "linkId": "350961856234",
      "text": "SI.L1-B.1.XII – Flaw Remediation"
    },
    {
      "item": [
        {
          "item": [
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "514312894888",
              "text": "Notes / Evidence "
            }
          ],
          "type": "choice",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/variable",
              "valueExpression": {
                "name": "weight",
                "language": "text/fhirpath",
                "expression": "1",
                "extension": [
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                    "valueString": "simple"
                  },
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                    "valueString": "1"
                  }
                ]
              }
            },
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "radio-button",
                    "display": "Radio Button"
                  }
                ]
              }
            }
          ],
          "linkId": "892692932760",
          "text": "Do you have a malicious code protection policy document?",
          "repeats": false,
          "answerOption": [
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 100
                }
              ],
              "valueCoding": {
                "display": "Yes"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 0
                }
              ],
              "valueCoding": {
                "display": "No"
              }
            }
          ]
        },
        {
          "item": [
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "342802754726",
              "text": "Notes / Evidence "
            }
          ],
          "type": "string",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "radio-button",
                    "display": "Radio Button"
                  }
                ]
              }
            }
          ],
          "linkId": "942706169228",
          "text": "Do you need assistance?",
          "repeats": false,
          "answerOption": [
            {
              "valueString": "Yes"
            },
            {
              "valueString": "No"
            }
          ]
        },
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "100",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "100"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "457010911238",
              "text": "Select all locations where malicious code protection is implemented:",
              "repeats": true,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Email Gateway"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 10
                    }
                  ],
                  "valueCoding": {
                    "display": "Web Proxy/Gateway"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Perimeter Firewall"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 10
                    }
                  ],
                  "valueCoding": {
                    "display": "VPN Gateway"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 10
                    }
                  ],
                  "valueCoding": {
                    "display": "Endpoints (Workstations, Laptops)"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Servers"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 10
                    }
                  ],
                  "valueCoding": {
                    "display": "Mobile Devices"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "388699038922",
              "text": "Notes / Evidence "
            },
            {
              "linkId": "120577885697_helpText",
              "type": "display",
              "text": "Identify and secure critical points in systems and networks where integrity controls must be applied to prevent unauthorized changes.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "120577885697",
          "prefix": "1.",
          "text": "Protection Locations"
        },
        {
          "item": [
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Enter primary anti-malware solution"
                }
              ],
              "linkId": "149423997720",
              "text": "Primary Anti-Malware Product/Solution: e.g., Microsoft Defender, McAfee, Symantec"
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Enter version or release identifier"
                }
              ],
              "linkId": "343942743605",
              "text": "Anti-Malware Version/Release: Version number or release identifier"
            },
            {
              "type": "text",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Describe your anti-malware implementation scope..."
                }
              ],
              "linkId": "581419297519",
              "text": "Implementation Scope: Describe the scope of your anti-malware implementation (e.g., all company endpoints, specific servers)"
            },
            {
              "item": [
                {
                  "type": "string",
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                      "valueString": "Type your comments here..."
                    }
                  ],
                  "linkId": "466807023155",
                  "text": "Notes / Evidence "
                }
              ],
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "radio-button",
                        "display": "Radio Button"
                      }
                    ]
                  }
                }
              ],
              "linkId": "394557514652",
              "text": "Real-Time Protection Enabled:",
              "repeats": false,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 100
                    }
                  ],
                  "valueCoding": {
                    "display": "Yes"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 0
                    }
                  ],
                  "valueCoding": {
                    "display": "No"
                  }
                }
              ]
            },
            {
              "item": [
                {
                  "type": "string",
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                      "valueString": "Type your comments here..."
                    }
                  ],
                  "linkId": "266083093634",
                  "text": "Notes / Evidence "
                }
              ],
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "radio-button",
                        "display": "Radio Button"
                      }
                    ]
                  }
                }
              ],
              "linkId": "137330973781",
              "text": "Centrally Managed:",
              "repeats": false,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 100
                    }
                  ],
                  "valueCoding": {
                    "display": "Yes"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 0
                    }
                  ],
                  "valueCoding": {
                    "display": "No"
                  }
                }
              ]
            },
            {
              "linkId": "123297792461_helpText",
              "type": "display",
              "text": "Deploy and maintain tools and processes to detect, prevent, and respond to malware infections in your systems.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
              "valueString": "Enter primary anti-malware solution"
            }
          ],
          "linkId": "123297792461",
          "prefix": "2.",
          "text": "Implementation Details"
        },
        {
          "type": "text",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
              "valueString": "Any additional context, challenges, or implementation details..."
            }
          ],
          "linkId": "************",
          "text": "Additional Notes or Comments"
        },
        {
          "type": "text",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
              "valueString": "List or describe the supporting documentation you have available(policies, configuration guides, deployment records, etc.)..."
            }
          ],
          "linkId": "************",
          "text": "Supporting Documentation",
          "item": [
            {
              "linkId": "************_helpText",
              "type": "display",
              "text": "Note: Have documentation available that demonstrates your malicious code protection policies, anti-malware configurations, and deployment procedures.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ]
        },
        {
          "linkId": "340771388729_helpText",
          "type": "display",
          "text": "Provide protection from malicious code at appropriate locations within organizational information systems",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "help",
                    "display": "Help-Button"
                  }
                ],
                "text": "Help-Button"
              }
            }
          ]
        }
      ],
      "type": "group",
      "linkId": "340771388729",
      "text": "SI.L1-B.1.XIII – Malicious Code Protection"
    },
    {
      "item": [
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "radio-button",
                        "display": "Radio Button"
                      }
                    ]
                  }
                }
              ],
              "linkId": "************",
              "text": "How frequently are malicious code protection mechanisms updated?",
              "repeats": false,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 100
                    }
                  ],
                  "valueCoding": {
                    "display": "Real-time updates (as available)"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 75
                    }
                  ],
                  "valueCoding": {
                    "display": "Hourly"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 50
                    }
                  ],
                  "valueCoding": {
                    "display": "Daily"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 25
                    }
                  ],
                  "valueCoding": {
                    "display": "Weekly"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 15
                    }
                  ],
                  "valueCoding": {
                    "display": "Manual updates only"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "************",
              "text": "Notes / Evidence "
            },
            {
              "linkId": "370529733824_helpText",
              "type": "display",
              "text": "Regularly update malware protection tools and definitions to ensure defense against the latest threats.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "370529733824",
          "prefix": "1.",
          "text": "Update Frequency"
        },
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "100",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "100"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "************",
              "text": "How are malicious code protection updates managed?",
              "repeats": true,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Automatic updates enabled"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Centralized update management system"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 10
                    }
                  ],
                  "valueCoding": {
                    "display": "Verification of successful updates"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 10
                    }
                  ],
                  "valueCoding": {
                    "display": "Rollback capability for problematic updates"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Testing of updates before deployment"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Notification of update status and failures"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "222629834244",
              "text": "Notes / Evidence "
            },
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "100",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "100"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "146442608630",
              "text": "Which external sources are scanned?",
              "repeats": true,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 10
                    }
                  ],
                  "valueCoding": {
                    "display": "Internet Downloads"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Email Attachments"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 10
                    }
                  ],
                  "valueCoding": {
                    "display": "Removable Media"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Cloud Storage"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Network Shares"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Other External Sources"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "692565504391",
              "text": "Notes / Evidence "
            },
            {
              "linkId": "400782620614_helpText",
              "type": "display",
              "text": "Establish procedures to manage and verify timely updates to malicious code protection systems.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "400782620614",
          "prefix": "2.",
          "text": "Update Management Process"
        },
        {
          "type": "text",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
              "valueString": "Any additional context challenges, or implementation details..."
            }
          ],
          "linkId": "660268414578",
          "text": "Additional Notes or Comments"
        },
        {
          "type": "text",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
              "valueString": "List o describe the supporting documentation you have available (update procedures, verification logs, rollback plans, etc.)..."
            }
          ],
          "linkId": "717091491475",
          "text": "Supporting Documentation",
          "item": [
            {
              "linkId": "717091491475_helpText",
              "type": "display",
              "text": "Note: Have documentation available that demonstrates your update management procedures, verification processes, and rollback capabilities.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ]
        },
        {
          "linkId": "363972470334_helpText",
          "type": "display",
          "text": "Update malicious code protection mechanisms when new releases are available",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "help",
                    "display": "Help-Button"
                  }
                ],
                "text": "Help-Button"
              }
            }
          ]
        }
      ],
      "type": "group",
      "linkId": "363972470334",
      "text": "SI.L1-B.1.XIV – Update Malicious Code Protection"
    },
    {
      "item": [
        {
          "item": [
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "646475656187",
              "text": "Notes / Evidence "
            }
          ],
          "type": "choice",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "radio-button",
                    "display": "Radio Button"
                  }
                ]
              }
            }
          ],
          "linkId": "470606272303",
          "text": "Do you have a system scanning policy documentation, file scanning policy, and scanning procedure documentation?",
          "repeats": false,
          "answerOption": [
            {
              "valueCoding": {
                "display": "Yes"
              }
            },
            {
              "valueCoding": {
                "display": "No"
              }
            }
          ]
        },
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "radio-button",
                        "display": "Radio Button"
                      }
                    ]
                  }
                }
              ],
              "linkId": "189466095401",
              "text": "Does your organization have antivirus/anti-malware software installed on all systems?",
              "repeats": false,
              "answerOption": [
                {
                  "valueCoding": {
                    "display": "Yes"
                  }
                },
                {
                  "valueCoding": {
                    "display": "No"
                  }
                },
                {
                  "valueCoding": {
                    "display": "Partially (some systems only)"
                  }
                }
              ],
              "item": [
                {
                  "type": "string",
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                      "valueString": "Type your comments here..."
                    }
                  ],
                  "linkId": "720788097516",
                  "text": "Notes / Evidence "
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Enter your current antivirus/anti-malware solution"
                }
              ],
              "linkId": "694425083943",
              "text": "What antivirus/anti-malware solution is currently deployed? e.g., Microsoft Defender, Norton, McAfee, etc."
            },
            {
              "linkId": "359679551926_helpText",
              "type": "display",
              "text": "Deploy anti-malware solutions to regularly scan systems and files for malicious software and remove threats promptly.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "359679551926",
          "prefix": "1.",
          "text": "Anti-Malware Implementation"
        },
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "-- Select Frequency --"
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "radio-button",
                        "display": "Radio Button"
                      }
                    ]
                  }
                }
              ],
              "linkId": "508929065591",
              "text": "How frequently are full system scans conducted?",
              "repeats": false,
              "answerOption": [
                {
                  "valueCoding": {
                    "display": "Daily"
                  }
                },
                {
                  "valueCoding": {
                    "display": "Weekily"
                  }
                },
                {
                  "valueCoding": {
                    "display": "Bi-weekly"
                  }
                },
                {
                  "valueCoding": {
                    "display": "Monthly"
                  }
                },
                {
                  "valueCoding": {
                    "display": "Quarterly"
                  }
                },
                {
                  "valueCoding": {
                    "display": "Custom Schedule"
                  }
                }
              ],
              "item": [
                {
                  "type": "string",
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                      "valueString": "Type your comments here..."
                    }
                  ],
                  "linkId": "112297593264",
                  "text": "Notes / Evidence "
                }
              ]
            },
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "-- Select Scan Depth --"
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "radio-button",
                        "display": "Radio Button"
                      }
                    ]
                  }
                }
              ],
              "linkId": "889472415570",
              "text": "What level of thoroughness is used for periodic scans?",
              "repeats": false,
              "answerOption": [
                {
                  "valueCoding": {
                    "display": "Quick Scan (critical files only)"
                  }
                },
                {
                  "valueCoding": {
                    "display": "Standard Scan (system files and common user directories)"
                  }
                },
                {
                  "valueCoding": {
                    "display": "Full Scan (entire file system)"
                  }
                },
                {
                  "valueCoding": {
                    "display": "Custom Scan (specific directories)"
                  }
                }
              ],
              "item": [
                {
                  "type": "string",
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                      "valueString": "Type your comments here..."
                    }
                  ],
                  "linkId": "119870353096",
                  "text": "Notes / Evidence "
                }
              ]
            },
            {
              "linkId": "558460360931_helpText",
              "type": "display",
              "text": "Schedule regular scans of systems and files to detect and address malware or security issues consistently.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "558460360931",
          "prefix": "2.",
          "text": "Periodic Scanning Implementation"
        },
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "radio-button",
                        "display": "Radio Button"
                      }
                    ]
                  }
                }
              ],
              "linkId": "740865411316",
              "text": "Are files from external sources scanned in real-time?",
              "repeats": false,
              "answerOption": [
                {
                  "valueCoding": {
                    "display": "Yes"
                  }
                },
                {
                  "valueCoding": {
                    "display": "No"
                  }
                },
                {
                  "valueCoding": {
                    "display": "Partially (some sources only)"
                  }
                }
              ],
              "item": [
                {
                  "type": "string",
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                      "valueString": "Type your comments here..."
                    }
                  ],
                  "linkId": "499524923589",
                  "text": "Notes / Evidence "
                }
              ]
            },
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "radio-button",
                        "display": "Radio Button"
                      }
                    ]
                  }
                }
              ],
              "linkId": "842602142275",
              "text": "Do you employ file integrity monitoring for critical system files?",
              "repeats": false,
              "answerOption": [
                {
                  "valueCoding": {
                    "display": "Yes"
                  }
                },
                {
                  "valueCoding": {
                    "display": "No"
                  }
                },
                {
                  "valueCoding": {
                    "display": "Planned"
                  }
                }
              ],
              "item": [
                {
                  "type": "string",
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                      "valueString": "Type your comments here..."
                    }
                  ],
                  "linkId": "658767359751",
                  "text": "Notes / Evidence "
                }
              ]
            },
            {
              "linkId": "527252274149_helpText",
              "type": "display",
              "text": "Use real-time scanning to detect threats immediately and monitor file integrity to prevent unauthorized changes.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "527252274149",
          "prefix": "3.",
          "text": "Real-time Scanning & File Integrity"
        },
        {
          "item": [
            {
              "type": "text",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Describe your process for reviewing and documenting scan results..."
                }
              ],
              "linkId": "707425868010",
              "text": "How are scan results reviewed and documented? Describe your process for reviewing and documenting scan results..."
            },
            {
              "type": "open-choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "-- Select Response Timeframe --"
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "radio-button",
                        "display": "Radio Button"
                      }
                    ]
                  }
                }
              ],
              "linkId": "986030389075",
              "text": "What is your response timeframe when malware or vulnerabilities are detected?",
              "repeats": false,
              "answerOption": [
                {
                  "valueCoding": {
                    "display": "Immediate (within hours)"
                  }
                },
                {
                  "valueCoding": {
                    "display": "Within 24 hours"
                  }
                },
                {
                  "valueCoding": {
                    "display": "Within 48 hours"
                  }
                },
                {
                  "valueCoding": {
                    "display": "Within a week"
                  }
                }
              ],
              "item": [
                {
                  "type": "string",
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                      "valueString": "Type your comments here..."
                    }
                  ],
                  "linkId": "936289816171",
                  "text": "Notes / Evidence "
                }
              ]
            },
            {
              "type": "text",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Describe your process for remediating issues detected during scanning.."
                }
              ],
              "linkId": "164191875680",
              "text": "Describe your remediation process for identified issues: Describe your process for remediating issues detected during scanning..."
            },
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "radio-button",
                        "display": "Radio Button"
                      }
                    ]
                  }
                }
              ],
              "linkId": "967054991522",
              "text": "Has scanning effectiveness been tested?",
              "repeats": false,
              "answerOption": [
                {
                  "valueCoding": {
                    "display": "Yes"
                  }
                },
                {
                  "valueCoding": {
                    "display": "No"
                  }
                }
              ],
              "item": [
                {
                  "type": "string",
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                      "valueString": "Type your comments here..."
                    }
                  ],
                  "linkId": "145622130157",
                  "text": "Notes / Evidence "
                }
              ]
            },
            {
              "type": "text",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "List or describe the supporting documentation you have available(scanning policies , procedures , scan logs , remediation records, etc.)..."
                }
              ],
              "linkId": "901609884580",
              "text": "Supporting Documentation",
              "item": [
                {
                  "linkId": "901609884580_helpText",
                  "type": "display",
                  "text": "Note: Have documentation available that demonstrates your scanning policies, procedures, scan results, and remediation processes.",
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                      "valueCodeableConcept": {
                        "coding": [
                          {
                            "system": "http://hl7.org/fhir/questionnaire-item-control",
                            "code": "help",
                            "display": "Help-Button"
                          }
                        ],
                        "text": "Help-Button"
                      }
                    }
                  ]
                }
              ]
            },
            {
              "linkId": "123247885877_helpText",
              "type": "display",
              "text": "Establish procedures to review scan results, respond to detected threats, and test scanning tools for effectiveness.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "123247885877",
          "prefix": "4.",
          "text": "Results Handling & Testing"
        },
        {
          "linkId": "237888898879_helpText",
          "type": "display",
          "text": "Perform periodic scans of the information system and real-time scans of files from external sources",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "help",
                    "display": "Help-Button"
                  }
                ],
                "text": "Help-Button"
              }
            }
          ]
        }
      ],
      "type": "group",
      "linkId": "237888898879",
      "text": "SI.L1-B.1.XV – System & File Scanning"
    }
  ]
}`;