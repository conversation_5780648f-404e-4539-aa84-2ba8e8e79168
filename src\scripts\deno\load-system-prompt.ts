// load-system-prompt.ts
// Run with: deno run --allow-read --allow-net load-system-prompt.ts --slug=my-workspace --file=prompt.md --workspaces-url=AnythingLLM-base-url

import { Command } from "https://deno.land/x/cliffy@v0.25.7/command/mod.ts";

interface CliOptions {
  slug: string;
  file: string;
  apiKey?: string;
  workspacesUrl: string;
}

function logInfo(message: string) {
  console.log(`[INFO] ${message}`);
}

function logError(message: string) {
  console.error(`[ERROR] ${message}`);
}

async function readPromptFromFile(filePath: string): Promise<string | null> {
  try {
    const text = await Deno.readTextFile(filePath);
    logInfo(`Read prompt from ${filePath} (${text.length} characters).`);
    return text.trim();
  } catch (err) {
    logError(`File not found or cannot be read: ${filePath}, ${err}`);
    return null;
  }
}

function buildUpdateUrl(baseUrl: string, workspaceSlug: string): string {
  let url = baseUrl.trim().replace(/\/+$/, ""); 
  url = url.replace(/\/api\/workspaces$/, ""); 
  return `${url}/api/workspace/${workspaceSlug}/update`;
}

async function updateWorkspaceSystemPrompt(
  workspaceSlug: string,
  prompt: string,
  apiKey: string | undefined,
  workspacesUrl: string,
): Promise<boolean> {
  try {
    const updateUrl = buildUpdateUrl(workspacesUrl, workspaceSlug);

    const payload = {
      chatProvider: "default",
      chatMode: "chat",
      openAiHistory: 20,
      openAiPrompt: prompt,
      queryRefusalResponse:
        "There is no relevant information in this workspace to answer your query.",
      openAiTemp: 0.7,
    };

    const headers: HeadersInit = {
      "Content-Type": "application/json",
    };
    if (apiKey) {
      headers["Authorization"] = `Bearer ${apiKey}`;
    }

    const res = await fetch(updateUrl, {
      method: "POST",
      headers,
      body: JSON.stringify(payload),
    });

    if (!res.ok) {
      throw new Error(`HTTP ${res.status}: ${await res.text()}`);
    }

    logInfo(`Updated workspace: ${workspaceSlug}`);
    return true;
  } catch (err) {
    logError(`Failed to update ${workspaceSlug}: ${err}`);
    return false;
  }
}

if (import.meta.main) {
  await new Command()
    .name("load-system-prompt")
    .version("1.0.0")
    .description("Update a workspace's system prompt in AnythingLLM.")
    .option("-s, --slug <slug:string>", "Workspace slug", { required: true })
    .option("-f, --file <file:string>", "Path to prompt file", { required: true })
    .option("-k, --api-key <key:string>", "API key for authentication")
    .option(
      "-u, --workspaces-url <url:string>",
      "Base URL for workspaces API",
      { required: true },
    )
    .action(async (options: CliOptions) => {
      const prompt = await readPromptFromFile(options.file);
      if (!prompt) {
        logError("No prompt loaded. Exiting.");
        Deno.exit(1);
      }

      const updated = await updateWorkspaceSystemPrompt(
        options.slug,
        prompt,
        options.apiKey,
        options.workspacesUrl,
      );

      if (updated) {
        logInfo("Workspace prompt update complete.");
      } else {
        logError("Workspace prompt update failed.");
      }
    })
    .parse(Deno.args);
}
