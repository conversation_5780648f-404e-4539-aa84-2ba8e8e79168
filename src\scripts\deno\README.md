### Run these commands from this path:


```script
wsl -d Ubuntu
sudo apt install sqlite3
```

Generate the RSSD from the ai-context-engineering prompts.
```script
cd www.opsfolio.com/src/scripts/deno
surveilr ingest files -r  www.opsfolio.com/src/ai-context-engineering/
```

```script
cat ai_ctxe_prompt.sql | sqlite3 resource-surveillance.sqlite.db
```

### Run these commands to generate the concatenated prompts

```script
cd deno
deno cache --reload compose-and-persist-prompt.surveilr-SQL.ts
deno cache --reload export_transform.ts

deno run --allow-read --allow-write --allow-net --allow-env compose-and-persist-prompt.surveilr-SQL.ts > output.sql

cat output.sql | sqlite3 resource-surveillance.sqlite.db

deno run --allow-read --allow-write --allow-net --allow-env export-transform.ts
```

### The default path for the generated prompts will be

src/ai-context-engineering/.build
