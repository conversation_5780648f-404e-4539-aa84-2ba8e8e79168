import * as rt from "./r4q-runtime.ts";
/**
 * @file access-control.auto.ts
 * @generated This file was auto-generated from the FHIR R4 Questionnaire "Access Control".
 * Do not edit this file manually; re-run the generator if the source Questionnaire changes.
 * Profiles: http://hl7.org/fhir/4.0/StructureDefinition/Questionnaire
 *
 * Normalizes LHC JSON and FHIR QuestionnaireResponse into the type-safe `AccessControl` interface.
 */

// this is the module signature, used by importers to identify the module
// using r4q-runtime.ts `moduleSignature` function
export const accessControlModuleSignature: rt.ModuleSignature = {
  title: "Access Control",
  filename: "access-control.auto.ts",
  titleCamel: "`accessControl`",
  titlePascal: "`AccessControl`",
  titleKebab: "`access-control`",
  lhcFormResponseAdapterFnName: "accessControlLhcFormResponseAdapter",
  fhirQuestionnaireResponseAdapterFnName: "accessControlFhirQuestionnaireResponseAdapter",
  sourceTextConstName: "accessControlSource",
}

// deno-lint-ignore no-explicit-any
type Any = any;
/**
 * Form Help (from display/help controls):
 * - Establish clear access control rules, including who can access what information, how access is granted, reviewed, and revoked
 * - Maintain a detailed and up-to-date record of all user accounts, including their access levels and status
 * - Grant users and systems only the minimum access necessary to perform their tasks
 * - Regularly review user accounts to verify access is still appropriate and remove or adjust accounts as needed
 * - Establish and follow formal procedures to manage user accounts throughout their lifecycle
 * - Limit information system access to authorized users, processes acting on behalf of authorized users, or devices (including other information systems).
 * - Implement controls to monitor and regulate transactions, ensuring only authorized actions are performed within systems and applications
 * - Limit system functions and capabilities based on user roles to ensure individuals can only perform actions necessary for their job responsibilities
 * - Require formal approval before critical transactions are executed to prevent unauthorized or fraudulent activities.
 * - Limit information system access to the types of transactions and functions that authorized users are permitted to execute.
 * - Manage and secure connections to external systems to protect your network from unauthorized access and data breaches.
 * - Use verification techniques to confirm the identity and security of external connections before allowing access to your systems
 * - Define and enforce restrictions on external connections to minimize exposure and reduce security risks.
 * - Verify and control/limit connections to and use of external information systems.
 * - Secure and monitor systems that are accessible to the public to prevent unauthorized access and data leakage.
 * - Establish regular procedures to review and validate information before it is published or shared
 * - Designate and control who is allowed to publish or distribute organizational information
 * - Control information posted or processed on publicly accessible information systems.
 */
/** Map of normalized property names to their source `linkId`. */
export const accessControlLinkIds = {
  doYouHaveAnAccessControlPolicy: "************",
  doesYourOrganizationHaveADocumentedAccessControlPolicyThatAddresses: "************",
  notesEvidence: "************",
  howManyAccountsAreCurrentlyInYourSystems: "************",
  activeUserAccounts: "************",
  inactiveDisabledUserAccounts: "************",
  serviceAccounts: "************",
  sharedAccounts: "************",
  howIsThePrincipleOfLeastPrivilegeImplemented: "************",
  notesEvidence2: "************",
  howAreAccountLifecycleProcessesManaged: "************",
  notesEvidence3: "************",
  howFrequentlyAreUserAccountsReviewedForValidityAndAppropriateAccess: "************",
  notesEvidence4: "************",
  implementationStatus: "************",
  notesEvidence5: "************",
  howDoYouLimitUserAccessToSpecificTransactionsAndFunctions: "************",
  notesEvidence6: "************",
  whatTypesOfFunctionsAreRestrictedBasedOnUserRoles: "************",
  notesEvidence7: "************",
  howAreHighRiskTransactionsAuthorized: "************",
  notesEvidence8: "************",
  implementationStatus2: "************",
  notesEvidence9: "************",
  whatTypesOfExternalSystemsDoesYourOrganizationConnectTo: "************",
  notesEvidence10: "************",
  howDoYouVerifyExternalSystemConnections: "************",
  notesEvidence11: "************",
  whatLimitationsArePlacedOnExternalConnections: "************",
  notesEvidence12: "************",
  implementationStatus3: "************",
  notesEvidence13: "************",
  whatPubliclyAccessibleSystemsDoesYourOrganizationOperate: "************",
  notesEvidence14: "************",
  howDoYouEnsureFciIsNotPostedOnPublicSystems: "************",
  notesEvidence15: "633971923340",
  whoIsAuthorizedToPostContentToPublicSystems: "624223914711",
  numberOfAuthorizedPersonnel: "374839487767",
  chooseAllThatApply: "177243885107",
  notesEvidence16: "163760226494"
} as const;

/** Normalized view of "Access Control" answers. */
export interface AccessControl {
  /**
   * Do you have an Access Control Policy?
   * linkId: ************
   * FHIR type: choice
   * Section: AC.L1-B.1.I - Authorized Access Control
   * Options: "Yes", " No - if no, would you like help creating one for your company?"
   * Required: no
   */
  doYouHaveAnAccessControlPolicy?: "Yes" | " No - if no, would you like help creating one for your company?";

  /**
   * Does your organization have a documented access control policy that addresses:
   * linkId: ************
   * FHIR type: choice
   * Section: AC.L1-B.1.I - Authorized Access Control > Access Control Policy Elements
   * Options: "Purpose, scope, roles, and responsibilities", "Management commitment", "Coordination among organizational entities", "Compliance requirements"
   * Required: no
   */
  doesYourOrganizationHaveADocumentedAccessControlPolicyThatAddresses?: ("Purpose, scope, roles, and responsibilities" | "Management commitment" | "Coordination among organizational entities" | "Compliance requirements")[];

  /**
   * Notes / Evidence
   * linkId: ************
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: AC.L1-B.1.I - Authorized Access Control > Access Control Policy Elements
   * Required: no
   */
  notesEvidence?: string;

  /**
   * How many accounts are currently in your systems? 
   * linkId: ************
   * FHIR type: display
   * Section: AC.L1-B.1.I - Authorized Access Control > User Account Registry
   * Required: no
   */
  howManyAccountsAreCurrentlyInYourSystems?: string;

  /**
   * Active user accounts:
   * linkId: ************
   * FHIR type: integer
   * Section: AC.L1-B.1.I - Authorized Access Control > User Account Registry
   * Required: no
   */
  activeUserAccounts?: number;

  /**
   * Inactive/disabled user accounts:
   * linkId: ************
   * FHIR type: integer
   * Section: AC.L1-B.1.I - Authorized Access Control > User Account Registry
   * Required: no
   */
  inactiveDisabledUserAccounts?: number;

  /**
   * Service accounts:
   * linkId: ************
   * FHIR type: integer
   * Section: AC.L1-B.1.I - Authorized Access Control > User Account Registry
   * Required: no
   */
  serviceAccounts?: number;

  /**
   * Shared accounts:
   * linkId: ************
   * FHIR type: integer
   * Section: AC.L1-B.1.I - Authorized Access Control > User Account Registry
   * Required: no
   */
  sharedAccounts?: number;

  /**
   * How is the principle of least privilege implemented?
   * linkId: ************
   * FHIR type: choice
   * Section: AC.L1-B.1.I - Authorized Access Control > Principle of Least Privilege Implementation
   * Options: "Fully implemented across all systems", "Partially implemented", "Not implemented"
   * Required: no
   */
  howIsThePrincipleOfLeastPrivilegeImplemented?: "Fully implemented across all systems" | "Partially implemented" | "Not implemented";

  /**
   * Notes / Evidence
   * linkId: ************
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: AC.L1-B.1.I - Authorized Access Control > Principle of Least Privilege Implementation
   * Required: no
   */
  notesEvidence2?: string;

  /**
   * How are account lifecycle processes managed?
   * linkId: ************
   * FHIR type: choice
   * Section: AC.L1-B.1.I - Authorized Access Control > Account Management Processes
   * Options: "Automated identity management system", "Manual process with approval workflow", "Integration with HR systems", " Regular account reviews and recertification"
   * Required: no
   */
  howAreAccountLifecycleProcessesManaged?: ("Automated identity management system" | "Manual process with approval workflow" | "Integration with HR systems" | " Regular account reviews and recertification")[];

  /**
   * Notes / Evidence
   * linkId: ************
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: AC.L1-B.1.I - Authorized Access Control > Account Management Processes
   * Required: no
   */
  notesEvidence3?: string;

  /**
   * How frequently are user accounts reviewed for validity and appropriate access?
   * linkId: ************
   * FHIR type: choice
   * Section: AC.L1-B.1.I - Authorized Access Control > Account Management Processes > Account Review Frequency
   * Options: "Monthly", " Quarterly", "Annually", "Other (specify):"
   * Required: no
   */
  howFrequentlyAreUserAccountsReviewedForValidityAndAppropriateAccess?: "Monthly" | " Quarterly" | "Annually" | "Other (specify):";

  /**
   * Notes / Evidence
   * linkId: ************
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: AC.L1-B.1.I - Authorized Access Control > Account Management Processes > Account Review Frequency
   * Required: no
   */
  notesEvidence4?: string;

  /**
   * Implementation Status
   * linkId: ************
   * FHIR type: choice
   * Section: AC.L1-B.1.II - Transaction & Function Control
   * Options: "Fully Implemented", "Partially Implemented", "Not Implemented"
   * Required: no
   */
  implementationStatus?: "Fully Implemented" | "Partially Implemented" | "Not Implemented";

  /**
   * Notes / Evidence 
   * linkId: ************
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: AC.L1-B.1.II - Transaction & Function Control
   * Required: no
   */
  notesEvidence5?: string;

  /**
   * How do you limit user access to specific transactions and functions?
   * linkId: ************
   * FHIR type: choice
   * Section: AC.L1-B.1.II - Transaction & Function Control > Transaction Control Implementation
   * Options: "Role-based access control (RBAC)", "Function-based permissions (create, read, update, delete)", "Application-level access controls", "Time-based access restrictions", "Location-based access restrictions"
   * Required: no
   */
  howDoYouLimitUserAccessToSpecificTransactionsAndFunctions?: "Role-based access control (RBAC)" | "Function-based permissions (create, read, update, delete)" | "Application-level access controls" | "Time-based access restrictions" | "Location-based access restrictions";

  /**
   * Notes / Evidence 
   * linkId: ************
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: AC.L1-B.1.II - Transaction & Function Control > Transaction Control Implementation
   * Required: no
   */
  notesEvidence6?: string;

  /**
   * What types of functions are restricted based on user roles?
   * linkId: ************
   * FHIR type: choice
   * Section: AC.L1-B.1.II - Transaction & Function Control > Function Restrictions by Role
   * Options: "Administrative functions (user management, system configuration)", "Financial transactions and approvals", "Data export and bulk download functions", "Report generation and access", "System-level commands and utilities"
   * Required: no
   */
  whatTypesOfFunctionsAreRestrictedBasedOnUserRoles?: ("Administrative functions (user management, system configuration)" | "Financial transactions and approvals" | "Data export and bulk download functions" | "Report generation and access" | "System-level commands and utilities")[];

  /**
   * Notes / Evidence 
   * linkId: ************
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: AC.L1-B.1.II - Transaction & Function Control > Function Restrictions by Role
   * Required: no
   */
  notesEvidence7?: string;

  /**
   * How are high-risk transactions authorized?
   * linkId: ************
   * FHIR type: choice
   * Section: AC.L1-B.1.II - Transaction & Function Control > Transaction Authorization Requirements
   * Options: "Manager approval required", "Two-person authorization", "Automated business rules and limits", "No special authorization required"
   * Required: no
   */
  howAreHighRiskTransactionsAuthorized?: ("Manager approval required" | "Two-person authorization" | "Automated business rules and limits" | "No special authorization required")[];

  /**
   * Notes / Evidence 
   * linkId: ************
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: AC.L1-B.1.II - Transaction & Function Control > Transaction Authorization Requirements
   * Required: no
   */
  notesEvidence8?: string;

  /**
   * Implementation Status
   * linkId: ************
   * FHIR type: choice
   * Section: AC.L1-B.1.III - External Connections
   * Options: " Fully Implemented", " Partially Implemented", " Not Implemented"
   * Required: no
   */
  implementationStatus2?: " Fully Implemented" | " Partially Implemented" | " Not Implemented";

  /**
   * Notes / Evidence 
   * linkId: ************
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: AC.L1-B.1.III - External Connections
   * Required: no
   */
  notesEvidence9?: string;

  /**
   * What types of external systems does your organization connect to?
   * linkId: ************
   * FHIR type: choice
   * Section: AC.L1-B.1.III - External Connections >  External System Connections
   * Options: "Cloud services (email, file storage, applications)", "Business partner networks", "Vendor/supplier systems", "Government systems and portals", "Personal devices (BYOD)", "Remote access system", "No external connections"
   * Required: no
   */
  whatTypesOfExternalSystemsDoesYourOrganizationConnectTo?: ("Cloud services (email, file storage, applications)" | "Business partner networks" | "Vendor/supplier systems" | "Government systems and portals" | "Personal devices (BYOD)" | "Remote access system" | "No external connections")[];

  /**
   * Notes / Evidence 
   * linkId: ************
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: AC.L1-B.1.III - External Connections >  External System Connections
   * Required: no
   */
  notesEvidence10?: string;

  /**
   * How do you verify external system connections?
   * linkId: ************
   * FHIR type: choice
   * Section: AC.L1-B.1.III - External Connections > Connection Verification Methods
   * Options: "Digital certificates and PKI", "VPN connections with authentication", "Firewall rules and IP restrictions", "Signed interconnection agreements", "Continuous monitoring and logging"
   * Required: no
   */
  howDoYouVerifyExternalSystemConnections?: ("Digital certificates and PKI" | "VPN connections with authentication" | "Firewall rules and IP restrictions" | "Signed interconnection agreements" | "Continuous monitoring and logging")[];

  /**
   * Notes / Evidence
   * linkId: ************
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: AC.L1-B.1.III - External Connections > Connection Verification Methods
   * Required: no
   */
  notesEvidence11?: string;

  /**
   * What limitations are placed on external connections?
   * linkId: ************
   * FHIR type: choice
   * Section: AC.L1-B.1.III - External Connections > Connection Control Limitations
   * Options: "Time-based access restrictions", "Restrictions on data types that can be shared", "Limited to specific user groups", "Management approval required for each connection", "Comprehensive audit trails and logging"
   * Required: no
   */
  whatLimitationsArePlacedOnExternalConnections?: ("Time-based access restrictions" | "Restrictions on data types that can be shared" | "Limited to specific user groups" | "Management approval required for each connection" | "Comprehensive audit trails and logging")[];

  /**
   * Notes / Evidence
   * linkId: ************
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: AC.L1-B.1.III - External Connections > Connection Control Limitations
   * Required: no
   */
  notesEvidence12?: string;

  /**
   * Implementation Status
   * linkId: ************
   * FHIR type: choice
   * Section: AC.L1-B.1.IV - Control Public Information
   * Options: "Fully Implemented", "Partially Implemented", "Not Implemented"
   * Required: no
   */
  implementationStatus3?: "Fully Implemented" | "Partially Implemented" | "Not Implemented";

  /**
   * Notes / Evidence
   * linkId: ************
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: AC.L1-B.1.IV - Control Public Information
   * Required: no
   */
  notesEvidence13?: string;

  /**
   * What publicly accessible systems does your organization operate?
   * linkId: ************
   * FHIR type: choice
   * Section: AC.L1-B.1.IV - Control Public Information > Publicly Accessible Systems
   * Options: "Company website", " Social media accounts", "Customer portals or self-service systems", "Corporate blog or news site", "Public forums or discussion boards", "No publicly accessible systems"
   * Required: no
   */
  whatPubliclyAccessibleSystemsDoesYourOrganizationOperate?: ("Company website" | " Social media accounts" | "Customer portals or self-service systems" | "Corporate blog or news site" | "Public forums or discussion boards" | "No publicly accessible systems")[];

  /**
   * Notes / Evidence
   * linkId: ************
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: AC.L1-B.1.IV - Control Public Information > Publicly Accessible Systems
   * Required: no
   */
  notesEvidence14?: string;

  /**
   * How do you ensure FCI is not posted on public systems?
   * linkId: ************
   * FHIR type: choice
   * Section: AC.L1-B.1.IV - Control Public Information > Content Review Process
   * Options: "Pre-publication review and approval process", "Designated reviewers trained to identify FCI", "Automated content scanning for sensitive information", "Periodic audits of published content", "Procedures for rapid removal of inappropriate content"
   * Required: no
   */
  howDoYouEnsureFciIsNotPostedOnPublicSystems?: ("Pre-publication review and approval process" | "Designated reviewers trained to identify FCI" | "Automated content scanning for sensitive information" | "Periodic audits of published content" | "Procedures for rapid removal of inappropriate content")[];

  /**
   * Notes / Evidence
   * linkId: 633971923340
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: AC.L1-B.1.IV - Control Public Information > Content Review Process
   * Required: no
   */
  notesEvidence15?: string;

  /**
   * Who is authorized to post content to public systems?
   * linkId: 624223914711
   * FHIR type: display
   * Section: AC.L1-B.1.IV - Control Public Information > Authorized Publishing Personnel
   * Required: no
   */
  whoIsAuthorizedToPostContentToPublicSystems?: string;

  /**
   * Number of authorized personnel:
   * linkId: 374839487767
   * FHIR type: integer
   * Section: AC.L1-B.1.IV - Control Public Information > Authorized Publishing Personnel
   * Required: no
   */
  numberOfAuthorizedPersonnel?: number;

  /**
   * Choose all that apply:
   * linkId: 177243885107
   * FHIR type: choice
   * Section: AC.L1-B.1.IV - Control Public Information > Authorized Publishing Personnel
   * Options: "Marketing department", "Communications/PR team", "Executive leadership", "IT administrators"
   * Required: no
   */
  chooseAllThatApply?: ("Marketing department" | "Communications/PR team" | "Executive leadership" | "IT administrators")[];

  /**
   * Notes / Evidence
   * linkId: 163760226494
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: AC.L1-B.1.IV - Control Public Information > Authorized Publishing Personnel
   * Required: no
   */
  notesEvidence16?: string;
}

/** Convert an LHC JSON response into a normalized AccessControl object. */
export function accessControlLhcFormResponseAdapter(input: Any): AccessControl {
  return {
    doYouHaveAnAccessControlPolicy: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")) as AccessControl["doYouHaveAnAccessControlPolicy"],
    doesYourOrganizationHaveADocumentedAccessControlPolicyThatAddresses: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "************")) as AccessControl["doesYourOrganizationHaveADocumentedAccessControlPolicyThatAddresses"],
    notesEvidence: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")),
    howManyAccountsAreCurrentlyInYourSystems: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")),
    activeUserAccounts: rt.coerceOptionalNumber(rt.findLhcValueByLinkId(input, "************")),
    inactiveDisabledUserAccounts: rt.coerceOptionalNumber(rt.findLhcValueByLinkId(input, "************")),
    serviceAccounts: rt.coerceOptionalNumber(rt.findLhcValueByLinkId(input, "************")),
    sharedAccounts: rt.coerceOptionalNumber(rt.findLhcValueByLinkId(input, "************")),
    howIsThePrincipleOfLeastPrivilegeImplemented: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")) as AccessControl["howIsThePrincipleOfLeastPrivilegeImplemented"],
    notesEvidence2: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")),
    howAreAccountLifecycleProcessesManaged: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "************")) as AccessControl["howAreAccountLifecycleProcessesManaged"],
    notesEvidence3: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")),
    howFrequentlyAreUserAccountsReviewedForValidityAndAppropriateAccess: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")) as AccessControl["howFrequentlyAreUserAccountsReviewedForValidityAndAppropriateAccess"],
    notesEvidence4: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")),
    implementationStatus: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")) as AccessControl["implementationStatus"],
    notesEvidence5: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")),
    howDoYouLimitUserAccessToSpecificTransactionsAndFunctions: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")) as AccessControl["howDoYouLimitUserAccessToSpecificTransactionsAndFunctions"],
    notesEvidence6: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")),
    whatTypesOfFunctionsAreRestrictedBasedOnUserRoles: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "************")) as AccessControl["whatTypesOfFunctionsAreRestrictedBasedOnUserRoles"],
    notesEvidence7: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")),
    howAreHighRiskTransactionsAuthorized: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "************")) as AccessControl["howAreHighRiskTransactionsAuthorized"],
    notesEvidence8: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")),
    implementationStatus2: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")) as AccessControl["implementationStatus2"],
    notesEvidence9: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")),
    whatTypesOfExternalSystemsDoesYourOrganizationConnectTo: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "************")) as AccessControl["whatTypesOfExternalSystemsDoesYourOrganizationConnectTo"],
    notesEvidence10: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")),
    howDoYouVerifyExternalSystemConnections: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "************")) as AccessControl["howDoYouVerifyExternalSystemConnections"],
    notesEvidence11: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")),
    whatLimitationsArePlacedOnExternalConnections: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "************")) as AccessControl["whatLimitationsArePlacedOnExternalConnections"],
    notesEvidence12: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")),
    implementationStatus3: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")) as AccessControl["implementationStatus3"],
    notesEvidence13: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")),
    whatPubliclyAccessibleSystemsDoesYourOrganizationOperate: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "************")) as AccessControl["whatPubliclyAccessibleSystemsDoesYourOrganizationOperate"],
    notesEvidence14: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")),
    howDoYouEnsureFciIsNotPostedOnPublicSystems: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "************")) as AccessControl["howDoYouEnsureFciIsNotPostedOnPublicSystems"],
    notesEvidence15: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "633971923340")),
    whoIsAuthorizedToPostContentToPublicSystems: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "624223914711")),
    numberOfAuthorizedPersonnel: rt.coerceOptionalNumber(rt.findLhcValueByLinkId(input, "374839487767")),
    chooseAllThatApply: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "177243885107")) as AccessControl["chooseAllThatApply"],
    notesEvidence16: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "163760226494")),
  };
}

/** Convert a FHIR QuestionnaireResponse into a normalized AccessControl object. */
export function accessControlFhirQuestionnaireResponseAdapter(qr: Any): AccessControl {
  return {
    doYouHaveAnAccessControlPolicy: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")) as AccessControl["doYouHaveAnAccessControlPolicy"],
    doesYourOrganizationHaveADocumentedAccessControlPolicyThatAddresses: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "************")) as AccessControl["doesYourOrganizationHaveADocumentedAccessControlPolicyThatAddresses"],
    notesEvidence: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")),
    howManyAccountsAreCurrentlyInYourSystems: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")),
    activeUserAccounts: rt.coerceOptionalNumber(rt.findQrAnswerByLinkId(qr, "************")),
    inactiveDisabledUserAccounts: rt.coerceOptionalNumber(rt.findQrAnswerByLinkId(qr, "************")),
    serviceAccounts: rt.coerceOptionalNumber(rt.findQrAnswerByLinkId(qr, "************")),
    sharedAccounts: rt.coerceOptionalNumber(rt.findQrAnswerByLinkId(qr, "************")),
    howIsThePrincipleOfLeastPrivilegeImplemented: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")) as AccessControl["howIsThePrincipleOfLeastPrivilegeImplemented"],
    notesEvidence2: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")),
    howAreAccountLifecycleProcessesManaged: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "************")) as AccessControl["howAreAccountLifecycleProcessesManaged"],
    notesEvidence3: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")),
    howFrequentlyAreUserAccountsReviewedForValidityAndAppropriateAccess: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")) as AccessControl["howFrequentlyAreUserAccountsReviewedForValidityAndAppropriateAccess"],
    notesEvidence4: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")),
    implementationStatus: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")) as AccessControl["implementationStatus"],
    notesEvidence5: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")),
    howDoYouLimitUserAccessToSpecificTransactionsAndFunctions: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")) as AccessControl["howDoYouLimitUserAccessToSpecificTransactionsAndFunctions"],
    notesEvidence6: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")),
    whatTypesOfFunctionsAreRestrictedBasedOnUserRoles: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "************")) as AccessControl["whatTypesOfFunctionsAreRestrictedBasedOnUserRoles"],
    notesEvidence7: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")),
    howAreHighRiskTransactionsAuthorized: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "************")) as AccessControl["howAreHighRiskTransactionsAuthorized"],
    notesEvidence8: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")),
    implementationStatus2: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")) as AccessControl["implementationStatus2"],
    notesEvidence9: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")),
    whatTypesOfExternalSystemsDoesYourOrganizationConnectTo: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "************")) as AccessControl["whatTypesOfExternalSystemsDoesYourOrganizationConnectTo"],
    notesEvidence10: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")),
    howDoYouVerifyExternalSystemConnections: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "************")) as AccessControl["howDoYouVerifyExternalSystemConnections"],
    notesEvidence11: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")),
    whatLimitationsArePlacedOnExternalConnections: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "************")) as AccessControl["whatLimitationsArePlacedOnExternalConnections"],
    notesEvidence12: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")),
    implementationStatus3: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")) as AccessControl["implementationStatus3"],
    notesEvidence13: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")),
    whatPubliclyAccessibleSystemsDoesYourOrganizationOperate: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "************")) as AccessControl["whatPubliclyAccessibleSystemsDoesYourOrganizationOperate"],
    notesEvidence14: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")),
    howDoYouEnsureFciIsNotPostedOnPublicSystems: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "************")) as AccessControl["howDoYouEnsureFciIsNotPostedOnPublicSystems"],
    notesEvidence15: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "633971923340")),
    whoIsAuthorizedToPostContentToPublicSystems: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "624223914711")),
    numberOfAuthorizedPersonnel: rt.coerceOptionalNumber(rt.findQrAnswerByLinkId(qr, "374839487767")),
    chooseAllThatApply: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "177243885107")) as AccessControl["chooseAllThatApply"],
    notesEvidence16: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "163760226494")),
  };
}

/**
 * NOTE TO DEVELOPERS:
 * -------------------
 * This Interpreter class is provided only as an EXAMPLE scaffold to demonstrate
 * how to consume the normalized type-safe interface generated for this
 * Questionnaire. It shows minimal factories (`fromLhc`, `fromQuestionnaireResponse`)
 * and convenience methods (`validateRequiredFields`, `assessReadiness`) but it is
 * NOT intended for production use.
 *
 * In real applications:
 * - Treat this class as SAMPLE CODE only.
 * - Replace or extend it with proper business logic, rules engines, or validation
 *   frameworks appropriate to your domain.
 * - Do not rely on the simplistic readiness scoring or validation in production
 *   scenarios; they are illustrative, not authoritative.
 *
 * Best practice: use the generated TypeScript interface (`AccessControl`) as your
 * contract for normalized data, then integrate with your own rules processors,
 * compliance engines, or plain TypeScript/JavaScript functions as needed.
 */
export class AccessControlInterpreter {
  constructor(readonly value: AccessControl) { }

  /** Factory: build from LHC JSON. */
  static fromLhcFormResponse(input: Any): AccessControlInterpreter {
    return new AccessControlInterpreter(accessControlLhcFormResponseAdapter(input));
  }

  /** Factory: build from FHIR QuestionnaireResponse. */
  static fromQuestionnaireResponse(qr: Any): AccessControlInterpreter {
    return new AccessControlInterpreter(accessControlFhirQuestionnaireResponseAdapter(qr));
  }

  /** Check required fields and report any missing or blank. */
  validateRequiredFields(): { ok: boolean; missing: Array<keyof AccessControl> } {
    const missing: Array<keyof AccessControl> = [];
    const req: Array<keyof AccessControl> = [];
    for (const k of req) {
      const v = (this.value as Any)[k];
      if (rt.isBlank(v)) missing.push(k);
    }
    return { ok: missing.length === 0, missing };
  }

  /**
   * Assess readiness with a simple completeness score. This is meant to be
   * used to help understand how complete the types are and serves as an
   * example of how to use the generated code.
   * - requiredCovered: percentage of required fields that are non-blank
   * - overallFilled: count of non-blank fields among all known properties
   */
  assessReadiness(): {
    formTitle: string;
    requiredCovered: number; // 0..1
    totalRequired: number;
    totalFilled: number;
    totalFields: number;
    missingRequired: Array<keyof AccessControl>;
  } {
    const req: Array<keyof AccessControl> = [];
    const all: Array<keyof AccessControl> = ["doYouHaveAnAccessControlPolicy", "doesYourOrganizationHaveADocumentedAccessControlPolicyThatAddresses", "notesEvidence", "howManyAccountsAreCurrentlyInYourSystems", "activeUserAccounts", "inactiveDisabledUserAccounts", "serviceAccounts", "sharedAccounts", "howIsThePrincipleOfLeastPrivilegeImplemented", "notesEvidence2", "howAreAccountLifecycleProcessesManaged", "notesEvidence3", "howFrequentlyAreUserAccountsReviewedForValidityAndAppropriateAccess", "notesEvidence4", "implementationStatus", "notesEvidence5", "howDoYouLimitUserAccessToSpecificTransactionsAndFunctions", "notesEvidence6", "whatTypesOfFunctionsAreRestrictedBasedOnUserRoles", "notesEvidence7", "howAreHighRiskTransactionsAuthorized", "notesEvidence8", "implementationStatus2", "notesEvidence9", "whatTypesOfExternalSystemsDoesYourOrganizationConnectTo", "notesEvidence10", "howDoYouVerifyExternalSystemConnections", "notesEvidence11", "whatLimitationsArePlacedOnExternalConnections", "notesEvidence12", "implementationStatus3", "notesEvidence13", "whatPubliclyAccessibleSystemsDoesYourOrganizationOperate", "notesEvidence14", "howDoYouEnsureFciIsNotPostedOnPublicSystems", "notesEvidence15", "whoIsAuthorizedToPostContentToPublicSystems", "numberOfAuthorizedPersonnel", "chooseAllThatApply", "notesEvidence16"];

    let reqFilled = 0;
    const missingReq: Array<keyof AccessControl> = [];
    for (const k of req) {
      const v = (this.value as Any)[k];
      if (!rt.isBlank(v)) reqFilled++;
      else missingReq.push(k);
    }

    let totalFilled = 0;
    for (const k of all) {
      if (!rt.isBlank((this.value as Any)[k])) totalFilled++;
    }

    return {
      formTitle: "Access Control",
      requiredCovered: req.length ? reqFilled / req.length : 1,
      totalRequired: req.length,
      totalFilled,
      totalFields: all.length,
      missingRequired: missingReq,
    };
  }
}

/** The original source */
export const accessControlSource = `{
  "resourceType": "Questionnaire",
  "meta": {
    "profile": [
      "http://hl7.org/fhir/4.0/StructureDefinition/Questionnaire"
    ],
    "tag": [
      {
        "system": "InternalAssessment",
        "code": "AccessControl-L1",
        "display": "CMMC Level 1"
      }
    ]
  },
  "extension": [
    {
      "url": "http://hl7.org/fhir/StructureDefinition/variable",
      "valueExpression": {
        "name": "displayOrder",
        "language": "text/fhirpath",
        "expression": "2",
        "extension": [
          {
            "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
            "valueString": "simple"
          },
          {
            "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
            "valueString": "2"
          }
        ]
      }
    }
  ],
  "version": "1.0",
  "name": "access_control_cmmc_l1_assessment",
  "title": "Access Control",
  "status": "draft",
  "date": "2025-08-25",
  "publisher": "Netspective",
  "description": "Access Control (Limit information system access to authorized users and processes)",
  "purpose": "This assessment evaluates how the organization limits system access to authorized users, processes, and devices. It ensures users are granted only the permissions needed to perform their job functions and that access to sensitive transactions and functions is restricted. The focus is on verifying external connections, controlling information on public-facing systems, and enforcing least privilege across the enterprise.",
  "approvalDate": "2025-08-25",
  "item": [
    {
      "item": [
        {
          "item": [
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "text-box",
                        "display": "Text Box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "599842914392",
              "text": "Notes / Evidence"
            },
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "radio-button",
                        "display": "Radio Button"
                      }
                    ]
                  }
                }
              ],
              "linkId": "184584712182",
              "text": "Implementation Status",
              "repeats": false,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 100
                    }
                  ],
                  "valueCoding": {
                    "display": "Fully Implemented"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 50
                    }
                  ],
                  "valueCoding": {
                    "display": "Partially Implemented"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 0
                    }
                  ],
                  "valueCoding": {
                    "display": "Not Implemented"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "text-box",
                        "display": "Text Box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "560290762218",
              "text": "Notes / Evidence"
            }
          ],
          "type": "choice",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/variable",
              "valueExpression": {
                "name": "weight",
                "language": "text/fhirpath",
                "expression": "2",
                "extension": [
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                    "valueString": "simple"
                  },
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                    "valueString": "2"
                  }
                ]
              }
            },
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "radio-button",
                    "display": "Radio Button"
                  }
                ]
              }
            }
          ],
          "linkId": "************",
          "text": "Do you have an Access Control Policy?",
          "repeats": false,
          "answerOption": [
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 100
                }
              ],
              "valueCoding": {
                "display": "Yes"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 0
                }
              ],
              "valueCoding": {
                "display": " No - if no, would you like help creating one for your company?"
              }
            }
          ]
        },
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "100",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "100"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "************",
              "text": "Does your organization have a documented access control policy that addresses:",
              "repeats": true,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 25
                    }
                  ],
                  "valueCoding": {
                    "display": "Purpose, scope, roles, and responsibilities"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 25
                    }
                  ],
                  "valueCoding": {
                    "display": "Management commitment"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 25
                    }
                  ],
                  "valueCoding": {
                    "display": "Coordination among organizational entities"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 25
                    }
                  ],
                  "valueCoding": {
                    "display": "Compliance requirements"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "************",
              "text": "Notes / Evidence"
            },
            {
              "linkId": "************_helpText",
              "type": "display",
              "text": "Establish clear access control rules, including who can access what information, how access is granted, reviewed, and revoked",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "************",
          "prefix": "1.",
          "text": "Access Control Policy Elements"
        },
        {
          "item": [
            {
              "type": "display",
              "linkId": "************",
              "text": "How many accounts are currently in your systems? "
            },
            {
              "type": "integer",
              "linkId": "************",
              "text": "Active user accounts:",
              "repeats": false
            },
            {
              "type": "integer",
              "linkId": "************",
              "text": "Inactive/disabled user accounts:",
              "repeats": false
            },
            {
              "type": "integer",
              "linkId": "************",
              "text": "Service accounts:",
              "repeats": false
            },
            {
              "type": "integer",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/maxValue",
                  "valueInteger": 100
                }
              ],
              "linkId": "************",
              "text": "Shared accounts:",
              "repeats": false
            },
            {
              "linkId": "************_helpText",
              "type": "display",
              "text": "Maintain a detailed and up-to-date record of all user accounts, including their access levels and status",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "************",
          "prefix": "2.",
          "text": "User Account Registry"
        },
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "radio-button",
                        "display": "Radio Button"
                      }
                    ]
                  }
                }
              ],
              "linkId": "************",
              "text": "How is the principle of least privilege implemented?",
              "repeats": false,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 100
                    }
                  ],
                  "valueCoding": {
                    "display": "Fully implemented across all systems"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 50
                    }
                  ],
                  "valueCoding": {
                    "display": "Partially implemented"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 0
                    }
                  ],
                  "valueCoding": {
                    "display": "Not implemented"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "text-box",
                        "display": "Text Box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "************",
              "text": "Notes / Evidence"
            },
            {
              "linkId": "159744780603_helpText",
              "type": "display",
              "text": "Grant users and systems only the minimum access necessary to perform their tasks",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "159744780603",
          "prefix": "3.",
          "text": "Principle of Least Privilege Implementation"
        },
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "100",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "100"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "************",
              "text": "How are account lifecycle processes managed?",
              "repeats": true,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 25
                    }
                  ],
                  "valueCoding": {
                    "display": "Automated identity management system"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 25
                    }
                  ],
                  "valueCoding": {
                    "display": "Manual process with approval workflow"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 25
                    }
                  ],
                  "valueCoding": {
                    "display": "Integration with HR systems"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 25
                    }
                  ],
                  "valueCoding": {
                    "display": " Regular account reviews and recertification"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "text-box",
                        "display": "Text Box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "************",
              "text": "Notes / Evidence"
            },
            {
              "item": [
                {
                  "type": "choice",
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/variable",
                      "valueExpression": {
                        "name": "weight",
                        "language": "text/fhirpath",
                        "expression": "1",
                        "extension": [
                          {
                            "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                            "valueString": "simple"
                          },
                          {
                            "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                            "valueString": "1"
                          }
                        ]
                      }
                    },
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                      "valueCodeableConcept": {
                        "coding": [
                          {
                            "system": "http://hl7.org/fhir/questionnaire-item-control",
                            "code": "radio-button",
                            "display": "Radio Button"
                          }
                        ]
                      }
                    }
                  ],
                  "linkId": "************",
                  "text": "How frequently are user accounts reviewed for validity and appropriate access?",
                  "repeats": false,
                  "answerOption": [
                    {
                      "extension": [
                        {
                          "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                          "valueDecimal": 25
                        }
                      ],
                      "valueCoding": {
                        "display": "Monthly"
                      }
                    },
                    {
                      "extension": [
                        {
                          "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                          "valueDecimal": 25
                        }
                      ],
                      "valueCoding": {
                        "display": " Quarterly"
                      }
                    },
                    {
                      "extension": [
                        {
                          "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                          "valueDecimal": 25
                        }
                      ],
                      "valueCoding": {
                        "display": "Annually"
                      }
                    },
                    {
                      "extension": [
                        {
                          "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                          "valueDecimal": 25
                        }
                      ],
                      "valueCoding": {
                        "display": "Other (specify):"
                      }
                    }
                  ]
                },
                {
                  "type": "string",
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                      "valueString": "Type your comments here..."
                    },
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                      "valueCodeableConcept": {
                        "coding": [
                          {
                            "system": "http://hl7.org/fhir/questionnaire-item-control",
                            "code": "text-box",
                            "display": "Text Box"
                          }
                        ]
                      }
                    }
                  ],
                  "linkId": "************",
                  "text": "Notes / Evidence"
                },
                {
                  "linkId": "************_helpText",
                  "type": "display",
                  "text": "Regularly review user accounts to verify access is still appropriate and remove or adjust accounts as needed",
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                      "valueCodeableConcept": {
                        "coding": [
                          {
                            "system": "http://hl7.org/fhir/questionnaire-item-control",
                            "code": "help",
                            "display": "Help-Button"
                          }
                        ],
                        "text": "Help-Button"
                      }
                    }
                  ]
                }
              ],
              "type": "group",
              "linkId": "************",
              "text": "Account Review Frequency"
            },
            {
              "linkId": "************_helpText",
              "type": "display",
              "text": "Establish and follow formal procedures to manage user accounts throughout their lifecycle",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "************",
          "prefix": "4.",
          "text": "Account Management Processes"
        },
        {
          "linkId": "************_helpText",
          "type": "display",
          "text": "Limit information system access to authorized users, processes acting on behalf of authorized users, or devices (including other information systems).",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "help",
                    "display": "Help-Button"
                  }
                ],
                "text": "Help-Button"
              }
            }
          ]
        }
      ],
      "type": "group",
      "linkId": "************",
      "text": "AC.L1-B.1.I - Authorized Access Control"
    },
    {
      "item": [
        {
          "type": "choice",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/variable",
              "valueExpression": {
                "name": "weight",
                "language": "text/fhirpath",
                "expression": "1",
                "extension": [
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                    "valueString": "simple"
                  },
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                    "valueString": "1"
                  }
                ]
              }
            },
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "radio-button",
                    "display": "Radio Button"
                  }
                ]
              }
            }
          ],
          "linkId": "************",
          "text": "Implementation Status",
          "repeats": false,
          "answerOption": [
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 100
                }
              ],
              "valueCoding": {
                "display": "Fully Implemented"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 50
                }
              ],
              "valueCoding": {
                "display": "Partially Implemented"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 0
                }
              ],
              "valueCoding": {
                "display": "Not Implemented"
              }
            }
          ]
        },
        {
          "type": "string",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
              "valueString": "Type your comments here..."
            },
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "text-box",
                    "display": "Text Box"
                  }
                ]
              }
            }
          ],
          "linkId": "************",
          "text": "Notes / Evidence "
        },
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "100",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "100"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "radio-button",
                        "display": "Radio Button"
                      }
                    ]
                  }
                }
              ],
              "linkId": "************",
              "text": "How do you limit user access to specific transactions and functions?",
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Role-based access control (RBAC)"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Function-based permissions (create, read, update, delete)"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Application-level access controls"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Time-based access restrictions"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Location-based access restrictions"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "************",
              "text": "Notes / Evidence "
            },
            {
              "linkId": "899089109837_helpText",
              "type": "display",
              "text": "Implement controls to monitor and regulate transactions, ensuring only authorized actions are performed within systems and applications",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "899089109837",
          "prefix": "1.",
          "text": "Transaction Control Implementation"
        },
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1.5",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1.5"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "100",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "100"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "************",
              "text": "What types of functions are restricted based on user roles?",
              "repeats": true,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Administrative functions (user management, system configuration)"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Financial transactions and approvals"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Data export and bulk download functions"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Report generation and access"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "System-level commands and utilities"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "************",
              "text": "Notes / Evidence "
            },
            {
              "linkId": "561249826496_helpText",
              "type": "display",
              "text": "Limit system functions and capabilities based on user roles to ensure individuals can only perform actions necessary for their job responsibilities",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "561249826496",
          "prefix": "2.",
          "text": "Function Restrictions by Role"
        },
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "100",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "100"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "************",
              "text": "How are high-risk transactions authorized?",
              "repeats": true,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 25
                    }
                  ],
                  "valueCoding": {
                    "display": "Manager approval required"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 25
                    }
                  ],
                  "valueCoding": {
                    "display": "Two-person authorization"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 25
                    }
                  ],
                  "valueCoding": {
                    "display": "Automated business rules and limits"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 25
                    }
                  ],
                  "valueCoding": {
                    "display": "No special authorization required"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "************",
              "text": "Notes / Evidence "
            },
            {
              "linkId": "338456195634_helpText",
              "type": "display",
              "text": "Require formal approval before critical transactions are executed to prevent unauthorized or fraudulent activities.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "338456195634",
          "prefix": "3.",
          "text": "Transaction Authorization Requirements"
        },
        {
          "linkId": "700726342337_helpText",
          "type": "display",
          "text": "Limit information system access to the types of transactions and functions that authorized users are permitted to execute.",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "help",
                    "display": "Help-Button"
                  }
                ],
                "text": "Help-Button"
              }
            }
          ]
        }
      ],
      "type": "group",
      "linkId": "700726342337",
      "text": "AC.L1-B.1.II - Transaction & Function Control"
    },
    {
      "item": [
        {
          "type": "choice",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/variable",
              "valueExpression": {
                "name": "weight",
                "language": "text/fhirpath",
                "expression": "2",
                "extension": [
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                    "valueString": "simple"
                  },
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                    "valueString": "2"
                  }
                ]
              }
            },
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "radio-button",
                    "display": "Radio Button"
                  }
                ]
              }
            }
          ],
          "linkId": "************",
          "text": "Implementation Status",
          "repeats": false,
          "answerOption": [
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 100
                }
              ],
              "valueCoding": {
                "display": " Fully Implemented"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 50
                }
              ],
              "valueCoding": {
                "display": " Partially Implemented"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 0
                }
              ],
              "valueCoding": {
                "display": " Not Implemented"
              }
            }
          ]
        },
        {
          "type": "string",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
              "valueString": "Type your comments here..."
            }
          ],
          "linkId": "************",
          "text": "Notes / Evidence "
        },
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "100.3",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "100.3"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "************",
              "text": "What types of external systems does your organization connect to?",
              "repeats": true,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 14.29
                    }
                  ],
                  "valueCoding": {
                    "display": "Cloud services (email, file storage, applications)"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 14.29
                    }
                  ],
                  "valueCoding": {
                    "display": "Business partner networks"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 14.29
                    }
                  ],
                  "valueCoding": {
                    "display": "Vendor/supplier systems"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 14.29
                    }
                  ],
                  "valueCoding": {
                    "display": "Government systems and portals"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 14.29
                    }
                  ],
                  "valueCoding": {
                    "display": "Personal devices (BYOD)"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 14.29
                    }
                  ],
                  "valueCoding": {
                    "display": "Remote access system"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 14.29
                    }
                  ],
                  "valueCoding": {
                    "display": "No external connections"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "************",
              "text": "Notes / Evidence "
            },
            {
              "linkId": "118413869969_helpText",
              "type": "display",
              "text": "Manage and secure connections to external systems to protect your network from unauthorized access and data breaches.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "118413869969",
          "prefix": "1.",
          "text": " External System Connections"
        },
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "100",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "100"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "************",
              "text": "How do you verify external system connections?",
              "repeats": true,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Digital certificates and PKI"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "VPN connections with authentication"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Firewall rules and IP restrictions"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Signed interconnection agreements"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Continuous monitoring and logging"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "************",
              "text": "Notes / Evidence"
            },
            {
              "linkId": "397995568740_helpText",
              "type": "display",
              "text": "Use verification techniques to confirm the identity and security of external connections before allowing access to your systems",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "397995568740",
          "prefix": "2.",
          "text": "Connection Verification Methods"
        },
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "100",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "100"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "************",
              "text": "What limitations are placed on external connections?",
              "repeats": true,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Time-based access restrictions"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Restrictions on data types that can be shared"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Limited to specific user groups"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Management approval required for each connection"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Comprehensive audit trails and logging"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "************",
              "text": "Notes / Evidence"
            },
            {
              "linkId": "354025378477_helpText",
              "type": "display",
              "text": "Define and enforce restrictions on external connections to minimize exposure and reduce security risks.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "354025378477",
          "prefix": "3.",
          "text": "Connection Control Limitations"
        },
        {
          "linkId": "293091353060_helpText",
          "type": "display",
          "text": "Verify and control/limit connections to and use of external information systems.",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "help",
                    "display": "Help-Button"
                  }
                ],
                "text": "Help-Button"
              }
            }
          ]
        }
      ],
      "type": "group",
      "linkId": "293091353060",
      "text": "AC.L1-B.1.III - External Connections"
    },
    {
      "type": "group",
      "linkId": "942841103790",
      "text": "AC.L1-B.1.IV - Control Public Information",
      "item": [
        {
          "type": "choice",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/variable",
              "valueExpression": {
                "name": "weight",
                "language": "text/fhirpath",
                "expression": "1",
                "extension": [
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                    "valueString": "simple"
                  },
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                    "valueString": "1"
                  }
                ]
              }
            },
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "radio-button",
                    "display": "Radio Button"
                  }
                ]
              }
            }
          ],
          "linkId": "************",
          "text": "Implementation Status",
          "repeats": false,
          "answerOption": [
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 100
                }
              ],
              "valueCoding": {
                "display": "Fully Implemented"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 50
                }
              ],
              "valueCoding": {
                "display": "Partially Implemented"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 0
                }
              ],
              "valueCoding": {
                "display": "Not Implemented"
              }
            }
          ]
        },
        {
          "type": "string",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
              "valueString": "Type your comments here..."
            }
          ],
          "linkId": "************",
          "text": "Notes / Evidence",
          "item": [
            {
              "linkId": "************_helpText",
              "type": "display",
              "text": "",
              "_text": {
                "extension": [
                  {
                    "url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml",
                    "valueString": "<!-- meta: {...} -->"
                  }
                ]
              },
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ]
        },
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "99.96",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "99.96"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "************",
              "text": "What publicly accessible systems does your organization operate?",
              "repeats": true,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 16.66
                    }
                  ],
                  "valueCoding": {
                    "display": "Company website"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 16.66
                    }
                  ],
                  "valueCoding": {
                    "display": " Social media accounts"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 16.66
                    }
                  ],
                  "valueCoding": {
                    "display": "Customer portals or self-service systems"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 16.66
                    }
                  ],
                  "valueCoding": {
                    "display": "Corporate blog or news site"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 16.66
                    }
                  ],
                  "valueCoding": {
                    "display": "Public forums or discussion boards"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 16.66
                    }
                  ],
                  "valueCoding": {
                    "display": "No publicly accessible systems"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "************",
              "text": "Notes / Evidence"
            },
            {
              "linkId": "501427838641_helpText",
              "type": "display",
              "text": "Secure and monitor systems that are accessible to the public to prevent unauthorized access and data leakage.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "501427838641",
          "prefix": "1.",
          "text": "Publicly Accessible Systems"
        },
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "100",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "100"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "************",
              "text": "How do you ensure FCI is not posted on public systems?",
              "repeats": true,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Pre-publication review and approval process"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Designated reviewers trained to identify FCI"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Automated content scanning for sensitive information"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Periodic audits of published content"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Procedures for rapid removal of inappropriate content"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "633971923340",
              "text": "Notes / Evidence"
            },
            {
              "linkId": "786703783052_helpText",
              "type": "display",
              "text": "Establish regular procedures to review and validate information before it is published or shared",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "786703783052",
          "prefix": "2.",
          "text": "Content Review Process"
        },
        {
          "item": [
            {
              "type": "display",
              "linkId": "624223914711",
              "text": "Who is authorized to post content to public systems?"
            },
            {
              "type": "integer",
              "linkId": "374839487767",
              "text": "Number of authorized personnel:",
              "repeats": false
            },
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "100",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "100"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "177243885107",
              "text": "Choose all that apply:",
              "repeats": true,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 25
                    }
                  ],
                  "valueCoding": {
                    "display": "Marketing department"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 25
                    }
                  ],
                  "valueCoding": {
                    "display": "Communications/PR team"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 25
                    }
                  ],
                  "valueCoding": {
                    "display": "Executive leadership"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 25
                    }
                  ],
                  "valueCoding": {
                    "display": "IT administrators"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "163760226494",
              "text": "Notes / Evidence"
            },
            {
              "linkId": "815496752107_helpText",
              "type": "display",
              "text": "Designate and control who is allowed to publish or distribute organizational information",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "815496752107",
          "prefix": "3.",
          "text": "Authorized Publishing Personnel"
        },
        {
          "linkId": "942841103790_helpText",
          "type": "display",
          "text": "Control information posted or processed on publicly accessible information systems.",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "help",
                    "display": "Help-Button"
                  }
                ],
                "text": "Help-Button"
              }
            }
          ]
        }
      ]
    }
  ]
}`;