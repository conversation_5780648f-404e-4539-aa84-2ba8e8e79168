# Opsfolio Global Product Customer Journey Map 

**Stage 0 — Marketing Funnel:**
* Customer goes through marketing funnel, covered by other customer journey maps

**Stage 1 — Engagement:**

* Pain: Customer requires compliance certification to comply with applicable regulations and desires a compliance-as-code approach
* Customer decides to purchase Opsfolio’s compliance automation.
* Customer works with Opsfolio business and technical teams to align on SOW

**Stage 2 — Adoption of Expectations & Outcomes Hub:**

* Pain: compliance tasks scattered across spreadsheets and PM tools.
* Opsfolio team uploads structured project roadmap to EOH
* Integrates compliance tasks with their existing project management system.
* Payoff: compliance requirements are now actionable tasks, visible and tracked.
* Outcome: leadership gains confidence that compliance isn’t falling through cracks.

**Stage 3 — Fleetfolio:**

* Customer requires asset visibility for compliance (CMMC and other frameworks require asset inventory).
* Pain: manual server/software tracking is inconsistent, error-prone, and audit-risky.
* Fleetfolio implemented to auto-discover and monitor assets.
* Payoff: compliance teams can instantly generate asset lists and health reports.
* Outcome: asset inventory requirement satisfied, plus operational insights gained.

**Stage 4 — Maturity:**

* Customer now runs compliance as a continuous process:
  * **EOH** → ensures tasks are assigned and completed.
  * **Fleetfolio** → ensures assets are continuously tracked and monitored.
* Net payoff: reduced audit prep time, stronger operational security, and trust from customers/DoD.

