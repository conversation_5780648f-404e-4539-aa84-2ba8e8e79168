/* ::: Reverse proxy css > Starts ::: */
body {
  font-size: 14px !important;
  font-family:
    ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji" !important;
}
.tail-folio blockquote,
.tail-folio dl,
.tail-folio dd,
.tail-folio h1,
.tail-folio h2,
.tail-folio h3,
.tail-folio h4,
.tail-folio h5,
.tail-folio h6,
.tail-folio hr,
.tail-folio figure,
.tail-folio p,
.tail-folio pre {
  margin: 0 !important;
}
.tail-folio header .p-6 {
  padding: 1rem 1.4rem !important;
}
.tail-folio .p-4 {
  padding: 1rem !important;
}
.tail-folio .p-3 {
  padding: 0.75rem !important;
}
.tail-folio .gap-3 {
  gap: 0.75rem !important;
}
.tail-folio header .hover\:text-accent-foreground:hover {
  color: #fff !important;
}
.tail-folio nav li button,
.tail-folio nav li,
nav li a,
.tail-folio .text-sm {
  font-size: 14px !important;
}
.tail-folio header ul {
  margin-bottom: 0px !important;
}

.tail-folio footer.bg-secondary {
  background-color: #1f2937 !important;
}
.tail-folio footer .grid {
  grid-template-rows: repeat(var(--tblr-rows, 1), 1fr) !important;
  grid-template-columns: repeat(var(--tblr-columns, 2), 1fr) !important;
}
.tail-folio footer ul {
  padding-left: 0px !important;
}
.tail-folio footer .mt-6 {
  margin-top: 1.5rem !important;
}
.tail-folio footer .space-y-4 {
  gap: 0rem !important;
}

.tail-folio .max-w-7xl {
  max-width: 1280px;
}
.tail-folio nav a img {
  width: 160px !important;
  flex-shrink: 0;
}
.tail-folio nav a {
  text-decoration: none !important;
}
.tail-folio nav .transition span {
  flex-shrink: 0;
}
.tail-folio nav .transition .rounded-full {
  flex-shrink: 0;
}
.tail-folio ol,
.tail-folio ul {
  margin: 0 !important;
  padding: 0 !important;
}
.tail-folio nav li button {
  padding-left: 12px !important;
  padding-right: 12px !important;
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}
.tail-folio nav .grid {
  grid-template-rows: auto !important;
  grid-template-columns: auto !important;
}
.tail-folio nav .grid a {
  color: #000 !important;
}
.tail-folio nav .absolute.top-full {
  width: 400px;
}

.tail-folio footer .lg\:pt-32 {
  padding-top: 6rem !important;
}
.tail-folio footer p.text-xs {
  font-size: 12px !important;
}
.tail-folio footer p.leading-5 {
  line-height: 1.25rem !important;
}
.tail-folio .flex.space-x-6 {
  margin-left: 20px !important;
}
.tail-folio .flex.space-x-6 a {
  margin-left: -20px !important;
}
.tail-folio footer .space-y-8 p.text-sm.leading-6 {
  margin-top: 30px !important;
}
.tail-folio .flex.space-x-6 img {
  width: 50px !important;
  height: 50px !important;
}
.tail-folio footer li a {
  text-decoration: none !important;
}

.tail-folio .w-6 {
  width: 1.5rem !important;
}
.tail-folio .h-6 {
  height: 1.5rem !important;
}
.tail-folio .px-6 {
  padding-right: 1.5rem !important;
  padding-left: 1.5rem !important;
}
.tail-folio .py-6 {
  padding-top: 1.5rem !important;
  padding-bottom: 1.5rem !important;
}
.tail-folio .px-3 {
  padding-left: 0.75rem !important;
  padding-right: 0.75rem !important;
}
.tail-folio .mt-6 {
  margin-top: 1.5rem !important;
}
.tail-folio .text-base {
  font-size: 16px !important;
  line-height: 24px !important;
}
.tail-folio .fixed .flow-root a {
  margin-top: 0px !important;
  color: #020817 !important;
  text-decoration: none !important;
}
.tail-folio .fixed .flow-root a:hover {
  color: #40a9ff !important;
}

.tail-folio .fixed .flow-root .space-y-2 {
  gap: 0rem !important;
}
.tail-folio .fixed .flow-root a.bg-primary {
  background-color: #1424ff !important;
  color: #fff !important;
}
.tail-folio .fixed .flow-root a.bg-primary:hover {
  color: #40a9ff !important;
}

/* ::: Reverse proxy css < Ends ::: */

/* :::::: markdown css ::::::> starts */
/* .markdown-content.dark-mode, .markdown-content.dark-mode a, .markdown-content.dark-mode .breadcrumb a, .markdown-content.dark-mode h1, .markdown-content.dark-mode h2, .markdown-content.dark-mode h4, .markdown-content.dark-mode ul li::marker, .markdown-content.dark-mode details[open] summary   {
 color: #d1d5db!important;
} */

.prose strong {
  font-weight: 600 !important;
}

.prose ul {
  margin-bottom: 1rem;
  list-style-position: inside;
  list-style-type: disc;
}

/* Optional if you're using Tailwind + typography plugin */

.markdown-content blockquote {
  background: #f3f4f6 !important;
  padding: 1px 35px;
  border-radius: 12px;
  margin-bottom: 40px;
  padding-top: 26px !important;
}
.markdown-content blockquote h3 {
  font-size: 20px !important;
  margin-bottom: 5px !important;
  margin-top: 0px !important;
}
.markdown-content blockquote a {
  color: #fff !important;
  text-decoration: none !important;
  display: inline-block;
  background: #1424ff !important;
  padding: 5px 15px;
  border-radius: 5px;
  font-weight: 500 !important;
  margin-top: 15px;
  font-size: 14px;
}
.markdown-content blockquote a strong {
  font-weight: 500 !important;
}

.markdown-content a {
  color: hsl(var(--primary)) !important;
  text-decoration: none;
}

.markdown-content a:hover {
  text-decoration: none;
}

.markdown-content .breadcrumb a {
  text-decoration: none;
  color: #000 !important;
}

.markdown-content .breadcrumb a:hover {
  text-decoration: none;
  color: #000 !important;
}

.markdown-content,
.markdown-content p,
.markdown-content ul,
.markdown-content ol {
  font-size: 16px !important;
  line-height: 28px !important;
  margin-bottom: 20px !important;
}

.card-content a:hover {
  text-decoration: none !important;
}

.markdown-content {
  color: #000 !important;
}
/* .markdown-content div p:nth-child(1) {
  font-size: 20px !important;
}  */
.markdown-content h1 {
  font-size: 36px !important;
  margin-bottom: 15px !important;
  line-height: 46px;
  letter-spacing: 0px;
  font-weight: 700;
  color: #000 !important;
  margin-top: 15px;
  border-bottom: 1px solid #cbd5e1;
  padding-bottom: 12px;
}
.markdown-content #related-articles {
  margin-top: 16px !important;
}
.markdown-content h2 {
  /* font-size: 32px !important;
  line-height: 40px !important;
  margin-top: 40px !important;
  margin-bottom: 15px !important;
  font-weight: 700; */
  margin-bottom: 1rem !important;
  margin-top: 2rem !important;
  font-size: 24px !important;
  line-height: 32px !important;
  font-weight: 600 !important;
  color: #000 !important;
}

.markdown-content h3 {
  font-size: 20px !important;
  line-height: 35px !important;
  margin-top: 28px !important;
  margin-bottom: 15px !important;
  font-weight: 600;
  color: #000 !important;
}

.markdown-content h4 {
  font-size: 20px !important;
  line-height: 30px !important;
  margin-bottom: 15px !important;
  font-weight: 600;
  color: #000 !important;
}

.markdown-content ul {
  padding-left: 35px !important;
  list-style: disc;
}
.markdown-content ol {
  padding-left: 35px !important;
  list-style: numeric;
}
.markdown-content ol li {
  padding-left: 5px !important;
}
.markdown-content ul li::marker {
  color: #000 !important;
}

.markdown-content li + li {
  margin-top: 4px !important;
}

.markdown-content pre {
  white-space: normal !important;
}

/* table > */
.markdown-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 10px 0px 20px 0px !important;
  background: white !important;
}

.markdown-content th,
.markdown-content td {
  border: 1px solid #e5e7eb;
  padding: 8px 12px;
}

.breadcrumb li {
  margin-left: 7px !important;
}

/* < table */

/* accordion > */
.markdown-content details summary {
  padding: 0px 0px 0px 30px;
  margin-bottom: 5px;
  position: relative;
  cursor: pointer;
}

.markdown-content details[open] summary {
  color: #000;
}

.markdown-content details summary {
  list-style: none;
  font-size: 20px;
}

.markdown-content summary::-webkit-details-marker {
  display: none !important;
  height: 0;
  width: 0;
  opacity: 0;
}
#activityLog h1 {
  display: none;
}
.markdown-content summary::after {
  content: " ";
  display: inline-block;
  width: 24px;
  height: 24px;
  background: url("/assets/images/ico-right-arrow.png") no-repeat center center;
  background-size: 24px;
  position: absolute;
  left: 0px;
}

.markdown-content details[open] summary:after {
  background: url("/assets/images/ico-down-arrow.png") no-repeat center center;
  width: 24px;
  height: 24px;
  background-size: 24px;
  position: absolute;
  left: 0px;
}

h3.notify {
  color: #991b1b !important;
  font-weight: 500 !important;
  margin-top: 0px !important;
  margin-bottom: 0px !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
}
.markdown-content b,
.markdown-content strong {
  font-weight: bold !important;
}

a h3 {
  margin-top: 0px !important;
}

#soc2-plans h3 {
  margin-top: 0px !important;
}

/*::: media query starts :::> */
@media (max-width: 576px) {
  .markdown-content h1 {
    font-size: 32px !important;
    line-height: 40px !important;
  }

  .markdown-content h2 {
    font-size: 24px !important;
    line-height: 32px !important;
  }
}

/* :::::: markdown css ::::::> ends */

.markdown-content.resource-card h2 {
  font-size: 24px !important;
  line-height: 32px !important;
}
.markdown-content.resource-card blockquote {
  background: #f3f4f6 !important;
  padding: 1px 35px;
  border-radius: 12px;
  margin-bottom: 40px;
  padding-top: 26px !important;
  border-left: 4px solid #1424ff;
}
.markdown-content.resource-card blockquote ul {
  margin-top: -5px !important;
  margin-bottom: 20px !important;
  padding-left: 0px !important;
}
.markdown-content.resource-card blockquote ul li {
  list-style-type: none !important;
  font-style: italic !important;
  color: #777 !important;
  font-size: 16px !important;
}
.markdown-content.resource-card .text-primary-foreground h2 {
  color: #fff !important;
}
.markdown-content.resource-card .text-primary-foreground button {
  color: #fff !important;
}
.markdown-content.resource-card .text-primary-foreground button a {
  color: #fff !important;
}
.markdown-content.resource-card hr {
  margin: 50px 0 !important;
}

/* Breadcrumb css starts */
.c-breadcrumbs__link[aria-disabled="true"] {
  pointer-events: none;
  cursor: default;
}
.crumbs-wrapper {
  background: #fafaff;
}
.crumbs-container a.c-breadcrumbs__link {
  font-size: 14px !important;
  background: transparent !important;
  padding: 5px 10px !important;
  border-radius: 30px !important;
}
.crumbs-container a.c-breadcrumbs__link:hover {
  background: #e8edf5 !important;
}
.crumbs-container a.c-breadcrumbs__link.is-current {
  background: #e8edf5 !important;
}
.crumbs-container .c-breadcrumbs__separator svg {
  width: 13px !important;
  margin-top: 2px !important;
  margin-left: 3px !important;
  margin-right: 3px !important;
}
/* Breadcrumb css ends */
/* Controls accordion - hippa page */
.new-accordion {
  margin: 20px 0px;
}

.new-accordion details summary {
  background: #e4e4e7;
  padding: 15px 10px 15px 15px;
  margin-bottom: 5px;
  position: relative;
}

.new-accordion details[open] summary {
  background: #e4e4e7;
  color: #000;
}

.new-accordion details summary {
  list-style: none;
}

.new-accordion summary::-webkit-details-marker {
  display: none;
}

.new-accordion summary::after {
  content: " ";
  display: inline-block;
  width: 24px;
  height: 24px;
  background: url("/assets/images/ico-right-arrow.png") no-repeat center center;
  background-size: 24px;
  left: inherit !important;
  right: 7px !important;
  top: 17px !important;
}

.new-accordion details[open] summary:after {
  background: url("/assets/images/ico-down-arrow.png") no-repeat center center;
  width: 24px;
  height: 24px;
  background-size: 24px;
  left: inherit !important;
  right: 7px !important;
  top: 17px !important;
}
