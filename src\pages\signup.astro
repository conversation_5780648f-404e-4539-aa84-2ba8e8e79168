---
import UserSignUp from "@/components/zitadel/signup";
import UserAttributes from "../content/loginAttributes.json";
import SEO from "@/components/SEO.astro";

const params = Astro.url.searchParams;
const tenantId = params.get("organizationId") as string;
const auditInvite = params.get("auditInvite") as unknown as boolean;
const decodedTenantId = atob(tenantId);

let rawJourneyKey = Astro.url.searchParams.get("journey");
const journeyKey = rawJourneyKey?.replace(/^"|"$/g, ""); // remove wrapping quotes

const selectedAttributes =
  journeyKey && journeyKey in UserAttributes
    ? UserAttributes[journeyKey as keyof typeof UserAttributes]
    : UserAttributes["default"];

const enableOpenObserve =
  import.meta.env.ENABLE_OPEN_OBSERVE !== undefined
    ? import.meta.env.ENABLE_OPEN_OBSERVE
    : false;
---

<head>
  <!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-NWW38GX7');</script>
<!-- End Google Tag Manager -->
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width" />
  <link rel="icon" type="image/svg+xml" href="/favicon.png" />
  <SEO
    title="Sign Up - Opsfolio - Compliance as a Service | SOC2, HIPAA, ISO Certification"
    description="Get SOC2, HIPAA, ISO certified fast with expert-guided compliance. 98% audit pass rate, 60% faster than DIY. Your Compliance System of Record."
    keywords="Policies, compliance, SOC2, HIPAA, ISO 27001, cybersecurity, audit, certification"
  />
  <link rel="stylesheet" href="/assets/ds/tailwind/css/tailwind.min.css" />
  {
    enableOpenObserve == "true" && (
      <script
        type="module"
        crossorigin="anonymous"
        src="/assets/scripts/index.756a5bbf.js"
      />
    )
  }
</head>
<!-- Google Tag Manager (noscript) -->
<noscript
  ><iframe
    src="https://www.googletagmanager.com/ns.html?id=GTM-NWW38GX7"
    height="0"
    width="0"
    style="display:none;visibility:hidden"></iframe></noscript
>
<!-- End Google Tag Manager (noscript) -->

<UserSignUp
  client:only="react"
  journeyKey={journeyKey}
  attributes={selectedAttributes}
/>
