---
import Layout from "../../layouts/Layout.astro";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Database, Camera, Zap, Clock, CheckCircle, ArrowRight, FileCheck } from "lucide-react";


const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Task',"aria-disabled": true ,
  },
  {
    index: "2", text: "Collect Evidence","aria-disabled": true 
  },
];
---
<Layout title="Opsfolio - Compliance as a Service | SOC2, HIPAA, ISO Certification"
  description="Get SOC2, HIPAA, ISO certified fast with expert-guided compliance. 98% audit pass rate, 60% faster than DIY. Your Compliance System of Record."
  keywords="Evidence, compliance, SOC2, HIPAA, ISO 27001, cybersecurity, audit, certification"
  url="/task/evidence/"
  breadcrumbLinks={breadcrumbLinks}>
  {/* Hero Section */}
      <section class="relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5 pt-16 pb-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center">
            <Badge variant="outline" className="mb-4">
              Evidence Collection & Management
            </Badge>
            <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
              Automate Audit Evidence Collection
            </h1>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Never scramble for evidence again. Our automated collection system continuously gathers, 
              organizes, and validates audit evidence from your systems, ensuring you're always audit-ready.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
                <a href="/get-started">
                  <Button size="lg">
                      Start Evidence Collection
                      <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </a>

                <a href="/contact">
                  <Button variant="outline" size="lg">See Evidence Demo
                  </Button>
                </a>
            </div>
          </div>
        </div>
      </section>

      {/* Key Features */}
      <section class="py-16 sm:py-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl lg:text-center">
            <h2 class="text-base font-semibold leading-7 text-primary">Evidence Features</h2>
            <p class="mt-2 text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Intelligent Evidence Automation
            </p>
          </div>
          <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <dl class="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
              <div class="flex flex-col">
                <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                  <Database className="h-5 w-5 flex-none text-primary" />
                  Automated Collection
                </dt>
                <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p class="flex-auto">
                    Connect to your systems and automatically collect evidence on a continuous basis. 
                    No more manual screenshots or document gathering before audits.
                  </p>
                </dd>
              </div>
              <div class="flex flex-col">
                <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                  <Camera className="h-5 w-5 flex-none text-primary" />
                  Smart Screenshots
                </dt>
                <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p class="flex-auto">
                    Automated screenshot capture with OCR and intelligent analysis. Validate 
                    configurations and settings across all your cloud and on-premise systems.
                  </p>
                </dd>
              </div>
              <div class="flex flex-col">
                <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                  <Zap className="h-5 w-5 flex-none text-primary" />
                  Real-time Validation
                </dt>
                <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p class="flex-auto">
                    Continuously validate evidence quality, completeness, and compliance mapping. 
                    Get alerts when evidence expires or configurations drift.
                  </p>
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </section>

      {/* Evidence Types */}
      <section class="py-16 sm:py-20 bg-muted">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
         
          <div class="mx-auto max-w-2xl text-center">
            <h2 class="text-base font-semibold leading-7 text-primary">Evidence Coverage</h2>
            <p class="mt-2 text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Comprehensive Evidence Collection
            </p>
          </div>
          
          <div class="mx-auto mt-16 grid max-w-4xl grid-cols-1 gap-12 sm:mt-20 lg:grid-cols-2">

            <div class="space-y-4 text-left md:ml-48 lg:ml-48">
              <h3 class="font-semibold text-foreground">Technical Evidence</h3>
              <ul class="space-y-2 text-muted-foreground">
                <li class="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  Access control configurations
                </li>
                <li class="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  Security monitoring logs
                </li>
                <li class="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  Vulnerability scan reports
                </li>
                <li class="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  Backup and recovery tests
                </li>
                <li class="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  Network security controls
                </li>
                <li class="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  Encryption implementations
                </li>
              </ul>
            </div>

            <!-- Right Column -->
            <div class="space-y-4 text-left md:ml-48 lg:ml-10">
              <h3 class="font-semibold text-foreground">Operational Evidence</h3>
              <ul class="space-y-2 text-muted-foreground">
                <li class="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  Employee training records
                </li>
                <li class="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  Policy acknowledgments
                </li>
                <li class="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  Incident response activities
                </li>
                <li class="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  Risk assessment documentation
                </li>
                <li class="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  Vendor security assessments
                </li>
                <li class="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  Business continuity testing
                </li>
              </ul>
            </div>

          </div>
        </div>
      </section>


      {/* Implementation Process */}
      <section class="py-16 sm:py-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl lg:text-center">
            <h2 class="text-base font-semibold leading-7 text-primary">Implementation</h2>
            <p class="mt-2 text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Evidence System Live in 1 Week
            </p>
          </div>
          <div class="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-6 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-4 lg:gap-8">
            {[
              {
                day: "Day 1",
                title: "System Integration",
                description: "Connect evidence collection agents to your cloud and on-premise systems."
              },
              {
                day: "Days 2-3",
                title: "Evidence Mapping",
                description: "Map evidence requirements to your specific compliance frameworks and controls."
              },
              {
                day: "Days 4-5",
                title: "Automation Setup",
                description: "Configure automated collection schedules and validation rules for each evidence type."
              },
              {
                day: "Days 6-7",
                title: "Validation & Training",
                description: "Validate evidence quality and train your team on the evidence management platform."
              }
            ].map((step) => (
              <div class="flex flex-col items-start">
                <div class="rounded-md bg-primary/10 p-2 ring-1 ring-primary/20">
                  <Clock className="h-6 w-6 text-primary" />
                </div>
                <dt class="mt-4 font-semibold text-foreground">{step.day}</dt>
                <dd class="mt-2 text-sm font-medium text-primary">{step.title}</dd>
                <dd class="mt-2 leading-7 text-muted-foreground text-sm">{step.description}</dd>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section class="py-16 sm:py-20 bg-muted">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Ready to Automate Your Evidence Collection?
            </h2>
            <p class="mx-auto mt-6 max-w-xl text-lg leading-8 text-muted-foreground">
              Stop spending weeks gathering evidence before audits. Maintain continuous audit readiness with automated collection.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
                <a href="/get-started">
                  <Button size="lg">Setup Evidence Collection
                  </Button>
                </a>
                <a href="/resources">
                  <Button variant="outline" size="lg">View Evidence Examples
                  </Button>
                </a>
            </div>
          </div>
        </div>
      </section>
</Layout>