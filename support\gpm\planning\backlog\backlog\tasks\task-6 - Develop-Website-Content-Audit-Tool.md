---
id: task-6
title: Develop Website Content Audit Tool
status: In Progress
assignee:
  - <PERSON><PERSON>
  <PERSON> <PERSON><PERSON><PERSON><PERSON>
created_date: '2025-08-29 10:10'
labels: []
dependencies: []
priority: medium
---

## Description

Prompts have been defined for auditing website content across multiple dimensions (focus, clarity, scannability, persuasiveness, calls-to-action/conversions, compliance/positioning, SEO, UX/design, and technical factors). The tool needs to:

Run audits: For each webpage, automatically run its content against all relevant audit prompts.

Generate structured output: Each audit should follow a standardized review format:

Executive Summary (1–2 paragraphs)
Remediated Contents (AI Fixes)
Strengths (with evidence citations)
Weaknesses (with evidence citations)
Recommendations (tied explicitly to evidence)
Observation vs. Hypothesis Table

Page-wise summary: Aggregate results of multiple audits for a single page into a page-level executive summary.

Site-wide summary: Combine all page-level executive summaries into a final executive summary for the entire site.


Tasks:

- [ ] Prepare expectations and UAT for the above application.
- [ ] Create DSPy-based script for the above use-case.
