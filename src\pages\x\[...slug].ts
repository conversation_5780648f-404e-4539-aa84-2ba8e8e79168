import { AstroGlobal } from "astro";

// Define the shape of our redirect entries
type Redirect = {
  src: string;    // shortlink key
  target: string; // full target URL (internal or external)
};

// Central redirect registry Ravi can edit directly
const redirects: Redirect[] = [
  { src: "cmmc-self-assess", target: "/login?journey=cmmc-self-assessment" },
  { src: "home", target: "/" },
  { src: "docs", target: "/documentation/getting-started" },
  { src: "contact", target: "/contact" },
];

// Build a lookup map for fast access
const redirectMap: Record<string, string> = Object.fromEntries(
  redirects.map(r => [r.src, r.target])
);

export async function GET({ params, redirect }: AstroGlobal) {
  const slug = params.slug;
  const target = redirectMap[slug];

  if (!target) {
    // 404 if no match
    return new Response(`Shortlink not found: ${slug}`, { status: 404 });
  }

  // Permanent redirect
  return redirect(target, 301);
}