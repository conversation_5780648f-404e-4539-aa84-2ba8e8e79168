---
import Layout from "../../../../layouts/Layout.astro";
import ControlDetail from "@/components/ControlDetail.tsx";
import { getCmmcControlByIdentifier } from "../../../../services/auditServices";
import type { ControlsType } from "../../../../services/auditTypes";
import { ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";


const regimeId = 7;
const tenantId = "239518031485599747";

const { slug } = Astro.params;

// Split by "/"
const [auditTypeIDStr, slugval] = slug.split("/");
const auditTypeID = Number(auditTypeIDStr);

const levelMap: Record<number, number> = {
  12: 1,
  13: 2,
  14: 3
};
const auditTypeIDText = auditTypeID
  ? `CMMC Level ${levelMap[auditTypeID] ?? auditTypeID}`
  : "";

let currentControl: ControlsType | null = null;

try {
  // currentControl = await getControlByIdentifier(
  //   regimeId,
  //   auditTypeID,
  //   tenantId,
  //   slugval,
  // );
  currentControl = await getCmmcControlByIdentifier(
    auditTypeID,
    tenantId,
    slugval,
  );
} catch (err) {
  console.error("Failed to fetch control:", err);
}
//console.log("Current Control:", currentControl);
const breadcrumbLinks = [
  { index: "0", text: "Home" },
  { index: "1", text: "Regime", "aria-disabled": true },
  { index: "2", text: "CMMC", "aria-disabled": true },
  { index: "3", text: "Controls", "aria-disabled": true },
  auditTypeIDText
    ? { index: "4", text: auditTypeIDText, "aria-disabled": true }
    : { index: "4", remove: true },
];
---

<Layout
  title="CMMC Controls Reference - Complete Compliance Guide"
  description="The Cybersecurity Maturity Model Certification (CMMC) program aligns with the information security requirements of the U.S."  
  keywords="CMMC controls, Trust Services Criteria, security controls, availability controls"
  url="regime/cmmc/controls"
  breadcrumbLinks={breadcrumbLinks}
>
  <div class="min-h-screen bg-background">
    {
      currentControl ? (

    <section
    class="relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5 pt-16 pb-20"
  >
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
      <div class="mx-auto max-w-4xl text-center">
        <h1
          class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl"
        >
          CMMC Control -  {currentControl.control_code}
        </h1>
        <p
          class="mt-6 text-lg leading-8 text-muted-foreground max-w-3xl mx-auto"
        >
        {currentControl.question}        
        </p>
      </div>

       <div class="mt-10 flex items-center justify-center gap-x-6">
            <a href="/regime/cmmc/controls">
              <Button size="lg" variant="default">
                View All Controls
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </a>
          </div>
    </div>
    </section>
    <section class="py-20">
      <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <ControlDetail client:only="react" controlData={currentControl} name="CMMC" />
      </div>
    </section>
      ) : (
        <p class="text-center py-20 text-muted-foreground">
          No control found for this slug.
        </p>
      )
    }
  </div>
</Layout>
