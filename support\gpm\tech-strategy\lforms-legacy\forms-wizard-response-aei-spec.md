# Netspective Self‑Service Multi‑form Response Assessment & Evaluation Interpretation (`RAEI`)

> Design and implementation guide

This document defines a reusable, deterministic scoring and reporting
infrastructure for any set of LHC‑Forms–based wizards driven by the
[Netspective Self‑Service Multi‑form Navigation and Data Entry Wizard](./forms-wizard-spec.md)
infrastructure. It explains how "scoring" (response* assessment and evaluation
interpretation*) metadata lives alongside questions, how scores (_assessments_)
are computed from responses, how provenance is preserved, and how results flow
into dashboards and evaluation reports.

## Goals and non‑goals

Goals

- Make assessment and evluation interpretation deterministic, explainable, and
  repeatable from just two inputs: the exact LHC‑Form definitions used and the
  answers captured.
- Keep all per‑question scoring rules (_assessment criteria_) co‑located with
  the question in LHC‑Forms JSON using supported metadata mechanisms (FHIR
  extensions or a reserved meta block).
- Support multiple question types out of the box: yes/no, single‑select,
  multi‑select, free text, numbers, dates, and computed/derived fields.
- Provide a simple aggregation model (weights, sums, averages, min/max,
  pass/fail thresholds) with clear evaluation order.
- Preserve full provenance for audit: which form version, which question, which
  rules produced which score.
- Emit results to both (a) a normalized SQL store for analytics and dashboards
  and (b) lightweight HTML/Markdown/PDF reports.

Non‑goals

- A visual scoring designer in v1 (author scoring in LHC-Form JSON).
- Probabilistic scoring. v1 is deterministic; ML assist or LLMs can be added
  later.

## Assessment criteria (scoring) metadata lives in LHC‑Forms JSON

We will support two equivalent encodings; the runtime knows how to read either.

<mark>The priority is to implement Option B first and then later extend it for
Option A. Option A is the best for long-term maintenance and applicability but
Option B is easier to implement so we'll start there.</mark>

**Option A**: FHIR Questionnaire extensions on the question (preferred when
forms are FHIR‑native)

- Each item (Questionnaire.item) can carry extensions under a Netspective
  namespace:

  - `https://schemas.netspective.com/lforms/scoring/v1/type` (binary | single |
    multi | text | number | date | computed)
  - `.../weight` (number)
  - `.../choices` (array of {code, score, weight?})
  - `.../textRules` (object describing empty/length/regex scoring)
  - `.../numberRules` (ranges, clamps)
  - `.../dateRules` (recency windows)
  - `.../computed` (expression string with allowed functions)
  - `.../aggregation` (for section/group items only: sum | avg | min | max |
    threshold)
  - `.../maxScore` (optional explicit cap)
  - `.../explain/humans` (human‑readable rule summary)
  - `.../explain/aiLlmPrompt` (ai LLM prompt snippet)
  - `.../evaluationSQL/computeClause` (SQL clause that can be used to compute
    scores)
  - `.../evaluationSQL/presentationClause` (SQL clause that can be used to
    present data in a report)

**Option B**: LHC‑Forms JSON reserved meta block on the item

```json
{
    "linkId": "X",
    // LHC-Form standard keys and values
    "responseAEI": {
        "evaluation": {
            "criteria": { nature, weight, /* response evaluation criteria for specific question X */ },
            // optional SQL if anything is special
            "sql": {
                "whereClause": "", // TODO figure out what to give for SQLite queries
            }
        },
        "presentation": {
            "sql": {
                "selectClause" : "", // TODO figure out what to give for SQLite queries
            },
            "explain": {
                "humans": "explanation for humans (deterministic)",
                "aiLlmPrompt": "explanation for LLM prompt (probabalistic)"
            }
        }
    }
}
```

Example for a yes/no:

```json
{
  "linkId": "q_smoking",
  "text": "Do you currently smoke?",
  "type": "boolean",
  "responseAEI": {
    "evaluation": {
      "criteria": {
        "nature": "binary",
        "weight": 1.0, // optional, default 1.0
        "yes": 100, // optional, default 100
        "no": 0, // optional, default 0
        "maxScore": 100 // optional, default 100
      }
      // optional "sql": {}
    },
    "presentation": {
      // optional "sql": {}
      "explain": {
        "humans": "Yes=100, No=0",
        "aiLlmPrompt": "Yes=100, No=0"
      }
    }
  }
}
```

Single‑select with weighted choices:

```json
{
  "linkId": "q_storage_encryption",
  "text": "Storage encryption level",
  "type": "choice",
  "answerOption": [
    { "valueCoding": { "code": "none", "display": "None" } },
    { "valueCoding": { "code": "partial", "display": "Partial" } },
    { "valueCoding": { "code": "full", "display": "Full" } }
  ],
  "responseAEI": {
    "evaluation": {
      "criteria": {
        "nature": "single-choice",
        "weight": 2.0, // optional, default 1.0
        "choices": [
          { "code": "none", "score": 0 },
          { "code": "partial", "score": 50 },
          { "code": "full", "score": 100 }
        ]
      }
      // optional "sql": {}
    },
    "presentation": {
      // optional "sql": {}
      "explain": {
        "humans": "Yes=100, No=0",
        "aiLlmPrompt": "Yes=100, No=0"
      }
    }
  }
}
```

Multi‑select with per‑choice weights:

```json
{
  "linkId": "q_controls",
  "text": "Which controls are implemented?",
  "type": "choice",
  "repeats": true,
  "answerOption": [
    { "valueCoding": { "code": "A" } },
    { "valueCoding": { "code": "B" } },
    { "valueCoding": { "code": "C" } }
  ],
  "responseAEI": {
    "evaluation": {
      "criteria": {
        "nature": "multi-choice",
        "weight": 1.0, // optional, default 1.0
        "choices": [
          { "code": "A", "score": 40, "weight": 1.0 },
          { "code": "B", "score": 30, "weight": 0.5 },
          { "code": "C", "score": 60, "weight": 1.5 }
        ],
        "aggregation": "sum", // optional, default "sum"
        "maxScore": 100 // optional, default no max
      }
      // optional "sql": {}
    },
    "presentation": {
      // optional "sql": {}
      "explain": {
        "humans": "Sum of selected choices; cap at 100",
        "aiLlmPrompt": "Sum of selected choices; cap at 100"
      }
    }
  }
}
```

Free‑text with length and regex scoring:

```json
{
  "linkId": "q_policy_text",
  "text": "Paste policy excerpt",
  "type": "text",
  "responseAEI": {
    "evaluation": {
      "criteria": {
        "nature": "text",
        "weight": 1.0,
        "textRules": {
          "emptyScore": 0,
          "lengthBands": [
            { "min": 1, "max": 100, "score": 20 },
            { "min": 101, "max": 300, "score": 60 },
            { "min": 301, "score": 100 }
          ],
          "regex": [
            { "pattern": "encryption", "flags": "i", "score": 10 },
            { "pattern": "key management", "flags": "i", "score": 10 }
          ],
          "cap": 100
        }
      }
      // optional "sql": {}
    },
    "presentation": {
      // optional "sql": {}
      "explain": {
        "humans": "Bands+keyword points, capped 100",
        "aiLlmPrompt": "Bands+keyword points, capped 100"
      }
    }
  }
}
```

Numbers and dates:

```json
{
  "linkId": "q_rto_hours",
  "text": "Declared RTO (hours)",
  "type": "integer",
  "responseAEI": {
    "evaluation": {
      "criteria": {
        "nature": "number",
        "weight": 1.0,
        "numberRules": [
          { "lte": 1, "score": 100 },
          { "gt": 1, "lte": 4, "score": 70 },
          { "gt": 4, "score": 30 }
        ]
      }
      // optional "sql": {}
    },
    "presentation": {
      // optional "sql": {}
      "explain": {
        "humans": "<=1h best",
        "aiLlmPrompt": "<=1h best"
      }
    }
  }
}
```

Section/group with aggregation:

- [ ] TODO: Need to define this, talk with Shahid if you need help.

```json
{
  "linkId": "sec_backup",
  "type": "group",
  "responseAEI": {
    "evaluation": {
      "aggregation": {
        "nature": "avg",
        "weight": 1.2,
        "threshold": { "min": 70, "passScore": 100, "failScore": 0 }
      }
      // optional "sql": {}
    },
    "presentation": {
      // optional "sql": {}
      "explain": {
        "humans": "Average inner questions; apply pass/fail",
        "aiLlmPrompt": "Average inner questions; apply pass/fail"
      }
    }
  }
}
```

---

## Deterministic scoring semantics

Evaluation order

1. For each question with `responseAEI`, compute a raw score in \[−∞, +∞] per
   its type rules.
2. Apply per‑question cap/maxScore if provided.
3. Multiply by question `weight` (default 1.0) to obtain `weighted_score`.
4. Propagate up to containing section/group nodes:
   - Combine children using section `aggregation` (sum|avg|min|max|threshold).
   - Apply section `sectionWeight`/`weight` if present.
5. The wizard or form total is the aggregation of its top‑level items (same
   rules).
6. Optional normalization of totals to 0–100 can be applied at the report layer;
   keep raw and normalized values.

Binary

- yes/no or boolean. If not answered, treat as empty; see “missing answers”
  below.

Single‑select

- Look up selected code. If missing in `choices`, score 0 unless `unknownScore`
  is set.

Multi‑select

- For each selected code, compute choiceScore × choiceWeight (default 1.0).
- Aggregate using `aggregation` (default sum) and cap if `maxScore` given.

Text

- If empty, use `emptyScore` (default 0).
- Otherwise, sum band score (first match wins unless `accumulateBands=true`)
  plus any regex scores; cap if configured.

Number

- Evaluate first matching rule in order unless `accumulate=true`; typical rules
  are bands (lt/lte/gt/gte, min/max).

Date

- Convert to epoch; compute recency vs now or a reference date using windows
  (e.g., “updated within 12 months = 100, 12–24 = 60, else 0”).

Computed

- Evaluate a sandboxed expression (see security). Inputs are normalized answers
  map, e.g., `val("q_rto_hours") <= 1 ? 100 : 70`.

Missing answers

- Default: missing yields score 0 unless the scoring block specifies
  `missingScore`.
- Reports must distinguish “0 due to missing” vs “0 due to rule.”

Determinism guarantee

- The scoring engine must be a pure function of: exact form JSON (including
  scoring metadata) + answer payload + engine version. Persist a
  `scoring_fingerprint` hash of these inputs with outputs.

---

## Data flow and provenance

Directory layout (per tenant → user → session), generalized:

```
/TENANT_ID/
  /USER_ID/
    /SESSION_ID/
      session.manifest.json          # data for tenant_id, user_id, etc. if not available in LHC Form responses
      provenance/
        FORM_KEY-definition.lhc-form.json
        ... (all definitions used, verbatim)
      responses/
        FORM_KEY.lhc-form-response.json
        ...
      generated/
        session.sqlite.db              # surveilr RSSD
        SESSION_ID.report.html
        SESSION_ID.report.pdf
        SESSION_ID.scores.json         # talk with Shahid
```

Fields that must be either in `session.manifest.json` or in the LHC Form Answers
so that the content gets into `surveilr` RSSD:

- tenant_id, user_id, session_id

Ingestion into RSSD (SQLite):

- Keep raw JSON and normalized tables.

### Suggested RSSD SQL views

- See [lforms.surveilr.sql](./lforms.surveilr.sql) which contains
  `uniform_resource_lform_item`.
- See [Fixtures README](./fixtures/README.md) to learn how to fill an RSSD and
  setup the view above.

`uniform_resource_lform_item` is a convenience view that pulls **top-level
LHC-Form or FHIR Questionnaire “items”** out of JSON stored in
`uniform_resource`, along with some key metadata about each item.

Think of it as a _flat table_ of “form items” (questions, sections, etc.)
extracted from the JSON definitions stored in the `surveilr` database.

#### **Where the data comes from**

1. **`uniform_resource_file` view** This is an existing view that already
   filters `uniform_resource` rows down to those that represent files (in this
   case, form definitions). It also provides metadata like:

   - `uniform_resource_id` – the unique ID for this stored resource.
   - `source_path` and `file_path_rel` – where the file came from.
   - `size_bytes` – file size.

2. **`uniform_resource` table** This table holds the actual file content in the
   `content` column, plus more metadata (`uri`, `nature`, etc.). Here, the
   `content` is an LHC-Form JSON or a FHIR Questionnaire JSON.

#### How the `uniform_resource_lform_item` view works

Step 1 – Base CTE (`base`):

- Joins `uniform_resource_file` (`uf`) with the full `uniform_resource` table
  (`ur`) so we can get:

  - The file metadata (`uniform_resource_id`, `nature`, `source_path`, etc.)
  - The JSON content from the `content` column.
- Casts `content` to TEXT as `content_json` so SQLite’s JSON1 functions can work
  with it.

Step 2 – Level 0 items (`level0_items`)

- LHC-Forms use a top-level array called `"items"` to store form
  fields/questions.
- FHIR Questionnaire uses `"item"` instead.
- This step **extracts each object** from either:
  - `$.items` **or**
  - `$.item`
- It uses `json_each()` to iterate over the array and pull each array element
  (`je.value`) as `item_obj`.
- Only processes elements where `json_type(...) = 'object'` (ignores strings,
  numbers, nulls).

The `UNION ALL` just combines the `"items"` and `"item"` cases.

Step 3 – Final SELECT

For each `item_obj` found at the top level:

- **`lform_name`** – Picks the form’s title/name from the root JSON using
  `COALESCE()` (tries several keys: `name`, `title`, `id`, etc.).
- **`item_id`** – Picks a unique identifier for the item, preferring:

  - `questionCode` (common in LHC-Forms), or
  - `linkId` (FHIR).
- **Other columns** – Pulls selected properties from the item JSON:

  - `data_type` – from `$.dataType`
  - `item_name`, `item_code`, `item_question`, `item_label`, `item_text` –
    direct metadata fields
  - `response_aei_nature` – from a nested path
    `$.responseAEI.evaluation.criteria.nature`
  - `response_aei` – the full `responseAEI` JSON block
- **Filter** – `WHERE item_id IS NOT NULL` ensures we only keep meaningful
  items.

#### What you get when you query `uniform_resource_lform_item` view

Each row = **one top-level form item** from one form file.

Columns tell you:

- Which form file it came from (`uniform_resource_id`, `file_path_rel`,
  `lform_name`)
- Which item it is (`item_id`, `item_name`, etc.)
- Key metadata from the item (`data_type`, labels/text)
- Any scoring or evaluation rules defined in `responseAEI`

Instead of manually digging into JSON in `uniform_resource` every time, you can:

- Quickly list the “questions” or “fields” in stored forms.
- See their identifiers, labels, and data types.
- Get any scoring metadata (`responseAEI`) for processing answers later.

---

## Scoring engine: normalization and computation

Input

- LHC‑Forms definition JSON (per form)
- Answer JSON (native LHC‑Forms output initially; optionally FHIR
  QuestionnaireResponse later)
- Engine config (e.g., default behavior for missing answers)
- Extract the answers from `item_value` column.
- Extract scoring blocks from `response_aei`; build an evaluation plan

Evaluation algorithm (pseudocode). Should be implemented in HTML reporting
Javascript first then moved to SQL when better understood.

```ts
function scoreForm(formDef, answers): FormScoreResult {
  const plan = buildPlan(formDef); // parse items -> nodes with scoring blocks
  const ctx = { values: normalize(answers), now: Date.now() };

  // leaf questions
  for (const q of plan.questionsInOrder) {
    const raw = scoreQuestion(q.scoring, ctx.values[q.linkId], ctx);
    const capped = cap(raw, q.scoring.maxScore);
    const weighted = capped * (q.scoring.weight ?? 1);
    emitQuestionScore(q, { raw, weighted, explain: q.scoring.explain || deriveExplain(q) });
  }

  // sections/groups bottom-up
  for (const s of plan.sectionsBottomUp) {
    const childScores = fetchChildWeightedScores(s);
    const aggRaw = aggregate(childScores, s.scoring.aggregation ?? "sum");
    const weighted = aggRaw * (s.scoring.weight ?? 1);
    const final = applyThresholdIfAny(weighted, s.scoring.threshold);
    emitSectionScore(s, { raw: aggRaw, weighted: final, explain: ... });
  }

  // form total
  const formRaw = aggregate(topLevelSectionWeightedScores(formDef), "sum");
  const formWeighted = formRaw; // already includes weights
  const normalized = normalize0to100(formWeighted); // optional config
  return { raw: formRaw, weighted: formWeighted, normalized };
}
```

## Report generation

Initially generate the report by running the SQL and getting the data from LHC
Form as SQL rows, running the scoring in Javascript, and presenting the report
in HTML.

Later once the scoring is better known, switch to generation of the report in
SQL.

Inputs

- Session id, tenant id
- Optional report profile (which sections to show, threshold colors, templates)

Process

1. Query `raei_form_total`, `raei_section_rollup`, `raei_session_scoring` for
   the session.
2. Merge with tenant/user/session metadata and per‑question provenance
   (`questions.scoring_json`).
3. Render HTML with:
   - Summary KPIs (session total normalized)
   - Per‑form cards with totals and trend vs prior session (if available)
   - Sections with bar charts or tables
   - Question‑level table including answer, score, weighted score, and “explain”
4. Export HTML→PDF (wkhtmltopdf/Puppeteer) and a parallel Markdown for archival
   and diffs.
5. Save artifacts under `/artifacts`.

Styling guidance

- Keep CSS tokens consistent with Wizard theming.
- Use color‑safe ranges for accessibility (e.g., green ≥80, amber 50–79, red
  <50).

---

## Error handling and edge cases

- Unknown choice codes in answers: treat as score 0, tag
  `is_unknown_choice = true` in a diagnostics table.
- Missing answers: use `missingScore` if provided; otherwise 0; flag
  `is_missing = true`.
- Division by zero or invalid expressions: emit `scoring_error` event, set score
  to 0, capture stack and offending rule in a diagnostics log/table.
- Version drift: if a form definition changes between steps in the same session,
  the manifest will show different `definition_sha256` values; the report must
  reflect the exact version per response.

## Testing strategy

Unit

- Per‑type scorers with golden test vectors (binary, single, multi, text,
  number, date, computed) using pure SQL.
- Section aggregation variants and caps/thresholds using pure SQL.

Property tests

- Idempotence: scoring the same inputs twice yields identical outputs.
- Monotonicity where applicable (e.g., longer text should never reduce length
  band score unless bands specify otherwise).

Integration

- End‑to‑end from wizard → artifacts with synthetic forms and answers.
- Ingestion to RSSD and view queries.

Explainability

- For each scored item, assert that `explain` includes the rule path and inputs
  used.

## Deliverables

- JSON Schema for `responseAEI` v1 and for section aggregation blocks.
- Scoring engine (TS) with:
  - SQL Parsing for FHIR extensions and `responseAEI`
  - Deterministic evaluators per type
- Report generator:
  - SQL views to generate the content that will become HTML in the client side

## Example: compact scoring JSON blocks by type

Binary

```json
{ "nature": "binary", "weight": 1, "yes": 100, "no": 0, "missingScore": 0 }
```

Single

```json
{
  "nature": "single",
  "weight": 2,
  "choices": [
    { "code": "none", "score": 0 },
    { "code": "partial", "score": 50 },
    { "code": "full", "score": 100 }
  ]
}
```

Multi

```json
{
  "nature": "multi",
  "weight": 1,
  "choices": [
    { "code": "A", "score": 40 },
    { "code": "B", "score": 30, "weight": 0.5 },
    { "code": "C", "score": 60, "weight": 1.5 }
  ],
  "aggregation": "sum",
  "maxScore": 100
}
```

Text

```json
{
  "nature": "text",
  "weight": 1,
  "textRules": {
    "emptyScore": 0,
    "lengthBands": [
      { "min": 1, "max": 100, "score": 20 },
      { "min": 101, "max": 300, "score": 60 },
      { "min": 301, "score": 100 }
    ],
    "regex": [{ "pattern": "encryption", "flags": "i", "score": 10 }]
  }
}
```

Number

```json
{
  "nature": "number",
  "weight": 1,
  "numberRules": [
    { "lte": 1, "score": 100 },
    { "gt": 1, "lte": 4, "score": 70 },
    { "gt": 4, "score": 30 }
  ]
}
```

Date

```json
{
  "nature": "date",
  "weight": 1,
  "dateRules": [
    { "withinDays": 365, "score": 100 },
    { "withinDays": 730, "score": 60 },
    { "olderThanDays": 730, "score": 0 }
  ]
}
```

Computed

```json
{
  "nature": "computed",
  "weight": 1,
  "expr": "val('q_rto_hours') <= 1 ? 100 : 70"
}
```

---

## Implementation notes and guidance

- Keep scoring code independent of UI; expose a pure SQL API usable in browser
  and server runtimes.
- Prefer explicit caps to prevent runaway totals; if omitted, let section/form
  totals be unbounded and normalize only for display.
- Always store both raw and weighted scores; dashboards often need both.
- Make the “explain” value first‑class. Every score in the DB should be
  traceable to the exact rule and input that produced it.

## Migration and extensibility

- Version `responseAEI` as `v1`. Introduce `version` field if/when schemas
  evolve.
- Add plugin hooks for future scorers (e.g., cosine similarity on text,
  rubric‑based graders).
- Allow tenant‑level profiles to adjust normalization and threshold palettes
  without altering raw scoring.

---

This infrastructure lets teams author all scoring rules where they belong—in the
forms—while the wizard and backend provide reliable orchestration, persistence,
analytics, and reporting. The end result is a transparent, deterministic
pipeline: form definition + answers → scores → dashboards/reports, with
audit‑ready provenance at every step.
