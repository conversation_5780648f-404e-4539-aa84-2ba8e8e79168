---
import Layout from "../../../../layouts/Layout.astro";
import { Badge } from "@/components/ui/badge";
import SOC2Controls from "@/components/SOC2Controls";
import { Shield, FileText, Users, Clock } from "lucide-react";
import * as soc2HeroContent from "@/content/control-explorer/soc2.md";
import ControlsExplorer from "@/components/ControlsExplorer";
import { getSoc2ControlList } from "../../../../services/auditServices";
import type {
ControlsRegimeType,
TransformedArrayType,
ControlRegimEnvironment,
ControlsType,
} from "../../../../services/auditTypes";
const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1",
    text: "Regime",
    "aria-disabled": true,
  },
  {
    index: "2",
    text: "SOC2",
    "aria-disabled": false,
  },
  {
    index: "3",
    text: "Controls",
    "aria-disabled": true,
  },
];

//type 1 cntrl dynamic

const regimeId = 1;
let tenantId = '239518031485599747';
tenantId = tenantId ? tenantId : "";


// --- Type I ---
const auditTypeID_Type1 = 2;
let auditControlsType1: ControlsType[] = [];
try {
  auditControlsType1 = await getSoc2ControlList(auditTypeID_Type1, tenantId);
} catch (err) {
  console.error("Failed to load Type I controls:", err);
  auditControlsType1 = [];
}
let auditControlType1 = auditControlsType1.map(item => ({ ...item }));
const transformedArrayType1: TransformedArrayType[] = [];
let currentCommonCriteriaType1: string = "";
auditControlType1.forEach((control) => {
  const typedControl = control as ControlsRegimeType;
  if (typedControl["common_criteria"] !== currentCommonCriteriaType1) {
    currentCommonCriteriaType1 = typedControl["common_criteria"];
    transformedArrayType1.push({
      common_criteria: currentCommonCriteriaType1,
      controls: [typedControl],
    });
  } else {
    transformedArrayType1[transformedArrayType1.length - 1].controls.push(typedControl);
  }
});
const ControlDataType1: ControlRegimEnvironment[] = [];
for (const item of transformedArrayType1) {
  const title = item["common_criteria"];
  const controlsForTitle = await Promise.all(
    item.controls.map(async (control) => ({
      controlCode: control["control_code"],
      controlQuestion: control["question"],
      controlIdentifier: control["control_identifier"],
      defaultSlug: control["slug"] ? control["slug"] : "",
    })),
  );
  ControlDataType1.push({
    title: title,
    controls: controlsForTitle,
  });
}

// --- Type II ---
const auditTypeID_Type2 = 3;
let auditControlsType2: ControlsType[] = [];
try {
  auditControlsType2 = await getSoc2ControlList(auditTypeID_Type2, tenantId);
} catch (err) {
  console.error("Failed to load Type II controls:", err);
  auditControlsType2 = [];
}
let auditControlType2 = auditControlsType2.map(item => ({ ...item }));
const transformedArrayType2: TransformedArrayType[] = [];
let currentCommonCriteriaType2: string = "";
auditControlType2.forEach((control) => {
  const typedControl = control as ControlsRegimeType;
  if (typedControl["common_criteria"] !== currentCommonCriteriaType2) {
    currentCommonCriteriaType2 = typedControl["common_criteria"];
    transformedArrayType2.push({
      common_criteria: currentCommonCriteriaType2,
      controls: [typedControl],
    });
  } else {
    transformedArrayType2[transformedArrayType2.length - 1].controls.push(typedControl);
  }
});
const ControlDataType2: ControlRegimEnvironment[] = [];
for (const item of transformedArrayType2) {
  const title = item["common_criteria"];
  const controlsForTitle = await Promise.all(
    item.controls.map(async (control) => ({
      controlCode: control["control_code"],
      controlQuestion: control["question"],
      controlIdentifier: control["control_identifier"],
      defaultSlug: control["slug"] ? control["slug"] : "",
    })),
  );
  ControlDataType2.push({
    title: title,
    controls: controlsForTitle,
  });
}



 const hasRealData =
  Array.isArray(ControlDataType1) &&
  ControlDataType1.some(section => Array.isArray(section.controls) && section.controls.length > 0);
 //console.log("auditControls", ControlData,hasRealData);
---

<Layout
  title="SOC 2 Controls Reference - Complete Compliance Guide"
  description="Comprehensive SOC 2 controls reference covering Type I and Type II requirements. Navigate all SOC 2 controls with detailed explanations and compliance mapping."
  keywords="SOC 2 controls, SOC 2 Type I, SOC 2 Type II, Trust Services Criteria, security controls, availability controls"
  url="https://opsfolio.com/regime/soc2/controls"
  breadcrumbLinks={breadcrumbLinks}
>
  {/* Hero Section */}
  <section
    class="relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5 pt-16 pb-20"
  >
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
      <div class="mx-auto max-w-4xl text-center">
        <Badge variant="outline" className="mb-4">
          {soc2HeroContent.frontmatter.badge}
        </Badge>
        <h1
          class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl"
        >
          {soc2HeroContent.frontmatter.title}
        </h1>
        <p
          class="mt-6 text-lg leading-8 text-muted-foreground max-w-3xl mx-auto"
        >
          {soc2HeroContent.frontmatter.description}
        </p>
      </div>
    </div>
  </section>

  {hasRealData ? (
    <ControlsExplorer 
      controlData={ControlDataType1}
      controlDataType2={ControlDataType2}
      controlPageUrl=""
      sessionId={Number(regimeId)}
      auditTypeId={Number(1)} 
      controlType="soc2"
      client:only="react" />
  ) : (
    <SOC2Controls client:only="react" />
  )
  }

  

  {/* Last Updated */}
  <section class="py-8 border-t bg-muted/30">
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
      <div class="mx-auto max-w-4xl text-center">
        <div
          class="flex items-center justify-center gap-2 text-sm text-muted-foreground"
        >
          <Clock className="h-4 w-4" />
          <span>Last Updated: September 3, 2025</span>
        </div>
      </div>
    </div>
  </section>
</Layout>
