---
id: task-4
title: Opsfolio Threat Exposure Management (TEM) CTA Page with LHC Form
status: In Progress
assignee:
  - <PERSON><PERSON><PERSON><PERSON><PERSON>
  <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
  - <PERSON><PERSON><PERSON><PERSON>
  - <PERSON><PERSON>
created_date: '2025-08-29 09:56'
labels: []
dependencies: []
priority: high
---

## Description

We need to create a landing page for **Threat Exposure Management (TEM)** under Opsfolio.com.  
The page should load an **LHC form** (`https://opsfolio.com/task/threat-exposure-management`) and allow both anonymous and authenticated users to begin an assessment.  

This will serve as the **entry point CTA** for Opsfolio Threat Exposure Management, eventually integrating with Surveilr for automated report generation and Fleet/Quality Folio workflows.  

---

## Requirements  

### Landing Page  (`https://opsfolio.com/task/threat-exposure-management') & CTA  ( Page need to be done Astro)
- Create a landing page on Opsfolio.com  -done
- Add a **CTA button**: `"Get TEM Assessment"`  ---done
- When clicked CTA button load another page which loads the LHC form `).  

### Form Handling  
- **Anonymous users**: If a user is not logged in and clicks `Submit`, prompt them to log in before completing submission.  
- **Authenticated users**: Can directly submit the LHC form.  

### Submission Flow (Phase 1 - Initial)  
- On submission, capture all form responses.  
- Prepare an **email to `<EMAIL>`** containing the submitted data.  

### Submission Flow (Phase 2 - Later Enhancements)  
- Trigger **security report generation** via Surveilr after form submission.  
- Responses ingested into Surveilr’s evidence warehouse.  
- Generate a **SQLpage report** as part of Fleet or Quality Folio dashboards.  



## Acceptance Criteria  
- [x] Landing page created with **Get TEM Assessment** CTA button.  
- [ ] LHC form loads correctly in the landing page CTA click on `https://opsfolio.com/task/threat-exposure-management`.  
- [ ] Anonymous users are redirected to login before submission.  
- [ ] Form responses are emailed to `<EMAIL>` after submission (Phase 1).  
- [ ] Future-proofing for Surveilr integration + SQLpage reports documented.  

---

## Notes  
- Use the provided LHC form schema.  
- Ensure accessibility and consistent UI with Opsfolio design system.  
- Keep architecture extensible for **Surveilr + SQLpage integration** in Phase 2.
