{"resourceType": "Questionnaire", "meta": {"profile": ["http://hl7.org/fhir/4.0/StructureDefinition/Questionnaire"]}, "title": "CMMC Level 2 - Audit and Accountability (AU)", "status": "draft", "item": [{"item": [{"item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "AU.L2-3.3.1_A_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "AU.L2-3.3.1_A_Q", "text": "Has your organization formally specified the event types required to be logged?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "AU.L2-3.3.1_A_GRP", "prefix": "(a)", "text": "Specification of Event Types to be Logged"}, {"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "AU.L2-3.3.1_B_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "AU.L2-3.3.1_B_Q", "text": "What information content has been defined for inclusion in audit records?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Timestamps"}}, {"valueCoding": {"display": "Source and destination addresses"}}, {"valueCoding": {"display": "User or process identifiers"}}, {"valueCoding": {"display": "Event descriptions"}}, {"valueCoding": {"display": "Success or failure indications"}}]}], "type": "group", "linkId": "AU.L2-3.3.1_B_GRP", "prefix": "(b)", "text": "Definition of Audit Record Content"}, {"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "AU.L2-3.3.1_C_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "AU.L2-3.3.1_C_Q", "text": "Are audit records being actively generated by all in-scope systems?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "AU.L2-3.3.1_C_GRP", "prefix": "(c)", "text": "Creation of Audit Records"}, {"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "AU.L2-3.3.1_D_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "AU.L2-3.3.1_D_Q", "text": "Do the generated audit records contain the content that was defined?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "AU.L2-3.3.1_D_GRP", "prefix": "(d)", "text": "Inclusion of Defined Content"}, {"type": "group", "prefix": "(e)", "linkId": "AU.L2-3.3.1_E_GRP", "text": "Definition of Retention Requirements", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "AU.L2-3.3.1_E_NOTES", "text": "Notes / Evidence ", "repeats": false, "item": [{"linkId": "AU.L2-3.3.1_E_NOTES_helpText", "type": "display", "text": "90 days online, 1 year offline", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}], "type": "text", "linkId": "AU.L2-3.3.1_E_Q", "text": "What is the defined retention period for audit records?", "required": true, "repeats": false}]}, {"type": "group", "prefix": "(f)", "linkId": "AU.L2-3.3.1_F_GRP", "text": "Retention of Audit Records", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "AU.L2-3.3.1_F_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "AU.L2-3.3.1_F_Q", "text": "Are audit records retained according to the defined policy?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "AU.L2-3.3.1_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "AU.L2-3.3.1_help", "type": "display", "text": "Create and retain system audit logs and records to the extent needed to enable the monitoring, analysis, investigation, and reporting of unlawful or unauthorized system activity.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}], "type": "group", "linkId": "AU.L2-3.3.1", "text": "AU.L2-3.3.1 – System Auditing"}, {"type": "group", "linkId": "AU.L2-3.3.2", "text": "AU.L2-3.3.2 – User Accountability", "item": [{"item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "AU.L2-3.3.2_A_NOTES", "text": "Notes / Evidence", "repeats": false}, {"linkId": "AU.L2-3.3.2_A_Q_helpText", "type": "display", "text": "unique user ID, not just a shared account", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "AU.L2-3.3.2_A_Q", "text": "Does your audit log content policy explicitly define the fields needed to trace actions to a specific user?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "AU.L2-3.3.2_A_GRP", "prefix": "(a)", "text": "Definition of Content for Traceability"}, {"type": "group", "prefix": "(b)", "linkId": "AU.L2-3.3.2_B_GRP", "text": "Inclusion of Traceability Content", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "AU.L2-3.3.2_B_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "AU.L2-3.3.2_B_Q", "text": "Do the created audit records contain the defined content required for user traceability?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "AU.L2-3.3.2_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "AU.L2-3.3.2_help", "type": "display", "text": "Ensure that the actions of individual system users can be uniquely traced to those users so they can be held accountable for their actions.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}]}, {"type": "group", "linkId": "AU.L2-3.3.3", "text": "AU.L2-3.3.3 – Event Review", "item": [{"item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "AU.L2-3.3.3_A_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "AU.L2-3.3.3_A_Q", "text": "Is there a defined process and frequency for reviewing the list of system events being logged?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes, annually"}}, {"valueCoding": {"display": "Yes, following major incidents or system changes"}}, {"valueCoding": {"display": "Yes, on another defined frequency"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "AU.L2-3.3.3_A_GRP", "prefix": "(a)", "text": "Definition of Review Process"}, {"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "AU.L2-3.3.3_B_NOTES", "text": "Notes / Evidence ", "repeats": false, "item": [{"linkId": "AU.L2-3.3.3_B_NOTES_helpText", "type": "display", "text": "provide meeting minutes or review records", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "AU.L2-3.3.3_B_Q", "text": "Are these reviews performed according to the defined process?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "AU.L2-3.3.3_B_GRP", "prefix": "(b)", "text": "Execution of Review"}, {"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "AU.L2-3.3.3_C_NOTES", "text": "Notes / Evidence ", "repeats": false, "item": [{"linkId": "AU.L2-3.3.3_C_NOTES_helpText", "type": "display", "text": "change request tickets", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "AU.L2-3.3.3_C_Q", "text": "Is the system's logging configuration updated based on the findings of these reviews?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "AU.L2-3.3.3_C_GRP", "prefix": "(c)", "text": "Updating Logged Events"}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "AU.L2-3.3.3_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "AU.L2-3.3.3_help", "type": "display", "text": "Review and update logged events.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}]}, {"type": "group", "linkId": "AU.L2-3.3.4", "text": "AU.L2-3.3.4 – Audit Failure Alerting", "item": [{"item": [{"type": "group", "prefix": "(a)", "linkId": "AU.L2-3.3.4_A_GRP", "text": "Identification of Alert <PERSON>nts", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "AU.L2-3.3.4_A_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "text", "linkId": "AU.L2-3.3.4_A_Q", "text": "Who (by role or individual) is designated to receive alerts in the event of an audit logging process failure?", "required": true, "repeats": false}]}, {"type": "group", "prefix": "(b)", "linkId": "AU.L2-3.3.4_B_GRP", "text": "Definition of Audit Failures", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "AU.L2-3.3.4_B_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "AU.L2-3.3.4_B_Q", "text": "Which types of audit logging failures are defined to generate an alert?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Logging service/process failure or stop"}}, {"valueCoding": {"display": "Audit storage capacity reached"}}, {"valueCoding": {"display": "Other failure in record capturing"}}]}]}, {"type": "group", "prefix": "(c)", "linkId": "AU.L2-3.3.4_C_GRP", "text": "Alerting Implementation", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "AU.L2-3.3.4_C_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "AU.L2-3.3.4_C_Q", "text": "Are alerts automatically generated and sent to the identified personnel upon an audit process failure?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "AU.L2-3.3.4_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "AU.L2-3.3.4_help", "type": "display", "text": "<PERSON><PERSON> in the event of an audit logging process failure.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}]}, {"type": "group", "linkId": "AU.L2-3.3.5", "text": "AU.L2-3.3.5 – Audit Correlation", "item": [{"item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "AU.L2-3.3.5_A_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "AU.L2-3.3.5_A_Q", "text": "Do you have a defined process for audit record review, analysis, and reporting to investigate suspicious activity?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "AU.L2-3.3.5_A_GRP", "prefix": "(a)", "text": "Definition of Review Processes"}, {"type": "group", "prefix": "(b)", "linkId": "AU.L2-3.3.5_B_GRP", "text": "Correlation of Processes", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "AU.L2-3.3.5_B_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "AU.L2-3.3.5_B_Q", "text": "How do you correlate audit information from different log sources to support investigation and response?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Automated via a SIEM or log management platform"}}, {"valueCoding": {"display": "Manual consolidation and review process"}}]}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "AU.L2-3.3.5_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "AU.L2-3.3.5_help", "type": "display", "text": "Correlate audit record review, analysis, and reporting processes for investigation and response to indications of unlawful, unauthorized, suspicious, or unusual activity.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}]}, {"type": "group", "linkId": "AU.L2-3.3.6", "text": "AU.L2-3.3.6 – Reduction & Reporting", "item": [{"item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "AU.L2-3.3.6_A_NOTES", "text": "Notes / Evidence .", "repeats": false, "item": [{"linkId": "AU.L2-3.3.6_A_NOTES_helpText", "type": "display", "text": "query/filter features in a SIEM", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "AU.L2-3.3.6_A_Q", "text": "Do you have a capability to reduce or filter audit records to support on-demand analysis?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "AU.L2-3.3.6_A_GRP", "prefix": "(a)", "text": "Audit Record Reduction"}, {"type": "group", "prefix": "(b)", "linkId": "AU.L2-3.3.6_B_GRP", "text": "Report Generation", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "AU.L2-3.3.6_B_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "AU.L2-3.3.6_B_Q", "text": "Do you have a capability to generate customizable audit reports on demand?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "AU.L2-3.3.6_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "AU.L2-3.3.6_help", "type": "display", "text": "Provide audit record reduction and report generation to support on-demand analysis and reporting.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}]}, {"type": "group", "linkId": "AU.L2-3.3.7", "text": "AU.L2-3.3.7 – Authoritative Time Source", "item": [{"item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "AU.L2-3.3.7_A_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "AU.L2-3.3.7_A_Q", "text": "Do internal system clocks generate timestamps for all audit records?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "AU.L2-3.3.7_A_GRP", "prefix": "(a)", "text": "Use of Internal Clocks for Timestamps"}, {"type": "group", "prefix": "(b)", "linkId": "AU.L2-3.3.7_B_GRP", "text": "Specification of Authoritative Source", "item": [{"item": [{"type": "text", "linkId": "AU.L2-3.3.7_B_NOTES", "text": "Notes / Evidence", "repeats": false}, {"linkId": "AU.L2-3.3.7_B_Q_helpText", "type": "display", "text": "NTP server", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "text", "linkId": "AU.L2-3.3.7_B_Q", "text": "What is the specified authoritative time source for clock synchronization?", "required": true, "repeats": false}]}, {"type": "group", "prefix": "(c)", "linkId": "AU.L2-3.3.7_C_GRP", "text": "Synchronization with Authoritative Source", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "AU.L2-3.3.7_C_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "AU.L2-3.3.7_C_Q", "text": "Are all internal system clocks automatically compared and synchronized with the authoritative time source?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "AU.L2-3.3.7_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "AU.L2-3.3.7_help", "type": "display", "text": "Provide a system capability that compares and synchronizes internal system clocks with an authoritative source to generate time stamps for audit records.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}]}, {"item": [{"item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "AU.L2-3.3.8_ABC_NOTES", "text": "Notes / Evidence ", "repeats": false, "item": [{"linkId": "AU.L2-3.3.8_ABC_NOTES_helpText", "type": "display", "text": "restricted permissions, separate log server, write-once media", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "AU.L2-3.3.8_ABC_Q", "text": "How is audit information (logs) protected from unauthorized entity?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Access"}}, {"valueCoding": {"display": "Modification"}}, {"valueCoding": {"display": "Deletion"}}]}], "type": "group", "linkId": "AU.L2-3.3.8_ABC_GRP", "prefix": "(a)", "text": "Protection of Audit Information"}, {"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "AU.L2-3.3.8_DEF_NOTES", "text": "Notes / Evidence", "repeats": false}, {"linkId": "AU.L2-3.3.8_DEF_Q_helpText", "type": "display", "text": "SIEM, log forwarders", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "AU.L2-3.3.8_DEF_Q", "text": "How are audit logging tools protected from unauthorized entity?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Access"}}, {"valueCoding": {"display": "Modification"}}, {"valueCoding": {"display": "Deletion"}}]}], "type": "group", "linkId": "AU.L2-3.3.8_DEF_GRP", "prefix": "(b)", "text": "Protection of Audit Logging Tools"}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "AU.L2-3.3.8_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "AU.L2-3.3.8_help", "type": "display", "text": "Protect audit information and audit logging tools from unauthorized access, modification, and deletion.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}], "type": "group", "linkId": "AU.L2-3.3.8", "text": "AU.L2-3.3.8 – Audit Protection"}, {"type": "group", "linkId": "AU.L2-3.3.9", "text": "AU.L2-3.3.9 – Audit Management", "item": [{"item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "AU.L2-3.3.9_A_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "AU.L2-3.3.9_A_Q", "text": "Is there a formally defined subset of privileged users who are authorized to manage audit logging functionality?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "AU.L2-3.3.9_A_GRP", "prefix": "(a)", "text": "Definition of Authorized Users"}, {"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "AU.L2-3.3.9_B_NOTES", "text": "Notes / Evidence", "repeats": false}, {"linkId": "AU.L2-3.3.9_B_Q_helpText", "type": "display", "text": "modifying audit configurations", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "AU.L2-3.3.9_B_Q", "text": "Is the ability to manage audit functions technically restricted to only this defined subset of users?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "AU.L2-3.3.9_B_GRP", "prefix": "(b)", "text": "Limitation of Access"}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "AU.L2-3.3.9_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "AU.L2-3.3.9_help", "type": "display", "text": "Limit management of audit logging functionality to a subset of privileged users.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}]}]}