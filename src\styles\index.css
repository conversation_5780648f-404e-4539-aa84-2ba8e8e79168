@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 236 100% 54%;
    --primary-foreground: 0 0% 98%;
    --primary-glow: 239 100% 67%;

    --secondary: 215 27.9% 16.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;

    --accent: 236 100% 54%;
    --accent-foreground: 0 0% 98%;

    --success: 142 76% 36%;
    --success-foreground: 355.7 100% 97.3%;

    --warning: 32 95% 44%;
    --warning-foreground: 355.7 100% 97.3%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 236 100% 54%;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow)));
    --gradient-subtle: linear-gradient(180deg, hsl(var(--background)), hsl(var(--muted)));
    --gradient-hero: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(239 100% 67%) 100%);

    /* Shadows */
    --shadow-elegant: 0 10px 30px -10px hsl(var(--primary) / 0.1);
    --shadow-glow: 0 0 40px hsl(var(--primary-glow) / 0.15);
    --shadow-card: 0 4px 6px -1px hsl(0 0% 0% / 0.05);

    /* Transitions */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 236 100% 54%;
    --primary-foreground: 222.2 84% 4.9%;
    --primary-glow: 239 100% 67%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 236 100% 54%;
    --accent-foreground: 222.2 84% 4.9%;

    --success: 142 76% 36%;
    --success-foreground: 355.7 100% 97.3%;

    --warning: 32 95% 44%;
    --warning-foreground: 355.7 100% 97.3%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 236 100% 54%;

    /* Dark mode gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow)));
    --gradient-subtle: linear-gradient(180deg, hsl(var(--background)), hsl(var(--muted)));
    --gradient-hero: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(239 100% 67%) 100%);

    /* Dark mode shadows */
    --shadow-elegant: 0 10px 30px -10px hsl(var(--primary) / 0.2);
    --shadow-glow: 0 0 40px hsl(var(--primary-glow) / 0.3);
    --shadow-card: 0 4px 6px -1px hsl(0 0% 0% / 0.3);
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
   * {
    @apply border-border;
  } 

  body {
     @apply bg-background text-foreground; 
  }
}