/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable unicorn/no-null */
import sqlite3 from "sqlite3";
import matter from "gray-matter";
import type {
  AssetData,
  ControlDetailType,
  ControlsType,
  ControlType,
  EvidenceContentType,
  HippaControl,
  HiTrustControl,
  NetspectiveControlType,
  PoliciesType,
  Soc2Type,
  ThsaControl,
  ScfDomainData,
  ScfData,
  ControlRegimSideMenuData,
} from "./auditTypes.ts";
import dbPath from "./dbPath.json" with { type: "json" };

import type {
  AuditControls,
  ControlPolicy,
  PolicyEvidence,
} from "support/services/dataSchema";
import { z } from "zod";

const CvssMetricsSchema = z.object({
  baseScore: z.number(),
  exploitabilityScore: z.number(),
  impactScore: z.number(),
});

const CvssSchema = z.object({
  version: z.string(),
  vector: z.string(),
  metrics: CvssMetricsSchema,
});

const PackageSchema = z.object({
  name: z.string(),
  SPDXID: z.string(),
  versionInfo: z.string(),
  supplier: z.string(),
  downloadLocation: z.string(),
  filesAnalyzed: z.boolean(),
  sourceInfo: z.string(),
  licenseConcluded: z.string(),
  licenseDeclared: z.string(),
  copyrightText: z.string(),
});

const RelatedVulnerabilitySchema = z.object({
  id: z.string(),
  dataSource: z.string(),
  namespace: z.string(),
  severity: z.string(),
  urls: z.array(z.string()),
  description: z.string(),
  cvss: z.array(CvssSchema),
});

const VulnerabilitySchema = z.object({
  id: z.string(),
  dataSource: z.string(),
  namespace: z.string(),
  severity: z.string(),
  urls: z.array(z.string()),
  description: z.string(),
  cvss: z.array(CvssSchema),
  fix: z.object({
    versions: z.array(z.string()),
    state: z.string(),
  }),
});

const MatchDetailSchema = z.object({
  type: z.string(),
  matcher: z.string(),
  searchedBy: z.object({
    language: z.string(),
    namespace: z.string(),
    package: z.object({
      name: z.string(),
      version: z.string(),
    }),
  }),
  found: z.object({
    versionConstraint: z.string(),
    vulnerabilityID: z.string(),
  }),
});

const ArtifactSchema = z.object({
  id: z.string(),
  name: z.string(),
  version: z.string(),
  type: z.string(),
  locations: z.array(z.object({ path: z.string() })),
  language: z.string(),
  cpes: z.array(z.string()),
  purl: z.string(),
});

const SourceSchema = z.object({
  type: z.string(),
  target: z.string(),
});

const DistroSchema = z.object({
  name: z.string(),
  version: z.string(),
  idLike: z.string().nullable(),
});

const DescriptorSchema = z.object({
  name: z.string(),
  version: z.string(),
});

const SbomGrypeSchema = z.object({
  matches: z.array(z.object({
    vulnerability: VulnerabilitySchema,
    relatedVulnerabilities: z.array(RelatedVulnerabilitySchema),
    matchDetails: z.array(MatchDetailSchema),
    artifact: ArtifactSchema,
  })),
  source: SourceSchema,
  distro: DistroSchema,
  descriptor: DescriptorSchema,
});

const SbomSyftSchema = z.object({
  packages: z.array(PackageSchema),
});

const ServerDataSchema = z.object({
  name: z.string(),
  server: z.string(),
  description: z.string(),
  port: z.string(),
  experimental_version: z.string(),
  production_version: z.string(),
  latest_vendor_version: z.string(),
  resource_utilization: z.string(),
  log_file: z.string(),
  url: z.string(),
  vendor_link: z.string(),
  installation_date: z.string(),
  criticality: z.string(),
  owner: z.string(),
  tag: z.string(),
  asset_criticality: z.union([z.string(), z.null()]),
  asymmetric_keys: z.union([z.string(), z.null()]),
  cryptographic_key: z.union([z.string(), z.null()]),
  symmetric_keys: z.union([z.string(), z.null()]),
  status: z.string(),
});

const ServerDataArraySchema = z.array(ServerDataSchema);
const ServerStatusSchema = z.object({
  id: z.string(),
  image: z.string(),
  name: z.string(),
  status: z.string(),
});

const DeviceObjectSchema = z.object({
  device_id: z.string(),
  content: z.string(),
  name: z.string(),
});

const DeviceArraySchema = z.array(DeviceObjectSchema);
const DeviceContentItemSchema = z.object({
  id: z.string(),
  image: z.string(),
  name: z.string(),
  status: z.string(),
});

const policyDataSchema = z.object({
  version: z.string(),
  filePath: z.string(),
  name: z.string(),
  policyIds: z.string(),
});

export type SbomGrype = z.infer<typeof SbomGrypeSchema>;
export type sbomSyft = z.infer<typeof SbomSyftSchema>;
export type Vulnerability = z.infer<typeof VulnerabilitySchema>;
export type MatchDetail = z.infer<typeof MatchDetailSchema>;
export type RelatedVulnerability = z.infer<typeof RelatedVulnerabilitySchema>;
export type Artifact = z.infer<typeof ArtifactSchema>;
export type Package = z.infer<typeof PackageSchema>;
export type ServerDataArray = z.infer<typeof ServerDataArraySchema>;
export type ServerStatus = z.infer<typeof ServerStatusSchema>;
export type DeviceArray = z.infer<typeof DeviceArraySchema>;
export type DeviceContentItem = z.infer<typeof DeviceContentItemSchema>;
export type PolicyData = z.infer<typeof policyDataSchema>;
export interface ThreatModelType {
  ThreatModel: {
    DrawingSurfaceList: {
      DrawingSurfaceModel: {
        Borders: {
          "a:KeyValueOfguidanyType": ThreatModelKeyValueItem[];
        };
        Lines: {
          "a:KeyValueOfguidanyType": ThreatModelKeyValueItem[];
        };
      };
    };
    ThreatInstances: {
      "a:KeyValueOfstringThreatpc_P0_PhOB": ThreatModelJSON[];
    };
    MetaInformation: {
      "Assumptions": string;
      "Contributors": string;
      "ExternalDependencies": string;
      "HighLevelSystemDescription": string;
      "Owner": string;
      "Reviewer": string;
      "ThreatModelName": string;
    };
    Validations: {
      "a:Validation": {
        "a:Enabled": string;
        "a:Message": string;
        "a:Source": string;
      };
    };
  };
}
export interface ThreatModelKeyValueItem {
  "a:Value": {
    Properties?: {
      "a:anyType"?: {
        "b:DisplayName": string;
        "b:Value": {
          "#text": string;
        };
      }[];
    };
    "@i:type": string;
  };
}
interface KeyValue {
  "a:Key": string;
  "a:Value": string;
}

interface Properties {
  "a:KeyValueOfstringstring": KeyValue[];
}

interface StateInformation {
  "@i:nil": string;
}

interface InteractionString {
  "@i:nil": string;
}

interface ThreatModel {
  "b:ChangedBy": string;
  "b:DrawingSurfaceGuid": string;
  "b:FlowGuid": string;
  "b:Id": string;
  "b:InteractionKey": string;
  "b:InteractionString": InteractionString;
  "b:ModifiedAt": string;
  "b:Priority": string;
  "b:Properties": Properties;
  "b:SourceGuid": string;
  "b:State": string;
  "b:StateInformation": StateInformation;
  "b:TargetGuid": string;
  "b:Title": {
    "@i:nil": string;
  };
  "b:TypeId": string;
  "b:Upgraded": string;
  "b:UserThreatCategory": {
    "@i:nil": string;
  };
  "b:UserThreatDescription": {
    "@i:nil": string;
  };
  "b:UserThreatShortDescription": {
    "@i:nil": string;
  };
  "b:Wide": string;
}

interface ThreatModelData {
  "a:Key": string;
  "a:Value": ThreatModel;
  "b:Priority": string;
  "b:State": string;
  "b:Properties": Properties;
}

type ThreatModelJSON = Record<string, ThreatModelData>;

const envData = import.meta.env;
let surveillanceDb = envData.PUBLIC_CONTENT_DB_PATH as string;

interface MatchingPolicy {
  title: string;
  path: string;
}

export const getEvidence = (
  fid: string,
  tenantId = ""
): Promise<EvidenceContentType[]> => {
  return new Promise((resolve, reject) => {
    const surveillanceDb = dbPath[tenantId as keyof typeof dbPath] as string;

    if (typeof surveillanceDb !== "string" || surveillanceDb.trim() === "") {
      return reject(new Error(`Invalid database path for tenantId: ${tenantId}`));
    }

    const db = new sqlite3.Database(surveillanceDb, sqlite3.OPEN_READONLY, (err) => {
      if (err) {
        console.error("Error opening database:", err);
        return reject(err);
      }

      const query = `
        WITH RankedResources AS (
          SELECT
            content,
            party_id,
            ROW_NUMBER() OVER (
              PARTITION BY frontmatter
              ORDER BY
                CASE WHEN party_id = ? THEN 1 ELSE 2 END
            ) AS rn
          FROM
            uniform_resource_fts
          WHERE
            frontmatter MATCH ? AND (party_id = ? OR party_id IS NULL)
        )
        SELECT content FROM RankedResources WHERE rn = 1;
      `;

      const params = [tenantId, `"${fid}"`, tenantId];

      db.all(query, params, (queryErr, rows: EvidenceContentType[]) => {
        db.close((closeErr) => {
          if (closeErr) {
            console.error("Error closing database:", closeErr.message);
          }

          if (queryErr) {
            console.error("Error executing query:", queryErr.message);
            return reject(queryErr);
          }

          resolve(rows ?? []);
        });
      });
    });
  });
};


export const countMatchingEvidence = async (
  fid: string,
  tenantId = ""
): Promise<number> => {
  return new Promise((resolve, reject) => {

    surveillanceDb = dbPath[tenantId as keyof typeof dbPath] as string;
    if (typeof surveillanceDb !== "string" || surveillanceDb.trim() === "") {
      throw new Error(`Invalid database path for tenantId: ${tenantId}`);
    }

    const db = new sqlite3.Database(
      surveillanceDb,
      sqlite3.OPEN_READONLY,
      (err: Error | null) => {
        if (err) return reject(err);

        const query = `
          WITH RECURSIVE split_values AS (
            SELECT 1 AS id, CAST(NULL AS TEXT) AS value, satisfies_values AS remaining_values, uri, frontmatter
            FROM policy_mapping_requirements_view
            UNION ALL
            SELECT
              id + 1,
              TRIM(SUBSTR(remaining_values, 1, CASE WHEN INSTR(remaining_values || ',', ',') > 0 THEN INSTR(remaining_values || ',', ',') ELSE LENGTH(remaining_values) + 1 END - 1)),
              SUBSTR(remaining_values, CASE WHEN INSTR(remaining_values || ',', ',') > 0 THEN INSTR(remaining_values || ',', ',') + 1 ELSE LENGTH(remaining_values) + 1 END),
              uri, frontmatter
            FROM split_values
            WHERE remaining_values != ''
          )
          SELECT TRIM(value) AS satisfies_value, uri
          FROM split_values
          WHERE satisfies_value = ? AND frontmatter LIKE ?
          ORDER BY uri;
        `;

        const params = [fid, `%\"${fid}\"%`];

        db.all(query, params, (err: Error | null, rows: any[]) => {
          db.close((closeErr: Error | null) => {
            if (closeErr) {
              console.error("Error closing database:", closeErr.message);
            }

            if (err) {
              reject(err);
            } else {
              resolve(rows?.length ?? 0);
            }
          });
        });
      }
    );
  });
};

export const countMatchingPolicies = (
  fid: string,
  tenantId = "",
): Promise<string> => {
  return new Promise((resolve, reject) => {

    const surveillanceDbPath = dbPath[tenantId as keyof typeof dbPath] as string;
    if (typeof surveillanceDbPath !== "string" || surveillanceDbPath.trim() === "") {
      throw new Error(`Invalid database path for tenantId: ${tenantId}`);
    }

    const db = new sqlite3.Database(
      surveillanceDbPath,
      sqlite3.OPEN_READONLY,
      (err: Error | null) => {
        if (err) return reject(err);

        const query = `
          WITH RankedResources AS (
            SELECT
              frontmatter,
              party_id,
              ROW_NUMBER() OVER (
                PARTITION BY frontmatter
                ORDER BY CASE WHEN party_id = ? THEN 1 ELSE 2 END
              ) AS rn
            FROM uniform_resource_fts
            WHERE frontmatter MATCH ? AND (party_id = ? OR party_id IS NULL)
          )
          SELECT COUNT(*) AS count FROM RankedResources WHERE rn = 1;
        `;

        const matchTerm = `"${fid}"`;
        const params = [tenantId, matchTerm, tenantId];

        db.get(query, params, (err: Error | null, row: { count: number }) => {
          db.close((closeErr: Error | null) => {
            if (closeErr) {
              console.error("Error closing database:", closeErr.message);
            }

            if (err) {
              reject(err);
            } else {
              resolve(row?.count?.toString() ?? "0");
            }
          });
        });
      }
    );
  });
};

export const getPolicyByTenant = async (
  controlsWithoutPolicies: AuditControls[],
  tenantId = "",
): Promise<AuditControls[]> => {
  const policies = await getAllPolicies(tenantId);
  await Promise.all(controlsWithoutPolicies.map(async (item) => {
    await Promise.all(
      item.controls.map(async (c) => {
        const idToSearch = c.fiiID ?? c.controlCode;
        if (idToSearch.length > 0) {
          const policyData = await getPolicyData(
            policies,
            idToSearch,
            tenantId,
          );
          const hasNonEmptyEvidence = policyData.some(
            (item) => item.evidence !== undefined && item.evidence.length > 0,
          );
          const status = hasNonEmptyEvidence
            ? "Evidence Available"
            : "Out of Compliance";
          c.policy = policyData;
          c.status = status;
        }
      }),
    );
  }));
  return controlsWithoutPolicies;
};

export const getAllPolicies = (
  tenantId = "",
): Promise<PolicyData[]> => {
  return new Promise((resolve, reject) => {

    const dbFile = dbPath[tenantId as keyof typeof dbPath] as string;
    if (typeof dbFile !== "string" || dbFile.trim() === "") {
      throw new Error(`Invalid database path for tenantId: ${tenantId}`);
    }

    const db = new sqlite3.Database(dbFile, sqlite3.OPEN_READONLY, (err) => {
      if (err) {
        reject(err);
        return;
      }

      const query = `
        WITH RankedResources AS (
          SELECT
            content_digest AS version,
            uri AS filePath,
            json_extract(frontmatter, '$.title') AS name,
            json_extract(frontmatter, '$.satisfies') AS policyIds,
            party_id,
            ROW_NUMBER() OVER (
              PARTITION BY json_extract(frontmatter, '$.title'), json_extract(frontmatter, '$.satisfies')
              ORDER BY CASE WHEN party_id = '${tenantId}' THEN 1 ELSE 2 END
            ) AS rn
          FROM uniform_resources_with_tenants
          WHERE
            (party_id = '${tenantId}' OR party_id IS NULL)
            AND nature LIKE 'md%'
        )
        SELECT
          version,
          filePath,
          name,
          policyIds
        FROM RankedResources
        WHERE rn = 1
        ORDER BY filePath;
      `;

      db.all(query, [], (err: Error | null, rows: PolicyData[]) => {
        db.close((closeErr) => {
          if (closeErr) {
            console.error(`Error closing database: ${closeErr.message}`);
          }
        });

        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  });
};

const evidenceData = (
  fid: string,
  tenantId = "",
): Promise<EvidenceContentType[]> => {
  return new Promise((resolve, reject) => {

    const dbFile = dbPath[tenantId as keyof typeof dbPath] as string;
    if (typeof dbFile !== "string" || dbFile.trim() === "") {
      throw new Error(`Invalid database path for tenantId: ${tenantId}`);
    }

    const db = new sqlite3.Database(dbFile, sqlite3.OPEN_READONLY, (err) => {
      if (err) {
        reject(err);
        return;
      }

      const query = `
        SELECT content
        FROM uniform_resource
        WHERE frontmatter LIKE '%"${fid}"%'
      `;

      db.all(query, [], (err: Error | null, row: EvidenceContentType[]) => {
        db.close((closeErr) => {
          if (closeErr) {
            console.error("Error closing DB:", closeErr.message);
          }
        });

        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  });
};

const getMatchingEvidence = async (
  fid: string,
  tenantId: string,
): Promise<PolicyEvidence[]> => {
  const evidenceResults: PolicyEvidence[] = [];
  try {
    const contentData = await evidenceData(fid, tenantId);
    let j = 0;
    for (const contentValue of contentData) {
      const fileContents = contentValue.content.toString();
      const { data } = matter(fileContents);
      const satisfies = data.satisfies as string;
      if (satisfies.includes(fid)) {
        const tempEvidence: PolicyEvidence = {
          evidenceId: j + 1,
          code: fid,
          status: "",
          updatedAt: new Date(),
          createdAt: new Date(),
        };
        evidenceResults.push(tempEvidence);
        j++;
      }
    }
  } catch (error) {
    throw new Error(`Error counting matching evidence: ${error?.toString()}`);
  }
  return [...new Set(evidenceResults)];
};

async function getPolicyData(
  originalArray: PolicyData[],
  idToFind: string,
  tenantId: string,
): Promise<ControlPolicy[]> {
  const policyArray: ControlPolicy[] = [];
  let i = 0;
  try {
    const evidence = await getMatchingEvidence(idToFind, tenantId);
    for (const control of originalArray) {
      if (control.policyIds !== null && control.policyIds.length > 0) {
        const parsedPolicyIds: string[] = JSON.parse(
          control.policyIds,
        ) as string[];
        if (
          Array.isArray(parsedPolicyIds) &&
          parsedPolicyIds.includes(idToFind)
        ) {
          const path = control.filePath?.split("/src");
          let policyFilePath = "";
          if (path.length > 0) {
            policyFilePath = "/src" + path[1];
          }
          const filePathExists = policyArray.some(
            (policy) => policy.filePath === policyFilePath,
          );
          if (!filePathExists) {
            i++;
            const tempObj: ControlPolicy = {
              version: control.version as string,
              filePath: policyFilePath,
              name: control.name as string,
              policyId: i,
              updatedAt: new Date(),
              createdAt: new Date(),
              status: "",
              evidence: evidence,
            };
            policyArray.push(tempObj);
          }
        }
      }
    }
  } catch (error) {
    throw new Error(`Error getting policy data: ${error?.toString()}`);
  }
  return policyArray;
}

export const getAicpaControlsData = (
  auditTypeName: string,
  tenantId = "",
): Promise<Soc2Type[]> =>
  new Promise((resolve, reject) => {

    surveillanceDb = dbPath[tenantId as keyof typeof dbPath] as string;
    if (typeof surveillanceDb !== "string" || surveillanceDb.trim() === "") {
      throw new Error(`Invalid database path for tenantId: ${tenantId}`);
    }

    const tableName =
      auditTypeName === "SOC2 Type II"
        ? "uniform_resource_aicpa_soc2_type2_controls"
        : "uniform_resource_aicpa_soc2_controls";

    const db = new sqlite3.Database(surveillanceDb, sqlite3.OPEN_READONLY, (err) => {
      if (err) {
        reject(err);
        return;
      }

      const query = `
        WITH RankedControls AS (
          SELECT
              "#" as rowNum,
              "Control Identifier",
              "Common Criteria",
              "Questions Descriptions",
              "Fii ID",
              ROW_NUMBER() OVER (PARTITION BY "Common Criteria" ORDER BY "Control Identifier" ASC) as RowNum
          FROM ${tableName}
        )
        SELECT
            "#",
            "Control Identifier",
            "Common Criteria",
            "Questions Descriptions",
            "Fii ID"
        FROM RankedControls
        ORDER BY "Common Criteria", RowNum, "Control Identifier";
      `;

      db.all(query, [], (err, rows: Soc2Type[]) => {
        db.close((closeErr) => {
          if (closeErr) {
            console.error("Error closing DB:", closeErr.message);
          }
        });

        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  });

export const getHipaaData = (tenantId = ""): Promise<HippaControl[]> => {
  return new Promise((resolve, reject) => {

    surveillanceDb = dbPath[tenantId as keyof typeof dbPath] as string;
    if (typeof surveillanceDb !== "string" || surveillanceDb.trim() === "") {
      throw new Error(`Invalid database path for tenantId: ${tenantId}`);
    }

    const db = new sqlite3.Database(surveillanceDb, sqlite3.OPEN_READONLY, (err) => {
      if (err) {
        reject(err);
        return;
      }

      const query = `
          SELECT 
            "#",
            "Common Criteria",
            "HIPAA Security Rule Reference",
            "Safeguard",
            "Handled by nQ",
            "FII Id"
          FROM uniform_resource_hipaa_security_rule_safeguards`;

      db.all(query, [], (err, rows: HippaControl[]) => {
        db.close((closeErr) => {
          if (closeErr) {
            console.error("Error closing database:", closeErr.message);
          }
        });

        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  });
};

export const getHiTrustData = (tenantId = ""): Promise<HiTrustControl[]> => {
  return new Promise((resolve, reject) => {

    const dbFile = dbPath[tenantId as keyof typeof dbPath] as string;
    if (typeof dbFile !== "string" || dbFile.trim() === "") {
      throw new Error(`Invalid database path for tenantId: ${tenantId}`);
    }

    const db = new sqlite3.Database(dbFile, sqlite3.OPEN_READONLY, (err) => {
      if (err) {
        return reject(err);
      }

      const query = `
        SELECT 
          "#",
          "Common Criteria",
          "HI-TRUST Reference",
          "Safeguard",
          "Handled by nQ",
          "FII Id"
        FROM uniform_resource_hi_trust_security_rule_safeguards`;

      db.all(query, [], (err, rows: HiTrustControl[]) => {
        db.close((closeErr) => {
          if (closeErr) {
            console.error("Error closing database:", closeErr.message);
          }
        });

        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  });
};

export async function getMatchingPolicyData(
  fid: string,
  allPosts: PoliciesType[],
  organizationName: string,
): Promise<MatchingPolicy[]> {
  const fileSet = new Set<string>();
  const titleMap = new Map<string, string>();

  for (const post of allPosts) {
    const satisfies = post.frontmatter.satisfies as string;
    const file = post.file as string;
    const fileName = extractPath(file);
    if (
      file.includes(`policies/${organizationName}/`) &&
      checkSatisfies(fid, satisfies) && !fileSet.has(post.frontmatter.title)
    ) {
      titleMap.set(post.frontmatter.title, fileName);
      fileSet.add(post.frontmatter.title);
    }
  }

  for (const post of allPosts) {
    const satisfies = post.frontmatter.satisfies as string;
    const file = post.file as string;
    const fileName = extractPath(file);

    if (
      file.includes(`policies/common/`) &&
      checkSatisfies(fid, satisfies) &&
      !fileSet.has(post.frontmatter.title) &&
      !titleMap.has(post.frontmatter.title)
    ) {
      titleMap.set(post.frontmatter.title, fileName);
      fileSet.add(post.frontmatter.title);
    }
  }

  const matchingPolicies: MatchingPolicy[] = Array.from(
    titleMap,
    ([title, path]) => ({
      title,
      path,
    }),
  );

  return matchingPolicies;
}

function extractPath(file: string): string {
  // Split the file path by "/"
  const parts = file.split("/");

  // Find the index of "policies" in the path
  const policiesIndex = parts.indexOf("policies");

  // If "policies" is found, select elements from that index onwards
  if (policiesIndex !== -1) {
    // Remove the extension from the last element (file name with extension)
    const fileNameWithExtension = parts.at(-1);
    const fileName = (fileNameWithExtension == null)
      ? ""
      : fileNameWithExtension.split(".")[0];
    parts[parts.length - 1] = fileName;

    const selectedParts = parts.slice(policiesIndex + 1); // Select elements from "policies" onwards
    return selectedParts.join("/");
  }

  // If "policies" is not found, return the entire path
  return parts.join("/");
}

function checkSatisfies(
  fid: string,
  satisfies: string | string[] | undefined,
): boolean {
  return Array.isArray(satisfies)
    ? satisfies.includes(fid)
    : typeof satisfies === "string" && satisfies === fid;
}

export function getPrevControlData(
  allPosts: PoliciesType[],
  slug: string,
  organizationName = "",
  tenantId = "",
  auditTypeID: number,
): Promise<ControlType | null> {

  surveillanceDb = dbPath[tenantId as keyof typeof dbPath] as string;
  if (typeof surveillanceDb !== "string" || surveillanceDb.trim() === "") {
    throw new Error(`Invalid database path for tenantId: ${tenantId}`);
  }

  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(
      surveillanceDb,
      sqlite3.OPEN_READONLY,
      (err) => {
        if (err) {
          reject(err);
          return;
        }

        const query = `
          SELECT display_order, control_identifier, fii, control_code
          FROM control
          WHERE display_order < (
            SELECT display_order
            FROM control
            WHERE control_identifier = ?
              AND audit_type_id = ?
          )
          AND audit_type_id = ?
          ORDER BY display_order DESC
          LIMIT 1;
        `;

        db.get(query, [slug, auditTypeID, auditTypeID], (err, row: ControlType) => {
          if (err) {
            db.close(() => reject(err));
            return;
          }

          if (row !== undefined && row !== null) {
            const fid = String(row.fii);
            getMatchingPolicyData(fid, allPosts, organizationName)
              .then((policies) => {
                row.policies = policies;
                db.close((err) => {
                  if (err) reject(`Error closing database: ${err.message}`);
                  else resolve(row);
                });
              })
              .catch((policyError) => {
                db.close(() => reject(policyError));
              });
          } else {
            db.close((err) => {
              if (err) reject(`Error closing database: ${err.message}`);
              else resolve(null);
            });
          }
        });
      },
    );
  });
}

export function getNextControlData(
  allPosts: PoliciesType[],
  slug: string,
  organizationName = "",
  tenantId = "",
  auditTypeID: number,
): Promise<ControlType | null> {

  surveillanceDb = dbPath[tenantId as keyof typeof dbPath] as string;
  if (typeof surveillanceDb !== "string" || surveillanceDb.trim() === "") {
    throw new Error(`Invalid database path for tenantId: ${tenantId}`);
  }

  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(
      surveillanceDb,
      sqlite3.OPEN_READONLY,
      (err) => {
        if (err) {
          reject(err);
          return;
        }

        const query = `
          SELECT display_order, control_identifier, fii, control_code
          FROM control
          WHERE display_order > (
            SELECT display_order
            FROM control
            WHERE control_identifier = ?
              AND audit_type_id = ?
          )
          AND audit_type_id = ?
          ORDER BY display_order ASC
          LIMIT 1;
        `;

        db.get(query, [slug, auditTypeID, auditTypeID], (err, row: ControlType) => {
          if (err) {
            db.close(() => reject(err));
            return;
          }

          if (row !== undefined && row !== null) {
            const fid = String(row.fii);
            getMatchingPolicyData(fid, allPosts, organizationName)
              .then((policies) => {
                row.policies = policies;
                db.close((err) => {
                  if (err) reject(`Error closing database: ${err.message}`);
                  else resolve(row);
                });
              })
              .catch((policyError) => {
                db.close(() => reject(policyError));
              });
          } else {
            db.close((err) => {
              if (err) reject(`Error closing database: ${err.message}`);
              else resolve(null);
            });
          }
        });
      },
    );
  });
}

export const getAuditControls = (
  auditType: number,
  tenantId = "",
): Promise<AuditControls[]> => {
  return new Promise((resolve, reject) => {
    surveillanceDb = dbPath[tenantId as keyof typeof dbPath] as string;
    if (typeof surveillanceDb !== "string" || surveillanceDb.trim() === "") {
      throw new Error(`Invalid database path for tenantId: ${tenantId}`);
    }

    const db = new sqlite3.Database(surveillanceDb, sqlite3.OPEN_READONLY, (err) => {
      if (err) {
        reject(err);
        return;
      }

      const query = `
        SELECT
          display_order as controlId,
          common_criteria AS name,
          CURRENT_TIMESTAMP AS createdAt,
          CURRENT_TIMESTAMP AS updatedAt,
          'active' AS recordStatus,
          json_group_array(
              json_object(
                  'controlCode', control_code,
                  'reviewNote', '',
                  'question', question,
                  'fiiID', fii,
                  'status', ''
              )
          ) AS controls
        FROM control
        WHERE audit_type_id = ?
        GROUP BY common_criteria
        ORDER BY display_order ASC;
      `;

      db.all(query, [auditType], (err, rows: AuditControls[]) => {
        if (err) {
          db.close(() => reject(err));
          return;
        }

        try {
          for (const item of rows) {
            item.controls = JSON.parse(item.controls);
          }

          db.close((err) => {
            if (err) {
              reject(`Error closing database: ${err.message}`);
            } else {
              resolve(rows);
            }
          });
        } catch (parseError) {
          db.close(() => reject(parseError));
        }
      });
    });
  });
};

export function getControlData(
  allPosts: PoliciesType[],
  slug: string,
  auditTypeID: number,
  organizationName = "",
  tenantId: string,
): Promise<ControlDetailType | null> {
  return new Promise((resolve, reject) => {

    surveillanceDb = dbPath[tenantId as keyof typeof dbPath] as string;
    if (typeof surveillanceDb !== "string" || surveillanceDb.trim() === "") {
      throw new Error(`Invalid database path for tenantId: ${tenantId}`);
    }

    const db = new sqlite3.Database(surveillanceDb, sqlite3.OPEN_READONLY, (err) => {
      if (err) {
        reject(err);
        return;
      }

      const query = `
        SELECT display_order, control_code, control_identifier, fii, common_criteria, expected_evidence, question
        FROM control
        WHERE control_identifier = ? AND audit_type_id = ?
      `;

      db.get(query, [slug, auditTypeID], (err, row: ControlDetailType | undefined) => {
        if (err) {
          db.close(() => reject(err));
          return;
        }

        if (!row) {
          db.close(() => resolve(null)); // no result
          return;
        }

        // Avoid returning a Promise in the callback
        void (async () => {
          try {
            const fid = row.fii as string;
            const policies = await getMatchingPolicyData(fid, allPosts, organizationName);
            row.policies = policies;
            db.close((err) => {
              if (err) {
                reject(`Error closing database: ${err.message}`);
              } else {
                resolve(row);
              }
            });
          } catch (error) {
            db.close(() => reject(error));
          }
        })();
      });
    });
  });
}

export const getNetspectiveControlsData = (
  auditTypeName: string,
  tenantId = "",
): Promise<NetspectiveControlType[]> =>
  new Promise((resolve, reject) => {

    const dbFile = dbPath[tenantId as keyof typeof dbPath] as string;
    if (typeof dbFile !== "string" || dbFile.trim() === "") {
      throw new Error(`Invalid database path for tenantId: ${tenantId}`);
    }

    const tableName = auditTypeName.toLowerCase().replaceAll(" ", "_");
    const db = new sqlite3.Database(dbFile, sqlite3.OPEN_READONLY, (err) => {
      if (err) {
        reject(err);
        return;
      }

      const query = `
        SELECT "#", "Control Identifier", "Name", "Common Criteria", "Questions Descriptions"
        FROM ${tableName}
      `;

      db.all(query, [], (err, rows: NetspectiveControlType[]) => {
        db.close((closeErr) => {
          if (closeErr) {
            console.error("Error closing database:", closeErr.message);
          }

          if (err) {
            reject(err);
          } else {
            resolve(rows);
          }
        });
      });
    });
  });

export const getScfControlsData = (
  auditTypeName: string,
  tenantId = "",
): Promise<Soc2Type[]> =>
  new Promise((resolve, reject) => {
    let fieldName = auditTypeName.startsWith("US ")
      ? auditTypeName.replaceAll(/Model\s*/g, "")
      : `US ${auditTypeName.replaceAll(/Model\s*/g, "")}`;

    if (fieldName === "US CMMC 2.0 LEVEL 3") {
      fieldName = "US CMMC 2.0  Level 3";
    }

    surveillanceDb = dbPath[tenantId as keyof typeof dbPath] as string;
    if (typeof surveillanceDb !== "string" || surveillanceDb.trim() === "") {
      throw new Error(`Invalid database path for tenantId: ${tenantId}`);
    }

    const db = new sqlite3.Database(surveillanceDb, sqlite3.OPEN_READONLY, (err) => {
      if (err) {
        reject(err);
        return;
      }

      const query = `
          SELECT 
            "${fieldName}" AS control,
            control_identifier AS "Control Identifier",
            "SCF Domain" AS "Common Criteria",
            "SCF Control",
            "SCF Control Question",
            "SCF #",
            "${fieldName}"
          FROM scf_view
          WHERE "${fieldName}" != ""
        `;

      db.all(query, [], (err, rows: Soc2Type[]) => {
        db.close((closeErr) => {
          if (closeErr) {
            console.error("Error closing database:", closeErr.message);
          }

          if (err) {
            reject(err);
          } else {
            resolve(rows);
          }
        });
      });
    });
  });


export const hiTrustService = (tenantId = ""): Promise<HiTrustControl[]> => {
  return new Promise((resolve, reject) => {

    surveillanceDb = dbPath[tenantId as keyof typeof dbPath] as string;
    if (typeof surveillanceDb !== "string" || surveillanceDb.trim() === "") {
      throw new Error(`Invalid database path for tenantId: ${tenantId}`);
    }

    const db = new sqlite3.Database(surveillanceDb, sqlite3.OPEN_READONLY, (err) => {
      if (err) {
        reject(err);
        return;
      }

      const query = `
          SELECT "#", "Control Identifier", "Fii ID", "Common Criteria", "Name", "Description"
          FROM uniform_resource_hitrust_e1_assessment
        `;

      db.all(query, [], (err, rows: HiTrustControl[]) => {
        db.close((closeErr) => {
          if (closeErr) {
            console.error("Error closing database:", closeErr.message);
          }

          if (err) {
            reject(err);
          } else {
            resolve(rows);
          }
        });
      });
    });
  });
};


export const getVulnerabilities = (tenantId = ""): Promise<SbomGrype> => {
  return new Promise((resolve, reject) => {

    surveillanceDb = dbPath[tenantId as keyof typeof dbPath] as string;
    if (typeof surveillanceDb !== "string" || surveillanceDb.trim() === "") {
      throw new Error(`Invalid database path for tenantId: ${tenantId}`);
    }

    const db = new sqlite3.Database(surveillanceDb, sqlite3.OPEN_READONLY, (err) => {
      if (err) {
        reject(err);
        return;
      }

      const query = `
        SELECT content, uri
        FROM uniform_resource
        WHERE uri LIKE 'sbomGrype'
        ORDER BY last_modified_at DESC
        LIMIT 1;
      `;

      db.get(query, [], (err: Error | null, row: { content: string }) => {
        db.close((closeErr) => {
          if (closeErr) {
            console.error("Error closing database:", closeErr.message);
          }

          if (err) {
            reject(err);
          } else {
            try {
              if (!row) {
                resolve({} as SbomGrype);
              } else {
                const parsedContent: SbomGrype = JSON.parse(row.content);
                resolve(parsedContent);
              }
            } catch (parseErr) {
              reject(parseErr);
            }
          }
        });
      });
    });
  });
};

export const getPackages = (): Promise<sbomSyft> => {

  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(
      surveillanceDb,
      sqlite3.OPEN_READONLY,
      (err: Error | null) => {
        if (err) {
          reject(err);
          return;
        }

        const query = `SELECT content, uri FROM uniform_resource WHERE uri LIKE 'sbomSyft';`;

        db.get(query, [], (err: Error | null, row: { content: string }) => {
          db.close((closeErr: Error | null) => {
            if (closeErr) {
              console.error("Error closing database:", closeErr.message);
            }

            if (err) {
              reject(err);
              return;
            }

            try {
              const parsedContent = JSON.parse(row.content);
              resolve(parsedContent as sbomSyft);
            } catch (parseError) {
              reject(parseError);
            }
          });
        });
      }
    );
  });
};

export const getAssetData = (): Promise<AssetData[]> => {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(
      "resource-surveillance-aggregated.sqlite.db",
      sqlite3.OPEN_READONLY,
      (err: Error | null) => {
        if (err) {
          reject(err);
          return;
        }

        const query = `
          SELECT ur.uri, ur.device_id, ur.content, d.name, d.state
          FROM uniform_resource AS ur
          JOIN device AS d ON ur.device_id = d.device_id
          WHERE ur.nature = 'json' AND ur.uri = 'listContainers'
          LIMIT 20;
        `;

        db.all(query, [], (err, rows: AssetData[]) => {
          db.close((closeErr: Error | null) => {
            if (closeErr) {
              console.error("Error closing database:", closeErr.message);
            }

            if (err) {
              reject(err);
            } else {
              resolve(rows);
            }
          });
        });
      }
    );
  });
};

export const getThsaData = (tenantId = ""): Promise<ThsaControl[]> => {
  return new Promise((resolve, reject) => {

    surveillanceDb = dbPath[tenantId as keyof typeof dbPath] as string;
    if (typeof surveillanceDb !== "string" || surveillanceDb.trim() === "") {
      throw new Error(`Invalid database path for tenantId: ${tenantId}`);
    }

    const db = new sqlite3.Database(surveillanceDb, sqlite3.OPEN_READONLY, (err) => {
      if (err) {
        reject(err);
        return;
      }

      const query = `
        SELECT 
          "#", 
          "SCF Domain" AS "Common Criteria", 
          "SCF #" AS "Control Identifier", 
          "SCF #" AS "FII Id", 
          "SCF Control Question"
        FROM uniform_resource_thsa;
      `;

      db.all(query, [], (err, rows: ThsaControl[]) => {
        db.close((closeErr) => {
          if (closeErr) {
            console.error("Error closing database:", closeErr.message);
          }

          if (err) {
            reject(err);
          } else {
            resolve(rows);
          }
        });
      });
    });
  });
};

const getServerStatus = (serverName: string): Promise<DeviceArray> => {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(
      "src/components/native/ThreatModel/resource-surveillance-aggregated.sqlite.db",
      sqlite3.OPEN_READONLY,
      (err: Error | null) => {
        if (err) {
          reject(err);
          return;
        }

        const query = `
          SELECT ur.device_id, ur.content, d.name
          FROM uniform_resource AS ur
          JOIN device AS d ON ur.device_id = d.device_id
          WHERE ur.nature = 'json' AND ur.uri = 'listContainers' AND d.name = ?
          LIMIT 1;
        `;

        console.log(query);

        db.all(
          query,
          [serverName],
          (
            err: Error | null,
            row: { device_id: string; content: string; name: string }[],
          ) => {
            db.close((closeErr) => {
              if (closeErr) {
                console.error("Error closing database:", closeErr.message);
              }

              if (err) {
                reject(err);
              } else {
                resolve(row);
              }
            });
          }
        );
      }
    );
  });
};

export const getServerData = (): Promise<{
  data: ServerDataArray;
  total: number;
}> => {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(
      "public/assets/ds/portfolio/opsfolio.fcr.auto.sqla.sqlite.db",
      sqlite3.OPEN_READONLY,
      (err: Error | null) => {
        if (err) {
          return reject(err);
        }

        const query = `SELECT * FROM asset_service_view;`;
        db.all(query, [], (err: Error | null, row: ServerDataArray) => {
          if (err) {
            db.close(); // early close
            return reject(err);
          }

          // Use an IIFE to handle async logic inside the callback
          void (async () => {
            try {
              for (const item of row) {
                const serverName = item.server.replace(/^[^-]+ - /, "");
                const serverStatus: DeviceArray = await getServerStatus(serverName);
                const searchName = `/${item.name}`;

                if (serverStatus.length > 0) {
                  const parsedContent: DeviceContentItem[] = JSON.parse(serverStatus[0].content);
                  const found = parsedContent.find(
                    (data: DeviceContentItem) => data.name === searchName,
                  );
                  item.status = found?.status ?? item.tag;
                } else {
                  item.status = item.tag;
                }
              }

              resolve({ data: row, total: row.length });
            } catch (error) {
              reject(error);
            } finally {
              db.close((err) => {
                if (err) console.error("Error closing DB:", err.message);
              });
            }
          })();
        });
      },
    );
  });
};

export const getThreadModel = (): Promise<ThreatModelType> => {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(
      "src/components/native/ThreatModel/resource-surveillance-aggregated.sqlite.db",
      sqlite3.OPEN_READONLY,
      (err: Error | null) => {
        if (err) {
          return reject(err);
        }

        const query = `SELECT content, uri FROM uniform_resource_transform WHERE uri LIKE '%Opsfolio-tm.xml%';`;

        db.get(query, [], (err: Error | null, row: { content: string }) => {
          if (err) {
            db.close(); // close before rejecting
            return reject(err);
          }

          try {
            const parsedContent: ThreatModelType = JSON.parse(row.content);
            resolve(parsedContent);
          } catch (parseError) {
            reject(parseError);
          } finally {
            db.close((closeErr: Error | null) => {
              if (closeErr) {
                console.error("Error closing database:", closeErr.message);
              }
            });
          }
        });
      },
    );
  });
};

export const getControlList = (
  auditType: string,
  tenantId = "",
): Promise<ControlsType[]> => {
  return new Promise((resolve, reject) => {

    surveillanceDb = dbPath[tenantId as keyof typeof dbPath] as string;
    if (typeof surveillanceDb !== "string" || surveillanceDb.trim() === "") {
      throw new Error(`Invalid database path for tenantId: ${tenantId}`);
    }

    const db = new sqlite3.Database(surveillanceDb, sqlite3.OPEN_READONLY, (err) => {
      if (err) {
        return reject(err);
      }

      const query = `
        SELECT 
          display_order,
          control_identifier,
          control_code,
          fii,
          common_criteria,
          expected_evidence,
          question,
          control_regime,
          audit_type 
        FROM control 
        WHERE audit_type_id = ${auditType} 
        ORDER BY display_order ASC`;

      db.all(query, [], (err, rows: ControlsType[]) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }

        db.close((closeErr) => {
          if (closeErr) {
            console.error("Error closing database:", closeErr.message);
          }
        });
      });
    });
  });
};

export const getControlListByRegimeIdAndAuditTypeId = (
  regime = 0,
  auditType = 0,
  tenantId = "",
): Promise<ControlsType[]> => {
  return new Promise((resolve, reject) => {

    surveillanceDb = dbPath[tenantId as keyof typeof dbPath] as string;
    //console.log("Database Path:", surveillanceDb);
    if (typeof surveillanceDb !== "string" || surveillanceDb.trim() === "") {
      //throw new Error(`Invalid database path for tenantId: ${tenantId}`);
      console.error("Invalid database path for tenantId:");
    }

    const db = new sqlite3.Database(surveillanceDb, sqlite3.OPEN_READONLY, (err) => {
      if (err) {
        return reject(err);
        //return console.error("Error closing database:invalid path");
        
      }

      const query =
        auditType === 0
          ? `SELECT display_order, control_identifier, control_code, fii, common_criteria, expected_evidence, question, control_regime, audit_type
             FROM control 
             WHERE control_regime_id = ${regime} 
             ORDER BY display_order ASC`
          : `SELECT display_order, control_identifier, control_code, fii, common_criteria, expected_evidence, question, control_regime, audit_type
             FROM control 
             WHERE control_regime_id = ${regime} AND audit_type_id = ${auditType}
             ORDER BY display_order ASC`;

      db.all(query, [], (err, rows: ControlsType[]) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }

        db.close((closeErr) => {
          if (closeErr) {
            console.error("Error closing database:", closeErr.message);
          }
        });
      });
    });
  });
};


export const getControlByIdentifier = (
  regime = 0,
  auditType = 0,
  tenantId = "",
  identifier = "", // strictly control_identifier
): Promise<ControlsType | null> => {
  return new Promise((resolve, reject) => {
    const surveillanceDb = dbPath[tenantId as keyof typeof dbPath] as string;

    if (typeof surveillanceDb !== "string" || surveillanceDb.trim() === "") {
      console.error("Invalid database path for tenantId:", tenantId);
      return resolve(null);
    }

    const db = new sqlite3.Database(
      surveillanceDb,
      sqlite3.OPEN_READONLY,
      (err) => {
        if (err) {
          return reject(err);
        }

        const query =
          auditType === 0
            ? `SELECT display_order, control_identifier, control_code, fii, common_criteria, expected_evidence, question, control_regime, audit_type
               FROM control 
               WHERE control_regime_id = ? AND control_identifier = ?
               LIMIT 1`
            : `SELECT display_order, control_identifier, control_code, fii, common_criteria, expected_evidence, question, control_regime, audit_type
               FROM control 
               WHERE control_regime_id = ? AND audit_type_id = ? AND control_identifier = ?
               LIMIT 1`;

        const params =
          auditType === 0
            ? [regime, identifier]
            : [regime, auditType, identifier];

        db.get(query, params, (err, row: ControlsType) => {
          if (err) {
            reject(err);
          } else {
            resolve(row || null);
          }

          db.close((closeErr) => {
            if (closeErr) {
              console.error("Error closing database:", closeErr.message);
            }
          });
        });
      },
    );
  });
};


export const checkData = (
  tenantId = "",
): Promise<ControlsType[]> => {
  return new Promise((resolve, reject) => {

    surveillanceDb = dbPath[tenantId as keyof typeof dbPath] as string;
    if (typeof surveillanceDb !== "string" || surveillanceDb.trim() === "") {
      throw new Error(`Invalid database path for tenantId: ${tenantId}`);
    }

    const db = new sqlite3.Database(surveillanceDb, sqlite3.OPEN_READONLY, (err) => {
      if (err) {
        return reject(err);
      }

      const query = `
        SELECT
          uri,
          REGEXP_SUBSTR(content, '<(QueryResult|QueriesResult|EvidenceResult|img|a|evidence)[^>]*\\s+satisfies\\s*=\\s*"([^"]+)"', 1, 1, NULL, 1) AS tag,
          REGEXP_SUBSTR(content, '<(QueryResult|QueriesResult|EvidenceResult|img|a|evidence)[^>]*\\s+satisfies\\s*=\\s*"([^"]+)"', 1, 1, NULL, 2) AS satisfies_value
        FROM
          uniform_resource;
      `;

      db.all(query, [], (err, rows: ControlsType[]) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }

        db.close((closeErr) => {
          if (closeErr) {
            console.error("Error closing database:", closeErr.message);
          }
        });
      });
    });
  });
};

export const fetchAllSCFDomainData = (
  tenantId = "",
): Promise<ScfDomainData[]> => {
  return new Promise((resolve, reject) => {

    surveillanceDb = dbPath[tenantId as keyof typeof dbPath] as string;
    if (typeof surveillanceDb !== "string" || surveillanceDb.trim() === "") {
      throw new Error(`Invalid database path for tenantId: ${tenantId}`);
    }

    const db = new sqlite3.Database(surveillanceDb, sqlite3.OPEN_READONLY, (err) => {
      if (err) {
        return reject(err);
      }

      const query = `
        SELECT display_order, scf_domain, scf_id, 
               security_privacy_design_principle, principle_intent 
        FROM scf_domain
      `;

      db.all(query, [], (err, rows: ScfDomainData[]) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }

        db.close((closeErr) => {
          if (closeErr) {
            console.error("Error closing database:", closeErr.message);
          }
        });
      });
    });
  });
};

export const getScfDomainDataByIdentifier = (
  tenantId = "",
  identifier: string
): Promise<ScfDomainData> => {
  return new Promise((resolve, reject) => {
    surveillanceDb = dbPath[tenantId as keyof typeof dbPath] as string;
    if (typeof surveillanceDb !== "string" || surveillanceDb.trim() === "") {
      throw new Error(`Invalid database path for tenantId: ${tenantId}`);
    }

    const db = new sqlite3.Database(surveillanceDb, sqlite3.OPEN_READONLY, (err) => {
      if (err) {
        return reject(err);
      }

      const query = `
        SELECT display_order, scf_domain, scf_id,
               security_privacy_design_principle, principle_intent
        FROM scf_domain
        WHERE scf_id LIKE ?
      `;

      db.get(query, [`%${identifier}%`], (err, row: ScfDomainData) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }

        db.close((closeErr) => {
          if (closeErr) {
            console.error("Error closing database:", closeErr.message);
          }
        });
      });
    });
  });
};

export const getAllScfDataByFiiId = (
  tenantId = "",
  identifier: string
): Promise<ScfData[]> => {
  return new Promise((resolve, reject) => {
    surveillanceDb = dbPath[tenantId as keyof typeof dbPath] as string;
    if (typeof surveillanceDb !== "string" || surveillanceDb.trim() === "") {
      throw new Error(`Invalid database path for tenantId: ${tenantId}`);
    }

    const db = new sqlite3.Database(surveillanceDb, sqlite3.OPEN_READONLY, (err) => {
      if (err) {
        return reject(err);
      }

      const query = `
        SELECT scf_domain, scf_control, fii, scf_description, question
        FROM scf
        WHERE fii LIKE ?
      `;

      db.all(query, [`%${identifier}%`], (err, rows: ScfData[]) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }

        db.close((closeErr) => {
          if (closeErr) {
            console.error("Error closing database:", closeErr.message);
          }
        });
      });
    });
  });
};

export const getScfDataByControlId = (
  tenantId = "",
  identifier: string
): Promise<ScfData> => {
  return new Promise((resolve, reject) => {
    surveillanceDb = dbPath[tenantId as keyof typeof dbPath] as string;
    if (typeof surveillanceDb !== "string" || surveillanceDb.trim() === "") {
      throw new Error(`Invalid database path for tenantId: ${tenantId}`);
    }

    const db = new sqlite3.Database(surveillanceDb, sqlite3.OPEN_READONLY, (err) => {
      if (err) {
        return reject(err);
      }

      const scfIdentifier = identifier.split("-")[2];
      const mainQuery = `SELECT scf_domain, scf_control, fii, scf_description, question, methods_to_comply FROM scf WHERE fii = ?`;

      db.get(mainQuery, [identifier], (err, row: ScfData) => {
        if (err) {
          db.close();
          return reject(err);
        }

        if (row === null || row === undefined) {
          db.close();
          return resolve(null as unknown as ScfData);
        }

        const domainQuery = `SELECT scf_domain FROM scf_domain WHERE scf_id = ?`;

        db.get(domainQuery, [scfIdentifier], (innerErr, innerRow: { scf_domain: string }) => {
          db.close((closeErr) => {
            if (closeErr) {
              console.error("Error closing database:", closeErr.message);
            }
          });

          if (innerErr) {
            return reject(innerErr);
          }

          if (innerRow !== null && innerRow !== undefined) {
            row.scf_domain = innerRow.scf_domain;
          }

          resolve(row);
        });
      });
    });
  });
};

export const getControlRegimeSideMenu = (
  tenantId = ""
): Promise<ControlRegimSideMenuData[]> => {
  return new Promise((resolve, reject) => {
    surveillanceDb = dbPath[tenantId as keyof typeof dbPath] as string;
    if (typeof surveillanceDb !== "string" || surveillanceDb.trim() === "") {
      throw new Error(`Invalid database path for tenantId: ${tenantId}`);
    }

    const db = new sqlite3.Database(surveillanceDb, sqlite3.OPEN_READONLY, (err) => {
      if (err) {
        return reject(err);
      }

      const query = `SELECT control_regime_id, parent_id, name 
                     FROM tenant_based_control_regime 
                     WHERE status = 'active' AND tenant_id = ?`;

      db.all(query, [tenantId], (err, rows: ControlRegimSideMenuData[]) => {
        db.close((closeErr) => {
          if (closeErr) {
            console.error("Error closing database:", closeErr.message);
          }
        });

        if (err) {
          return reject(err);
        }

        resolve(rows);
      });
    });
  });
};

export const getauditDataBySessionAndAudit = (
  sessionId = 0,
  auditTypeId = 0,
  tenantId = ""
): Promise<ControlRegimSideMenuData> => {
  return new Promise((resolve, reject) => {
    surveillanceDb =
      tenantId.length > 0
        ? (dbPath[tenantId as keyof typeof dbPath] as string)
        : (surveillanceDb as string);
    if (typeof surveillanceDb !== "string" || surveillanceDb.trim() === "") {
      throw new Error(`Invalid database path for tenantId: ${tenantId}`);
    }

    const db = new sqlite3.Database(surveillanceDb, sqlite3.OPEN_READONLY, (err) => {
      if (err) return reject(err);

      const query = `
        SELECT 
          audit.control_regime_id AS audit_id,
          audit.parent_id,
          audit.name,
          parent.name AS regime_name,
          (SELECT COUNT(parent_id) FROM control_regime WHERE parent_id = ?) AS audit_count
        FROM control_regime audit
        INNER JOIN control_regime parent 
          ON audit.parent_id = parent.control_regime_id
        WHERE audit.parent_id = ? AND audit.control_regime_id = ?
      `;

      db.get(query, [sessionId, sessionId, auditTypeId], (err, row: ControlRegimSideMenuData) => {
        db.close((closeErr) => {
          if (closeErr) {
            console.error("Error closing database:", closeErr.message);
          }
        });

        if (err) {
          return reject(err);
        }

        resolve(row);
      });
    });
  });
};


export const getHipaaControlList = ( tenantId = "",): Promise<ControlsType[]> => {
  return new Promise((resolve, reject) => {

    surveillanceDb = dbPath[tenantId as keyof typeof dbPath] as string;
    //console.log("Database Path:", surveillanceDb);
    if (typeof surveillanceDb !== "string" || surveillanceDb.trim() === "") {
      //throw new Error(`Invalid database path for tenantId: ${tenantId}`);
      console.error("Invalid database path for tenantId:");
    }

    const db = new sqlite3.Database(surveillanceDb, sqlite3.OPEN_READONLY, (err) => {
      if (err) {
        return reject(err);
        //return console.error("Error closing database:invalid path");
        
      }

      const query = `SELECT display_order, control_identifier, control_code, fii, common_criteria, expected_evidence, question FROM hipaa_control ORDER BY display_order ASC`;

      db.all(query, [], (err, rows: ControlsType[]) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }

        db.close((closeErr) => {
          if (closeErr) {
            console.error("Error closing database:", closeErr.message);
          }
        });
      });
    });
  });
};


export const getHipaaControlByIdentifier = (
  tenantId = "",
  identifier = "", // strictly control_identifier
): Promise<ControlsType | null> => {
  return new Promise((resolve, reject) => {
    const surveillanceDb = dbPath[tenantId as keyof typeof dbPath] as string;

    if (typeof surveillanceDb !== "string" || surveillanceDb.trim() === "") {
      console.error("Invalid database path for tenantId:", tenantId);
      return resolve(null);
    }

    const db = new sqlite3.Database(
      surveillanceDb,
      sqlite3.OPEN_READONLY,
      (err) => {
        if (err) {
          return reject(err);
        }

        const query = `SELECT display_order, control_identifier, control_code, fii, common_criteria, expected_evidence, question
               FROM hipaa_control 
               WHERE control_identifier = ?
               LIMIT 1`;

        const params = [identifier] ;

        db.get(query, params, (err, row: ControlsType) => {
          if (err) {
            reject(err);
          } else {
            resolve(row || null);
          }

          db.close((closeErr) => {
            if (closeErr) {
              console.error("Error closing database:", closeErr.message);
            }
          });
        });
      },
    );
  });
};


export const getSoc2ControlList = ( controlId="",tenantId = "",): Promise<ControlsType[]> => {
  return new Promise((resolve, reject) => {

    surveillanceDb = dbPath[tenantId as keyof typeof dbPath] as string;
    console.log("Database Path:", surveillanceDb);
    if (typeof surveillanceDb !== "string" || surveillanceDb.trim() === "") {
      //throw new Error(`Invalid database path for tenantId: ${tenantId}`);
      console.error("Invalid database path for tenantId:");
    }

    const db = new sqlite3.Database(surveillanceDb, sqlite3.OPEN_READONLY, (err) => {
      if (err) {
        return reject(err);
        //return console.error("Error closing database:invalid path");        
      }

      const query = `SELECT display_order, control_identifier, control_code, fii, common_criteria,
       expected_evidence, question FROM soc2_control where control_type_id = ?  ORDER BY display_order ASC`;
      const params = [controlId] ;

     db.all(query, params, (err, row: ControlsType[]) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }

        db.close((closeErr) => {
          if (closeErr) {
            console.error("Error closing database:", closeErr.message);
          }
        });
      });
    });
  });
};

export const getSoc2ControlByIdentifier = (
  controlId="",
  tenantId = "",
  identifier = "", // strictly control_identifier
): Promise<ControlsType | null> => {
  return new Promise((resolve, reject) => {
    const surveillanceDb = dbPath[tenantId as keyof typeof dbPath] as string;

    if (typeof surveillanceDb !== "string" || surveillanceDb.trim() === "") {
      console.error("Invalid database path for tenantId:", tenantId);
      return resolve(null);
    }

    const db = new sqlite3.Database(
      surveillanceDb,
      sqlite3.OPEN_READONLY,
      (err) => {
        if (err) {
          return reject(err);
        }

        const query = `SELECT display_order, control_identifier, control_code, fii, common_criteria, expected_evidence, question
               FROM soc2_control 
               WHERE control_identifier = ? AND control_type_id = ?
               LIMIT 1`;

        const params = [identifier,controlId] ;

        db.get(query, params, (err, row: ControlsType) => {
          if (err) {
            reject(err);
          } else {
            resolve(row || null);
          }

          db.close((closeErr) => {
            if (closeErr) {
              console.error("Error closing database:", closeErr.message);
            }
          });
        });
      },
    );
  });
};


export const getCmmcControlList = ( controlId="",tenantId = "",): Promise<ControlsType[]> => {
  return new Promise((resolve, reject) => {

    surveillanceDb = dbPath[tenantId as keyof typeof dbPath] as string;
    console.log("Database Path:", surveillanceDb);
    if (typeof surveillanceDb !== "string" || surveillanceDb.trim() === "") {
      //throw new Error(`Invalid database path for tenantId: ${tenantId}`);
      console.error("Invalid database path for tenantId:");
    }

    const db = new sqlite3.Database(surveillanceDb, sqlite3.OPEN_READONLY, (err) => {
      if (err) {
        return reject(err);
        //return console.error("Error closing database:invalid path");        
      }

      const query = `SELECT display_order, control_identifier, control_code, fii, common_criteria,
       expected_evidence, question FROM cmmc_control where control_type_id = ?  ORDER BY display_order ASC`;
      const params = [controlId] ;

     db.all(query, params, (err, row: ControlsType[]) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }

        db.close((closeErr) => {
          if (closeErr) {
            console.error("Error closing database:", closeErr.message);
          }
        });
      });
    });
  });
};

export const getCmmcControlByIdentifier = (
  controlId="",
  tenantId = "",
  identifier = "", // strictly control_identifier
): Promise<ControlsType | null> => {
  return new Promise((resolve, reject) => {
    const surveillanceDb = dbPath[tenantId as keyof typeof dbPath] as string;

    if (typeof surveillanceDb !== "string" || surveillanceDb.trim() === "") {
      console.error("Invalid database path for tenantId:", tenantId);
      return resolve(null);
    }

    const db = new sqlite3.Database(
      surveillanceDb,
      sqlite3.OPEN_READONLY,
      (err) => {
        if (err) {
          return reject(err);
        }

        const query = `SELECT display_order, control_identifier, control_code, fii, common_criteria, expected_evidence, question
               FROM cmmc_control 
               WHERE control_identifier = ? AND control_type_id = ?
               LIMIT 1`;

        const params = [identifier,controlId] ;

        db.get(query, params, (err, row: ControlsType) => {
          if (err) {
            reject(err);
          } else {
            resolve(row || null);
          }

          db.close((closeErr) => {
            if (closeErr) {
              console.error("Error closing database:", closeErr.message);
            }
          });
        });
      },
    );
  });
};

export const getControlListforHitrust = (
  //regime = 0,
 // auditType = 0,
  tenantId = "",
): Promise<ControlsType[]> => {
  return new Promise((resolve, reject) => {

    surveillanceDb = dbPath[tenantId as keyof typeof dbPath] as string;
    //console.log("Database Path:", surveillanceDb);
    if (typeof surveillanceDb !== "string" || surveillanceDb.trim() === "") {
      //throw new Error(`Invalid database path for tenantId: ${tenantId}`);
      console.error("Invalid database path for tenantId:");
    }

    const db = new sqlite3.Database(surveillanceDb, sqlite3.OPEN_READONLY, (err) => {
      if (err) {
        return reject(err);
        //return console.error("Error closing database:invalid path");
        
      }

      const query =
        `SELECT display_order,control_identifier,control_code,fii,common_criteria,expected_evidence,question
         FROM hitrust_control ORDER BY display_order ASC`;

      db.all(query, [], (err, rows: ControlsType[]) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }

        db.close((closeErr) => {
          if (closeErr) {
            console.error("Error closing database:", closeErr.message);
          }
        });
      });
    });
  });
};

export const getControlByIdentifierHitrust = (
  tenantId = "",
  identifier = "", // strictly control_identifier
): Promise<ControlsType | null> => {
  return new Promise((resolve, reject) => {
    const surveillanceDb = dbPath[tenantId as keyof typeof dbPath] as string;

    if (typeof surveillanceDb !== "string" || surveillanceDb.trim() === "") {
      console.error("Invalid database path for tenantId:", tenantId);
      return resolve(null);
    }

    const db = new sqlite3.Database(
      surveillanceDb,
      sqlite3.OPEN_READONLY,
      (err) => {
        if (err) {
          return reject(err);
        }

        const query = ` SELECT display_order,control_identifier,control_code,fii,common_criteria,expected_evidence,question
         FROM hitrust_control where control_identifier=?`;

        const params = [identifier];

        db.get(query, params, (err, row: ControlsType) => {
          if (err) {
            reject(err);
          } else {
            resolve(row || null);
          }

          db.close((closeErr) => {
            if (closeErr) {
              console.error("Error closing database:", closeErr.message);
            }
          });
        });
      },
    );
  });
};

export const getISOControlList = (  
  tenantId = "",
): Promise<ControlsType[]> => {
  return new Promise((resolve, reject) => {

    surveillanceDb = dbPath[tenantId as keyof typeof dbPath] as string;
    //console.log("Database Path:", surveillanceDb);
    if (typeof surveillanceDb !== "string" || surveillanceDb.trim() === "") {
      //throw new Error(`Invalid database path for tenantId: ${tenantId}`);
      console.error("Invalid database path for tenantId:");
    }
    const db = new sqlite3.Database(surveillanceDb, sqlite3.OPEN_READONLY, (err) => {
      if (err) {
        return reject(err);
        //return console.error("Error closing database:invalid path");        
      }
      const query = `SELECT display_order, control_identifier, control_code, fii, common_criteria, expected_evidence, question
        FROM iso27001_control ORDER BY display_order ASC`;
      db.all(query, [], (err, rows: ControlsType[]) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }

        db.close((closeErr) => {
          if (closeErr) {
            console.error("Error closing database:", closeErr.message);
          }
        });
      });
    });
  });
};

export const getControlByIdentifierIso = (
  
  tenantId = "",
  identifier = "", // strictly control_identifier
): Promise<ControlsType | null> => {

  return new Promise((resolve, reject) => {
    const surveillanceDb = dbPath[tenantId as keyof typeof dbPath] as string;

    if (typeof surveillanceDb !== "string" || surveillanceDb.trim() === "") {
      console.error("Invalid database path for tenantId:", tenantId);
      return resolve(null);
    }

    const db = new sqlite3.Database(
      surveillanceDb,
      sqlite3.OPEN_READONLY,
      (err) => {
        if (err) {
          return reject(err);
        }

        const query =`SELECT display_order, control_identifier, control_code, fii, common_criteria, expected_evidence, question
               FROM iso27001_control
               WHERE control_identifier = ?
               LIMIT 1`
         // auditType === 0
            // ? 
            // : ` SELECT display_order, control_identifier, control_code, fii, common_criteria, expected_evidence, question
            //    FROM iso27001_control
            //    WHERE control_identifier = ?
            //    LIMIT 1`;

        const params =[identifier]
          // auditType === 0
          //   ? [identifier]
          //   : [identifier];

        db.get(query, params, (err, row: ControlsType) => {
          if (err) {
            reject(err);
          } else {
            resolve(row || null);
          }

          db.close((closeErr) => {
            if (closeErr) {
              console.error("Error closing database:", closeErr.message);
            }
          });
        });
      },
    );
  });
};
