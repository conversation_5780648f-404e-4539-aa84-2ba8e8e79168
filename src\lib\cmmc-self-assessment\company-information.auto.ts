import * as rt from "./r4q-runtime.ts";
/**
 * @file company-information.auto.ts
 * @generated This file was auto-generated from the FHIR R4 Questionnaire "Company Information".
 * Do not edit this file manually; re-run the generator if the source Questionnaire changes.
 * Profiles: http://hl7.org/fhir/4.0/StructureDefinition/Questionnaire
 *
 * Normalizes LHC JSON and FHIR QuestionnaireResponse into the type-safe `CompanyInformation` interface.
 */

// this is the module signature, used by importers to identify the module
// using r4q-runtime.ts `moduleSignature` function
export const companyInformationModuleSignature: rt.ModuleSignature = {
  title: "Company Information",
  filename: "company-information.auto.ts",
  titleCamel: "`companyInformation`",
  titlePascal: "`CompanyInformation`",
  titleKebab: "`company-information`",
  lhcFormResponseAdapterFnName: "companyInformationLhcFormResponseAdapter",
  fhirQuestionnaireResponseAdapterFnName: "companyInformationFhirQuestionnaireResponseAdapter",
  sourceTextConstName: "companyInformationSource",
}

// deno-lint-ignore no-explicit-any
type Any = any;
/**
 * Form Help (from display/help controls):
 * - Provide essential information about your organization for CMMC compliance tracking.
 */
/** Map of normalized property names to their source `linkId`. */
export const companyInformationLinkIds = {
  organizationName: "715544477968",
  formCompletedBy: "655141523763",
  positionTitle: "761144039651",
  emailAddress: "441278853405",
  workPhone: "375736159279",
  mobilePhone: "948589414714",
  assessmentDate: "276403539223",
  industry: "789286873476",
  employeeCount: "697235963218",
  contractTypes: "863463230823",
  cageCode: "805221373063",
  dunsNumber: "374784155003"
} as const;

/** Normalized view of "Company Information" answers. */
export interface CompanyInformation {
  /**
   * Organization Name
   * linkId: 715544477968
   * FHIR type: string
   * Entry format: Enter your organization name
   * Required: yes
   */
  organizationName: string;

  /**
   * Form Completed By
   * linkId: 655141523763
   * FHIR type: string
   * Entry format: Your full name
   * Required: yes
   */
  formCompletedBy: string;

  /**
   * Position/Title
   * linkId: 761144039651
   * FHIR type: string
   * Entry format: Your job title
   * Required: no
   */
  positionTitle?: string;

  /**
   * Email Address
   * linkId: 441278853405
   * FHIR type: string
   * Entry format: <EMAIL>
   * Required: yes
   */
  emailAddress: string;

  /**
   * Work Phone
   * linkId: 375736159279
   * FHIR type: string
   * Entry format: (*************
   * Required: yes
   */
  workPhone: string;

  /**
   * Mobile Phone
   * linkId: 948589414714
   * FHIR type: string
   * Entry format: (*************
   * Required: yes
   */
  mobilePhone: string;

  /**
   * Assessment Date
   * linkId: 276403539223
   * FHIR type: date
   * Required: no
   */
  assessmentDate?: Date;

  /**
   * Industry
   * linkId: 789286873476
   * FHIR type: string
   * Entry format: Defense, Technology, etc.
   * Required: no
   */
  industry?: string;

  /**
   * Employee Count
   * linkId: 697235963218
   * FHIR type: string
   * Entry format: 1-10, 11-50, 51-200, etc.
   * Required: no
   */
  employeeCount?: string;

  /**
   * Contract Types
   * linkId: 863463230823
   * FHIR type: text
   * Entry format: Prime contracts, subcontracts, etc. (comma-separated)
   * Required: no
   */
  contractTypes?: string;

  /**
   * CAGE Code
   * linkId: 805221373063
   * FHIR type: string
   * Entry format: 5-character CAGE code
   * Section: Organization Identifiers
   * Required: no
   */
  cageCode?: string;

  /**
   * DUNS Number
   * linkId: 374784155003
   * FHIR type: string
   * Entry format: 9-digit DUNS number
   * Section: Organization Identifiers
   * Required: no
   */
  dunsNumber?: string;
}

/** Convert an LHC JSON response into a normalized CompanyInformation object. */
export function companyInformationLhcFormResponseAdapter(input: Any): CompanyInformation {
  return {
    organizationName: rt.coerceString(rt.findLhcValueByLinkId(input, "715544477968")),
    formCompletedBy: rt.coerceString(rt.findLhcValueByLinkId(input, "655141523763")),
    positionTitle: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "761144039651")),
    emailAddress: rt.coerceString(rt.findLhcValueByLinkId(input, "441278853405")),
    workPhone: rt.coerceString(rt.findLhcValueByLinkId(input, "375736159279")),
    mobilePhone: rt.coerceString(rt.findLhcValueByLinkId(input, "948589414714")),
    assessmentDate: rt.coerceOptionalDate(rt.findLhcValueByLinkId(input, "276403539223")),
    industry: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "789286873476")),
    employeeCount: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "697235963218")),
    contractTypes: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "863463230823")),
    cageCode: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "805221373063")),
    dunsNumber: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "374784155003")),
  };
}

/** Convert a FHIR QuestionnaireResponse into a normalized CompanyInformation object. */
export function companyInformationFhirQuestionnaireResponseAdapter(qr: Any): CompanyInformation {
  return {
    organizationName: rt.coerceString(rt.findQrAnswerByLinkId(qr, "715544477968")),
    formCompletedBy: rt.coerceString(rt.findQrAnswerByLinkId(qr, "655141523763")),
    positionTitle: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "761144039651")),
    emailAddress: rt.coerceString(rt.findQrAnswerByLinkId(qr, "441278853405")),
    workPhone: rt.coerceString(rt.findQrAnswerByLinkId(qr, "375736159279")),
    mobilePhone: rt.coerceString(rt.findQrAnswerByLinkId(qr, "948589414714")),
    assessmentDate: rt.coerceOptionalDate(rt.findQrAnswerByLinkId(qr, "276403539223")),
    industry: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "789286873476")),
    employeeCount: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "697235963218")),
    contractTypes: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "863463230823")),
    cageCode: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "805221373063")),
    dunsNumber: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "374784155003")),
  };
}

/**
 * NOTE TO DEVELOPERS:
 * -------------------
 * This Interpreter class is provided only as an EXAMPLE scaffold to demonstrate
 * how to consume the normalized type-safe interface generated for this
 * Questionnaire. It shows minimal factories (`fromLhc`, `fromQuestionnaireResponse`)
 * and convenience methods (`validateRequiredFields`, `assessReadiness`) but it is
 * NOT intended for production use.
 *
 * In real applications:
 * - Treat this class as SAMPLE CODE only.
 * - Replace or extend it with proper business logic, rules engines, or validation
 *   frameworks appropriate to your domain.
 * - Do not rely on the simplistic readiness scoring or validation in production
 *   scenarios; they are illustrative, not authoritative.
 *
 * Best practice: use the generated TypeScript interface (`CompanyInformation`) as your
 * contract for normalized data, then integrate with your own rules processors,
 * compliance engines, or plain TypeScript/JavaScript functions as needed.
 */
export class CompanyInformationInterpreter {
  constructor(readonly value: CompanyInformation) { }

  /** Factory: build from LHC JSON. */
  static fromLhcFormResponse(input: Any): CompanyInformationInterpreter {
    return new CompanyInformationInterpreter(companyInformationLhcFormResponseAdapter(input));
  }

  /** Factory: build from FHIR QuestionnaireResponse. */
  static fromQuestionnaireResponse(qr: Any): CompanyInformationInterpreter {
    return new CompanyInformationInterpreter(companyInformationFhirQuestionnaireResponseAdapter(qr));
  }

  /** Check required fields and report any missing or blank. */
  validateRequiredFields(): { ok: boolean; missing: Array<keyof CompanyInformation> } {
    const missing: Array<keyof CompanyInformation> = [];
    const req: Array<keyof CompanyInformation> = ["organizationName", "formCompletedBy", "emailAddress", "workPhone", "mobilePhone"];
    for (const k of req) {
      const v = (this.value as Any)[k];
      if (rt.isBlank(v)) missing.push(k);
    }
    return { ok: missing.length === 0, missing };
  }

  /**
   * Assess readiness with a simple completeness score. This is meant to be
   * used to help understand how complete the types are and serves as an
   * example of how to use the generated code.
   * - requiredCovered: percentage of required fields that are non-blank
   * - overallFilled: count of non-blank fields among all known properties
   */
  assessReadiness(): {
    formTitle: string;
    requiredCovered: number; // 0..1
    totalRequired: number;
    totalFilled: number;
    totalFields: number;
    missingRequired: Array<keyof CompanyInformation>;
  } {
    const req: Array<keyof CompanyInformation> = ["organizationName", "formCompletedBy", "emailAddress", "workPhone", "mobilePhone"];
    const all: Array<keyof CompanyInformation> = ["organizationName", "formCompletedBy", "positionTitle", "emailAddress", "workPhone", "mobilePhone", "assessmentDate", "industry", "employeeCount", "contractTypes", "cageCode", "dunsNumber"];

    let reqFilled = 0;
    const missingReq: Array<keyof CompanyInformation> = [];
    for (const k of req) {
      const v = (this.value as Any)[k];
      if (!rt.isBlank(v)) reqFilled++;
      else missingReq.push(k);
    }

    let totalFilled = 0;
    for (const k of all) {
      if (!rt.isBlank((this.value as Any)[k])) totalFilled++;
    }

    return {
      formTitle: "Company Information",
      requiredCovered: req.length ? reqFilled / req.length : 1,
      totalRequired: req.length,
      totalFilled,
      totalFields: all.length,
      missingRequired: missingReq,
    };
  }
}

/** The original source */
export const companyInformationSource = `{
  "resourceType": "Questionnaire",
  "meta": {
    "profile": [
      "http://hl7.org/fhir/4.0/StructureDefinition/Questionnaire"
    ],
    "tag": [
      {
        "display": "Company Data"
      },
      {
        "display": "Business Profile"
      },
      {
        "display": "Compliance Information"
      },
      {
        "display": "Organizational Details"
      },
      {
        "display": "Registration Data"
      },
      {
        "display": "Contact Information"
      }
    ]
  },
  "title": "Company Information",
  "status": "draft",
  "date": "2025-08-27",
  "publisher": "Netspective",
  "description": "Company Information",
  "purpose": "This section collects details about the organization performing the assessment. It establishes the business context in which safeguarding requirements apply, including the company’s name, location, industry sector, size, and contract role. The purpose is to document whether the organization operates information systems that process, store, or transmit Federal Contract Information (FCI). It also records key points of contact, the scope of operations subject to assessment, and any subcontracting relationships where the safeguarding clause must be flowed down. This information forms the baseline for evaluating compliance with FAR 52.204-21 and mapping safeguards to the organization's environment",
  "approvalDate": "2025-08-27",
  "item": [
    {
      "type": "group",
      "linkId": "158032884208",
      "text": "Organization Details",
      "item": [
        {
          "linkId": "158032884208_helpText",
          "type": "display",
          "text": "Provide essential information about your organization for CMMC compliance tracking.",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "help",
                    "display": "Help-Button"
                  }
                ],
                "text": "Help-Button"
              }
            }
          ]
        }
      ]
    },
    {
      "type": "string",
      "extension": [
        {
          "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
          "valueString": "Enter your organization name"
        }
      ],
      "linkId": "715544477968",
      "text": "Organization Name",
      "required": true
    },
    {
      "type": "string",
      "extension": [
        {
          "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
          "valueString": "Your full name"
        }
      ],
      "linkId": "655141523763",
      "text": "Form Completed By",
      "required": true
    },
    {
      "type": "string",
      "extension": [
        {
          "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
          "valueString": "Your job title"
        }
      ],
      "linkId": "761144039651",
      "text": "Position/Title"
    },
    {
      "type": "string",
      "extension": [
        {
          "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
          "valueString": "<EMAIL>"
        }
      ],
      "linkId": "441278853405",
      "text": "Email Address",
      "required": true
    },
    {
      "type": "string",
      "extension": [
        {
          "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
          "valueString": "(*************"
        }
      ],
      "linkId": "375736159279",
      "text": "Work Phone",
      "required": true
    },
    {
      "type": "string",
      "extension": [
        {
          "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
          "valueString": "(*************"
        }
      ],
      "linkId": "948589414714",
      "text": "Mobile Phone",
      "required": true
    },
    {
      "type": "date",
      "linkId": "276403539223",
      "text": "Assessment Date"
    },
    {
      "type": "string",
      "extension": [
        {
          "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
          "valueString": "Defense, Technology, etc."
        }
      ],
      "linkId": "789286873476",
      "text": "Industry"
    },
    {
      "type": "string",
      "extension": [
        {
          "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
          "valueString": "1-10, 11-50, 51-200, etc."
        }
      ],
      "linkId": "697235963218",
      "text": "Employee Count"
    },
    {
      "type": "text",
      "extension": [
        {
          "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
          "valueString": "Prime contracts, subcontracts, etc. (comma-separated)"
        }
      ],
      "linkId": "863463230823",
      "text": "Contract Types"
    },
    {
      "item": [
        {
          "type": "string",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
              "valueString": "5-character CAGE code"
            }
          ],
          "linkId": "805221373063",
          "text": "CAGE Code"
        },
        {
          "type": "string",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
              "valueString": "9-digit DUNS number"
            }
          ],
          "linkId": "374784155003",
          "text": "DUNS Number"
        }
      ],
      "type": "group",
      "linkId": "127163950314",
      "text": "Organization Identifiers"
    }
  ]
}`;