---
import Layout from "../../../layouts/Layout.astro";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  CheckCircle,
  FileCheck,
  Clock,
  Target,
  ArrowRight,
  Shield,
  AlertCircle,
  TrendingUp,
} from "lucide-react";

// import soc2Report from "/assets/soc2-report.png";

const soc2Benefits = [
  "Unlock enterprise sales opportunities",
  "Build customer trust and credibility",
  "Streamline security questionnaires",
  "Demonstrate commitment to data protection",
  "Meet vendor security requirements",
];

const processSteps = [
  {
    step: "1",
    title: "Gap Assessment",
    description: "We audit your current controls against SOC2 requirements",
    duration: "Days 1-3",
  },
  {
    step: "2",
    title: "Policy & Control Implementation",
    description: "Deploy our proven policy framework and control automation",
    duration: "Days 4-11",
  },
  {
    step: "3",
    title: "Evidence Collection",
    description: "Automated evidence gathering and audit preparation",
    duration: "Days 10-15",
  },
  {
    step: "4",
    title: "Type 1 Audit",
    description: "Formal audit of control design by certified CPA firm",
    duration: "Days 16-18",
  },
  {
    step: "5",
    title: "Type 2 Preparation",
    description: "6-month operating effectiveness period with monitoring",
    duration: "Weeks 4-7",
  },
];

const frameworks = [
  {
    name: "SOC2 Type 1",
    description: "Point-in-time control design assessment",
    timeline: "2-4 weeks",
  },
  {
    name: "SOC2 Type 2",
    description: "6-month operating effectiveness audit",
    timeline: "1-2 months",
  },
  {
    name: "SOC2+ (Custom)",
    description: "Enhanced controls for regulated industries",
    timeline: "4-6 months",
  },
];

const testimonial = {
  quote:
    "We’re excited about the future. With Opsfolio CaaS as our partner, we’re not just achieving compliance; we’re building a sustainable future for our clients and the planet.",
  author: "Tara Gupta",
  role: "Founder & CEO",
  company: "Map Collective",
};

const currentYear = new Date().getFullYear();
const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1",
    text: "Regime",
    "aria-disabled": true,
  },
  {
    index: "2",
    text: "SOC2",
    "aria-disabled": true,
  },
];
---

<Layout
  title="SOC2 Certification in 2 Months - Get SOC2 Type 1 & Type 2 Fast"
  description="The fastest path to SOC2 Type 1 and Type 2 certification. Expert guidance, automated evidence collection, and proven policy frameworks that pass audits. 98% pass rate."
  keywords="SOC2 certification, SOC2 Type 1, SOC2 Type 2, SOC2 audit, compliance certification"
  url="/regime/soc2/"
  breadcrumbLinks={breadcrumbLinks}
>
  <main>
    {/* Hero Section */}
    <div
      class="relative isolate px-6 pt-14 lg:px-8 bg-gradient-to-br from-primary/5 via-background to-secondary/5"
    >
      <div class="mx-auto max-w-4xl py-16 sm:py-24 lg:py-28">
        <div class="text-center">
          <Badge variant="secondary" className="mb-4">SOC2 Compliance</Badge>
          <h1
            class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl"
          >
            Get <span class="text-primary">SOC2 Certified</span> in 2 Months,{
              " "
            }
            Not 12+
          </h1>
          <p
            class="mt-6 text-lg leading-8 text-muted-foreground max-w-3xl mx-auto"
          >
            The fastest path to SOC2 Type 1 and Type 2 certification. Expert
            guidance, automated evidence collection, and proven policy
            frameworks that pass audits.
          </p>
          <div class="mt-8 flex items-center justify-center gap-x-4 flex-wrap">
            <div class="flex items-center text-sm text-muted-foreground">
              <CheckCircle className="h-4 w-4 text-success mr-2" />
              98% First-Time Pass Rate
            </div>
            <div class="flex items-center text-sm text-muted-foreground">
              <CheckCircle className="h-4 w-4 text-success mr-2" />
              500+ Companies Certified
            </div>
            <div class="flex items-center text-sm text-muted-foreground">
              <CheckCircle className="h-4 w-4 text-success mr-2" />
              Expert-Guided Process
            </div>
          </div>
          <div class="mt-10 flex items-center justify-center gap-x-6">
            <a href="/get-started">
              <Button size="lg" variant="default">
                Get SOC2 Readiness Assessment
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </a>
            <a href="/resources">
              <Button variant="outline" size="lg"> Download SOC2 Guide </Button>
            </a>
            <a href="/regime/soc2/controls">
              <Button variant="outline" size="lg">Control Explorer</Button>
            </a>
          </div>
        </div>
      </div>
    </div>

    {/* Why SOC2 Section */}
    <div class="py-24 sm:py-32">
      <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h2
              class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl mb-6"
            >
              Why SOC2 Matters for Your Business
            </h2>
            <p class="text-lg text-muted-foreground mb-8">
              SOC2 is the gold standard for demonstrating your commitment to
              security, availability, and data protection. It's often required
              for enterprise sales and building customer trust.
            </p>
            <div class="space-y-4">
              {
                soc2Benefits.map((benefit) => (
                  <div class="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 text-success mt-0.5 flex-shrink-0" />
                    <span class="text-muted-foreground">{benefit}</span>
                  </div>
                ))
              }
            </div>
          </div>
          <div class="bg-background p-8 rounded-lg shadow-lg">
            <img
              src="/assets/soc2-report.png"
              alt="SOC2 Type 2 audit report showing security controls, availability criteria, and processing integrity assessments"
              class="w-full h-64 object-cover rounded-lg mb-6 shadow-lg"
            />
            <p class="text-sm text-muted-foreground text-center">
              Sample SOC2 Type 2 audit report with all Trust Services Criteria
            </p>
          </div>
        </div>
      </div>
    </div>

    {/* Process Section */}
    <div class="py-24 sm:py-32 bg-muted">
      <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <div class="mx-auto max-w-2xl text-center mb-16">
          <h2
            class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl"
          >
            Our Proven SOC2 Process
          </h2>
          <p class="mt-6 text-lg leading-8 text-muted-foreground">
            A step-by-step approach that gets you certified fast while building
            lasting compliance
          </p>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {
            processSteps.map((step, index) => (
              <Card key={step.step} className="relative">
                <CardHeader>
                  <div class="flex items-center space-x-3">
                    <div class="flex items-center justify-center w-8 h-8 bg-primary text-primary-foreground rounded-full font-bold">
                      {step.step}
                    </div>
                    <Badge variant="outline">{step.duration}</Badge>
                  </div>
                  <CardTitle className="text-xl">{step.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p class="text-muted-foreground">{step.description}</p>
                </CardContent>
                {index < processSteps.length - 1 && (
                  <div class="hidden md:block absolute top-1/2 -right-4 transform -translate-y-1/2">
                    <ArrowRight className="h-6 w-6 text-muted-foreground" />
                  </div>
                )}
              </Card>
            ))
          }
        </div>
      </div>
    </div>

    {/* SOC2 Types Section */}
    <div class="py-24 sm:py-32">
      <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <div class="mx-auto max-w-2xl text-center mb-16">
          <h2
            class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl"
          >
            Choose Your SOC2 Path
          </h2>
          <p class="mt-6 text-lg leading-8 text-muted-foreground">
            Different SOC2 options for different business needs and timelines
          </p>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          {
            frameworks.map((framework, index) => (
              <a href="/get-started">
                <Card
                  key={framework.name}
                  className={`relative ${index === 1 ? "border-2 border-primary" : ""} hover:shadow-lg transition-shadow duration-300`}
                >
                  {index === 1 && (
                    <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <Badge variant="default">Most Popular</Badge>
                    </div>
                  )}
                  <CardHeader className="text-center">
                    <CardTitle className="text-xl">{framework.name}</CardTitle>
                    <p class="text-muted-foreground">{framework.description}</p>
                  </CardHeader>
                  <CardContent className="text-center">
                    <div class="text-3xl font-bold text-primary mb-2">
                      {framework.timeline}
                    </div>
                    <p class="text-sm text-muted-foreground mb-6">
                      Typical timeline
                    </p>
                    <Button
                      className="w-full"
                      variant={index === 1 ? "default" : "outline"}
                    >
                      Get Started
                    </Button>
                  </CardContent>
                </Card>
              </a>
            ))
          }
        </div>
      </div>
    </div>

    {/* Testimonial Section */}
    <div class="py-24 sm:py-32 bg-muted">
      <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <Card className="max-w-4xl mx-auto text-center">
          <CardContent className="p-12">
            <blockquote class="text-xl text-muted-foreground italic mb-8">
              "{testimonial.quote}"
            </blockquote>
            <div>
              <div class="font-semibold text-foreground text-lg">
                {testimonial.author}
              </div>
              <div class="text-muted-foreground">
                {testimonial.role}, {testimonial.company}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>

    {/* CTA Section */}
    <div>
      <div class="px-6 py-24 sm:px-6 sm:py-32 lg:px-8">
        <div class="mx-auto max-w-2xl text-center">
          <h2
            class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl"
          >
            Get SOC2 Certified in {currentYear}
          </h2>
          <p
            class="mx-auto mt-6 max-w-xl text-lg leading-8 text-muted-foreground/90"
          >
            Start with a free readiness assessment and get your custom SOC2
            roadmap in 24 hours.
          </p>
          <div class="mt-10 flex items-center justify-center gap-x-6">
            <a href="/get-started">
              <Button size="lg" variant="default"
                >Start Free Assessment
              </Button>
            </a>
            <a href="/contact">
              <Button size="lg" variant="outline">Talk to SOC2 Expert </Button>
            </a>
          </div>
          <div class="mt-8 text-sm text-primary-foreground/80">
            Free assessment • No commitment • Expert consultation included
          </div>
        </div>
      </div>
    </div>
  </main>
</Layout>
