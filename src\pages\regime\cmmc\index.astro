---
import Layout from "../../../layouts/Layout.astro";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Flag, Shield, Building2, Clock, CheckCircle, ArrowRight } from "lucide-react";

const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Regime',"aria-disabled": true ,
  },
  {
    index: "2", text: "CMMC","aria-disabled": true 
  },
];
---

<Layout title="Opsfolio - Compliance as a Service | SOC2, HIPAA, ISO Certification"
        description="Get SOC2, HIPAA, ISO certified fast with expert-guided compliance. 98% audit pass rate, 60% faster than DIY. Your Compliance System of Record."
        keywords="CMMC Level 1, CMMC Level 2, CMMC Level 3, DoD contracts, cybersecurity maturity model certification, controlled unclassified information, defense industrial base compliance"
        url="/regime/cmmc/"
        breadcrumbLinks={breadcrumbLinks}>

      {/* Hero Section */}
      <section class="relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5 pt-16 pb-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center">
            <Badge variant="outline" className="mb-4">
              CMMC Compliance
            </Badge>
            <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
              Secure DoD Contracts with CMMC
            </h1>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Achieve Cybersecurity Maturity Model Certification (CMMC) to bid on Department of Defense 
              contracts. Protect Controlled Unclassified Information (CUI) with our proven methodology.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
              
                <a href="/regime/cmmc/self-assessment?journey=cmmc-self-assessment">
                  <Button size="lg" >
                  Start CMMC Self Assessment
                  <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </a>              
                <a href="/contact">
                  <Button variant="outline" size="lg" >Talk to CMMC Expert
                   </Button>
                </a>  
                <a href="/regime/cmmc/controls">
                  <Button variant="outline" size="lg">Control Explorer</Button>
                </a>            
            </div>
          </div>
        </div>
      </section>

      {/* CMMC Levels */}
      <section class="py-16 sm:py-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl lg:text-center">
            <h2 class="text-base font-semibold leading-7 text-primary">CMMC Levels</h2>
            <p class="mt-2 text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Choose Your Certification Level
            </p>
          </div>
          <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <dl class="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
              <div class="flex flex-col">
                <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                  <Flag className="h-5 w-5 flex-none text-primary" />
                  CMMC Level 1
                </dt>
                <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p class="flex-auto">
                    Basic safeguarding of Federal Contract Information (FCI). Self-assessment 
                    with 17 practices across 7 domains for foundational cybersecurity.
                  </p>
                </dd>
              </div>
              <div class="flex flex-col">
                <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                  <Shield className="h-5 w-5 flex-none text-primary" />
                  CMMC Level 2
                </dt>
                <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p class="flex-auto">
                    Advanced protection of Controlled Unclassified Information (CUI). 
                    Third-party assessment with 110 practices aligned to NIST SP 800-171.
                  </p>
                </dd>
              </div>
              <div class="flex flex-col">
                <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                  <Building2 className="h-5 w-5 flex-none text-primary" />
                  CMMC Level 3
                </dt>
                <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p class="flex-auto">
                    Enhanced protection for the most sensitive CUI. Expert-level assessment 
                    with additional practices for advanced persistent threats (APT).
                  </p>
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </section>

      {/* Implementation Timeline */}
      <section class="py-16 sm:py-20 bg-muted">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl lg:text-center">
            <h2 class="text-base font-semibold leading-7 text-primary">Implementation</h2>
            <p class="mt-2 text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              CMMC Level 2 in 4-6 Months
            </p>
          </div>
          <div class="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-6 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-4 lg:gap-8">
            {[
              {
                phase: "Month 1",
                title: "Gap Analysis",
                description: "Assess current cybersecurity posture against CMMC requirements and NIST SP 800-171."
              },
              {
                phase: "Months 2-3",
                title: "Control Implementation", 
                description: "Deploy technical controls, policies, and procedures to meet CMMC practice requirements."
              },
              {
                phase: "Month 4-5",
                title: "Documentation & Training",
                description: "Complete System Security Plan (SSP), Plan of Action (POA&M), and staff training."
              },
              {
                phase: "Month 6",
                title: "Assessment Prep",
                description: "Pre-assessment validation and preparation for official CMMC assessment."
              }
            ].map((phase) => (
              <div class="flex flex-col items-start">
                <div class="rounded-md bg-primary/10 p-2 ring-1 ring-primary/20">
                  <Clock className="h-6 w-6 text-primary" />
                </div>
                <dt class="mt-4 font-semibold text-foreground">{phase.phase}</dt>
                <dd class="mt-2 text-sm font-medium text-primary">{phase.title}</dd>
                <dd class="mt-2 leading-7 text-muted-foreground text-sm">{phase.description}</dd>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section class="py-16 sm:py-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Ready to Win DoD Contracts?
            </h2>
            <p class="mx-auto mt-6 max-w-xl text-lg leading-8 text-muted-foreground">
              Get CMMC certified and unlock lucrative Department of Defense contracting opportunities.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
              
                <a href="/get-started">
                  <Button size="lg" >Start CMMC Assessment
                   </Button>
                </a>              
                <a href="/resources">
                  <Button variant="outline" size="lg" >Download CMMC Guide
                  </Button>
                </a>              
            </div>
          </div>
        </div>
      </section>

</Layout>
