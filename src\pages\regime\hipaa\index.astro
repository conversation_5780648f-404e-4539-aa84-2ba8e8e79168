---
import Layout from "../../../layouts/Layout.astro";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Shield, FileText, Users, Clock, CheckCircle, ArrowRight, Heart } from "lucide-react";
//import complianceWorkflow from "/assets/hipaa-compliance.png";

const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Regime',"aria-disabled": true ,
  },
  {
    index: "2", text: "HIPAA","aria-disabled": true 
  },
];
---

<Layout title="HIPAA Compliance Made Simple - Healthcare Data Protection"
  description="Achieve HIPAA compliance in 90 days with expert guidance, automated controls, and continuous monitoring. Protect patient data with comprehensive healthcare compliance solutions."
  keywords="HIPAA compliance, healthcare data protection, PHI security, healthcare compliance, HIPAA training, healthcare cybersecurity"
  url="/regime/hipaa/"
  breadcrumbLinks={breadcrumbLinks}>

        {/* Hero Section */}
      <section class="relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5 pt-16 pb-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center">
            <Badge variant="outline" className="mb-4">
              HIPAA Compliance
            </Badge>
            <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
              HIPAA Compliance Made Simple
            </h1>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Protect patient data and achieve HIPAA compliance with our comprehensive platform. 
              Get expert guidance, automated controls, and continuous monitoring for healthcare applications.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
                <a href="/regime/hipaa/self-assessment?journey=hipaa-self-assessment">
                  <Button size="lg">
                      Start HIPAA Assessment
                    <ArrowRight className="ml-2 h-4 w-4" />                  
                  </Button>
                </a>  
               <a href="/contact">
                  <Button variant="outline" size="lg">Talk to HIPAA Expert</Button>
                </a>
                <a href="/regime/hipaa/controls">
                  <Button variant="outline" size="lg">Control Explorer</Button>
                </a>
            </div>
          </div>
        </div>
      </section>



      {/* HIPAA Overview */}
      <section class="py-16 sm:py-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8 space-y-16">
          
          {/* Row 1: Image + Heading/Description */}
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left: Image */}
            <div>
              <img
                src="/assets/hipaa-compliance.png"
                alt="HIPAA compliance workflow"
                class="w-full rounded-xl shadow-xl ring-1 ring-border"
              />
            </div>

            {/* Right: Heading + Description */}
            <div>
              <h2 class="text-base font-semibold leading-7 text-primary">HIPAA Fundamentals</h2>
              <p class="mt-2 text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                Comprehensive HIPAA Protection
              </p>
              <p class="mt-6 text-lg leading-8 text-muted-foreground">
                HIPAA compliance is essential for any organization handling protected health information (PHI). 
                Our platform helps you implement the necessary safeguards and maintain ongoing compliance.
              </p>
            </div>
          </div>

          {/* Row 2: Points Section */}
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Left Column: 2 Points */}
            <dl class="space-y-8 text-base leading-7 text-muted-foreground">
              <div class="relative pl-9">
                <dt class="inline font-semibold text-foreground">
                  <Heart className="absolute left-0 top-1 h-5 w-5 text-primary" />
                  Privacy Rule Compliance
                </dt>
                <dd>
                  Implement policies and procedures to protect PHI, including access controls, 
                  training programs, and incident response procedures.
                </dd>
              </div>

              <div class="relative pl-9">
                <dt class="inline font-semibold text-foreground">
                  <Shield className="absolute left-0 top-1 h-5 w-5 text-primary" />
                  Security Rule Implementation
                </dt>
                <dd>
                  Deploy technical, administrative, and physical safeguards to protect electronic 
                  PHI (ePHI) from unauthorized access and breaches.
                </dd>
              </div>
            </dl>

            {/* Right Column: Third Point */}
            <dl class="text-base leading-7 text-muted-foreground">
              <div class="relative pl-9">
                <dt class="inline font-semibold text-foreground">
                  <FileText className="absolute left-0 top-1 h-5 w-5 text-primary" />
                  Business Associate Agreements
                </dt>
                <dd>
                  Manage BAA requirements and ensure all third-party vendors handling PHI 
                  meet HIPAA compliance standards.
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </section>



      {/* HIPAA Requirements */}
      <section class="py-16 sm:py-20 bg-muted">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl lg:text-center">
            <h2 class="text-base font-semibold leading-7 text-primary">HIPAA Requirements</h2>
            <p class="mt-2 text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Complete HIPAA Safeguards
            </p>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Our platform addresses all required HIPAA safeguards with automated controls and expert guidance.
            </p>
          </div>
          <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <dl class="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
              <div class="flex flex-col">
                <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                  <Users className="h-5 w-5 flex-none text-primary" />
                  Administrative Safeguards
                </dt>
                <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p class="flex-auto">
                    Security Officer assignment, workforce training, information access management, 
                    and security awareness programs.
                  </p>
                </dd>
              </div>
              <div class="flex flex-col">
                <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                  <Shield className="h-5 w-5 flex-none text-primary" />
                  Technical Safeguards
                </dt>
                <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p class="flex-auto">
                    Access controls, audit logs, integrity protection, transmission security, 
                    and encryption for data at rest and in transit.
                  </p>
                </dd>
              </div>
              <div class="flex flex-col">
                <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                  <FileText className="h-5 w-5 flex-none text-primary" />
                  Physical Safeguards
                </dt>
                <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p class="flex-auto">
                    Facility access controls, workstation use restrictions, device and media 
                    controls, and secure disposal procedures.
                  </p>
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </section>

      {/* Implementation Timeline */}
      <section class="py-16 sm:py-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl lg:text-center">
            <h2 class="text-base font-semibold leading-7 text-primary">Implementation</h2>
            <p class="mt-2 text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              HIPAA in 90 Days
            </p>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Our proven methodology gets you HIPAA compliant in 90 days with ongoing monitoring and support.
            </p>
          </div>
          <div class="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-6 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-4 lg:gap-8">
            {[
              {
                week: "Weeks 1-2",
                title: "Risk Assessment",
                description: "Complete risk assessment, gap analysis, and implementation planning."
              },
              {
                week: "Weeks 3-6",
                title: "Policy Development",
                description: "Create HIPAA policies, procedures, and workforce training materials."
              },
              {
                week: "Weeks 7-10",
                title: "Technical Controls",
                description: "Implement technical safeguards, access controls, and monitoring systems."
              },
              {
                week: "Weeks 11-12",
                title: "Validation & Testing",
                description: "Test controls, conduct staff training, and prepare for ongoing compliance."
              }
            ].map((phase) => (
              <div class="flex flex-col items-start">
                <div class="rounded-md bg-primary/10 p-2 ring-1 ring-primary/20">
                  <Clock className="h-6 w-6 text-primary" />
                </div>
                <dt class="mt-4 font-semibold text-foreground">{phase.week}</dt>
                <dd class="mt-2 text-sm font-medium text-primary">{phase.title}</dd>
                <dd class="mt-2 leading-7 text-muted-foreground text-sm">{phase.description}</dd>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section class="py-16 sm:py-20 bg-muted">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Ready to Achieve HIPAA Compliance?
            </h2>
            <p class="mx-auto mt-6 max-w-xl text-lg leading-8 text-muted-foreground">
              Get started with our HIPAA compliance assessment and receive a custom implementation roadmap in 24 hours.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
              
                 <a href="/get-started">
                  <Button size="lg">Start HIPAA Assessment</Button>
                </a>

                <a href="/resources">
                  <Button variant="outline" size="lg">Download HIPAA Guide</Button>
                </a>
              </Button>
            </div>
          </div>
        </div>
      </section>


</Layout>