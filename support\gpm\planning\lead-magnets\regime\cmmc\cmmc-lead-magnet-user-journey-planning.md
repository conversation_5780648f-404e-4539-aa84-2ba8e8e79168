# CMMC Lead <PERSON><PERSON><PERSON> User Journey Planning

### 🔧 Prompt: “Generate a User Journey Map for CMMC Self-Assessment Tool”

You're an expert in UX design, funnel strategy, and B2B cybersecurity products.

Generate a **detailed user journey map** for a CMMC Self-Assessment Tool that:

- Attracts leads via email outreach
- Allows defense contractor professionals to assess their current CMMC Level 1 readiness
- Produces a customized report with both:
    1. Self-attestation documentation (if they pass)
    2. Strategic recommendations to improve readiness

The tool’s business goals are:

- Showcase Opsfolio’s domain expertise
- Build trust with security and compliance leaders
- Convert users into paid offerings (technical advisory and white-glove CMMC compliance support)

Your output should include:

---

### 1. **Funnel Entry Points**

List the typical entry points for each persona along with emotional drivers at that stage.

For this campaign, we will be mostly focusing on cold email and LinkedIn outreach. Provide options for expansion of funnel construction for use if we have time and resources. 

---

### 2. **User Journey Stages**

Create a 5–7 stage journey from initial awareness to post-assessment. For each stage, include:

- **Stage Name**
- **Persona-specific User Goals**
- **Emotional State (per persona)**
- **Touchpoints or Interactions**
- **Opportunities to Build Trust (including entry, tool use, and exit)**
- **Content or CTA Suggestions**

---

### 3. **Conversion Levers**

For each persona, describe what types of trust-building elements and follow-ups can be included in the assessment and funnel that can move them into a paid tier:

- Founder/CEO: prefers ROI clarity and speed
- CTO: technical integration roadmap
- CISO: audit-readiness, policy unification
- GRC Officer: ease of evidence collection
- IT Director: secure defaults and implementation guides
- Program Manager: visibility into task ownership

---

### 4. **Post-Assessment Funnel Suggestions**

After the report is delivered, provide:

- Recommended follow-up emails per persona
- Call scheduling CTAs or light nurture flows
- Upsell pathways toward technical advisory or white glove service

---

### 5. **Design Notes**

- Incorporate insights from known pain points and motivators (see below).
- Assume the tool is largely self-service but supports future SME interactions.
- Assume no AI copilot yet, but that one may be added later.
- Highlight where users might drop off or need emotional reassurance.

---

### 6. Format

- The output should be a table with 5 columns: Stage, User Goal, Emotional State, Touchpoints, Conversion opportunities
- The columns should be the stages of the funnel - entry, tool use, post tool funnel, etc. Build on the sections given our previous user journey map (given below)
- The there should be 1 table total, not a table per persona

### Persona Reference:

### **Founder / CEO (SMBs)**

- **Pain Points**:
    - Legal and financial liability for compliance failure
    - Risk of losing DoD contracts
    - Lack of visibility and accountability into compliance
- **Motivators**:
    - Protect the business
    - Move fast toward a compliance milestone
    - Avoid reputation-damaging incidents
- **Value Proposition**:
    
    > "De-risk new and continuing defense contracts with fast CMMC outcomes."
    > 

---

### B. **CTO / Tech Leader**

- **Pain Points**:
    - Complex cybersecurity requirements
    - Compliance eating into engineering time
    - Shortage of security/compliance expertise
- **Motivators**:
    - Streamline security posture without building a compliance team
    - Align security with business priorities
- **Value Proposition**:
    
    > "Build trust in your stack — without building a compliance team."
    > 

---

### C. **CISO / Security Engineer**

- **Pain Points**:
    - Manual compliance work and audit bottlenecks
    - Siloed documentation and unclear team roles
    - Vendor coordination with MSPs
- **Motivators**:
    - Transparency, audit readiness, centralized evidence
- **Value Proposition**:
    
    > "Centralize policies, controls, and evidence in one platform."
    > 

---

### D. **GRC / Compliance Officer**

- **Pain Points**:
    - Manual processes, audit fatigue
    - Siloed automation and spreadsheet chaos
    - Lack of real-time compliance tracking
- **Motivators**:
    - Confidence in audit readiness, simplification of daily work
- **Value Proposition**:
    
    > "Automate the boring stuff — stay always audit-ready."
    > 

---

### E. **IT Director / Infrastructure Lead**

- **Pain Points**:
    - Configuration drift, compliance gaps in environments
    - Difficult-to-maintain documentation trails
- **Motivators**:
    - Streamlined implementation, low-overhead tools
- **Value Proposition**:
    
    > “Lock down your systems — without locking up your team.”
    > 

---

### F. **Operations / Program Manager**

- **Pain Points**:
    - Unclear cross-functional responsibilities
    - Difficulty tracking audit progress
- **Motivators**:
    - Clarity, visibility, cross-team coordination
- **Value Proposition**:
    
    > "Stay on top of compliance deadlines — without drowning in spreadsheets."
    > 

---

Please be informed by our initial V1 of the User Journey map:

1. **Initial Engagement**

**a.  Email Receipt**

Receives email: "Strategic CMMC Positioning for the October 1st Requirement"

**b.  CTA Engagement**

Clicks "GET YOUR STRATEGIC ADVANTAGE" in the email

**c. Assessment Entry**

Clicks "START YOUR FREE ASSESSMENT NOW"

1. **Account Setup & Authentication**

**d. Company Setup**

Clicks "Setup Company" to initialize account

**e. Account Sign-In**

Signs in to their account with credentials

f. **Company Information**

Fills out Company Information form and clicks "Save and Continue"

1. **CMMC Control Assessment**

g. **Access Control**

Completes Access Control Policy & Account Management section

**h. Authentication**

Completes Identification & Authentication section

1. **Media Protection**

Completes Media Protection section assessment

j. **Physical Access**

Completes Physical Access Authorization section

k. **System Protection**

Completes System & Communications Protection section

l. **System Integrity**

Completes System & Information Integrity section

1. **Assessment Completion**

m. **Policy Framework**

Completes Policy Framework Assessment and clicks "Continue"

n,  **Report Generation**

Navigates to final step and generates the assessment report

Output

[1. Funnel Entry: Cold Email / LinkedIn (1)](https://www.notion.so/1-Funnel-Entry-Cold-Email-LinkedIn-1-248a80472bf68080bc51dfb4202727f9?pvs=21)

[2. CTA Click + Assessment Start (1)](https://www.notion.so/2-CTA-Click-Assessment-Start-1-248a80472bf680989a2ef489bbb2c01d?pvs=21)

[3. Account & Company Setup (1)](https://www.notion.so/3-Account-Company-Setup-1-248a80472bf68034a15ec2282684f5e4?pvs=21)

[4. CMMC Control Assessment (1)](https://www.notion.so/4-CMMC-Control-Assessment-1-248a80472bf68069a89cee6e5be47769?pvs=21)

[5. Report Generation (1)](https://www.notion.so/5-Report-Generation-1-248a80472bf6809c9a53f16b026ff5d1?pvs=21)

[6. Post-Assessment Follow-Up (1)](https://www.notion.so/6-Post-Assessment-Follow-Up-1-248a80472bf68088a076d2c1cf26f6f6?pvs=21)

[7. Conversion Pathway (1)](https://www.notion.so/7-Conversion-Pathway-1-248a80472bf6808381a1fd3d8f62cabe?pvs=21)