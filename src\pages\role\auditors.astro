---
import Layout from "../../layouts/Layout.astro";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Users, FileCheck, Clock, Shield, CheckCircle, ArrowRight, Target } from "lucide-react";


const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Role',"aria-disabled": true ,
  },
  {
    index: "2", text: "Auditors","aria-disabled": true 
  },
];
---

<Layout title="For Auditors - Build a Trust Layer into Your Stack"
  description="Stop losing enterprise deals to compliance requirements. Get SOC2, HIPAA, and ISO certifications that integrate seamlessly with your development workflow."
  keywords="For Auditors, developer-friendly compliance, SOC2 for Auditors, enterprise sales, compliance automation, tech leadership"
  url="/role/auditors/"
  breadcrumbLinks={breadcrumbLinks}>
  {/* Hero Section */}
      <section class="relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5 pt-16 pb-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center">
            <Badge variant="outline" className="mb-4">
              For Auditors & Partners
            </Badge>
            <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
              Partner with the Leading Compliance Platform
            </h1>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Join our partner network and offer your clients a comprehensive compliance solution. 
              Get white-labeled tools, co-branded resources, and revenue sharing opportunities.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
                <a href="/contact">                  
                  <Button size="lg" variant="default">Join Partner Program
                      <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </a>
                <a href="/resources">
                  <Button variant="outline" size="lg">Partnership Materials
                  </Button>
                </a>
            </div>
          </div>
        </div>
      </section>

      {/* Key Benefits */}
      <section class="py-16 sm:py-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl lg:text-center">
            <h2 class="text-base font-semibold leading-7 text-primary">Auditor Benefits</h2>
            <p class="mt-2 text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Enhanced Client Value & Efficiency
            </p>
          </div>
          <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <dl class="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
              <div class="flex flex-col">
                <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                  <Users className="h-5 w-5 flex-none text-primary" />
                  Co-Branded Solutions
                </dt>
                <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p class="flex-auto">
                    Offer Opsfolio's platform under your brand. Provide clients with comprehensive 
                    compliance tools while maintaining your firm's relationship and expertise.
                  </p>
                </dd>
              </div>
              <div class="flex flex-col">
                <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                  <FileCheck className="h-5 w-5 flex-none text-primary" />
                  Streamlined Evidence Review
                </dt>
                <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p class="flex-auto">
                    Access organized, pre-validated evidence packages that reduce audit time by 50%. 
                    Focus on high-value testing instead of evidence collection.
                  </p>
                </dd>
              </div>
              <div class="flex flex-col">
                <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                  <Clock className="h-5 w-5 flex-none text-primary" />
                  Continuous Monitoring
                </dt>
                <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p class="flex-auto">
                    Support clients between audits with continuous control monitoring and remediation 
                    tracking. Increase client retention and recurring revenue.
                  </p>
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </section>

      {/* Partnership Programs */}
      <section class="py-16 sm:py-20 bg-muted">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="mx-auto max-w-2xl text-center flex flex-col items-center justify-center">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Partnership Programs
            </h2>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Choose the partnership model that best fits your practice and client needs.
            </p>
          </div>
          <div class="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-6 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-3 lg:gap-8">
            {[
              {
                icon: Shield,
                title: "Audit Firm Partners",
                description: "Big 4 and regional audit firms offering comprehensive compliance solutions to audit clients."
              },
              {
                icon: Target,
                title: "Boutique Consultants",
                description: "Independent compliance consultants and boutique firms specializing in specific frameworks."
              },
              {
                icon: CheckCircle,
                title: "Technology Resellers",
                description: "MSPs, VARs, and technology consultants adding compliance services to their portfolio."
              }
            ].map((item, index) => (
              <div class="flex flex-col items-start">
                <div class="rounded-md bg-primary/10 p-2 ring-1 ring-primary/20">
                  <item.icon className="h-6 w-6 text-primary" />
                </div>
                <dt class="mt-4 font-semibold text-foreground">{item.title}</dt>
                <dd class="mt-2 leading-7 text-muted-foreground">{item.description}</dd>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Partner Benefits */}
      <section class="py-16 sm:py-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="flex flex-col items-center justify-center text-center">
            <h2 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Why Partner with Opsfolio?
            </h2>
          </div>
          <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <dl class="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-2">
              <div class="flex flex-col">
                <dt class="text-base font-semibold leading-7 text-foreground">
                  Revenue Sharing
                </dt>
                <dd class="mt-1 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p class="flex-auto">
                    Earn recurring revenue from client subscriptions. Transparent commission structure 
                    with monthly payouts and detailed reporting.
                  </p>
                </dd>
              </div>
              <div class="flex flex-col">
                <dt class="text-base font-semibold leading-7 text-foreground">
                  Training & Certification
                </dt>
                <dd class="mt-1 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p class="flex-auto">
                    Get your team certified on the Opsfolio platform. Regular training sessions, 
                    documentation, and ongoing support.
                  </p>
                </dd>
              </div>
              <div class="flex flex-col">
                <dt class="text-base font-semibold leading-7 text-foreground">
                  Sales & Marketing Support
                </dt>
                <dd class="mt-1 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p class="flex-auto">
                    Access co-branded marketing materials, case studies, and joint sales presentations 
                    to help close more deals.
                  </p>
                </dd>
              </div>
              <div class="flex flex-col">
                <dt class="text-base font-semibold leading-7 text-foreground">
                  Technical Integration
                </dt>
                <dd class="mt-1 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p class="flex-auto">
                    White-labeled platform access, API integration capabilities, and custom branding 
                    options for enterprise partners.
                  </p>
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section class="py-16 sm:py-20 bg-muted">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Ready to Join Our Partner Network?
            </h2>
            <p class="mx-auto mt-6 max-w-xl text-lg leading-8 text-muted-foreground">
              Apply to become an Opsfolio partner and start offering best-in-class compliance solutions to your clients.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
              
                <a href="/contact">
                  <Button size="lg" variant="default">Apply for Partnership
                  </Button>
                </a>
                <a href="/resources">
                  <Button variant="outline" size="lg">Download Partner Kit
                 </Button>
                </a>
            </div>
          </div>
        </div>
      </section>
</Layout>
