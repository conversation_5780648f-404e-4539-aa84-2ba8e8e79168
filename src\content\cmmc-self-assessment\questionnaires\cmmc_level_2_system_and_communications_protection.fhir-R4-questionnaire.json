{"resourceType": "Questionnaire", "meta": {"profile": ["http://hl7.org/fhir/4.0/StructureDefinition/Questionnaire"]}, "title": "CMMC Level 2 - System and Communications Protection (SC)", "status": "draft", "item": [{"item": [{"item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.1_A_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.1_A_Q", "text": "Is the external system boundary formally defined and documented?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "SC.L2-3.13.1_A_GRP", "prefix": "(a)", "text": "External System Boundary Defined"}, {"type": "group", "prefix": "(b)", "linkId": "SC.L2-3.13.1_B_GRP", "text": "Key Internal System Boundaries Defined", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.1_B_NOTES", "text": "Notes / Evidence", "repeats": false}, {"linkId": "SC.L2-3.13.1_B_Q_helpText", "type": "display", "text": "DMZs, segmented networks", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.1_B_Q", "text": "Are key internal system boundaries defined and documented?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}, {"type": "group", "prefix": "(c)", "linkId": "SC.L2-3.13.1_C_GRP", "text": "Monitor External Boundary", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.1_C_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.1_C_Q", "text": "Are communications monitored at the external system boundary?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}, {"type": "group", "prefix": "(d)", "linkId": "SC.L2-3.13.1_D_GRP", "text": "Monitor Internal Boundaries", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.1_D_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.1_D_Q", "text": "Are communications monitored at key internal boundaries?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}, {"type": "group", "prefix": "(e)", "linkId": "SC.L2-3.13.1_E_GRP", "text": "Control External Boundary", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.1_E_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.1_E_Q", "text": "Are communications controlled at the external system boundary?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}, {"type": "group", "prefix": "(f)", "linkId": "SC.L2-3.13.1_F_GRP", "text": "Control Internal Boundaries", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.1_F_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.1_F_Q", "text": "Are communications controlled at key internal boundaries?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}, {"type": "group", "prefix": "(g)", "linkId": "SC.L2-3.13.1_G_GRP", "text": "Protect External Boundary", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.1_G_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.1_G_Q", "text": "Are communications protected at the external system boundary?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}, {"type": "group", "prefix": "(h)", "linkId": "SC.L2-3.13.1_H_GRP", "text": "Protect Internal Boundaries", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.1_H_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.1_H_Q", "text": "Are communications protected at key internal boundaries?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.1_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "SC.L2-3.13.1_help", "type": "display", "text": "Monitor, control, and protect communications (i.e., information transmitted or received by organizational systems) at the external boundaries and key internal boundaries of organizational systems.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}], "type": "group", "linkId": "SC.L2-3.13.1", "text": "SC.L2-3.13.1 – Boundary Protection"}, {"type": "group", "linkId": "SC.L2-3.13.2", "text": "SC.L2-3.13.2 – Security Engineering", "item": [{"item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.2_A_NOTES", "text": "Notes / Evidence", "repeats": false}, {"linkId": "SC.L2-3.13.2_A_Q_helpText", "type": "display", "text": "defense-in-depth", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.2_A_Q", "text": "Does your organization identify security-promoting architectural designs?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "SC.L2-3.13.2_A_GRP", "prefix": "(a)", "text": "Identification of Architectural Designs"}, {"type": "group", "prefix": "(b)", "linkId": "SC.L2-3.13.2_B_GRP", "text": "Identification of Software Development Techniques", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.2_B_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.2_B_Q", "text": "Does your organization identify secure software development techniques?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}, {"type": "group", "prefix": "(c)", "linkId": "SC.L2-3.13.2_C_GRP", "text": "Identification of Systems Engineering Principles", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.2_C_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.2_C_Q", "text": "Does your organization identify systems security engineering principles?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}, {"type": "group", "prefix": "(d)", "linkId": "SC.L2-3.13.2_D_GRP", "text": "Employment of Architectural Designs", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.2_D_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.2_D_Q", "text": "Are the identified secure architectural designs employed within your systems?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}, {"type": "group", "prefix": "(e)", "linkId": "SC.L2-3.13.2_E_GRP", "text": "Employment of Software Development Techniques", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.2_E_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.2_E_Q", "text": "Are the identified secure software development techniques employed?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}, {"type": "group", "prefix": "(f)", "linkId": "SC.L2-3.13.2_F_GRP", "text": "Employment of Systems Engineering Principles", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.2_F_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.2_F_Q", "text": "Are the identified systems security engineering principles employed?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.2_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "SC.L2-3.13.2_help", "type": "display", "text": "Employ architectural designs, software development techniques, and systems engineering principles that promote effective information security within organizational systems.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}]}, {"type": "group", "linkId": "SC.L2-3.13.3", "text": "SC.L2-3.13.3 – Role Separation", "item": [{"item": [{"type": "group", "prefix": "(a)", "linkId": "SC.L2-3.13.3_A_GRP", "text": "User Functionality Identification", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.3_A_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.3_A_Q", "text": "Is standard user functionality formally identified?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}, {"type": "group", "prefix": "(b)", "linkId": "SC.L2-3.13.3_B_GRP", "text": "System Management Functionality Identification", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.3_B_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.3_B_Q", "text": "Is system management functionality formally identified?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}, {"type": "group", "prefix": "(c)", "linkId": "SC.L2-3.13.3_C_GRP", "text": "Functionality Separation", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.3_C_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.3_C_Q", "text": "Is user functionality physically or logically separated from system management functionality?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.3_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "SC.L2-3.13.3_help", "type": "display", "text": "Separate user functionality from system management functionality.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}]}, {"item": [{"item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.4_A_NOTES", "text": "Notes / Evidence (e.g., functionality is provided by the OS).", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.4_A_Q", "text": "Do your systems prevent information transfer via shared resources when they are reallocated (object reuse)?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "SC.L2-3.13.4_A_GRP", "prefix": "(a)", "text": "Prevention of Information Transfer (Object Reuse)"}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.4_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "SC.L2-3.13.4_help", "type": "display", "text": "Prevent unauthorized and unintended information transfer via shared system resources.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}], "type": "group", "linkId": "SC.L2-3.13.4", "text": "SC.L2-3.13.4 – Shared Resource Control"}, {"type": "group", "linkId": "SC.L2-3.13.5", "text": "SC.L2-3.13.5 – Public-Access System Separation", "item": [{"item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.5_A_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.5_A_Q", "text": "Are all publicly accessible system components identified?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}, {"valueCoding": {"display": "N/A - No publicly accessible components"}}]}], "type": "group", "linkId": "SC.L2-3.13.5_A_GRP", "prefix": "(a)", "text": "Identification of Publicly Accessible Components"}, {"type": "group", "prefix": "(b)", "linkId": "SC.L2-3.13.5_B_GRP", "text": "Subnetwork Separation", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.5_B_NOTES", "text": "Notes / Evidence", "repeats": false}, {"linkId": "SC.L2-3.13.5_B_Q_helpText", "type": "display", "text": "a DMZ", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.5_B_Q", "text": "Are these components on a physically or logically separate subnetwork from the internal network?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.5_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "SC.L2-3.13.5_help", "type": "display", "text": "Implement subnetworks for publicly accessible system components that are physically or logically separated from internal networks.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}]}, {"type": "group", "linkId": "SC.L2-3.13.6", "text": "SC.L2-3.13.6 – Network Communication by Exception", "item": [{"item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.6_A_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.6_A_Q", "text": "Are boundary devices configured with a default-deny policy for all traffic?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "SC.L2-3.13.6_A_GRP", "prefix": "(a)", "text": "Default-Deny Policy"}, {"type": "group", "prefix": "(b)", "linkId": "SC.L2-3.13.6_B_GRP", "text": "Permit-by-Exception Policy", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.6_B_NOTES", "text": "Notes / Evidence", "repeats": false}, {"linkId": "SC.L2-3.13.6_B_Q_helpText", "type": "display", "text": "whitelisting", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.6_B_Q", "text": "Is network traffic only allowed through an explicit permit-by-exception basis?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.6_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "SC.L2-3.13.6_help", "type": "display", "text": "Deny network communications traffic by default and allow network communications traffic by exception (i.e., deny all, permit by exception).", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}]}, {"type": "group", "linkId": "SC.L2-3.13.7", "text": "SC.L2-3.13.7 – Split Tunneling", "item": [{"item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.7_A_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.7_A_Q", "text": "Are remote access solutions configured to prevent split tunneling?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "SC.L2-3.13.7_A_GRP", "prefix": "(a)", "text": "Prevention of Split Tunneling"}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.7_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "SC.L2-3.13.7_help", "type": "display", "text": "Prevent remote devices from simultaneously establishing non-remote connections with organizational systems and communicating via some other connection to resources in external networks (i.e., split tunneling).", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}]}, {"type": "group", "linkId": "SC.L2-3.13.8", "text": "SC.L2-3.13.8 – Data in Transit", "item": [{"item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.8_A_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.8_A_Q", "text": "Are cryptographic mechanisms for protecting CUI in transit identified?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "SC.L2-3.13.8_A_GRP", "prefix": "(a)", "text": "Identification of Cryptographic Mechanisms"}, {"type": "group", "prefix": "(b)", "linkId": "SC.L2-3.13.8_B_GRP", "text": "Identification of Physical Safeguards", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.8_B_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.8_B_Q", "text": "Are alternative physical safeguards identified for when encryption is not used?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}, {"type": "group", "prefix": "(c)", "linkId": "SC.L2-3.13.8_C_GRP", "text": "Implementation of Safeguards", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.8_C_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.8_C_Q", "text": "Is one of these methods (encryption or physical safeguards) implemented to protect CUI during transmission?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.8_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "SC.L2-3.13.8_help", "type": "display", "text": "Implement cryptographic mechanisms to prevent unauthorized disclosure of CUI during transmission unless otherwise protected by alternative physical safeguards.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}]}, {"type": "group", "linkId": "SC.L2-3.13.9", "text": "SC.L2-3.13.9 – Connections Termination", "item": [{"item": [{"type": "group", "prefix": "(a)", "linkId": "SC.L2-3.13.9_A_GRP", "text": "Definition of Inactivity Period", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.9_A_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.9_A_Q", "text": "Is there a defined period of inactivity for terminating network connections?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}, {"type": "group", "prefix": "(b)", "linkId": "SC.L2-3.13.9_B_GRP", "text": "Termination at End of Session", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.9_B_NOTES", "text": "Notes / Evidence", "repeats": false}, {"linkId": "SC.L2-3.13.9_B_Q_helpText", "type": "display", "text": "user logs out", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.9_B_Q", "text": "Are network connections terminated at the end of sessions?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}, {"type": "group", "prefix": "(c)", "linkId": "SC.L2-3.13.9_C_GRP", "text": "Termination After Inactivity", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.9_C_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.9_C_Q", "text": "Are network connections terminated after the defined period of inactivity?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.9_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "SC.L2-3.13.9_help", "type": "display", "text": "Terminate network connections associated with communications sessions at the end of the sessions or after a defined period of inactivity.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}]}, {"type": "group", "linkId": "SC.L2-3.13.10", "text": "SC.L2-3.13.10 – Key Management", "item": [{"item": [{"type": "group", "prefix": "(a)", "linkId": "SC.L2-3.13.10_A_GRP", "text": "Key Establishment", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.10_A_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.10_A_Q", "text": "Is there a documented process for establishing cryptographic keys?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}, {"type": "group", "prefix": "(b)", "linkId": "SC.L2-3.13.10_B_GRP", "text": "Key Management", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.10_B_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.10_B_Q", "text": "Is there a documented process for managing cryptographic keys throughout their lifecycle?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.10_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "SC.L2-3.13.10_help", "type": "display", "text": "Establish and manage cryptographic keys for cryptography employed in organizational systems.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}]}, {"type": "group", "linkId": "SC.L2-3.13.11", "text": "SC.L2-3.13.11 – CUI Encryption", "item": [{"item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.11_A_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.11_A_Q", "text": "Is FIPS-validated cryptography used for all cryptographic mechanisms that protect the confidentiality of CUI?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "SC.L2-3.13.11_A_GRP", "prefix": "(a)", "text": "Use of FIPS-Validated Cryptography"}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.11_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "SC.L2-3.13.11_help", "type": "display", "text": "Employ FIPS-validated cryptography when used to protect the confidentiality of CUI.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}]}, {"type": "group", "linkId": "SC.L2-3.13.12", "text": "SC.L2-3.13.12 – Collaborative Device Control", "item": [{"item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.12_A_NOTES", "text": "Notes / Evidence", "repeats": false}, {"linkId": "SC.L2-3.13.12_A_Q_helpText", "type": "display", "text": "webcams, networked microphones", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.12_A_Q", "text": "Are collaborative computing devices identified?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}, {"valueCoding": {"display": "N/A - No such devices are in use"}}]}], "type": "group", "linkId": "SC.L2-3.13.12_A_GRP", "prefix": "(a)", "text": "Identification of Collaborative Devices"}, {"type": "group", "prefix": "(b)", "linkId": "SC.L2-3.13.12_B_GRP", "text": "Indication of Use", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.12_B_NOTES", "text": "Notes / Evidence", "repeats": false}, {"linkId": "SC.L2-3.13.12_B_Q_helpText", "type": "display", "text": "a light", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.12_B_Q", "text": "Do these devices provide a clear physical or logical indication to users when they are active ?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}, {"type": "group", "prefix": "(c)", "linkId": "SC.L2-3.13.12_C_GRP", "text": "Prohibition of Remote Activation", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.12_C_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.12_C_Q", "text": "Is remote activation of these devices technically prohibited?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.12_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "SC.L2-3.13.12_help", "type": "display", "text": "Prohibit remote activation of collaborative computing devices and provide indication of devices in use to users present at the device.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}]}, {"type": "group", "linkId": "SC.L2-3.13.13", "text": "SC.L2-3.13.13 – Mobile Code", "item": [{"item": [{"type": "group", "prefix": "(a)", "linkId": "SC.L2-3.13.13_A_GRP", "text": "Control of Mobile Code", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.13_A_NOTES", "text": "Notes / Evidence", "repeats": false}, {"linkId": "SC.L2-3.13.13_A_Q_helpText", "type": "display", "text": "Java, JavaScript, ActiveX", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.13_A_Q", "text": "Is the use of mobile code controlled via policy and technical mechanisms?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}, {"type": "group", "prefix": "(b)", "linkId": "SC.L2-3.13.13_B_GRP", "text": "Monitoring of Mobile Code", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.13_B_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.13_B_Q", "text": "Is the use of mobile code monitored?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.13_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "SC.L2-3.13.13_help", "type": "display", "text": "Control and monitor the use of mobile code.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}]}, {"type": "group", "linkId": "SC.L2-3.13.14", "text": "SC.L2-3.13.14 – Voice over Internet Protocol", "item": [{"item": [{"type": "group", "prefix": "(a)", "linkId": "SC.L2-3.13.14_A_GRP", "text": "Control of VoIP", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.14_A_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.14_A_Q", "text": "Is the use of VoIP technologies controlled via policy and security configurations?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}, {"valueCoding": {"display": "N/A - VoIP not used"}}]}]}, {"type": "group", "prefix": "(b)", "linkId": "SC.L2-3.13.14_B_GRP", "text": "Monitoring of VoIP", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.14_B_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.14_B_Q", "text": "Is the use of VoIP technologies monitored for unauthorized activity?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.14_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "SC.L2-3.13.14_help", "type": "display", "text": "Control and monitor the use of Voice over Internet Protocol (VoIP) technologies.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}]}, {"type": "group", "linkId": "SC.L2-3.13.15", "text": "SC.L2-3.13.15 – Communications Authenticity", "item": [{"item": [{"type": "group", "prefix": "(a)", "linkId": "SC.L2-3.13.15_A_GRP", "text": "Session Authenticity", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.15_A_NOTES", "text": "Notes / Evidence", "repeats": false}, {"linkId": "SC.L2-3.13.15_A_Q_helpText", "type": "display", "text": "using TLS, IPsec", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.15_A_Q", "text": "Are mechanisms implemented to protect the authenticity of communication sessions ?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.15_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "SC.L2-3.13.15_help", "type": "display", "text": "Protect the authenticity of communications sessions.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}]}, {"type": "group", "linkId": "SC.L2-3.13.16", "text": "SC.L2-3.13.16 – Data at Rest", "item": [{"item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "SC.L2-3.13.16_A_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "check-box", "display": "Check-box"}]}}], "linkId": "SC.L2-3.13.16_A_Q", "text": "Which mechanisms are used to protect the confidentiality of CUI at rest?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "FIPS-validated cryptographic mechanisms (Encryption)"}}, {"valueCoding": {"display": "Physical access controls"}}, {"valueCoding": {"display": "Logical access controls (file/share permissions)"}}]}], "type": "group", "linkId": "SC.L2-3.13.16_A_GRP", "prefix": "(a)", "text": "Protection of CUI at Rest"}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "SC.L2-3.13.16_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "SC.L2-3.13.16_help", "type": "display", "text": "Protect the confidentiality of CUI at rest.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}]}]}