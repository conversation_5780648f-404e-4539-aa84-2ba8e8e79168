---
import Layout from "../../../../layouts/Layout.astro";
import ControlDetail from "@/components/ControlDetail.tsx";
import { getControlByIdentifierIso } from "../../../../services/auditServices";
import type { ControlsType } from "../../../../services/auditTypes";
import { ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";

const auditTypeID = 22;
const regimeId = 20;
let tenantId = '239518031485599747';

const { slug } = Astro.params;

let currentControl: ControlsType | null = null;

try {
  currentControl = await getControlByIdentifierIso(    
    tenantId,
    slug,
  );
} catch (err) {
  console.error("Failed to fetch control:", err);
}

const breadcrumbLinks = [
  { index: "0", text: "Home" },
  { index: "1", text: "Regime", "aria-disabled": true },
  { index: "2", text: "ISO 27001", "aria-disabled": true },
];
---

<Layout title="ISO 27001 Compliance Made Simple - Healthcare Data Protection"
  description="Achieve ISO 27001 compliance in 90 days with expert guidance, automated controls, and continuous monitoring. Protect patient data with comprehensive healthcare compliance solutions."
  keywords="ISO 27001 compliance, healthcare data protection, PHI security, healthcare compliance, ISO 27001 training, healthcare cybersecurity"
  keywords="ISO 27001 compliance, healthcare data protection, PHI security, healthcare compliance, HIPAA training, healthcare cybersecurity"
  url="/regime/iso-27001/"
  breadcrumbLinks={breadcrumbLinks}>
  <div class="min-h-screen bg-background">
    {
      currentControl ? (

    <section
    class="relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5 pt-16 pb-20"
  >
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
      <div class="mx-auto max-w-4xl text-center">
        <h1
          class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl"
        >
          ISO 27001 Control {currentControl.control_identifier}
        </h1>
        <p
          class="mt-6 text-lg leading-8 text-muted-foreground max-w-3xl mx-auto"
        >
        {currentControl.question}        
        </p>
      </div>

       <div class="mt-10 flex items-center justify-center gap-x-6">
            <a href="/regime/iso-27001/controls">
              <Button size="lg" variant="default">
                View All Controls
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </a>
          </div>
    </div>
    </section>
    <section class="py-20">
      <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <ControlDetail client:only="react" controlData={currentControl} name="ISO-27001" />
      </div>
    </section>
      ) : (
        <p class="text-center py-20 text-muted-foreground">
          No control found for this slug.
        </p>
      )
    }
  </div>
</Layout>
