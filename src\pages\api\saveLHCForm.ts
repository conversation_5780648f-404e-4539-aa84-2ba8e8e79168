import { readdir, unlink, writeFile, access, mkdir } from 'fs/promises';
import { constants } from 'fs';
import type { APIContext } from 'astro';
import { <PERSON>uffer } from 'node:buffer';
import axios from 'axios';
import path from 'path';
import type { AxiosError } from 'axios';

const GITHUB_TOKEN = import.meta.env.PUBLIC_GITHUB_PAT
const GITHUB_OWNER = import.meta.env.PUBLIC_GITHUB_OWNER
const GITHUB_REPO = import.meta.env.PUBLIC_GITHUB_REPO
const GITHUB_BRANCH = 'main';

export async function POST({ request }: APIContext) {
    try {
        const { formData, userID, fileName, type, sessionID, tenantID } = await request.json();
        if (!formData || !userID || !fileName || !type || !sessionID || !tenantID) {
            return new Response(JSON.stringify({ error: "Missing required fields" }), {
                status: 400,
                headers: { "Content-Type": "application/json" },
            });
        }
        const sanitizedFileName = fileName.replace(/\.lform-submittion\.json$/, ''); // Remove leading userId if exists   
        const safeSessionID = path.basename(sessionID);

        // Build directory paths safely
        const sessionDir = path.join('./src/data/lforms/', tenantID, userID, safeSessionID);
        const responseDir = path.join(sessionDir, 'responses');
        const filePath = path.join(responseDir, `${sanitizedFileName}.${type}.lform-submission.json`);
        const directory = `./src/data/lforms/${tenantID}/${userID}/${sessionID}/responses`;

        // Step 1: Read files in the directory
        let matchingFiles: string[] = [];
        try {
            await access(directory, constants.F_OK);
            const files = await readdir(directory);
            matchingFiles = files.filter(file => file.startsWith(sanitizedFileName));

            // Step 2: Delete each matching file
            await Promise.all(
                matchingFiles.map(file => unlink(path.join(directory, file)))
            );
        } catch {
            // Directory doesn't exist — skip deletion
        }

        // Step 2: Ensure directory exists before writing
        await mkdir(directory, { recursive: true });

        // Step 3: Write form data to file
        await writeFile(filePath, JSON.stringify(formData, null, 2), 'utf-8');

        if (GITHUB_TOKEN && GITHUB_OWNER && GITHUB_REPO) {
            const githubPath = `src/data/lforms/${tenantID}/${userID}/${sessionID}/responses/${sanitizedFileName}.${type}.lform-submittion.json`;
            const encodedContent = Buffer.from(JSON.stringify(formData, null, 2)).toString('base64');
            const apiUrl = `https://api.github.com/repos/${GITHUB_OWNER}/${GITHUB_REPO}/contents/${githubPath}`;
            let sha: string | undefined = undefined;
            try {
                const { data } = await axios.get(apiUrl, {
                    headers: {
                        Authorization: `Bearer ${GITHUB_TOKEN}`,
                        Accept: 'application/vnd.github+json',
                    },
                    params: { ref: GITHUB_BRANCH },
                });
                sha = data.sha;
            } catch (err: unknown) {
                const axiosErr = err as AxiosError;
                if (axiosErr.response?.status !== 404) throw err;
            }
            await axios.put(
                apiUrl,
                {
                    message: `chore: auto-commit form submission`,
                    content: encodedContent,
                    branch: GITHUB_BRANCH,
                    ...(sha && { sha }),
                },
                {
                    headers: {
                        Authorization: `Bearer ${GITHUB_TOKEN}`,
                        Accept: 'application/vnd.github+json',
                    },
                }
            );
        } else {
            console.log("Skipping GitHub sync: Missing token, owner, or repo.");
        }

        return new Response(JSON.stringify({ message: "Form data saved successfully!" }), {
            status: 200,
            headers: { "Content-Type": "application/json" },
        });

    } catch (error) {
        if (error instanceof Error) {
            console.error("Error during processing:", error.message);
        } else {
            console.error("Unknown error in processing:", error);
        }
        return new Response(JSON.stringify({ error: "Failed to process form data" }), {
            status: 500,
            headers: { "Content-Type": "application/json" },
        });
    }
}
