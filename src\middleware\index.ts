import type { MiddlewareHand<PERSON> } from "astro";
import { defineMiddleware, sequence } from "astro/middleware";
import { qualityfolioReverseProxyMiddleware } from "./qualityfolio-reverse-proxy.ts"
import axios from 'axios';

const privateRoutes = [
    "/regime/cmmc/self-assessment",
    "/regime/cra/self-assessment",
    "/regime/hipaa/self-assessment",
    "/gpm/",
    "/qualityfolio",
    "/cmmc-self-assessment-report",
    "/cmmc-report"
];

const ZITADEL_DOMAIN = import.meta.env.PUBLIC_ZITADEL_AUTHORITY as string;
const ZITADEL_TOKEN = import.meta.env.PUBLIC_ZITADEL_API_TOKEN as string;

const authenticationMiddleware: MiddlewareHandler = defineMiddleware(async (context, next) => {

    const sessionID = context.cookies.get("session_id")?.value;
    const userID = context.cookies.get("zitadel_user_id")?.value;
    const { pathname, searchParams } = context.url;
    // Allow public pages
    const isPrivate = privateRoutes.some((route) =>
        pathname === route || pathname.startsWith(`${route}/`)
    );

    const redirectToLogin = () => {
        const redirectPath = encodeURIComponent(pathname);
        context.cookies.set("redirect_after_login", redirectPath, {
            path: "/",
            httpOnly: false,
            maxAge: 60 * 5, // 5 minutes
        });

        let loginUrl = "/login";
        const journey = searchParams.get("journey");
        if (journey) {
            loginUrl += `?journey=${encodeURIComponent(journey)}`;
        }

        return context.redirect(loginUrl);
    };

    // Redirect to login if user is not authenticated and path is private

    // Case 1: Not authenticated and private path
    if ((!sessionID || !userID) && isPrivate) {
        return redirectToLogin();
    }

    // Case 2: Authenticated check with Zitadel
    if (sessionID && isPrivate) {
        try {
            const response = await axios.get(
                `${ZITADEL_DOMAIN}/v2/sessions/${sessionID}`,
                {
                    headers: {
                        Accept: "application/json",
                        Authorization: `Bearer ${ZITADEL_TOKEN}`,
                    },
                }
            );

            if (response.data?.session?.id) {
                return next();
            } else {
                return redirectToLogin();
            }
        } catch {
            return redirectToLogin();
        }
    }
    return next();
});
export const onRequest: MiddlewareHandler = defineMiddleware((context, next) => {
    return sequence(
        authenticationMiddleware,
        qualityfolioReverseProxyMiddleware
    )(context, next);
});