export interface Testimonial {
  quote: string;
  author: string;
  role: string;
  company: string;
}

export const mapCollectiveTestimonials: Testimonial[] = [
  {
    quote: "Map Collective is revolutionizing how businesses view supply chains by providing transparency and helping them decarbonize, making the planet a more sustainable place.",
    author: "<PERSON>",
    role: "Founder & CEO",
    company: "Map Collective"
  },
  {
    quote: "As we scale, ensuring security and privacy across our platform is crucial for our clients, and we needed a solution that would grow with us.",
    author: "<PERSON>",
    role: "Founder & CEO",
    company: "Map Collective"
  },
  {
    quote: "With Opsfolio CaaS, we were able to navigate the complexities of SOC 2 compliance, automate our audit preparations, and streamline data security across our platform.",
    author: "<PERSON>",
    role: "Founder & CEO",
    company: "Map Collective"
  },
  {
    quote: "Achieving SOC 2 certification has significantly improved our credibility in the market and helped us build stronger relationships with our clients, who trust our platform for their supply chain management needs.",
    author: "<PERSON>",
    role: "Founder & CEO",
    company: "Map Collective"
  },
  {
    quote: "We're excited about the future. With Opsfolio CaaS as our partner, we're not just achieving compliance; we're building a sustainable future for our clients and the planet.",
    author: "<PERSON>",
    role: "Founder & CEO",
    company: "Map Collective"
  }
];

// Individual testimonials for easy reuse
export const tara<PERSON><PERSON><PERSON>Testimonials = {
  vision: mapCollectiveTestimonials[0],
  scaling: mapCollectiveTestimonials[1], 
  solution: mapCollectiveTestimonials[2],
  results: mapCollectiveTestimonials[3],
  future: mapCollectiveTestimonials[4]
};