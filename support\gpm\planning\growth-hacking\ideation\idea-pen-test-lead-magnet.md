# Content Engineering Idea Proposal

Title: Free Security Assessment with Pen Test

## 1. Idea Title

Free Security Assessment with Pen Test

## 2. Revenue Connection

- [x] Acquire new customers
- [x] Convert leads → contracts
- [x] Renew contracts
- [x] Retain clients
- [x] Expand (upsell / cross-sell)

**Notes:**\
Primary = Acquire. Secondary = Convert + Expand.

## 3. Evidence Base

- References:
  - NN/g research on perceived value of “free” offers.
  - Competitors (SecureFrame, Drata) offer checklists, not actionable scans.
- Hypothesis:
  > Offering a free, scoped pen test will increase demo conversions by 25%
  > because prospects immediately see evidence of security gaps.

## 4. Funnel Mapping

- [x] Top of Funnel (ToFu): Lead capture through gated signup.
- [x] Middle of Funnel (MoFu): Email nurture with findings + CTA for
      consultation.
- [x] Bottom of Funnel (BoFu): Sales uses report to scope paid
      remediation/compliance services.
- [x] Retention/Expansion: Existing clients get periodic re-scans to demonstrate
      ongoing value.

## 5. Customer Journey Placement (CJM)

- Current CJM: Cold outreach → Landing page → Nurture → Demo.
- New Placement: This lead magnet **slots into the Landing Page step**.
- Statement: “This replaces checklist-only offers with a higher-value
  deliverable.”

## 6. Hypothesis & Test Strategy

- Hypothesis:
  > If we provide a free pen test, then 25% more leads will schedule demos vs.
  > checklist-based magnets.
- Test Methods:
  - A/B test “Free Pen Test” vs “Free Checklist” landing pages.
  - Track conversion rates → demo booked.
- Success Metrics:
  - Conversion rate (visitor → signup).
  - Demo booking rate (signup → demo).
  - Proposal win rate (demo → contract).

## 7. Competitive/Comparative Insight

- Competitors: SecureFrame (checklists), Drata (guides), Vanta (questionnaires).
- Differentiation: Opsfolio = **proof over paperwork** → tangible findings from
  a real scan.

## 8. Execution Requirements

**Content Needs:**

- Landing page copy + design.
- Nurture email sequence.
- Branded PDF template for scan results.

**Engineering Needs (ODC):**

- [ ] Decide scope of free pen test (external-only? lightweight scan?).
- [ ] Build automated scan workflow OR define manual engagement flow.
- [ ] Results pipeline → JSON → PDF generator (surveilr integration?).
- [ ] Secure data handling (customer-submitted domains/IPs).
- [ ] Add CRM integration for signup form (HubSpot/Salesforce).
- [ ] Create logging dashboard (track volume + outcomes).

## 9. Customer & Executive Talking Points

- “We don’t just tell you about compliance — we prove it.”
- “Our competitors offer checklists; we deliver real evidence.”
- “This differentiates Opsfolio’s brand promise of **certifiable trust, fast**.”

## 10. Example Workflow (User Experience)

1. Prospect sees outreach/ad.
2. Lands on “Free Security Assessment” page.
3. Submits info: name, email, target domain/IP.
4. Backend triggers scan (auto or manual).
5. PDF report emailed with CTA to “Book Consultation.”
6. Sales follows up to convert into proposal.

## 11. Open Engineering Questions (ODC)

- Scope: Do we limit scans to external surface only?
- Automation: Which open-source tools to containerize? (Nmap, OpenVAS, OWASP
  ZAP)
- Workflow: Automated vs manual scheduling?
- Reporting: JSON → PDF templating in surveilr?
- Security: How to prevent abuse (fake domains, competitor IPs)?
- Legal: Do we need explicit consent checkboxes on the signup form?
- Scaling: How do we throttle if 50+ requests come in at once?
- SLA: How quickly must results be delivered (24h, 48h)?

## 12. Next Steps

- [ ] Executive review for approval.
- [ ] ODC prototype scan workflow + reporting pipeline.
- [ ] Marketing draft landing page + nurture sequence.
- [ ] Sales prep talk track for consultation calls.
