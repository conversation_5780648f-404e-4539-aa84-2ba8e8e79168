import streamlit as st
import validators
from main import run_audit_streamlit, load_prompts, merge_prompts
from pydantic import ValidationError
from schemas import ComplianceAuditResult
from streamlit_ace import st_ace

st.set_page_config(page_title="Content Remediation Tool", layout="wide")

st.sidebar.title("Website Content Remediation Tool")
url = st.sidebar.text_input("Enter the webpage URL:")
# Page type input
page_type = st.sidebar.selectbox(
    "Select the Page Type:",
    ["", "Homepage", "Landing Page", "Blog Post", "Product Page", "Documentation", "About Us", "Contact Page", "Other"]
)

# If "Other" is selected, show text input
custom_page_type = ""
if page_type == "Other":
    custom_page_type = st.sidebar.text_input("Enter page type:")

# --- Main Area ---
st.title("Content Remediation Dashboard")

# Load defaults
default_prompts = load_prompts()

# UI for updates
ui_prompts = {}
with st.expander(f"View and Update Prompts", expanded=False):
    for prompt_name, default_text in default_prompts.items():
            ui_prompts[prompt_name] = st_ace(
            value=default_text,
            language="markdown",
            theme="github",
            height=200,
            auto_update=True
        )

# Merge
final_prompts = merge_prompts(default_prompts, ui_prompts)

run_audit_button = st.sidebar.button("Run Audit")
if run_audit_button:
    if url:
        if validators.url(url):
            selected_page_type = custom_page_type if page_type == "Other" else page_type

            if selected_page_type:
                st.success(f"Valid URL entered: {url}")
                st.info(f"Page Type: {selected_page_type}")

                with st.spinner("Running audit..."):
                    try:
                        result_bundle = run_audit_streamlit(url, selected_page_type, final_prompts)
                        st.success("Completed!")
                        if result_bundle["final_md"]:
                            with st.expander("Final Executive Summary", expanded=True):
                                st.markdown(result_bundle["final_md"], unsafe_allow_html=False)

                        # Display each prompt-wise result in separate expanders
                        for prompt_name, result in result_bundle["prompt_results"].items():
                            with st.expander(f"Audit Prompt-{prompt_name} Output", expanded=False):
                                if isinstance(result, ComplianceAuditResult):
                                    with st.expander("Executive Summary", expanded=False):
                                        st.markdown(result.executive_summary or "")
                                    with st.expander("Strengths", expanded=False):
                                        st.markdown(result.strengths or "")
                                    with st.expander("Weaknesses", expanded=False):
                                        st.markdown(result.weaknesses or "")
                                    with st.expander("Recommendations", expanded=False):
                                        st.markdown(result.recommendations or "")
                                    with st.expander("Remediated Content", expanded=False):
                                        st.markdown(result.remediated_content or "")
                                    with st.expander("Observation vs Hypothesis", expanded=False):
                                        st.markdown(result.observation_vs_hypothesis or "")
                                else:
                                    # If it's just a string result
                                    st.markdown(str(result))

                    except ValidationError:
                        st.error(
                            "The remediation could not complete successfully. "
                            "This usually happens if the page content is too large or the LLM hit a token limit. "
                            "Try a shorter page or smaller input."
                        )
                    except Exception as e:
                        st.error(f"An unexpected error occurred: {e}")

            else:
                st.warning("Please select or enter a page type from the sidebar.")
            
        else:
            st.error("Please enter a valid URL")
    else:
        st.warning("Please enter the URL of the Webpage to audit.")
