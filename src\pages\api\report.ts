import type { APIRoute } from "astro";
import Handlebars from "handlebars";
import fs from "fs/promises";
import path from "path";

const templatePath = path.resolve("src/templates/report.hbs");
let compiledTemplate: Handlebars.TemplateDelegate | null = null;

async function getTemplate() {
    if (!compiledTemplate) {
        const fileContent = await fs.readFile(templatePath, "utf-8");
        compiledTemplate = Handlebars.compile(fileContent);
    }
    return compiledTemplate;
}

export const POST: APIRoute = async ({ request }) => {
    const body = await request.json();
    const today = new Date().toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
    });

    const template = await getTemplate();
    const html = template({
        ...body,
        assessmentDate: today,
    });

    return new Response(html, {
        headers: {
            "Content-Type": "text/html; charset=utf-8",
            "Content-Disposition": "attachment; filename=cmmc-report.html",
        },
    });
};
