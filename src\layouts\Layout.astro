---
import "../styles/index.css";
import  '../styles/custom.css';
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import SEO from "@/components/SEO.astro";
import {Breadcrumbs} from "astro-breadcrumbs";
//import '../assets/styles/astro-breadcrumbs-config.scss';
import "astro-breadcrumbs/breadcrumbs.css";
import metaDataJson from "support/seo-meta.json"
import chatBotDataJson from "../config/chatBot.config.json"

const enableOpenObserve =
  import.meta.env.ENABLE_OPEN_OBSERVE !== undefined
    ? import.meta.env.ENABLE_OPEN_OBSERVE
    : false;
const {
  title: propTitle,
  description: propDescription,
  keywords: propKeywords,
  image="https://suite.opsfolio.com/assets/images/opsfolio-logo.svg",
  url: propUrl,
  type = "website",
  author,
  publishedTime,
  modifiedTime,
  schema,
  hideBreadcrumbs,
  breadcrumbLinks = []
} = Astro.props;


const privateRoutes = [
    "/regime/cmmc/self-assessment",
];
const currentPath = Astro.url.pathname;
const isPrivateRoute = privateRoutes.includes(currentPath);

const currentUrl = new URL(Astro.request.url);
const pathname = currentUrl.pathname;

const metaData: Record<string, {
  title: string;
  description: string;
  keywords: string;
  url: string;
}> = metaDataJson as Record<string, any>;


const meta = metaData[pathname] || {};

const title = propTitle || meta.title || "";
const description = propDescription || meta.description || "";
const keywords = propKeywords || meta.keywords || "";
const url = propUrl || meta.url || currentUrl.href;




const chatBotData: Record<string, {
  workspaceId: string;
  threadId: string;
  sampleQuestions: Array<string>;
  
}> = chatBotDataJson as Record<string, any>;


const chatBot = chatBotData[pathname] || {};

const workspaceId = chatBot.workspaceId || "";
const threadId = chatBot.threadId || "";
const sampleQuestions = chatBot.sampleQuestions || [];

---

<!doctype html>
<html lang="en">
  <head>
    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-NWW38GX7');</script>
    <!-- End Google Tag Manager -->
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width" />
    <link rel="icon" type="image/svg+xml" href="/favicon.png" />
  
    <meta name="generator" content={Astro.generator} />
     <link
      rel="stylesheet"
      href="https://lhcforms-static.nlm.nih.gov/lforms-versions/36.3.2/webcomponent/styles.css"
    />
    {!(pathname.startsWith('/qualityfolio')) && (
      <>
        <script
          type="module"
          crossorigin="anonymous"
          src="https://lhcforms-static.nlm.nih.gov/lforms-versions/34.0.0/webcomponent/assets/lib/zone.min.js"
        ></script>
        <script
          type="module"
          crossorigin="anonymous"
          src="https://lhcforms-static.nlm.nih.gov/lforms-versions/34.0.0/webcomponent/lhc-forms.js"
        ></script>
        <script
          type="module"
          crossorigin="anonymous"
          src="https://lhcforms-static.nlm.nih.gov/lforms-versions/34.0.0/fhir/R4/lformsFHIR.min.js"
        ></script>
      </>
    )}
    <SEO title={title} description={description} keywords={keywords} image={image} url={url} type={type} author={author} publishedTime={publishedTime} modifiedTime={modifiedTime} schema={schema}/>
    {!isPrivateRoute && (
      <script type="application/ld+json">
          {
            "@context": "https://schema.org",
            "@type": "Organization",
            "name": "Opsfolio",
            "url": "https://opsfolio.com",
            "logo": "https://opsfolio.com/assets/opsfolio.svg",
            "sameAs": [
              "https://www.linkedin.com/company/opsfolio",
              "https://twitter.com/opsfolio"
            ],
            "description": "Opsfolio offers Compliance as a Service with a focus on SOC2 certifications and healthcare SaaS regulatory standards.",
            "founder": {
              "@type": "Person",
              "name": "Opsfolio"
            },
            "address": {
              "@type": "PostalAddress",
              "streetAddress": "2313 Falling Creek Rd",
              "addressLocality": "Silver Spring",
              "addressRegion": "MD",
              "postalCode": "20904",
              "addressCountry": "US"
            },
            "contactPoint": [
              {
                "@type": "ContactPoint",
                "telephone": "+1-************",
                "contactType": "Customer Service"
              }
            ]
          }
      </script>
    )}
    {
        <>
          <script
            type="module"
            crossorigin="anonymous"
            src="/assets/scripts/index.756a5bbf.js"
          />
        </>  
    }
  </head>
  <body class={pathname.startsWith('/qualityfolio') ? 'tail-folio' : undefined}>
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NWW38GX7"
    height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->
    <div class="flex min-h-screen flex-col bg-background">
    <Header client:only="react"/>
    {!hideBreadcrumbs && (
      <article class="crumbs-wrapper">
        <div class="max-w-7xl mx-auto px-4 py-2 sm:px-4 lg:px-8 text-gray-500 crumbs-container">
          <Breadcrumbs
            linkTextFormat="capitalized"
            ariaLabel="Site navigation"
            ellipsisAriaLabel="Show more breadcrumbs"
            customizeLinks={breadcrumbLinks}
          >
            <svg
              slot="separator"
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
          </Breadcrumbs>
        </div>
      </article>
    )}
    <!-- Page Content -->
    <main class="flex-grow">
      <slot />
    </main>
    <Footer client:load workspaceId={workspaceId} threadId={threadId} sampleQuestions={sampleQuestions} enableOpenObserve={enableOpenObserve}/>
  </div>
  </body>
</html>
<style>
  html,
  body {
    margin: 0;
    width: 100%;
    height: 100%;
  }


  
</style>
<style is:global>
  body[data-scroll-locked] {
    padding-right: 0px!important;
    margin-right: 0px !important;
  }

  .lhc-form-body {
    border: 1px solid #fff !important;
    padding: 4px 0;
    margin: 0 0 2px;
    border-radius: 4px;
    box-sizing: border-box;
}
.lhc-item.lhc-tree-line:after {
    content: "";
    position: absolute;
    left: 12px;
    border-left: 0px solid #2f96b4 !important;
    height: 100%;
    width: 0;
    top: 0;
}
.lhc-item.lhc-tree-line:before {
    content: "";
    position: absolute;
    left: 12px;
    border-top: 0px solid #2f96b4 !important;
    top: 14px;
    width: 12px;
    height: 0;
}
.lhc-item.lhc-indentation {
    padding-left: 0px !important;
}

.lhc-de {
    margin-bottom: 5px !important;
    margin-top: 5px !important;
    border-width: 0px !important;
    }


</style>


