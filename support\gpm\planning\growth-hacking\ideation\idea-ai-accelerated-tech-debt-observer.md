# Free AI Tech Debt Assessment with Qualityfolio

> For general background see
> [Doctrine: Wrangle AI-Accelerated Tech Debt using Testing First Principles](https://github.com/strategy-coach/praxis/tree/main/ai-native-software-engineering/ai-accelerated-tech-debt)
> then read
> [How Qualityfolio and surveilr Work Together to Implement the Doctrine](https://github.com/strategy-coach/praxis/blob/main/ai-native-software-engineering/ai-accelerated-tech-debt/qualityfolio-as-tech-debt-manager.md).

## 1. Idea Title

**Free AI Tech Debt Assessment with Qualityfolio**

- A lead magnet that delivers immediate value by surfacing hidden AI-driven
  technical debt inside codebases.

## 2. Revenue Connection

- **Primary Goal:** Acquire new leads.
- **Strategic Role:** First step toward upselling **Opsfolio Compliance as a
  Service (CaaS)**.
- **Rationale:** By offering a baseline assessment, we expose urgent risks
  prospects can’t ignore—positioning Opsfolio CaaS as the trusted partner to
  contain and manage these risks long-term.

## 3. Evidence Base

- **Studies:**

  - <PERSON><PERSON><PERSON><PERSON><PERSON> has reported that “AI accelerates delivery velocity, but without
    governance, costs of remediation can exceed initial savings.”
  - <PERSON><PERSON><PERSON> has repeatedly cited **technical debt as a top enterprise risk**,
    especially in regulated industries.
  - Internal customer anecdotes: multiple clients already ask, “How do we
    measure what AI code is doing to our maintainability?”—showing clear demand.
- **Hypothesis Testing Strategy:**

  - Run **cold email campaigns** targeting engineering leaders with the hook “AI
    accelerates velocity but doubles your debt—want to see your real balance
    sheet?”
  - Use **LinkedIn campaigns** to drive gated signups for the free assessment.

## 4. Funnel Mapping

- **Top of Funnel (ToFu):**

  - Gated assessment behind an email form.
  - Optional demo using any **public GitHub repo** to showcase sample analysis.
- **Middle of Funnel (MoFu):**

  - Deliverable: Tech Debt Heatmap + Debt Velocity Score.
  - Sales can follow up with: “We’ve identified where AI debt is compounding
    fastest. Let’s talk about containing it.”
- **Bottom of Funnel (BoFu):**

  - Strong CTA: “Schedule a Fix Workshop” → paid engagement or Opsfolio CaaS
    trial.
- **Retention/Expansion:**

  - Enterprise rollout expands to continuous monitoring, integration into CI/CD,
    and compliance-driven dashboards.

## 5. Customer Journey Placement (CJM)

**Suggested placements:**

1. **Cold Outreach → Assessment → SDR Call → Demo → Pilot.**

   - Works well for compliance-driven buyers.
2. **Inbound SEO/LinkedIn Ads → Assessment → Automated nurture → CaaS consult.**

   - Effective for broader DevOps/SRE audience.
3. **Conference Booth → Instant Assessment Demo (public repo scan) → Sales
   follow-up.**

   - Captures high-intent leads on-site.

**Strong Recommendation:** Lean on **inbound + LinkedIn ads** as the highest
conversion path because the messaging resonates with the growing anxiety around
“AI-driven fragility.”

## 6. Hypothesis & Test Strategy

**Hypotheses:**

1. _If we offer a free AI Tech Debt Assessment, then 20% of downloads will
   convert into CaaS demos because debt surfaced = urgency created._
2. _If we gate the assessment behind an email, then conversion rates to SDR
   calls will outperform ungated by at least 30%, because the perceived value of
   “free scan” is high._
3. _If we show a sample assessment for any public repo, then sign-up intent will
   increase because prospects can see proof without risk._

**Test Plan:**

- A/B test **gated vs. ungated** landing page.
- Split nurture sequences: one focusing on **compliance/regulation**, the other
  on **engineering productivity**.
- Success metrics: # leads captured, # SDR calls booked, % converting to pilot.

## 7. Competitive/Comparative Insight

- **Comparables:** Drata’s SOC 2 readiness quizzes, SonarQube, Snyk, Code
  Climate.
- **Opsfolio Positioning:**

  - Those tools focus on static analysis or compliance readiness.
  - **Qualityfolio is the AI-era upgrade**—built explicitly to track AI
    provenance, TTL, and silent divergence.
  - This positions Opsfolio CaaS as the _only compliance partner with an
    in-house AI debt observability platform._

## 8. Execution Requirements

- **Content:** Landing page copy, report template design, nurture sequence
  emails.
- **Engineering (ODC):** Minimal for now—focus on building Qualityfolio’s debt
  tracking features in parallel, but MVP report can be semi-manual.
- **Marketing/Sales:**

  - Growth hacking team owns initial campaigns.
  - SDRs trained with talking points to push Opsfolio CaaS after assessment
    delivery.

## 9. Customer & Executive Talking Points

- “AI accelerates your coding velocity—but it doubles your debt. Qualityfolio
  keeps your balance sheet clean.”
- “Every line of AI code in a regulated system is a liability until you can
  prove it’s safe—Qualityfolio gives you that proof.”
- “Without debt observability, compliance costs balloon—Opsfolio CaaS contains
  the risk.”

## 10. Example Workflow (User Experience)

**Option A (self-serve demo):**

- Prospect enters GitHub URL (public repo).
- System runs scan → generates **sample debt report** instantly.
- Prospect gated to receive full PDF report via email.

**Option B (sales-assisted pilot):**

- Prospect signs up via landing page.
- SDR schedules a repo scan (manual ODC execution).
- Custom PDF/slide deck delivered in 48 hours with follow-up call.

**Recommendation:** Start with **Option B** (manual pilot) for credibility, move
to **Option A** later for scale.

## 11. Open Engineering Questions (ODC)

- How to scan **private repos** securely (OAuth, temporary access tokens)?
- Should scans run fully inside surveilr for provenance and audit trail?
- What scope is feasible for MVP (heatmap + Debt Velocity Score vs. full
  TTL/flake tracking)?
- How to prevent **false positives** that could erode trust?

## 12. Next Steps

1. **Marketing-first buildout:**

   - Create gated landing page with clear CTA.
   - Design report template (PDF + slide deck).
   - Write nurture sequence emails + SDR scripts.
2. **ODC parallel build:**

   - Implement basic debt metrics (AI provenance checks, untested module
     counts).
   - Pilot repo scans manually with a few design partners.
3. **Pilot Rollout:**

   - Target 5–10 early prospects via cold outreach and LinkedIn ads.
   - Collect feedback, refine report content.
4. **Scale to Automation:**

   - Integrate scanning pipeline into surveilr + Qualityfolio.
   - Add self-serve GitHub repo assessment on landing page.

## Why it matters

This idea positions **Qualityfolio as the lead magnet** and **Opsfolio CaaS as
the upsell**, directly tying marketing activity to revenue. By offering a free
AI Tech Debt Assessment, we capture attention, create urgency, and demonstrate
unique authority.

In the AI era, competitors measure _what’s written_; Qualityfolio measures
_what’s sustainable_.
