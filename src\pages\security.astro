---

import { <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Users, Server } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Layout from "../layouts/Layout.astro";

const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Security',"aria-disabled": true ,
  }
];
 
---
<Layout breadcrumbLinks={breadcrumbLinks}>

      <main class="pt-20">
        {/* Hero Section */}
        <section class="bg-gradient-to-br from-primary/5 to-secondary/5 py-16">
          <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="mx-auto max-w-2xl text-center">
              <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
                Security at Opsfolio
              </h1>
              <p class="mt-6 text-lg leading-8 text-muted-foreground">
                Your trust is our foundation. We maintain the highest security standards to protect 
                your sensitive compliance data and ensure complete confidentiality.
              </p>
            </div>
          </div>
        </section>

        {/* Certifications */}
        <section class="py-16">
          <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="mx-auto max-w-2xl text-center mb-12">
              <h2 class="text-3xl font-bold tracking-tight text-foreground">
                Our Security Certifications
              </h2>
              <p class="mt-4 text-lg text-muted-foreground">
                We practice what we preach with industry-leading certifications
              </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
              <Card className="text-center">
                <CardHeader>
                  <Shield className="h-12 w-12 text-primary mx-auto mb-4" />
                  <CardTitle>SOC 2 Type II</CardTitle>
                  <CardDescription>
                    Audited annually for security, availability, and confidentiality controls
                  </CardDescription>
                </CardHeader>
              </Card>
              
              <Card className="text-center">
                <CardHeader>
                  <Lock className="h-12 w-12 text-primary mx-auto mb-4" />
                  <CardTitle>ISO 27001</CardTitle>
                  <CardDescription>
                    International standard for information security management systems
                  </CardDescription>
                </CardHeader>
              </Card>
              
              <Card className="text-center">
                <CardHeader>
                  <FileCheck className="h-12 w-12 text-primary mx-auto mb-4" />
                  <CardTitle>HIPAA Compliant</CardTitle>
                  <CardDescription>
                    Protected health information handling for healthcare clients
                  </CardDescription>
                </CardHeader>
              </Card>
            </div>
          </div>
        </section>

        {/* Security Measures */}
        <section class="bg-muted py-16">
          <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="mx-auto max-w-2xl text-center mb-12">
              <h2 class="text-3xl font-bold tracking-tight text-foreground">
                Comprehensive Security Measures
              </h2>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <Card>
                <CardHeader>
                  <Lock className="h-8 w-8 text-primary mb-2" />
                  <CardTitle>Data Encryption</CardTitle>
                </CardHeader>
                <CardContent>
                  <p class="text-muted-foreground">
                    AES-256 encryption at rest and TLS 1.3 for data in transit. 
                    All customer data is encrypted using industry-standard protocols.
                  </p>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <Users className="h-8 w-8 text-primary mb-2" />
                  <CardTitle>Access Controls</CardTitle>
                </CardHeader>
                <CardContent>
                  <p class="text-muted-foreground">
                    Multi-factor authentication, role-based access control, and 
                    principle of least privilege for all system access.
                  </p>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <Server className="h-8 w-8 text-primary mb-2" />
                  <CardTitle>Infrastructure Security</CardTitle>
                </CardHeader>
                <CardContent>
                  <p class="text-muted-foreground">
                    Cloud infrastructure with automated security monitoring, 
                    intrusion detection, and 24/7 security operations center.
                  </p>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <Eye className="h-8 w-8 text-primary mb-2" />
                  <CardTitle>Continuous Monitoring</CardTitle>
                </CardHeader>
                <CardContent>
                  <p class="text-muted-foreground">
                    Real-time security monitoring, vulnerability scanning, 
                    and automated incident response capabilities.
                  </p>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <FileCheck className="h-8 w-8 text-primary mb-2" />
                  <CardTitle>Regular Audits</CardTitle>
                </CardHeader>
                <CardContent>
                  <p class="text-muted-foreground">
                    Third-party security assessments, penetration testing, 
                    and compliance audits performed annually.
                  </p>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <Shield className="h-8 w-8 text-primary mb-2" />
                  <CardTitle>Incident Response</CardTitle>
                </CardHeader>
                <CardContent>
                  <p class="text-muted-foreground">
                    Dedicated incident response team with documented procedures 
                    and 24/7 security monitoring capabilities.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Data Protection */}
        <section class="py-16">
          <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 class="text-3xl font-bold tracking-tight text-foreground mb-6">
                  Your Data Protection Rights
                </h2>
                <div class="space-y-4">
                  <div class="flex items-start space-x-3">
                    <Badge variant="outline" className="mt-1">1</Badge>
                    <div>
                      <h3 class="font-semibold">Data Ownership</h3>
                      <p class="text-muted-foreground">
                        You retain full ownership of your data. We never sell or share your information.
                      </p>
                    </div>
                  </div>
                  <div class="flex items-start space-x-3">
                    <Badge variant="outline" className="mt-1">2</Badge>
                    <div>
                      <h3 class="font-semibold">Data Portability</h3>
                      <p class="text-muted-foreground">
                        Export your data anytime in standard formats. No vendor lock-in.
                      </p>
                    </div>
                  </div>
                  <div class="flex items-start space-x-3">
                    <Badge variant="outline" className="mt-1">3</Badge>
                    <div>
                      <h3 class="font-semibold">Data Deletion</h3>
                      <p class="text-muted-foreground">
                        Request complete data deletion with secure purging from all systems.
                      </p>
                    </div>
                  </div>
                  <div class="flex items-start space-x-3">
                    <Badge variant="outline" className="mt-1">4</Badge>
                    <div>
                      <h3 class="font-semibold">Transparency</h3>
                      <p class="text-muted-foreground">
                        Clear visibility into how your data is processed and protected.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="bg-gradient-to-br from-primary/10 to-secondary/10 p-8 rounded-lg">
                <h3 class="text-xl font-semibold mb-4">Security Incident Response</h3>
                <p class="text-muted-foreground mb-6">
                  In the unlikely event of a security incident, we follow a comprehensive 
                  response protocol to protect your data and maintain transparency.
                </p>
                <div class="space-y-3">
                  <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-primary rounded-full"></div>
                    <span class="text-sm">Immediate containment and assessment</span>
                  </div>
                  <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-primary rounded-full"></div>
                    <span class="text-sm">Customer notification within 24 hours</span>
                  </div>
                  <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-primary rounded-full"></div>
                    <span class="text-sm">Detailed incident report and remediation</span>
                  </div>
                  <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-primary rounded-full"></div>
                    <span class="text-sm">Regulatory compliance reporting</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Contact */}
        <section class="bg-muted py-16">
          <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="mx-auto max-w-2xl text-center">
              <h2 class="text-3xl font-bold tracking-tight text-foreground mb-6">
                Questions About Our Security?
              </h2>
              <p class="text-lg text-muted-foreground mb-8">
                Our security team is available to discuss our practices and answer any questions 
                about how we protect your sensitive compliance data.
              </p>
              <div class="space-y-4">
              <p class="text-muted-foreground">
                <strong>Security Contact:</strong>
                <a href="mailto:<EMAIL>" class="hover:underline text-primary">
                  <EMAIL>
                </a>
              </p>
              <p class="text-muted-foreground">
                <strong>General Inquiries:</strong>
                <a href="mailto:<EMAIL>" class="hover:underline text-primary">
                  <EMAIL>
                </a>
              </p>
            </div>
            </div>
          </div>
        </section>
      </main>
      
      
</Layout>