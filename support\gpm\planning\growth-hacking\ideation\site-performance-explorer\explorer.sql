-- explorer.sql
-- title: Explorer
-- menu: Explorer
-- description: Ad-hoc explorer for opsfolio.web_traffic and opsfolio.astro_events with pagination.

-- Params: page, pagesize, sorting defaults
WITH params AS (
  SELECT
    coalesce(:from::timestamp, now() - interval '7 days') AS from_ts,
    coalesce(:to::timestamp, now()) AS to_ts,
    NULLIF(trim(coalesce(:env, '')), '')::text       AS env,
    NULLIF(trim(coalesce(:service, '')), '')::text   AS service,
    NULLIF(trim(coalesce(:path_like, '')), '')::text AS path_like,
    coalesce(:page::int, 1) AS page,
    coalesce(:pagesize::int, 200) AS pagesize,
    coalesce(:order_by, 'ts')::text AS order_by -- caution: sanitize in app layer
)

-- Web Traffic tab: paginated rows
SELECT
  wt.ts,
  wt.session_id,
  wt.request_path,
  wt.status_code,
  wt.referer,
  wt.user_agent,
  wt.env,
  wt.service,
  wt.source,
  wt.remote_addr,
  wt.body_bytes_sent
FROM opsfolio.web_traffic wt
JOIN params p ON true
WHERE wt.ts >= p.from_ts
  AND wt.ts < p.to_ts
  AND (p.env IS NULL OR p.env = wt.env)
  AND (p.service IS NULL OR p.service = wt.service)
  AND (p.path_like IS NULL OR wt.request_path ILIKE '%' || p.path_like || '%')
ORDER BY
  CASE WHEN p.order_by = 'ts' THEN wt.ts END DESC,
  wt.ts DESC
LIMIT p.pagesize OFFSET ((p.page - 1) * p.pagesize);

-- Astro Events tab: paginated rows
SELECT
  ev.ts,
  ev.user_id,
  ev.session_id,
  ev.event,
  ev.request_path,
  ev.status_code,
  ev.duration_ms,
  ev.env,
  ev.service,
  ev.source
FROM opsfolio.astro_events ev
JOIN params p ON true
WHERE ev.ts >= p.from_ts
  AND ev.ts < p.to_ts
  AND (p.env IS NULL OR p.env = ev.env)
  AND (p.service IS NULL OR p.service = ev.service)
  AND (p.path_like IS NULL OR ev.request_path ILIKE '%' || p.path_like || '%')
ORDER BY ev.ts DESC
LIMIT p.pagesize OFFSET ((p.page - 1) * p.pagesize);

-- Small helper: counts for pagination UI
SELECT
  (SELECT COUNT(*) FROM opsfolio.web_traffic wt WHERE wt.ts >= p.from_ts AND wt.ts < p.to_ts
     AND (p.env IS NULL OR p.env = wt.env)
     AND (p.service IS NULL OR p.service = wt.service)
     AND (p.path_like IS NULL OR wt.request_path ILIKE '%' || p.path_like || '%')
  ) AS web_traffic_count,
  (SELECT COUNT(*) FROM opsfolio.astro_events ev WHERE ev.ts >= p.from_ts AND ev.ts < p.to_ts
     AND (p.env IS NULL OR p.env = ev.env)
     AND (p.service IS NULL OR p.service = ev.service)
     AND (p.path_like IS NULL OR ev.request_path ILIKE '%' || p.path_like || '%')
  ) AS astro_events_count
FROM params p
LIMIT 1;
