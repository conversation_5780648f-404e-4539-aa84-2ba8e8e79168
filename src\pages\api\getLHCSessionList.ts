// src/pages/api/getSessionNames.ts
import { readdir, stat } from "fs/promises";
import type { APIContext } from "astro";
import path from "path";

// Your form definitions (only filePath is needed here)
const forms = [
    { filePath: "company-information.fhir-R4-questionnaire" },
    { filePath: "access-control-limit-information-system-access-to-authorized-users-and-processes.fhir-R4-questionnaire" },
    { filePath: "identification-authentication-verify-identities-of-users-and-processes.fhir-R4-questionnaire" },
    { filePath: "media-protection-protect-information-on-digital-and-non-digital-media.fhir-R4-questionnaire" },
    { filePath: "physical-protection-limit-physical-access-to-information-systems-and-facilities.fhir-R4-questionnaire" },
    { filePath: "system-communications-protection-monitor-control-and-protect-organizational-communications.fhir-R4-questionnaire" },
    { filePath: "system-information-integrity-identify-report-and-correct-information-system-flaws.fhir-R4-questionnaire" },
    { filePath: "policy-framework-assessment-policy-implementation-all-cmmc-level-1-practices.fhir-R4-questionnaire" },
];

export async function POST({ request }: APIContext) {
    try {
        const { tenantID, userID } = await request.json();

        if (!tenantID || !userID) {
            return new Response(
                JSON.stringify({ error: "Missing tenantID or userID" }),
                { status: 400, headers: { "Content-Type": "application/json" } }
            );
        }

        const baseDir = path.join("src", "data", "lforms", tenantID, userID);

        // Read directories inside the user folder
        const entries = await readdir(baseDir, { withFileTypes: true });

        // Collect session names with created time
        const sessions = await Promise.all(
            entries
                .filter((entry) => entry.isDirectory())
                .map(async (entry) => {
                    const sessionDir = path.join(baseDir, entry.name);

                    // Read all files inside this session folder
                    const sessionFiles = await readdir(sessionDir);

                    let include = false;

                    if (sessionFiles.length === 0) {
                        // ✅ Case 2: empty folder → always include
                        include = true;
                    } else {
                        // ✅ Case 1: folder has files → include only if any file matches required forms
                        include = sessionFiles.some((file) =>
                            forms.some((form) => file.includes(form.filePath))
                        );
                    }

                    if (!include) return null; // skip this directory

                    const stats = await stat(sessionDir);
                    return {
                        name: entry.name,
                        created: stats.ctimeMs,
                        modified: stats.mtimeMs,
                        fileCount: sessionFiles.length, // optional: how many files inside
                    };
                })
        );

        // Filter out skipped (null) sessions
        const filteredSessions = sessions.filter((s): s is NonNullable<typeof s> => s !== null);

        // Sort by created (latest first)
        filteredSessions.sort((a, b) => b.created - a.created);

        return new Response(
            JSON.stringify({
                sessionNames: filteredSessions.map((s) => s.name),
                sessions: filteredSessions,
            }),
            { status: 200, headers: { "Content-Type": "application/json" } }
        );
    } catch (err) {
        console.error("Error reading session names:", err);
        return new Response(
            JSON.stringify({ error: "Failed to get session names" }),
            { status: 500, headers: { "Content-Type": "application/json" } }
        );
    }
}
