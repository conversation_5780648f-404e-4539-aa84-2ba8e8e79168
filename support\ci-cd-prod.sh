echo -e "PUBLIC_NOVU_API_URL=${PUBLIC_NOVU_API_URL}\nPUBLIC_NOVU_API_KEY=${PUBLIC_NOVU_API_KEY}\nPUBLIC_NOVU_CONTACTUS_TEMPLATE=${PUBLIC_NOVU_CONTACTUS_TEMPLATE}\nPUBLIC_NOVU_CNT_ACKNOWLEDGEMENT_TEMPLATE=${PUBLIC_NOVU_CNT_ACKNOWLEDGEMENT_TEMPLATE}\nPUBLIC_NOVU_SUBSCRIBER_ID=${PUBLIC_NOVU_SUBSCRIBER_ID}\nPUBLIC_NOVU_CONTACT_ADMIN_MAIL=${PUBLIC_NOVU_CONTACT_ADMIN_MAIL}\nPUBLIC_CRM_CLIENT_ID=${PUBLIC_CRM_CLIENT_ID}\nPUBLIC_CRM_LIENT_SECRET_ID=${PUBLIC_CRM_LIENT_SECRET_ID}\nPUBLIC_GOOGLE_CAPTCHE_SITE_KEY=${PUBLIC_GOOGLE_CAPTCHE_SITE_KEY}\nPUBLIC_GOOGLE_CAPTCHE_SECRET_KEY=${PUBLIC_GOOGLE_CAPTCHE_SECRET_KEY}\nPUBLIC_ZITADEL_CLIENT_ID=${PUBLIC_ZITADEL_CLIENT_ID}\nPUBLIC_ZITADEL_AUTHORITY=${PUBLIC_ZITADEL_AUTHORITY}\nPUBLIC_ZITADEL_REDIRECT_URI=${PUBLIC_ZITADEL_REDIRECT_URI}\nPUBLIC_ZITADEL_LOGOUT_REDIRECT_URI=${PUBLIC_ZITADEL_LOGOUT_REDIRECT_URI}\nPUBLIC_ZITADEL_ORGANIZATION_ID=${PUBLIC_ZITADEL_ORGANIZATION_ID}\nPUBLIC_ZITADEL_PROJECT_ID=${PUBLIC_ZITADEL_PROJECT_ID}\nPUBLIC_ZITADEL_API_TOKEN=${PUBLIC_ZITADEL_API_TOKEN}\nPUBLIC_QUALITYFOLIO_URL=${PUBLIC_QUALITYFOLIO_URL}\nENABLE_QUALITYFOLIO_PREPARE=${ENABLE_QUALITYFOLIO_PREPARE}\nPUBLIC_QUALITYFOLIO_DB=${PUBLIC_QUALITYFOLIO_DB}\nPUBLIC_SITE_URL=${PUBLIC_SITE_URL}\nENABLE_OPEN_OBSERVE=${ENABLE_OPEN_OBSERVE}\nPUBLIC_ADMIN_EMAIL=${PUBLIC_ADMIN_EMAIL}\nPUBLIC_OPENOBSERVE_URL=${PUBLIC_OPENOBSERVE_URL}\nPUBLIC_OPENOBSERVE_TOKEN=${PUBLIC_OPENOBSERVE_TOKEN}" > .env
