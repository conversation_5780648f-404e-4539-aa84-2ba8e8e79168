# CMMC Lead <PERSON><PERSON><PERSON> Customer Journey Map

This document outlines the core customer journey stages, funnel architecture, trust-building mechanisms, and conversion tactics for the CMMC Self-Assessment Lead Magnet. It supports our core product goals:

- Acquire qualified leads via self-assessment tool
- Build trust in Opsfolio’s CMMC domain expertise
- Convert users to scoped technical assistance or white-glove compliance services

---

## 1. Funnel Entry Points

### Primary Entry:

- **Cold Email Campaign** with CTA driving to landing page
- **LinkedIn Outreach** with CTA driving to landing page

### Emotional Drivers by Persona:

- **CEO/Founder**: Fear of losing DoD contracts or being legally exposed
- **CTO/IT Director:** Anxious about being held responsible for vague, high-stakes compliance work they can’t scope clearly.
- **GRC/CISO:** Anxious about being blamed for a failed audit, overwhelmed by unclear controls, and skeptical of vendors who can’t prove results.

### Optional Future Expansion:

- **Google Search Ads** (“CMMC self-assessment,” “CMMC Level 1 checklist”)
- **LinkedIn Ads** (can be targeted by role/company)
- **Email Newsletter Sponsorships** (e.g., GovTech, Cybersecurity-specific)
- **Webinars or Panels** with CMMC experts (record → evergreen funnel)

---

## 2. 🔄 Customer Journey Stages

### **Stage 1: First Contact — Email or LinkedIn Outreach**

- **User Goal**: Understand what's at stake; evaluate value of the tool
- **Emotional State**: Afraid of deadline and possibility of losing contracts, unsure how to prepare or who can help
- **Touchpoint**: Cold email or LinkedIn message
- **Trust Builders**:
    - Clear statement of October 1, 2025 urgency
    - Mention of DoD enforcement + reputation risk
    - Ideally: Authority signals: “Used by top 200 contractors,” “Built by CMMC experts”
- **CTA**: “Start Free Assessment → Receive CMMC Readiness Report”

---

### **Stage 2: Landing Page**

- **User Goal**: Confirm this is legitimate and worth 15-30 minutes of time
- **Emotional State**: Cautious, skeptical
- **Touchpoint**: Landing page
- **Trust Builders**:
    - Clear explanation of what they’ll get (e.g., “Pass/fail + custom roadmap”)
    - Screenshots or visual cues of what the report looks like
    - Social proof if possible: Quote from a real assessor or contractor
- **CTA**: “Start Your Assessment Now” → creates account

---

### **Stage 3: CJ-Aware Sign-Up and Login Page**

* **User Goal**: Create an account to begin the assessment, with confidence they’re in the right place.
* **Emotional State**: Hesitant but curious; evaluating whether to commit a few minutes to sign-up.
* **Touchpoint**: Dynamic sign-up/login page with promo panel personalized to the user’s entry journey (e.g., LinkedIn, cold email, etc).
* **Trust Builders**:

  * Continuity between ad/email promise and the login promo panel
  * Reinforcement of immediate benefits (e.g., “Get instant access to CMMC Level 1 Self-Assessment”)
  * Trust strip (expertise, privacy, user data control)
* **CTA**: “Create Account” or “Sign In” → routes directly into the Self-Assessment

---

### **Stage 4: Account & Company Setup**

- **User Goal**: Set up access to assessment, keep time investment low
- **Emotional State**: Slight friction, minor annoyance
- **Touchpoint**: Form fill for company info + login creation
- **Trust Builders**:
    - Professional web page and user experience

---

### **Stage 5: CMMC Control Assessment**

- **User Goal**: Assess whether they’re compliant with Level 1
- **Emotional State**:
    - **CEO**: Alert, results-focused, overwhelmed by technical details
    - **GRC/IT**: Technical, slightly stressed about unknowns
- **Touchpoint**: Question-by-question assessment (broken into logical sections)
- **Trust Builders**:
    - Option to “Save and return later”
    - Consider encouraging use of chat to help with questions: “What does this control mean?”
- **CTA**: “Generate Your Report”

---

### **Stage 6: Report Generation**

- **User Goal**: Receive results that are *clear*, *useful*, and *actionable*
- **Emotional State**: Anticipation → satisfaction (or panic)
- **Touchpoint**: Assessment completion screen + downloadable report
- **Trust Builders**:
    - Clear status: “You meet X of 17 controls”
    - Key question: what else can we deliver here?
    - Option to download full PDF report (branded)
- **CTA**: * “Fix These Gaps Now → Get Our CMMC Level 1 Readiness Package” → links to **sales page** (no sales call)
* Reinforce: personalized POAMs alone are insufficient for compliance — your offer bridges the gap.


---

### **Stage 7: Post-Assessment Follow-Up**

- **User Goal**: Understand how to fix gaps, potentially engage help
- **Emotional State**: Motivated (or overwhelmed, if failed)
- **Touchpoints**:
    - Email with report
    - Drip follow-up emails (x3 over 7–10 days)
    - Post assessment sales page for services: start with 1 but could be dynamic based on results
- **Trust Builders**:
    - Personalized, highly credible, technically backed recommendations
    - Reminder of October 1st deadline and consequences

---

### **Stage 8: Conversion Pathway**

- **User Goal**: Solve compliance problem *before the deadline*
- **Emotional State**: Action-oriented, ROI-focused
- **Touchpoints**:
    - Email CTA: “Book Consultation with a CMMC Expert”
    - Call from SDR if no engagement from drip sequence
    - “Book Consultation” or “Download Sample SSP Template”
    - Direct sales email per persona
- **Trust Builders**:
    - Clarity of service options (Tiered services explained)
    - Case study (e.g., “How ACME Defense Passed in 41 Days”)
    - Pricing transparency (optional)

---

## 3. 🧠 Conversion Levers by Persona

| Persona | Conversion Lever |
| --- | --- |
| **CEO/Founder** | Emphasize risk reduction + speed (“Get peace of mind in <60 days”) |
| **CTO** | Integration clarity (“How this fits with your cloud/security setup”) |
| **CISO/GRC** | Sample SSP or POAM templates with commentary |
| **IT Director** | Implementation checklists or config guides |
| **Program Manager** | Gantt chart or milestone planner |

All of these can be attached to the follow-up email or hosted in a “Resource Center” post-assessment.

---

## 4. 📬 Post-Assessment Funnel

#### **i. Immediate Email After Completion**

**Trigger:**

* Send upon submission, using captured email

**Contents:**

* PDF download link or attachment
* Brief persona-relevant gap summary
* CTA to sales page

**Subject Examples:**

* “Your CMMC Self-Assessment Results + Action Plan”
* “Fix These Gaps Before Your Next DoD Contract Review”

#### **ii. Persona-Tailored Drip Sequence**

**Overall Strategy:** Educate → Reframe urgency → Reinforce benefits → Offer again

| Day | Email                            | Notes                                                      |
| --- | -------------------------------- | ---------------------------------------------------------- |
| 0   |  **Delivery Email**             | PDF report + 1-liner top gaps summary                      |
| 1   |  **What Your Report Means**    | Persona-specific explainer on why gaps matter              |
| 3   |  **Cost of Inaction**          | Financial risk, penalties, vendor loss, failed audit cases |
| 6   |  **Why You Can’t Just DIY It** | Subtle sell: mistakes from self-remediation                |
| 10  |  **Final Nudge**                | Limited-time incentive (discount, bonus SOPs)              |

### 📈 **Upsell Pathways**

- **Option 1**: Book paid scoped technical assistance package ($)
- **Option 2**: Apply for white-glove full-service compliance ($$$)

Optional: Pre-qualify based on assessment answers for personalized CTA.

---
