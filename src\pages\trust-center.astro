---
import Layout from "../layouts/Layout.astro";
import Viewcertificate from '@/components/ViewCertificate'; 
import * as type1 from '@/content/reports/soc2-type1.md';
import * as type2 from '@/content/reports/soc2-type2.md';
import * as conclusionContent from '@/content/reports/why-soc2.md';
import { marked } from 'marked';

const contentType1 = await type1.rawContent();
const htmlContentType1 = marked(contentType1);

const contentType2 = await type2.rawContent();
const htmlContentType2 = marked(contentType2);

const generalContent = await conclusionContent.rawContent();
const htmlGeneralContent = marked(generalContent);

const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Trust Center',"aria-disabled": true ,
  }
];
---

<Layout breadcrumbLinks={breadcrumbLinks}>
  <main class="container mx-auto max-w-4xl px-6 lg:px-8 p-4">
  <article set:html={htmlContentType1} class="mt-4 mb-4 markdown-content" id="type1"></article>
    <Viewcertificate client:only="react" certificateTemplate="soc2-type1-ceritificate"/>
    <a href="/assets/files/soc2-type-1-audit-report.pdf" download
      class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-11 rounded-md px-8"
    >SOC 2 Type I Report
   </a>
  <article set:html={htmlContentType2} class="mt-4 mb-4 markdown-content"></article>

    <Viewcertificate client:only="react" certificateTemplate="soc2-type2-ceritificate"/>
    <a href="/assets/files/soc2-type-2-audit-report.pdf" download
      class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-11 rounded-md px-8"
    >SOC 2 Type II Report
    </a>

    <article set:html={htmlGeneralContent} class="mt-4 mb-4 markdown-content"></article>
  </main>
</Layout>

