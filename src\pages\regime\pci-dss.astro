---
import Layout from "../../layouts/Layout.astro";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  CreditCard, 
  Shield, 
  Network, 
  Lock, 
  Eye, 
  CheckCircle,
  Server,
  UserCheck,
  AlertTriangle
} from "lucide-react";

const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Regime',"aria-disabled": true ,
  },
  {
    index: "2", text: "PCI DSS","aria-disabled": true 
  },
];
---
<Layout title="Opsfolio - Compliance as a Service | SOC2, HIPAA, ISO Certification"
  description="Get SOC2, HIPAA, ISO certified fast with expert-guided compliance. 98% audit pass rate, 60% faster than DIY. Your Compliance System of Record."
  keywords="PCIDSS, compliance, SOC2, HIPAA, ISO 27001, cybersecurity, audit, certification"
  url="/regime/pci-dss/"
  breadcrumbLinks={breadcrumbLinks}>

        {/* Hero Section */}
      <section class="py-24 sm:py-32 bg-gradient-to-b from-primary/5 to-background">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-3xl text-center">
            <Badge variant="outline" className="mb-4">
              <CreditCard className="w-4 h-4 mr-2" />
              Payment Security
            </Badge>
            <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
              PCI DSS Compliance
            </h1>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Secure payment card data with comprehensive PCI DSS compliance. Protect cardholder 
              information, maintain secure networks, and ensure continuous monitoring.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
              
                <a href="/get-started">
                  <Button size="lg">
                    Start PCI Assessment
                  </Button>
                </a>
              
                <a href="/contact">
                  <Button variant="outline" size="lg">
                     Schedule Audit
                  </Button>
                </a>
              
            </div>
          </div>
        </div>
      </section>

      {/* PCI DSS Requirements */}
      <section class="py-24 sm:py-32">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center mb-16">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              12 PCI DSS Requirements
            </h2>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Comprehensive security controls for payment card data protection
            </p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="border-border/50">
              <CardHeader>
                <Network className="h-8 w-8 text-primary mb-2" />
                <CardTitle>Secure Networks</CardTitle>
              </CardHeader>
              <CardContent>
                <p class="text-muted-foreground mb-4">
                  Install and maintain firewalls and network security configurations 
                  to protect cardholder data environments.
                </p>
                <div class="space-y-2">
                  <div class="flex items-center text-sm">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Firewall configuration
                  </div>
                  <div class="flex items-center text-sm">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Network segmentation
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-border/50">
              <CardHeader>
                <Lock className="h-8 w-8 text-primary mb-2" />
                <CardTitle>Data Protection</CardTitle>
              </CardHeader>
              <CardContent>
                <p class="text-muted-foreground mb-4">
                  Encrypt cardholder data during transmission and storage. 
                  Implement strong cryptographic controls.
                </p>
                <div class="space-y-2">
                  <div class="flex items-center text-sm">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Data encryption
                  </div>
                  <div class="flex items-center text-sm">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Key management
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-border/50">
              <CardHeader>
                <Shield className="h-8 w-8 text-primary mb-2" />
                <CardTitle>Vulnerability Management</CardTitle>
              </CardHeader>
              <CardContent>
                <p class="text-muted-foreground mb-4">
                  Maintain vulnerability management programs with regular 
                  security updates and anti-virus protection.
                </p>
                <div class="space-y-2">
                  <div class="flex items-center text-sm">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Security scanning
                  </div>
                  <div class="flex items-center text-sm">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Patch management
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-border/50">
              <CardHeader>
                <UserCheck className="h-8 w-8 text-primary mb-2" />
                <CardTitle>Access Controls</CardTitle>
              </CardHeader>
              <CardContent>
                <p class="text-muted-foreground mb-4">
                  Implement strong access controls with need-to-know principles 
                  and multi-factor authentication.
                </p>
                <div class="space-y-2">
                  <div class="flex items-center text-sm">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Role-based access
                  </div>
                  <div class="flex items-center text-sm">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Multi-factor auth
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-border/50">
              <CardHeader>
                <Eye className="h-8 w-8 text-primary mb-2" />
                <CardTitle>Monitoring & Testing</CardTitle>
              </CardHeader>
              <CardContent>
                <p class="text-muted-foreground mb-4">
                  Regularly monitor networks and test security systems 
                  to ensure ongoing protection effectiveness.
                </p>
                <div class="space-y-2">
                  <div class="flex items-center text-sm">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Log monitoring
                  </div>
                  <div class="flex items-center text-sm">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Penetration testing
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-border/50">
              <CardHeader>
                <AlertTriangle className="h-8 w-8 text-primary mb-2" />
                <CardTitle>Security Policies</CardTitle>
              </CardHeader>
              <CardContent>
                <p class="text-muted-foreground mb-4">
                  Maintain comprehensive security policies and incident 
                  response procedures for all personnel.
                </p>
                <div class="space-y-2">
                  <div class="flex items-center text-sm">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Security awareness
                  </div>
                  <div class="flex items-center text-sm">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Incident response
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Validation Levels */}
      <section class="py-24 sm:py-32 bg-muted">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center mb-16">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              PCI DSS Validation Levels
            </h2>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Determine your compliance requirements based on transaction volume
            </p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="text-center p-6 bg-gradient-to-b from-primary/5 to-primary/10 border-primary/20">
              <div class="text-3xl font-bold text-primary mb-2">Level 1</div>
              <div class="text-sm font-medium text-foreground mb-4">6M+ Transactions</div>
              <div class="space-y-2 text-sm text-muted-foreground">
                <div>Annual on-site audit</div>
                <div>Quarterly network scan</div>
                <div>Attestation of Compliance</div>
              </div>
            </Card>

            <Card className="text-center p-6">
              <div class="text-3xl font-bold text-primary mb-2">Level 2</div>
              <div class="text-sm font-medium text-foreground mb-4">1M - 6M Transactions</div>
              <div class="space-y-2 text-sm text-muted-foreground">
                <div>Annual self-assessment</div>
                <div>Quarterly network scan</div>
                <div>Attestation of Compliance</div>
              </div>
            </Card>

            <Card className="text-center p-6">
              <div class="text-3xl font-bold text-primary mb-2">Level 3</div>
              <div class="text-sm font-medium text-foreground mb-4">20K - 1M Transactions</div>
              <div class="space-y-2 text-sm text-muted-foreground">
                <div>Annual self-assessment</div>
                <div>Quarterly network scan</div>
                <div>Attestation of Compliance</div>
              </div>
            </Card>

            <Card className="text-center p-6">
              <div class="text-3xl font-bold text-primary mb-2">Level 4</div>
              <div class="text-sm font-medium text-foreground mb-4">&lt;20K Transactions</div>
              <div class="space-y-2 text-sm text-muted-foreground">
                <div>Annual self-assessment</div>
                <div>Quarterly network scan</div>
                <div>Compliance validation</div>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* Implementation Platform */}
      <section class="py-24 sm:py-32">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl mb-6">
                Automated PCI DSS Compliance
              </h2>
              <p class="text-lg text-muted-foreground mb-8">
                Our platform provides continuous monitoring, automated evidence collection, 
                and real-time compliance dashboards for all PCI DSS requirements.
              </p>
              
              <div class="space-y-6">
                <div class="flex items-start space-x-4">
                  <Server className="h-6 w-6 text-primary mt-1 flex-shrink-0" />
                  <div>
                    <h3 class="font-semibold text-foreground">Network Discovery & Segmentation</h3>
                    <p class="text-muted-foreground mt-1">
                      Automated cardholder data environment mapping with network segmentation validation.
                    </p>
                  </div>
                </div>

                <div class="flex items-start space-x-4">
                  <Eye className="h-6 w-6 text-primary mt-1 flex-shrink-0" />
                  <div>
                    <h3 class="font-semibold text-foreground">Continuous Monitoring</h3>
                    <p class="text-muted-foreground mt-1">
                      Real-time log analysis and security event correlation across your infrastructure.
                    </p>
                  </div>
                </div>

                <div class="flex items-start space-x-4">
                  <Shield className="h-6 w-6 text-primary mt-1 flex-shrink-0" />
                  <div>
                    <h3 class="font-semibold text-foreground">Vulnerability Management</h3>
                    <p class="text-muted-foreground mt-1">
                      Automated scanning and remediation tracking with risk prioritization.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div class="lg:text-center">
              <Card className="p-8 bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20">
                <CreditCard className="h-16 w-16 text-primary mx-auto mb-6" />
                <h3 class="text-2xl font-bold text-foreground mb-4">Payment Security Excellence</h3>
                <p class="text-muted-foreground mb-6">
                  Achieve and maintain PCI DSS compliance with our comprehensive automation platform.
                </p>
                <div class="grid grid-cols-2 gap-6">
                  <div class="text-center">
                    <div class="text-3xl font-bold text-primary">100%</div>
                    <div class="text-sm text-muted-foreground">Requirement Coverage</div>
                  </div>
                  <div class="text-center">
                    <div class="text-3xl font-bold text-primary">24/7</div>
                    <div class="text-sm text-muted-foreground">Monitoring</div>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section class="py-24 sm:py-32 bg-muted">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Secure Your Payment Processing
            </h2>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Start your PCI DSS compliance journey today. Protect customer payment data 
              and maintain trust with automated compliance monitoring.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
              <a href="/get-started">
                  <Button size="lg">
                    Begin Assessment
                  </Button>
              </a>              
              <a href="/contact">
                <Button variant="outline" size="lg">
                  Expert Consultation
                </Button>
              </a>              
            </div>
          </div>
        </div>
      </section>
</Layout>