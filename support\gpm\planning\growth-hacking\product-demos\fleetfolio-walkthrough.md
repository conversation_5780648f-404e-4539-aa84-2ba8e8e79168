# Walkthrough Script — Fleetfolio

**Opening (0:00–0:45)**
“Welcome to Fleetfolio — Opsfolio’s real-time asset intelligence dashboard. In every compliance engagement, one of the hardest problems is maintaining a complete and accurate asset inventory. Without knowing exactly what servers, systems, and applications are in scope, compliance becomes guesswork. Fleetfolio solves this with continuous, automated visibility.”

---

### Step 1: Asset Discovery & Installation (0:45–2:00)

* “Fleetfolio starts by installing a lightweight application on each remote server. This agent pulls live system information and feeds it directly into the dashboard.
* If direct installation isn’t possible because of restricted privileges, Fleetfolio supports an alternative: a script can be run locally on the server, with results securely imported back into the platform. This means you always have options — full installation or script-based evidence gathering.”

---

### Step 2: The Asset Listing Dashboard (2:00–4:00)

* “The core of Fleetfolio is the asset listing dashboard. Here, every server in your environment is represented, grouped across both physical boundaries (e.g. data center, machine) and logical boundaries (e.g. business unit, compliance scope).

* At a glance, you can see:

  * Online/offline status
  * Software versions installed
  * Restart history and uptime
  * IP and MAC addresses
  * Disk space and memory utilization
  * SSL certificate status
  * VPN listening ports

* Clicking into any server provides a deep profile of its technical state without needing to log in directly.”

---

### Step 3: Continuous Monitoring (4:00–5:00)

* “Fleetfolio isn’t just a static asset list. It’s a live monitoring system, refreshing in real time.
* Administrators can instantly see when a server goes offline, when a certificate expires, or when software versions fall behind.
* This reduces the manual effort of logging into each machine to collect evidence — the dashboard brings all details into one place.”

---

### Step 4: Compliance Evidence in Real Time (5:00–6:30)

* “Every compliance framework — CMMC, NIST, ISO, HIPAA, SOC 2 — requires an asset inventory. Fleetfolio provides this automatically.
* Instead of scrambling to assemble lists at audit time, compliance managers can generate a real-time, exportable report from Fleetfolio showing assets, software versions, and statuses.
* Because the data is collected continuously, you don’t just have a snapshot — you have a live evidence stream. This strengthens audit readiness and reduces the risk of gaps.”

---

### Step 5: Integration & Flexibility (6:30–7:30)

* “Fleetfolio can integrate with third-party monitoring and security tools to pull in additional context. Whether your servers are on-premises or cloud-based, physical or virtual, Fleetfolio brings the data into one unified platform.
* As environments grow and change, new servers and applications are automatically brought into scope.”

---

**Closing (7:30–8:00)**
“Fleetfolio gives organizations something they’ve never had before: continuous, real-time compliance evidence tied directly to their live infrastructure. With full asset listings, detailed health metrics, and exportable reports, Fleetfolio takes a traditionally painful audit requirement and turns it into an automated, always-on capability.”

---

Would you like me to also draft a **FAQ for Fleetfolio in the same voice** (expanding on installation, compliance use cases, and technical details), so you’ve got a matching set to the EOH assets?
