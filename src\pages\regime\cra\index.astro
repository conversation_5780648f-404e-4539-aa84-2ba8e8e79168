---
import Layout from "../../../layouts/Layout.astro";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Cloud, Flag, Building, Clock, CheckCircle, ArrowRight, Link } from "lucide-react";
import { 
  CheckCircle, 
  ArrowRight,
  Shield,
  FileCheck,
  Clock,
  Target,
  Zap,
  Lock,
  TrendingUp,
  GitBranch,
  Database,
  AlertTriangle
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import VideoPlaceholder from "@/components/sections/VideoPlaceholder";

const craGaps = [
  {
    challenge: "SBOM & supply chain",
    soc2Gap: "SOC 2 has vendor mgmt only",
    opsfolioSolution: "Fleetfolio automated SBOM + surveilr provenance",
    icon: GitBranch,
    impact: "Supply risk visibility"
  },
  {
    challenge: "Vulnerability handling",
    soc2Gap: "SOC 2 IRP only, no disclosure",
    opsfolioSolution: "PSIRT setup + public VDP + automated intake",
    icon: Shield,
    impact: "Reduced legal exposure"
  },
  {
    challenge: "24h ENISA reporting",
    soc2Gap: "No regulator-facing obligation",
    opsfolioSolution: "Opsfolio runbooks + surveilr timestamped evidence",
    icon: Clock,
    impact: "On-time reporting"
  },
  {
    challenge: "Secure defaults",
    soc2Gap: "SOC 2 config mgmt only",
    opsfolioSolution: "NUP SDL templates + CI/CD enforcement",
    icon: Lock,
    impact: "Lower breach risk"
  },
  {
    challenge: "Lifecycle support",
    soc2Gap: "SOC 2 silent on EOL",
    opsfolioSolution: "NUP lifecycle docs + surveilr evidence",
    icon: TrendingUp,
    impact: "Contract obligations met"
  },
  {
    challenge: "Customer security docs",
    soc2Gap: "SOC 2 internal only",
    opsfolioSolution: "Opsfolio CaaS produces CRA-ready user docs",
    icon: FileCheck,
    impact: "Procurement unlocked"
  }
];

const roadmapSteps = [
  {
    step: "1",
    title: "Map SOC 2 artifacts to CRA Annex I",
    description: "Leverage existing SOC 2 foundations"
  },
  {
    step: "2",
    title: "Stand up SBOM pipelines in Fleetfolio",
    description: "Automate supply chain transparency"
  },
  {
    step: "3",
    title: "Establish PSIRT & public VDP",
    description: "Meet vulnerability disclosure requirements"
  },
  {
    step: "4",
    title: "Codify SDL secure-by-default proof in NUP",
    description: "Embed security in development lifecycle"
  },
  {
    step: "5",
    title: "Define lifecycle/EOL commitments",
    description: "Plan product support obligations"
  },
  {
    step: "6",
    title: "Automate conformity file creation with surveilr",
    description: "Generate CRA compliance documentation"
  },
  {
    step: "7",
    title: "Pilot CRA self-assessment",
    description: "Validate compliance readiness"
  },
  {
    step: "8",
    title: "Scale across all products",
    description: "Roll out to entire product portfolio"
  }
];

const caasComponents = [
  {
    name: "surveilr",
    description: "Evidence, telemetry, provenance",
    icon: Database,
    url: "https://www.surveilr.com/",
    external: true
  },
  {
    name: "Qualityfolio",
    description: "QA/SRE assurance mapped to controls",
    icon: Target,
    url: "https://opsfolio.com/task/test-management/",
    external: false
  },
  {
    name: "Fleetfolio",
    description: "Asset, identity, and SBOM intelligence",
    icon: GitBranch,
    url: "https://opsfolio.com/task/assets-intelligence/",
    external: false
  },
  {
    name: "NUP",
    description: "Governance and lifecycle documentation",
    icon: FileCheck,
    url: "https://gpm.opsfolio.com/",
    external: true
  }
];


const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Regime',"aria-disabled": true ,
  },
  {
    index: "2", text: "European CRA","aria-disabled": true 
  },
];
---

<Layout  title="European Cyber Resilience Act (CRA) Compliance - Security by Design"
        description="CRA compliance focused on real security outcomes, not just checklists. Opsfolio's Compliance-as-Code ensures your products achieve security by design for EU market access."
        keywords="European Cyber Resilience Act, CRA compliance, security by design, EU regulation, compliance as code"
        url="/regime/cra"
        breadcrumbLinks={breadcrumbLinks}>
      <main>
        {/* Hero Section */}
        <div class="relative isolate px-6 pt-14 lg:px-8 bg-gradient-to-br from-primary/5 via-background to-secondary/5">
          <div class="mx-auto max-w-4xl py-16 sm:py-24 lg:py-28">
            <div class="text-center">
              <Badge variant="secondary" className="mb-4 flex items-center gap-2 w-fit mx-auto">
                <span class="inline-block w-4 h-3 bg-blue-600 rounded-sm"></span>
                <span class="inline-block w-4 h-3 bg-yellow-400 rounded-sm -ml-1"></span>
                European CRA
              </Badge>
              <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
                <span class="text-primary">Cyber Resilience Act</span> Compliance.{" "}
                Delivered as a Service.
              </h1>
              <p class="mt-6 text-lg leading-8 text-muted-foreground max-w-3xl mx-auto">
                More than checklists: Opsfolio helps EU manufacturers and suppliers of products
                with digital elements deliver audit-ready documentation, automated evidence, 
                and a unified system of record.
              </p>
              <div class="mt-8 flex items-center justify-center gap-x-4 flex-wrap">
                <div class="flex items-center text-sm text-muted-foreground">
                  <CheckCircle className="h-4 w-4 text-success mr-2" />
                  EU Market Access Ready
                </div>
                <div class="flex items-center text-sm text-muted-foreground">
                  <CheckCircle className="h-4 w-4 text-success mr-2" />
                  Security by Design Focus
                </div>
                <div class="flex items-center text-sm text-muted-foreground">
                  <CheckCircle className="h-4 w-4 text-success mr-2" />
                  Continuous Compliance
                </div>
              </div>
              <div class="mt-10 flex items-center justify-center gap-x-6">
                <a href="/regime/cra/self-assessment?journey=cra-self-assessment">
                <Button size="lg">
                    Get CRA Readiness Assessment
                    <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
                </a>
                <a href="/contact">
                  <Button variant="outline" size="lg" >
                      Talk to CRA Expert
                  </Button>
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Digital Product Lifecycle Visual */}
        <!-- <div class="py-16 bg-muted/30">
          <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <VideoPlaceholder
              title="Digital Product Lifecycle Under CRA"
              description="Understanding CRA obligations across design, development, production, support, and end-of-life phases"
              duration="4:30"
              aspectRatio="16:9"
              variant="demo"
              class="max-w-4xl mx-auto"
            />
          </div>
        </div> -->

        {/* The CRA Challenge */}
        <div class="py-24 sm:py-32">
          <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl mb-6">
                  Market Access Depends on CRA Compliance
                </h2>
                <p class="text-lg text-muted-foreground mb-8">
                  The European <strong>Cyber Resilience Act (CRA)</strong> is not just another framework. 
                  It is a <strong>binding EU regulation</strong> covering every "product with digital elements" 
                  — from consumer IoT to enterprise SaaS. CRA sets <strong>mandatory cybersecurity obligations</strong> 
                  across the product lifecycle.
                </p>
                <div class="space-y-4">
                  <div class="flex items-start space-x-3">
                    <AlertTriangle className="h-5 w-5 text-warning mt-0.5 flex-shrink-0" />
                    <span class="text-muted-foreground"><strong>Prescriptive obligations</strong> (not descriptive like SOC 2)</span>
                  </div>
                  <div class="flex items-start space-x-3">
                    <Shield className="h-5 w-5 text-success mt-0.5 flex-shrink-0" />
                    <span class="text-muted-foreground"><strong>Applies across all EU Member States</strong></span>
                  </div>
                  <div class="flex items-start space-x-3">
                    <Lock className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
                    <span class="text-muted-foreground"><strong>Market access requirement</strong>: no CRA, no EU sales</span>
                  </div>
                </div>
              </div>
              <div class="grid grid-cols-2 gap-6">
                <a href="/regime/soc2/" class="block hover:shadow-lg hover:scale-105 transition-transform rounded-2xl">
                <Card>
                  <CardHeader className="text-center">
                    <CardTitle className="text-lg">SOC 2</CardTitle>
                  </CardHeader>
                  <CardContent className="text-center">
                    <p class="text-sm text-muted-foreground">Audit Attestation</p>
                  </CardContent>
                </Card>
                </a>
                <Card className="border-2 border-primary">
                  <CardHeader className="text-center">
                    <CardTitle className="text-lg text-primary">CRA</CardTitle>
                  </CardHeader>
                  <CardContent className="text-center">
                    <p class="text-sm text-muted-foreground">Regulatory Market Access</p>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>

        {/* SOC 2 vs CRA Comparison */}
        <div class="py-24 sm:py-32 bg-muted">
          <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="mx-auto max-w-2xl text-center mb-16">
              <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                SOC 2 is strong. CRA goes further.
              </h2>
              <p class="mt-6 text-lg leading-8 text-muted-foreground">
                Your SOC 2 Type I & II achievements already demonstrate strong governance and operational discipline. But CRA requires more.
              </p>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
              <Card>
                <CardHeader className="text-center">
                  <CardTitle className="text-lg">Governance & Access Control</CardTitle>
                  <p class="text-sm text-muted-foreground">Shared Foundation</p>
                </CardHeader>
                <CardContent>
                  <div class="space-y-2">
                    <div class="flex items-center text-sm">
                      <CheckCircle className="h-4 w-4 text-success mr-2" />
                      Identity & Access Management
                    </div>
                    <div class="flex items-center text-sm">
                      <CheckCircle className="h-4 w-4 text-success mr-2" />
                      Encryption Standards
                    </div>
                    <div class="flex items-center text-sm">
                      <CheckCircle className="h-4 w-4 text-success mr-2" />
                      Audit Logging
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="text-center">
                  <CardTitle className="text-lg">SOC 2 Focus</CardTitle>
                  <p class="text-sm text-muted-foreground">Organization-Centric</p>
                </CardHeader>
                <CardContent>
                  <div class="space-y-2">
                    <div class="flex items-center text-sm">
                      <Target className="h-4 w-4 text-blue-500 mr-2" />
                      Descriptive framework
                    </div>
                    <div class="flex items-center text-sm">
                      <Target className="h-4 w-4 text-blue-500 mr-2" />
                      Audit snapshots
                    </div>
                    <div class="flex items-center text-sm">
                      <Target className="h-4 w-4 text-blue-500 mr-2" />
                      Internal controls focus
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card className="border-2 border-primary">
                <CardHeader className="text-center">
                  <CardTitle className="text-lg text-primary">CRA Requirements</CardTitle>
                  <p class="text-sm text-muted-foreground">Product-Centric</p>
                </CardHeader>
                <CardContent>
                  <div class="space-y-2">
                    <div class="flex items-center text-sm">
                      <Zap className="h-4 w-4 text-primary mr-2" />
                      SBOM & Supply Chain
                    </div>
                    <div class="flex items-center text-sm">
                      <Zap className="h-4 w-4 text-primary mr-2" />
                      PSIRT & 24h Reporting
                    </div>
                    <div class="flex items-center text-sm">
                      <Zap className="h-4 w-4 text-primary mr-2" />
                      Secure-by-default proof
                    </div>
                    <div class="flex items-center text-sm">
                      <Zap className="h-4 w-4 text-primary mr-2" />
                      Lifecycle support obligations
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>

        {/* Opsfolio CaaS Advantage */}
        <div class="py-24 sm:py-32">
          <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="mx-auto max-w-2xl text-center mb-16">
              <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                Closing the CRA Gaps with Compliance-as-Code
              </h2>
              <p class="mt-6 text-lg leading-8 text-muted-foreground">
                Opsfolio Compliance-as-a-Service (CaaS) transforms CRA readiness into a continuous, code-driven process.
              </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
              {caasComponents.map((component) => {
                const Icon = component.icon;
                return (
                  <a
                    href={component.url}
                    target={component.external ? "_blank" : "_self"}
                    rel={component.external ? "noopener noreferrer" : undefined}
                    class="block"
                  >
                    <Card className="text-center hover:shadow-lg hover:scale-105 hover:border-primary transition-transform min-h-[200px] flex flex-col justify-between">
                      <CardHeader>
                        <Icon className="h-8 w-8 text-primary mx-auto mb-1" />
                        <CardTitle className="text-lg mb-2">{component.name}</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p class="text-sm text-muted-foreground">
                          {component.description}
                        </p>
                      </CardContent>
                    </Card>
                  </a>
                );
              })}
            </div>

            
            <div class="text-center">
              <p class="text-lg text-muted-foreground">
                Together, these deliver a <strong>CRA Conformity File</strong> you can trust, backed by automated evidence.
              </p>
            </div>
          </div>
        </div>

        {/* CRA Gaps & Solutions */}
        <div class="py-24 sm:py-32 bg-muted">
          <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="mx-auto max-w-2xl text-center mb-16">
              <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                From SOC 2 Strength to CRA Readiness
              </h2>
              <p class="mt-6 text-lg leading-8 text-muted-foreground">
                How Opsfolio bridges the gap between your SOC 2 foundation and CRA requirements
              </p>
            </div>
            
            <div class="overflow-x-auto">
              <div class="min-w-full">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-1 bg-background rounded-lg p-1">
                  <div class="bg-muted/50 p-4 rounded font-semibold text-center">CRA Challenge</div>
                  <div class="bg-muted/50 p-4 rounded font-semibold text-center">SOC 2 Shortfall</div>
                  <div class="bg-muted/50 p-4 rounded font-semibold text-center">Opsfolio Solution</div>
                  <div class="bg-muted/50 p-4 rounded font-semibold text-center">Impact</div>
                </div>
                
                {craGaps.map((gap, index) => {
                  const Icon = gap.icon;
                  return (
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-1 mt-1">
                      <Card className="flex items-center p-4">
                        <Icon className="h-5 w-5 text-primary mr-3 flex-shrink-0" />
                        <span class="font-medium text-sm">{gap.challenge}</span>
                      </Card>
                      <Card className="flex items-center p-4">
                        <span class="text-sm text-muted-foreground">{gap.soc2Gap}</span>
                      </Card>
                      <Card className="flex items-center p-4">
                        <span class="text-sm font-medium">{gap.opsfolioSolution}</span>
                      </Card>
                      <Card className="flex items-center p-4">
                        <CheckCircle className="h-4 w-4 text-success mr-2" />
                        <span class="text-sm text-success">{gap.impact}</span>
                      </Card>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>

        {/* Compliance-as-Code Philosophy */}
        <div class="grid grid-cols-1 gap-12 items-center max-w-4xl mx-auto px-6 lg:px-8 py-24 sm:py-32 text-center">
          <div>
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl mb-6">
              Security Outcomes &gt; Checkbox Compliance
            </h2>
            <p class="text-lg text-muted-foreground mb-2">
              Traditional compliance consultancies deliver binders and reports. 
              Opsfolio delivers <strong>pipelines and automation</strong>.
            </p>
          </div>

          <div class="space-y-4 max-w-2xl mx-auto">
            <div class="flex items-start space-x-3 text-left">
              <GitBranch className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
              <span class="text-muted-foreground">Compliance artifacts are treated as <strong>code and data</strong>, not static PDFs</span>
            </div>
            <div class="flex items-start space-x-3 text-left">
              <Database className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
              <span class="text-muted-foreground">Evidence collection is <strong>continuous and automated</strong></span>
            </div>
            <div class="flex items-start space-x-3 text-left">
              <FileCheck className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
              <span class="text-muted-foreground">Documentation is <strong>generated and reusable</strong> across frameworks</span>
            </div>
            <div class="flex items-start space-x-3 text-left">
              <Target className="h-5 w-5 text-primary mt-1 flex-shrink-0" />
              <span class="text-muted-foreground">Focus shifts from "passing an audit" to <strong>proving security outcomes</strong></span>
            </div>
          </div>
        </div>

        {/* CRA Readiness Roadmap */}
        <div class="py-24 sm:py-32 bg-muted">
          <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="mx-auto max-w-2xl text-center mb-16">
              <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                Your Path with Opsfolio CaaS
              </h2>
              <p class="mt-6 text-lg leading-8 text-muted-foreground">
                A step-by-step roadmap to CRA compliance building on your SOC 2 foundation
              </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {roadmapSteps.map((step, index) => (
                <Card key={step.step} className="relative">
                  <CardHeader>
                    <div class="flex items-center justify-center w-8 h-8 bg-primary text-primary-foreground rounded-full font-bold mb-2 mx-auto">
                      {step.step}
                    </div>
                    <CardTitle className="text-lg text-center">{step.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p class="text-muted-foreground text-center text-sm">{step.description}</p>
                  </CardContent>
                  {index < roadmapSteps.length - 1 && index % 4 !== 3 && (
                    <div class="hidden lg:block absolute top-1/2 -right-3 transform -translate-y-1/2">
                      <ArrowRight className="h-5 w-5 text-muted-foreground" />
                    </div>
                  )}
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* Why Opsfolio for CRA */}
        <div class="py-24 sm:py-32">
          <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="mx-auto max-w-2xl text-center mb-16">
              <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                The Partner You Can Trust
              </h2>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              <Card className="text-center">
                <CardHeader>
                  <Shield className="h-8 w-8 text-primary mx-auto mb-2" />
                  <CardTitle className="text-lg">Deep Expertise</CardTitle>
                </CardHeader>
                <CardContent>
                  <p class="text-sm text-muted-foreground">Deep SOC 2, ISO, and GRC expertise</p>
                </CardContent>
              </Card>
              
              <Card className="text-center">
                <CardHeader>
                  <Database className="h-8 w-8 text-primary mx-auto mb-2" />
                  <CardTitle className="text-lg">Unified Platform</CardTitle>
                </CardHeader>
                <CardContent>
                  <p class="text-sm text-muted-foreground">Unified compliance platform (surveilr, Qualityfolio, Fleetfolio)</p>
                </CardContent>
              </Card>
              
              <Card className="text-center">
                <CardHeader>
                  <GitBranch className="h-8 w-8 text-primary mx-auto mb-2" />
                  <CardTitle className="text-lg">Engineering-First</CardTitle>
                </CardHeader>
                <CardContent>
                  <p class="text-sm text-muted-foreground">Compliance built into your software lifecycle</p>
                </CardContent>
              </Card>
              
              <Card className="text-center">
                <CardHeader>
                  <Target className="h-8 w-8 text-primary mx-auto mb-2" />
                  <CardTitle className="text-lg">Outcome-Driven</CardTitle>
                </CardHeader>
                <CardContent>
                  <p class="text-sm text-muted-foreground">We care more about security resilience than checklists</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div class="bg-muted">
          <div class="px-6 py-24 sm:px-6 sm:py-32 lg:px-8">
            <div class="mx-auto max-w-2xl text-center">
              <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                Keep Selling in the EU. Be CRA Ready.
              </h2>
              <p class="mx-auto mt-6 max-w-xl text-lg leading-8 text-muted-foreground/90">
                Opsfolio CaaS delivers the fastest, most credible path to CRA readiness — building on your SOC 2 strengths and filling the gaps with compliance-as-code.
              </p>
              <div class="mt-10 flex items-center justify-center gap-x-6">
                <a href="/get-started">
                  <Button 
                    size="lg" 
                    variant="default"
                  >
                  Start CRA Readiness Pilot
                  </Button>
                </a>
                <a href="/contact">
                  <Button 
                    size="lg" 
                    variant="outline"
                  >
                  Contact CRA Expert
                  </Button>
                </a>
              </div>
              <div class="mt-8 text-sm text-muted-foreground/80">
                Free assessment • No commitment • EU market access guidance included
              </div>
            </div>
          </div>
        </div>
      </main>
      
      </Layout>
