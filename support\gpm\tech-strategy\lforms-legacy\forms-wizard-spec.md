# Netspective Self‑Service Multi‑form Navigation and Data Entry Wizard

> Technology strategy and engineering spec generated via ChatGPT by <PERSON><PERSON> on August 11, 2025

<mark>Please read this carefully and make significant edits to this specification before starting any code. This was generated by <PERSON><PERSON> using AI and it's directionally correct but may have some AI inaccuracies.</mark>

This spec explains what a “Wizard” is, how it works, and how to build a reusable self‑service multi‑page data entry experience that composes multiple LHC‑Forms into a single guided flow. It’s written for engineers who will implement React and/or pure HTML/Web Components that plug into Astro websites.

---

## 1) What is a wizard and why use it

A wizard is a guided, step‑by‑step user experience that breaks a complex task into smaller pages (steps). Each step gathers a focused set of inputs. The wizard controls navigation (next, previous, jump, finish), validates data, and persists progress so users can stop and resume. This pattern:

* Reduces cognitive load by chunking forms into steps.
* Encourages accuracy through step‑level validation and inline help.
* Enables conditional branching: show the right steps to the right users.
* Improves completion rates with clear progress indicators and resume‑later capabilities.
* Standardizes UX across many multi‑form journeys without baking page content into code.

In our case, each step is an LHC‑Form (LForms) JSON definition. Cross‑form navigation and flow logic live in a separate Wizard Specification JSON so the components remain general‑purpose.

---

## 2) High‑level architecture

* Presentation

  * React component: `<NetspectiveWizard/>` for React projects.
  * Web Component: `<netspective-wizard>` for non‑React or Astro Islands.
  * Shared UI primitives: Stepper, Progress bar, Breadcrumbs (optional), Button group, Error summary, Save/Resume banner.

* LHC‑Forms integration

  * Each step renders an LHC‑Form instance from:

    * Inline JSON, or
    * A referenced JSON file (fetched at runtime).
  * Each LHC‑Form carries hidden metadata to drive the step’s UI (title, description, icon, tags, etc.).

* Orchestration

  * Wizard Specification JSON defines steps, ordering, branching rules, next/prev targets, completion criteria, and cross‑form data mappings.

* State and persistence

  * In‑memory store for current session state.
  * Pluggable persistence: localStorage/sessionStorage by default; optional server sync via REST/GraphQL.
  * Versioning and migration layer for wizard and form definitions.

* Validation

  * Step‑level synchronous validation using LHC‑Forms validation.
  * Cross‑step validation hooks in the wizard layer when needed.

* Security, privacy, compliance

  * No PHI leaves the browser unless an explicit persistence adapter is configured.
  * Secure resume tokens for server‑side persistence.

* Astro integration

  * Ship as ESM modules usable in Astro pages.
  * Prefer Astro Islands for client‑only hydration of the wizard container.

---

## 3) Data contracts

### 3.1 LHC‑Form metadata convention

We need a standard, hidden place inside each form to store UI meta. Use one of these supported encodings:

* FHIR Questionnaire flavor: `item.extension[]` with a Netspective URL namespace.
* LHC‑Forms JSON flavor: an extra object at the top level that LHC‑Forms will ignore but we can read.

Recommended FHIR extension URL namespace:

* `https://schemas.netspective.com/lforms/meta/pageTitle`
* `https://schemas.netspective.com/lforms/meta/pageDescription`
* `https://schemas.netspective.com/lforms/meta/icon`
* `https://schemas.netspective.com/lforms/meta/helpMarkdown`
* `https://schemas.netspective.com/lforms/meta/tags` (array)
* `https://schemas.netspective.com/lforms/meta/stepId` (explicit step id if needed)

Example (FHIR Questionnaire snippet inside an LHC‑Form compatible payload):

```json
{
  "resourceType": "Questionnaire",
  "status": "active",
  "item": [
    {
      "linkId": "root",
      "type": "group",
      "extension": [
        {
          "url": "https://schemas.netspective.com/lforms/meta/pageTitle",
          "valueString": "Patient demographics"
        },
        {
          "url": "https://schemas.netspective.com/lforms/meta/pageDescription",
          "valueString": "Tell us about the patient to personalize subsequent steps."
        },
        {
          "url": "https://schemas.netspective.com/lforms/meta/icon",
          "valueString": "user"
        }
      ]
    }
  ]
}
```

Or if using non‑FHIR LHC‑Forms JSON, include a reserved meta block:

```json
{
  "lformsVersion": "30.0",
  "name": "patient_demographics",
  "_netspectiveMeta": {
    "pageTitle": "Patient demographics",
    "pageDescription": "Tell us about the patient to personalize subsequent steps.",
    "icon": "user",
    "tags": ["intake","required"]
  },
  "items": [ /* standard LHC-Forms items */ ]
}
```

The wizard will look for the metadata in either encoding.

### 3.2 Wizard Specification JSON

This JSON controls the flow across forms. Keep form content in LHC‑Forms; keep routing and orchestration here.

Minimum viable schema:

```json
{
  "wizardId": "patient-intake-v1",
  "version": 1,
  "title": "Patient Intake",
  "description": "A guided intake across multiple forms.",
  "persistence": {
    "strategy": "localStorage",
    "namespace": "netspective.intake",
    "resumeTokenParam": "resume"
  },
  "theming": {
    "variant": "default"
  },
  "steps": [
    {
      "id": "demographics",
      "label": "Demographics",
      "source": {
        "type": "file",
        "path": "/forms/patient_demographics.json"
      },
      "next": "eligibility",
      "prev": null,
      "guard": {
        "precondition": [],
        "validation": "lhc" 
      }
    },
    {
      "id": "eligibility",
      "label": "Eligibility",
      "source": {
        "type": "file",
        "path": "/forms/eligibility_screen.json"
      },
      "branch": [
        {
          "when": "data.demographics.age >= 18",
          "goto": "consent_adult"
        },
        {
          "when": "true",
          "goto": "consent_minor"
        }
      ],
      "prev": "demographics"
    },
    {
      "id": "consent_adult",
      "label": "Consent (Adult)",
      "source": { "type": "file", "path": "/forms/consent_adult.json" },
      "next": "review",
      "prev": "eligibility"
    },
    {
      "id": "consent_minor",
      "label": "Consent (Minor)",
      "source": { "type": "file", "path": "/forms/consent_minor.json" },
      "next": "review",
      "prev": "eligibility"
    },
    {
      "id": "review",
      "label": "Review & submit",
      "source": { "type": "inline", "form": { /* optional LHC-Forms for attestation */ } },
      "next": null,
      "prev": ["consent_adult","consent_minor"]
    }
  ],
  "globalRules": {
    "onSubmit": [
      { "action": "emitEvent", "name": "wizard:submitted" },
      { "action": "persist", "destination": "server", "endpoint": "/api/intake/submit" }
    ]
  }
}
```

Notes

* `branch` is evaluated in order; the first predicate that evaluates true is taken.
* Predicates run in a sandbox against a read‑only projection of collected data.
* `prev` can be a single id or an array when multiple predecessors lead into a common step.
* `guard.validation` values: `lhc` (use the form engine), `none`, or a custom handler id.
* `persistence.strategy`: `none` | `sessionStorage` | `localStorage` | `server`.

### 3.3 Collected data model

At runtime, the wizard accumulates a keyed object:

```ts
type WizardData = {
  [stepId: string]: {
    formId: string;
    questionnaireResponse?: any; // if using FHIR outputs
    values?: Record<string, unknown>; // normalized map for branching
    status: "incomplete" | "complete";
    completedAt?: string;
  };
};
```

* Normalization adapters convert LHC‑Forms responses into a simple `values` map for branching conditions.
* If working in FHIR contexts, store the raw QuestionnaireResponse alongside normalized values.

---

## 4) Component APIs

### 4.1 React

```tsx
<NetspectiveWizard
  forms={[ { type: "file", path: "/forms/a.json" }, { type: "inline", form: {...} } ]}
  wizardSpec={wizardSpecJson}
  initialStepId="demographics"
  onEvent={(evt) => { /* analytics, error reporting */ }}
  persistenceAdapter={customAdapter?}
  theme="default"
/>
```

Props

* `forms`: optional preloaded forms; steps can also reference files by path.
* `wizardSpec`: required orchestration JSON.
* `initialStepId`: optional override; otherwise first step.
* `onEvent(evt)`: emits lifecycle events (examples below).
* `persistenceAdapter`: optional pluggable client or client‑server adapter.
* `theme`: selects CSS variables/tokens.

Events

* `wizard:initialized`, `wizard:navigate`, `wizard:validationFailed`, `step:saved`, `wizard:submitted`, `wizard:resumed`, `wizard:error`.

### 4.2 Web Component

```html
<netspective-wizard
  wizard-spec-url="/wizards/patient-intake.json"
  initial-step-id="demographics">
</netspective-wizard>
<script>
  const el = document.querySelector('netspective-wizard');
  el.addEventListener('wizard:submitted', (e) => console.log(e.detail));
  // Programmatic control
  el.next(); el.prev(); el.goto('review'); el.save(); el.resume('token');
</script>
```

---

## 5) Flow control and algorithms

* Initialization

  * Load wizard spec; validate against JSON Schema.
  * Preload first step’s form JSON; extract and cache step metadata.
  * Initialize state and persistence context.

* Navigation

  * prev: go to declared predecessor; if array, use the actual predecessor tracked in history.
  * next: if a `branch` array exists, evaluate predicates sequentially until one matches; otherwise use `next`.
  * History stack tracks visited steps for accurate back behavior across branches.
  * Direct navigation (jump) is allowed only to completed or currently reachable steps unless `allowFreeNav` flag is set.

* Validation

  * On next/finish, run LHC‑Forms validation; block navigation on errors and show a sticky error summary plus inline field errors.
  * Optional cross‑step validators can be declared in `globalRules` and run before submit.

* Data normalization

  * After a step saves, normalize the response into a flat map `values` using a mapping adapter:

    * Key = stable `linkId` or item name.
    * Value = primitive or array of primitives.
  * These maps feed branching conditions, progress calculation, and cross‑form prefill.

* Prefill and cross‑form data mapping (optional)

  * Wizard spec can include mappings like `steps.B.inputs.addressLine1 <- steps.A.values.home_address.line1`.
  * When loading step B, apply prefill before render.

* Persistence

  * Default: save on every successful next, previous, or explicit save.
  * localStorage keys: `${namespace}:${wizardId}:${resumeToken}`.
  * Server mode: use a resumable session token returned by backend.

* Submit

  * Collect all QuestionnaireResponses and normalized values.
  * Emit `wizard:submitted` event and run configured actions (e.g., POST bundle to server).

---

## 6) UI/UX requirements

* Step header reads title/description from the LHC‑Form metadata.
* Progress bar shows percent complete; calculation can be:

  * Simple: completed steps / total reachable steps.
  * Weighted: steps can declare `weight` in wizard spec.
* Save and resume affordances:

  * Persistent banner with current save status.
  * Copyable resume link if server persistence returns a token.
* Error handling:

  * Inline field errors from LHC‑Forms.
  * Top error summary listing clickable anchors.
* Accessibility:

  * Keyboard navigation for next/prev.
  * ARIA roles for stepper and progress.
  * Announce validation errors via live regions.
* Internationalization:

  * All labels and messages externalized.
  * Support LHC‑Forms localization if present; wizard chrome strings via i18n resources.
* Theming:

  * CSS variables for spacing, typography, colors.
  * Light/dark ready.

---

## 7) Implementation plan

Phase 1: Foundations

* Define JSON Schemas for Wizard Specification and `_netspectiveMeta`.
* Build a TypeScript core:

  * Spec loader and validator (Ajv).
  * State machine for steps, history, branching.
  * Normalization adapter for LHC‑Forms outputs.
  * Persistence adapters: memory, sessionStorage, localStorage.
* Build React `<NetspectiveWizard/>` with a StepShell that mounts an LHC‑Forms renderer.
* Build basic UI primitives: stepper, progress bar, button row, error summary.
* Write unit tests: branching logic, persistence, normalization.

Phase 2: Web Component and Astro

* Wrap the core in a Web Component for non‑React consumers.
* Create Astro examples:

  * Astro Island using React component.
  * Astro page using Web Component only.

Phase 3: Advanced features

* Server persistence adapter with resume tokens.
* Cross‑form prefill mapping engine.
* Cross‑step validators and completion rules.
* Analytics hooks.
* Export bundles:

  * FHIR QuestionnaireResponse bundle builder.
  * CSV/JSON exports for non‑FHIR projects.

---

## 8) Integration with LHC‑Forms

* Rendering: use the official LHC‑Forms renderer inside the step container.
* Loading: accept either inline form JSON or a path; caching layer avoids refetching across back/next.
* Metadata: first try FHIR extensions on the top group item; fallback to `_netspectiveMeta`.
* Outputs:

  * Ask LHC‑Forms for its native output (e.g., QuestionnaireResponse).
  * Normalize to `values` for wizard logic.

---

## 9) Error handling and diagnostics

* Spec errors: show a developer overlay in non‑prod builds with details and a link to the offending schema path.
* Runtime errors: emit `wizard:error` with a stable code; show user‑safe message and log details to console in dev.
* Form load failures: retry with exponential backoff; graceful message with “Try again” control.

---

## 10) Security and privacy

* Default to client‑only persistence; server sync must be explicitly configured.
* If server sync is enabled:

  * Use short‑lived signed resume tokens.
  * TLS only; no data in URLs beyond opaque token.
  * Support data redaction rules in the adapter for analytics.

---

## 11) Performance

* Lazy load forms only when needed.
* Preload likely next steps after idle using `requestIdleCallback`.
* Memoize normalization results per step version.
* Avoid re‑mounting the form DOM on simple data writes; interact through LHC‑Forms APIs.

---

## 12) Testing strategy

* Unit tests

  * Branch evaluation with various predicates.
  * Persistence round‑trip in all strategies.
  * Normalization correctness against sample forms.
* Component tests

  * Navigation, progress, error summary accessibility.
* E2E tests (Playwright/Cypress)

  * Happy path, branching path, resume‑later, validation failures.
* Contract tests

  * Validate sample wizard specs against JSON Schema in CI.

---

## 13) Example: minimal working set

Files

* `/wizards/patient-intake.json` — Wizard Specification JSON (as above).
* `/forms/patient_demographics.json` — LHC‑Form with metadata.
* `/forms/eligibility_screen.json` — LHC‑Form with branching answers.
* `/forms/consent_adult.json`, `/forms/consent_minor.json` — LHC‑Forms.

Astro usage (React Island)

```astro
---
import NetspectiveWizard from "netspective-wizard/react";
import wizardSpec from "../wizards/patient-intake.json";
---
<section>
  <NetspectiveWizard wizardSpec={wizardSpec} />
</section>
```

Astro usage (Web Component)

```astro
---
---
<section>
  <netspective-wizard wizard-spec-url="/wizards/patient-intake.json"></netspective-wizard>
  <script type="module" src="/assets/netspective-wizard-web.js"></script>
</section>
```

---

## 14) Extensibility roadmap

* Plugin system for:

  * Custom predicate functions (e.g., clinical calculators).
  * Custom validators and cross‑step rules.
  * Custom actions on submit (FHIR server, S3, email).
* Telemetry hooks for analytics and funnel reports.
* Visual Wizard Designer:

  * Graph editor to author steps and branches.
  * Live preview by mounting the same runtime engine.

---

## 15) Deliverables checklist for engineers

* JSON Schemas: Wizard Spec, `_netspectiveMeta`.
* TypeScript core library with 90%+ coverage on logic.
* React component and Web Component wrappers.
* Example Astro project with both integration modes.
* Sample wizards and forms demonstrating:

  * Linear flow
  * Conditional branching
  * Cross‑form prefill
  * Client‑only persistence and server persistence
* Dev guide:

  * How to author LHC‑Form metadata.
  * How to write branch predicates safely.
  * How to add a new persistence adapter.
  * Accessibility and i18n checklist.

This gives you a clean separation of concerns: LHC‑Forms own the per‑page content and validation, while the Wizard Specification owns orchestration and navigation. The components remain general‑purpose and portable across Astro, React, or vanilla web projects.
