Here’s a **ready-to-copy master prompt** you can paste into your AI coding
assistant to generate a full SQLPage analytics UI. It assumes:

- raw logs ultimately live in a table called **`uniform_resource`**
- you’ve already created **SQL views** that wrap/normalize those JSON/JSONL rows
  (no need to parse <PERSON><PERSON><PERSON> in SQLPage files)
- the UI should be SQL-first, with pages for Overview, Traffic, Funnel,
  Anomalies, and Explorer

---

## Master Prompt (copy everything between the lines)

You are an expert SQLPage + analytics engineer. Generate a _complete_ SQLPage
app for server-side funnel analytics. The data comes from normalized SQL
**views** that wrap rows from a base table named `uniform_resource` (which
stores the original JSON/JSONL logs). You do **not** need to parse JSON in
SQLPage; assume the views expose clean columns.

I have a SQLite table named `uniform_resource` with the schema below:

```sql
CREATE TABLE "uniform_resource" (
    "uniform_resource_id" VARCHAR PRIMARY KEY NOT NULL,
    "device_id" VARCHAR NOT NULL,
    "ingest_session_id" VARCHAR NOT NULL,
    "ingest_fs_path_id" VARCHAR,
    "ingest_session_imap_acct_folder_message" VARCHAR,
    "ingest_issue_acct_project_id" VARCHAR,
    "uri" TEXT NOT NULL,
    "content_digest" TEXT NOT NULL,
    "content" BLOB,
    "nature" TEXT,
    "size_bytes" INTEGER,
    "last_modified_at" TIMESTAMPTZ,
    "content_fm_body_attrs" TEXT CHECK(json_valid(content_fm_body_attrs) OR content_fm_body_attrs IS NULL),
    "frontmatter" TEXT CHECK(json_valid(frontmatter) OR frontmatter IS NULL),
    "elaboration" TEXT CHECK(json_valid(elaboration) OR elaboration IS NULL),
    "created_at" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT DEFAULT 'UNKNOWN',
    "updated_at" TIMESTAMPTZ,
    "updated_by" TEXT,
    "deleted_at" TIMESTAMPTZ,
    "deleted_by" TEXT,
    "activity_log" TEXT,
    FOREIGN KEY("device_id") REFERENCES "device"("device_id"),
    FOREIGN KEY("ingest_session_id") REFERENCES "ur_ingest_session"("ur_ingest_session_id"),
    FOREIGN KEY("ingest_fs_path_id") REFERENCES "ur_ingest_session_fs_path"("ur_ingest_session_fs_path_id"),
    FOREIGN KEY("ingest_session_imap_acct_folder_message") REFERENCES "ur_ingest_session_imap_acct_folder_message"("ur_ingest_session_imap_acct_folder_message_id"),
    FOREIGN KEY("ingest_issue_acct_project_id") REFERENCES "ur_ingest_session_plm_acct_project"("ur_ingest_session_plm_acct_project_id"),
    UNIQUE("device_id", "content_digest", "uri", "size_bytes")
);
```

The content field stores ingested results from tools like openobserve.

### Context & Objectives

- Build a lightweight analytics UI using **SQLPage** to analyze Opsfolio traffic
  and conversions from server logs (Nginx + Astro).
- Assume the normalized views already exist and are named:

  - `opsfolio.web_traffic` — normalized Nginx access events
  - `opsfolio.astro_events` — normalized app/feature events from Astro
- Each view is **time-partitioned** on `ts TIMESTAMP` and includes key fields:

  - Common: `ts`, `request_path`, `status_code`, `referer`, `user_agent`, `env`,
    `service`, `source`
  - Traffic: `session_id`, `remote_addr`, `body_bytes_sent`, (optionally
    `http_method`)
  - Events: `session_id`, `user_id`, `event`, `duration_ms`, `metadata`
- Goal: Give marketing/eng teams a **server-derived baseline** for traffic,
  attribution, funnel, and anomaly analysis—independent of GA4.

### Requirements

- Output a complete SQLPage site composed of **multiple `.sql` pages** +
  (optional) a shared `_layout.sql`.
- Use SQLPage comment metadata for titles and menus, e.g.:

  ```sql
  -- title: <Page Title>
  -- menu: <Menu Label>
  ```
- Each page must be **secure by default** (assume reverse proxy auth/SSO) but we
  will not implement auth in SQL here.

### Global UX Conventions

- All pages accept optional URL parameters:

  - `from` (ISO timestamp or date; default: now() - 7 days)
  - `to` (ISO timestamp or date; default: now())
  - `env` (default: all)
  - `service` (default: all)
  - `path_like` (default: null; ILIKE filter on `request_path`)
- For each page, begin with a “Filters” panel (form component) that preserves
  current selections and adds an **apply** button.
- Use **KPI** components for headline numbers; **table** components for tabular
  output; optionally include **chart** components if supported by SQLPage
  (bar/line over time).

### Deliverables (Files & Content)

Create the following files. Use portable ANSI SQL (Postgres-like). Do not depend
on JSON parsing in these pages.

#### 1) `_layout.sql` (optional but preferred)

- Shared top navigation: Overview, Traffic, Funnel, Anomalies, Explorer.
- Show current filter range in the header.
- Provide links that preserve query parameters (`from`, `to`, `env`, `service`,
  `path_like`).

#### 2) `index.sql` (Overview)

- **Title/Menu:** “Opsfolio Analytics Overview” / “Overview”
- **Filters form** (from/to/env/service/path\_like).
- KPIs (last N days based on filters):

  - Sessions (distinct `session_id` from `opsfolio.web_traffic`)
  - Unique users (distinct `COALESCE(user_id, session_id)` from
    `opsfolio.astro_events`)
  - Requests (count rows in `opsfolio.web_traffic` with `status_code` 200–299)
  - Error rate (share of 4xx/5xx in `opsfolio.web_traffic`)
- Top tables:

  - **Top Pages** (request\_path, unique\_sessions, avg\_status\_2xx\_rate)
    limited to 20.
  - **Top Referrers** (referer, sessions, est\_bounce\_rate). _Bounce proxy:_
    for first hit of a session, if no subsequent hit exists in window, count as
    bounce.
- Small time-series chart:

  - Requests per day/hour with unique sessions (use `date_trunc` based on total
    range length).

#### 3) `traffic.sql` (Traffic & Attribution)

- **Title/Menu:** “Traffic” / “Traffic”
- **Filters form** and show a compact **traffic summary**:

  - Requests (2xx, 3xx, 4xx, 5xx), unique sessions, data transferred (sum
    `body_bytes_sent`).
- Tables:

  - **Top Pages (7/14d by filters)** with unique sessions, requests, 2xx rate.
  - **Top Referrers** (sessions, 2xx rate, est\_bounce\_rate).
  - **User Agents** (family breakdown via simple pattern buckets; if not
    available, show top raw UAs).
- Time series:

  - By hour/day: unique sessions + total requests.
- Optional: a **Path filter** box (sets/updates `path_like`).

#### 4) `funnel.sql` (Route Funnel)

- **Title/Menu:** “Funnel” / “Funnel”
- **Filters form**.
- Assume canonical stages: `/` → `/get-started` → `/contact` → (optional)
  `/signup` if present in events.
- Produce a single-row **table** with counts:

  - Sessions total (web\_traffic)
  - Reached `/`
  - Reached `/get-started`
  - Reached `/contact`
  - Reached `/signup` (if `opsfolio.astro_events.event='signup'` or similar)
- Also output a **per-stage conversion** table:

  - Stage, Sessions, % of previous stage
- Include a table of **Top Session Paths** (first 10 sequences) built from
  concatenating top 5 paths per session (truncate sequences >5).

#### 5) `anomalies.sql` (Spike & Bot Heuristics)

- **Title/Menu:** “Anomalies” / “Anomalies”
- **Filters form**.
- **Sept-5-style spike finder:**

  - Group by hour (or day) with requests + unique sessions.
  - Flag time buckets above a z-score threshold (mean/stddev of bucket counts in
    window).
- **Bot heuristics:**

  - `user_agent ILIKE '%bot%' OR '%crawler%'` ratio by hour/day.
  - Sessions that only ever hit `/` and nothing else (“single-page-only
    sessions”).
  - High-frequency IPs: `remote_addr` with unusually high hits in short windows.
- Output tables for each heuristic plus a trend chart.

#### 6) `explorer.sql` (Ad-hoc Explorer)

- **Title/Menu:** “Explorer” / “Explorer”
- **Filters form**.
- Two tabs (logical sections):

  - **Web Traffic (opsfolio.web\_traffic)**: paginated table with columns (`ts`,
    `session_id`, `request_path`, `status_code`, `referer`, `user_agent`, `env`,
    `service`, `source`, `remote_addr`, `body_bytes_sent`)
  - **Astro Events (opsfolio.astro\_events)**: paginated table with columns
    (`ts`, `user_id`, `session_id`, `event`, `request_path`, `status_code`,
    `duration_ms`, `env`, `service`, `source`)
- Provide simple **ORDER BY ts DESC LIMIT 200** defaults, with pagination params
  `page`, `pagesize` (defaults: 1, 200).

### Filtering Logic (apply to all queries)

For every query on each page, apply the following _optional_ filters when
provided:

```sql
WHERE ts >= coalesce(:from::timestamp, now() - interval '7 days')
  AND ts <  coalesce(:to::timestamp, now())
  AND (:env IS NULL OR env = :env)
  AND (:service IS NULL OR service = :service)
  AND (:path_like IS NULL OR request_path ILIKE '%' || :path_like || '%')
```

- For referrer-focused queries, `request_path` filter still applies; if not
  appropriate, guard it with the same conditional pattern.
- Ensure `status_code BETWEEN 200 AND 299` where needed (e.g., success-only
  metrics).

### KPI & Computation Details

- **Sessions:** `COUNT(DISTINCT session_id)` on `opsfolio.web_traffic`.
- **Unique users:** `COUNT(DISTINCT COALESCE(user_id, session_id))` on
  `opsfolio.astro_events`.
- **Error rate:**
  `SUM(CASE WHEN status_code >= 400 THEN 1 ELSE 0 END)::float / COUNT(*)`.
- **Est. bounce rate (by referrer):**

  - For each session, find the **first** hit (`MIN(ts)`).
  - If **no** subsequent hit exists **after** the first hit within the time
    window, count as bounce.
  - Bounce rate = `bounces / sessions`.

### SQLPage Components

Use SQLPage components like:

- `KPI` for headline metrics.
- `table` for lists.
- `chart` (if supported) for time series (line chart with `ts_bucket` + metric).
- `form` for filters with fields:

  - date/time inputs for `from`, `to`
  - select for `env` (prod, staging, all)
  - select for `service` (nginx, astro, all)
  - text for `path_like`
  - submit button

### Performance

- Use `date_trunc('hour' | 'day', ts)` depending on window length:

  - If `to - from > 21 days` → `day`; else `hour`.
- Add `LIMIT` for top lists (10–50).
- Keep queries index-friendly (filters on `ts`, `request_path`, `session_id`,
  `status_code`).

### Output Format

- Return the **actual `.sql` file contents** for each file (`_layout.sql`,
  `index.sql`, `traffic.sql`, `funnel.sql`, `anomalies.sql`, `explorer.sql`) in
  code blocks.
- Each file must be **standalone** (no prose inside the code block) and runnable
  in SQLPage.
- Ensure all queries include the filtering logic above and won’t error if
  parameters are missing.

### Assumptions Recap

- Base raw data is in `uniform_resource` but you don’t need to read it directly.
- Normalized views already exist: `opsfolio.web_traffic`,
  `opsfolio.astro_events`.
- SQLPage connects to the same database exposing these views.
- We want clean, production-ready SQLPage code.

Generate the files now.
