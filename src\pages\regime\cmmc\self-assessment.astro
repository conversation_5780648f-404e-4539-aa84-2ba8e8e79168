---
import Layout from "../../../layouts/Layout.astro";
import LHCForm from "@/components/LForm";
import LformMenu from "@/components/LformMenu.astro"
const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Regime',"aria-disabled": true ,
  },
  {
    index: "2", text: "CMMC","aria-disabled": true, 
  },
  {
    index: "3", text: "Self Assessment" 
  },
];
---

<Layout title="Opsfolio - Compliance as a Service | SOC2, HIPAA, ISO Certification"
        description="Get SOC2, HIPAA, ISO certified fast with expert-guided compliance. 98% audit pass rate, 60% faster than DIY. Your Compliance System of Record."
        keywords="CMMC Level 1, CMMC Level 2, CMMC Level 3, DoD contracts, cybersecurity maturity model certification, controlled unclassified information, defense industrial base compliance"
        url="/regime/cmmc/"
        breadcrumbLinks={breadcrumbLinks}>
        <div class="flex min-h-screen">
                <!-- <LformMenu /> -->
                <main class="flex-1 p-6 mb-4">
                        <LHCForm client:only="react" /> 
                </main>
        </div>
</Layout>
