---
import Layout from "../../layouts/Layout.astro";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Brain, Zap, Shield, CheckCircle, Timer, Users, ArrowRight, Bot, Target, Layers } from "lucide-react";

  const features = [
    {
      icon: Brain,
      title: "AI-Powered Gap Analysis",
      description: "Our ML algorithms analyze your current state against audit requirements, predicting potential findings before they happen."
    },
    {
      icon: Bot,
      title: "Automated Evidence Assembly",
      description: "Smart evidence orchestration that automatically maps your existing documentation to audit requirements."
    },
    {
      icon: Zap,
      title: "Real-Time Readiness Score",
      description: "Dynamic scoring system that tracks your audit readiness in real-time across all compliance domains."
    },
    {
      icon: Target,
      title: "Predictive Risk Modeling",
      description: "Advanced analytics identify high-risk areas and provide targeted remediation recommendations."
    },
    {
      icon: Layers,
      title: "Multi-Dimensional Testing",
      description: "Comprehensive testing framework covering technical, process, and organizational controls."
    },
    {
      icon: Timer,
      title: "Audit Simulation Engine",
      description: "Full-scale audit simulations with realistic scenarios to stress-test your readiness."
    }
  ];

  const process = [
    {
      step: "01",
      title: "Intelligent Discovery",
      description: "AI-driven assessment of your current compliance posture across all systems and processes."
    },
    {
      step: "02", 
      title: "Gap Prediction",
      description: "Machine learning models predict potential audit findings before they occur."
    },
    {
      step: "03",
      title: "Automated Remediation",
      description: "Smart workflow orchestration guides you through targeted improvements."
    },
    {
      step: "04",
      title: "Continuous Validation",
      description: "Real-time monitoring ensures sustained compliance readiness."
    }
  ];

   const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Task',"aria-disabled": true ,
  },
  {
    index: "2", text: "Pass Audit","aria-disabled": true 
  },
];

---
<Layout title="Opsfolio - Compliance as a Service | SOC2, HIPAA, ISO Certification"
  description="Get SOC2, HIPAA, ISO certified fast with expert-guided compliance. 98% audit pass rate, 60% faster than DIY. Your Compliance System of Record."
  keywords="Evidence, compliance, SOC2, HIPAA, ISO 27001, cybersecurity, audit, certification"
  url="/task/audit-prep/"
  breadcrumbLinks={breadcrumbLinks}>

  {/* Hero Section */}
      <section class="relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5 pt-16 pb-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-3xl text-center">
            <Badge variant="outline" className="mb-4">AI-Powered Audit Preparation</Badge>
            <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
              Zero-Finding Audit Guarantee
            </h1>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Revolutionary AI-driven audit preparation that predicts, prevents, and eliminates 
              audit findings before they happen. The industry's first predictive compliance platform.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
                <a href="/get-started">
                  <Button size="lg">Get Audit Guarantee<ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </a>
            </div>
          </div>
        </div>
      </section>

      {/* Features Grid */}
      <section class="pt-20 pb-20 ">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center mb-16">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Next-Generation Audit Technology
            </h2>
            <p class="mt-4 text-lg text-muted-foreground">
              Advanced AI and machine learning capabilities that revolutionize audit preparation
            </p>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="border-border/50 hover:border-primary/50 transition-colors">
                <CardHeader>
                  <feature.icon className="h-8 w-8 text-primary mb-2" />
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section class="pt-20 pb-20 bg-muted">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center mb-16">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              The Predictive Process
            </h2>
            <p class="mt-4 text-lg text-muted-foreground">
              Our four-phase methodology ensures audit success through intelligent automation
            </p>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {process.map((phase) => (
              <div class="text-center">
                <div class="w-16 h-16 bg-primary/10 text-primary rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">
                  {phase.step}
                </div>
                <h3 class="text-xl font-semibold mb-3">{phase.title}</h3>
                <p class="text-muted-foreground">{phase.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section class="pt-20 pb-20 ">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Ready for Your Next Audit?
            </h2>
            <p class="mt-4 text-lg text-muted-foreground">
              Join leading organizations that trust our AI-powered audit preparation
            </p>
            <div class="mt-8">
                <a href="/get-started">
                  <Button size="lg">Start Free Assessment<ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </a>
            </div>
          </div>
        </div>
      </section>
</Layout>