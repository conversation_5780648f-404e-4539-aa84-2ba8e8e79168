// src/utils/getDynamicSitemapUrls.ts
import fs from "fs";
import path from "path";

export function getDynamicSitemapUrls(siteUrl: string) {
  const blogDir = path.join(process.cwd(), "src/content/blog");
  const caseStudyDir = path.join(
    process.cwd(),
    "src/content/resources/case-studies",
  );

  const slugify = (filename: string): string => {
    return filename
      .replace(/\.mdx?$/, "") // remove extension
      .toLowerCase() // convert to lowercase
      .replace(/['"]/g, "") // remove quotes
      .replace(/[^a-z0-9\-]+/g, "-") // replace non-alphanumeric (except hyphen) with hyphen
      .replace(/--+/g, "--") // preserve double hyphens
      .replace(/^-+|-+$/g, ""); // trim leading/trailing hyphens
  };

  const blogUrls = fs
    .readdirSync(blogDir)
    .filter((file) => file.endsWith(".md") || file.endsWith(".mdx"))
    .map((file) => `${siteUrl}/blog/${slugify(file)}`);

  const caseStudyUrls = fs
    .readdirSync(caseStudyDir)
    .filter((file) => file.endsWith(".md") || file.endsWith(".mdx"))
    .map((file) => `${siteUrl}/resources/case-studies/${slugify(file)}`);

  return [...blogUrls, ...caseStudyUrls];
}
