# Opsfolio Enterprise Assets Assessment (EAA) – Pen Test Style Assessments

This document describes the **initial manual workflow** for conducting EAA pen
test–style assessments using Opsfolio’s existing tooling.\
The process starts as a lightweight manual loop and will evolve into a fully
automated pipeline.

---

## Step 1 — Capture Assessment Requests (Lead Magnets)

- Assessment requests are captured via **Opsfolio.com forms** (LHC or React).
- Each form submission is:
  - Transformed into a **structured Zod JSON object**.
  - Accompanied by a **JSON Schema** representing its shape (to preserve request
    contracts).
- Transactional email handling:
  - JSON attachments are emailed to a designated Opsfolio transactional address.
  - **Action:** Confirm with <PERSON><PERSON> which transactional email address should be
    used.
  - Sales teams (Ravi + sales) must have visibility into all email traffic.

---

## Step 2 — Send via Email (Acting as Message Queue)

- The JSON document created from the form submission is attached to an email.
- Email serves as the **initial message queue**.
- At scale, we will migrate to a proper enterprise **message queue or service
  bus**.

---

## Step 3 — Manual Monitoring of Inbox

- A **human operator** monitors the inbox for new assessment requests.
- Initially, this can be handled by a **junior Security Engineer** (great for
  onboarding).
- **Proposed assignment:** <PERSON><PERSON>, unless <PERSON><PERSON> or <PERSON><PERSON><PERSON> suggest otherwise.
- <PERSON><PERSON> will also update and maintain the **RUNME tooling infrastructure**.

---

## Step 4 — Launching the Assessment

When a new JSON request is received:

1. Place the JSON into the `/session` volume of the **Opsfolio EAA Docker
   container**.\
   _Alternative:_ parse JSON into environment variables matching the runbook
   expectations.
2. Run the **Docker Compose / container for EAA**.
   - The container executes the assessment based on the provided scope and
     settings.
3. Automation options can be discussed for streamlining the handoff.

---

## Step 5 — Store Assessment Results

- Upon completion, the container outputs a **Surveilr RSSD database**.
- Store the RSSD in a known **Opsfolio.com location**.
- Launch either:
  - **Surveilr Web-UI**, or
  - **SQLPage**\
    to make results immediately queryable and reviewable.

---

## Step 6 — Expose Results in Opsfolio

- Use **Astro’s proxy pass** to embed the results view (Web-UI or SQLPage) into:
  - Tenant dashboards.
  - User session pages.
- This ensures customers can see their results in context as soon as they are
  available.

---

## Important Notes

- This phase is **entirely manual**:
  - JSON requests received by email.
  - Human operator places JSON into container session volume.
  - Human operator publishes results into Opsfolio.com.
- This approach is acceptable because:
  - It validates each moving part with real customers.
  - It allows Security Engineers to gain hands-on experience.

---

## Future Automation

- Once the workflow is proven, orchestration will be automated.
- Candidates for automation:
  - **CI/CD tools** (GitHub Actions, Dagger, etc.).
  - Automated inbox monitoring and JSON parsing.
  - Automatic container launch and results publication.
- Goal: Remove human intervention, leaving oversight only.

---

## Summary

- **Email = Message Queue** (for now).
- **Human-in-the-loop** execution ensures operational learning.
- Results feed directly back into Opsfolio.com for use in:
  - TEM dashboards.
  - POA&M workflows.
- **Next step:** Gradually move orchestration into pipelines once validated.

## Opsfolio TEM Flow Diagram

![Opsfolio TEM Flow Diagram](./eaa-tem-flow-diagram.png)

## Opsfolio TEM Component Diagram

![Opsfolio TEM Component Diagram](./eaa-tem-component-diagram.png)
