---
import Layout from "../../../../layouts/Layout.astro";
import { Badge } from "@/components/ui/badge";
import ControlsExplorer from "@/components/ControlsExplorer";
import { Clock } from "lucide-react";
import { getCmmcControlList } from "../../../../services/auditServices";
import type {
ControlsRegimeType,
TransformedArrayType,
ControlRegimEnvironment,
ControlsType,
} from "../../../../services/auditTypes";
import * as cmmcHeroContent from "@/content/control-explorer/cmmc.md";


const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Regime',"aria-disabled": true ,
  },
  {
    index: "2", text: "CMMC","aria-disabled": true 
  },
];

//cmmc 1 cntrl dynamic

const regimeId = 7;
let tenantId = '239518031485599747';
tenantId = tenantId ? tenantId : "";


// --- Type I ---
const cmmc_Type1 = 12;
let auditControlsType1: ControlsType[] = [];
try {
  auditControlsType1 = await getCmmcControlList(cmmc_Type1, tenantId);
} catch (err) {
  console.error("Failed to load Type I controls:", err);
  auditControlsType1 = [];
}
let auditControlType1 = auditControlsType1.map(item => ({ ...item }));
const transformedArrayType1: TransformedArrayType[] = [];
let currentCommonCriteriaType1: string = "";
auditControlType1.forEach((control) => {
  const typedControl = control as ControlsRegimeType;
  if (typedControl["common_criteria"] !== currentCommonCriteriaType1) {
    currentCommonCriteriaType1 = typedControl["common_criteria"];
    transformedArrayType1.push({
      common_criteria: currentCommonCriteriaType1,
      controls: [typedControl],
    });
  } else {
    transformedArrayType1[transformedArrayType1.length - 1].controls.push(typedControl);
  }
});
const ControlDataType1: ControlRegimEnvironment[] = [];
for (const item of transformedArrayType1) {
  const title = item["common_criteria"];
  const controlsForTitle = await Promise.all(
    item.controls.map(async (control) => ({
      controlCode: control["control_code"],
      controlQuestion: control["question"],
      controlIdentifier: control["control_identifier"],
      defaultSlug: control["slug"] ? control["slug"] : "",
    })),
  );
  ControlDataType1.push({
    title: title,
    controls: controlsForTitle,
  });
}

// --- Type II ---
const cmmc_Type2 = 13;
let auditControlsType2: ControlsType[] = [];
try {
  auditControlsType2 = await getCmmcControlList(cmmc_Type2, tenantId);
} catch (err) {
  console.error("Failed to load Type II controls:", err);
  auditControlsType2 = [];
}
let auditControlType2 = auditControlsType2.map(item => ({ ...item }));
const transformedArrayType2: TransformedArrayType[] = [];
let currentCommonCriteriaType2: string = "";
auditControlType2.forEach((control) => {
  const typedControl = control as ControlsRegimeType;
  if (typedControl["common_criteria"] !== currentCommonCriteriaType2) {
    currentCommonCriteriaType2 = typedControl["common_criteria"];
    transformedArrayType2.push({
      common_criteria: currentCommonCriteriaType2,
      controls: [typedControl],
    });
  } else {
    transformedArrayType2[transformedArrayType2.length - 1].controls.push(typedControl);
  }
});
const ControlDataType2: ControlRegimEnvironment[] = [];
for (const item of transformedArrayType2) {
  const title = item["common_criteria"];
  const controlsForTitle = await Promise.all(
    item.controls.map(async (control) => ({
      controlCode: control["control_code"],
      controlQuestion: control["question"],
      controlIdentifier: control["control_identifier"],
      defaultSlug: control["slug"] ? control["slug"] : "",
    })),
  );
  ControlDataType2.push({
    title: title,
    controls: controlsForTitle,
  });
}

// --- Type III ---
const cmmc_Type3 = 14;
let auditControlsType3: ControlsType[] = [];
try {
  auditControlsType3 = await getCmmcControlList( cmmc_Type3, tenantId);
} catch (err) {
  console.error("Failed to load Type II controls:", err);
  auditControlsType3 = [];
}
let auditControlType3 = auditControlsType3.map(item => ({ ...item }));
const transformedArrayType3: TransformedArrayType[] = [];
let currentCommonCriteriaType3: string = "";
auditControlType3.forEach((control) => {
  const typedControl = control as ControlsRegimeType;
  if (typedControl["common_criteria"] !== currentCommonCriteriaType3) {
    currentCommonCriteriaType3 = typedControl["common_criteria"];
    transformedArrayType3.push({
      common_criteria: currentCommonCriteriaType3,
      controls: [typedControl],
    });
  } else {
    transformedArrayType3[transformedArrayType3.length - 1].controls.push(typedControl);
  }
});
const ControlDataType3: ControlRegimEnvironment[] = [];
for (const item of transformedArrayType3) {
  const title = item["common_criteria"];
  const controlsForTitle = await Promise.all(
    item.controls.map(async (control) => ({
      controlCode: control["control_code"],
      controlQuestion: control["question"],
      controlIdentifier: control["control_identifier"],
      defaultSlug: control["slug"] ? control["slug"] : "",
    })),
  );
  ControlDataType3.push({
    title: title,
    controls: controlsForTitle,
  });
}


 const hasRealData =
  Array.isArray(ControlDataType1) &&
  ControlDataType1.some(section => Array.isArray(section.controls) && section.controls.length > 0);
 //console.log("auditControls", ControlData,hasRealData);
---

<Layout
  title="CMMC Controls Reference - Complete Compliance Guide"
  description="The Cybersecurity Maturity Model Certification (CMMC) program aligns with the information security requirements of the U.S. This initiative aims to ensure the protection of sensitive unclassified information shared between the Department and its contractors and subcontractors. The program enhances the Department's confidence that contractors and subcontractors adhere to cybersecurity requirements applicable to acquisition programs and systems handling controlled unclassified information"
  keywords="CMMC controls, Trust Services Criteria, security controls, availability controls"
  url="https://opsfolio.com/regime/cmmc/controls"
  breadcrumbLinks={breadcrumbLinks}
>
  {/* Hero Section */}
  <section
    class="relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5 pt-16 pb-20"
  >
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
      <div class="mx-auto max-w-4xl text-center">
        <Badge variant="outline" className="mb-4">
          {cmmcHeroContent.frontmatter.badge}
        </Badge>
        <h1
          class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl"
        >
          {cmmcHeroContent.frontmatter.title}
        </h1>
        <p
          class="mt-6 text-lg leading-8 text-muted-foreground max-w-3xl mx-auto"
        >
          {cmmcHeroContent.frontmatter.description}
        </p>
      </div>
    </div>
  </section>

  {hasRealData ? (
    <ControlsExplorer 
      controlData={ControlDataType1}
      controlDataType2={ControlDataType2}
      controlDataType3={ControlDataType3}
      controlPageUrl=""
      sessionId={Number(regimeId)}
      auditTypeId={Number()} 
      controlType="cmmc"
      client:only="react" />
  ) : "<div className=text-center py-20 text-muted-foreground>No controls data available.</div>"
  }

  

  {/* Last Updated */}
  <section class="py-8 border-t bg-muted/30">
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
      <div class="mx-auto max-w-4xl text-center">
        <div
          class="flex items-center justify-center gap-2 text-sm text-muted-foreground"
        >
          <Clock className="h-4 w-4" />
          <span>Last Updated: September 3, 2025</span>
        </div>
      </div>
    </div>
  </section>
</Layout>