---
import Layout from "../../../layouts/Layout.astro";
import { Badge } from "@/components/ui/badge";
import { Heart, Shield, Building2, Clock, CheckCircle, ArrowRight } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";


const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Regime',"aria-disabled": true ,
  },
  {
    index: "2", text: "HITRUST","aria-disabled": true 
  },
];
---
<Layout title="Opsfolio - Compliance as a Service | SOC2, HIPAA, ISO Certification"
  description="Get SOC2, HIPAA, ISO certified fast with expert-guided compliance. 98% audit pass rate, 60% faster than DIY. Your Compliance System of Record."
  keywords="Hitrust, compliance, SOC2, HIPAA, ISO 27001, cybersecurity, audit, certification"
  url="/regime/hitrust/"
  breadcrumbLinks={breadcrumbLinks}>
      {/* Hero Section */}
      <section class="relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5 pt-16 pb-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center">
            <Badge variant="outline" className="mb-4">
              HITRUST CSF Certification
            </Badge>
            <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
              Healthcare's Gold Standard for Security
            </h1>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Achieve HITRUST CSF certification, the most trusted and comprehensive security framework 
              in healthcare. Demonstrate the highest level of data protection to healthcare partners.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
                <a href="/get-started">
                  <Button size="lg">
                      Start HITRUST Assessment
                      <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </a>
                
                <a href="/contact">
                   <Button variant="outline" size="lg">Talk to HITRUST Expert
                   </Button>
                </a>
                <a href="/regime/hitrust/controls">
                  <Button variant="outline" size="lg" >
                    Control Explorer
                  </Button>
                </a>             
            </div>
          </div>
        </div>
      </section>

      {/* HITRUST Benefits */}
      <section class="py-16 sm:py-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl lg:text-center">
            <h2 class="text-base font-semibold leading-7 text-primary">HITRUST Benefits</h2>
            <p class="mt-2 text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Healthcare Industry Recognition
            </p>
          </div>
          <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <dl class="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
              <div class="flex flex-col">
                <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                  <Heart className="h-5 w-5 flex-none text-primary" />
                  Healthcare Trust
                </dt>
                <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p class="flex-auto">
                    HITRUST is the most recognized and trusted security certification in healthcare. 
                    Accelerate partnerships with hospitals, health systems, and health plans.
                  </p>
                </dd>
              </div>
              <div class="flex flex-col">
                <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                  <Shield className="h-5 w-5 flex-none text-primary" />
                  Comprehensive Framework
                </dt>
                <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p class="flex-auto">
                    HITRUST CSF incorporates multiple standards including HIPAA, ISO 27001, NIST, 
                    and PCI DSS into a single comprehensive framework.
                  </p>
                </dd>
              </div>
              <div class="flex flex-col">
                <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                  <Building2 className="h-5 w-5 flex-none text-primary" />
                  Regulatory Alignment
                </dt>
                <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-muted-foreground">
                  <p class="flex-auto">
                    Meet multiple regulatory requirements with one certification. HITRUST 
                    addresses HIPAA, HITECH, FDA, and state privacy regulations.
                  </p>
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </section>

      {/* Assessment Types */}
      <section class="py-16 sm:py-20 bg-muted">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl lg:text-center">
            <h2 class="text-base font-semibold leading-7 text-primary">Assessment Types</h2>
            <p class="mt-2 text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Choose Your HITRUST Path
            </p>
          </div>
          <div class="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-6 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-3 lg:gap-8">
            {[
              {
                type: "e1 Assessment",
                duration: "3-6 Months",
                description: "Entry-level assessment for organizations beginning their HITRUST journey. 1-year certification period."
              },
              {
                type: "i1 Assessment",
                duration: "6-9 Months", 
                description: "Intermediate assessment with validated controls testing. Enhanced credibility with 2-year certification."
              },
              {
                type: "r2 Assessment",
                duration: "9-12 Months",
                description: "Most comprehensive assessment with full third-party validation. Highest trust level with 2-year certification."
              }
            ].map((item) => (
              <div class="flex flex-col items-start">
                <div class="rounded-md bg-primary/10 p-2 ring-1 ring-primary/20">
                  <CheckCircle className="h-6 w-6 text-primary" />
                </div>
                <dt class="mt-4 font-semibold text-foreground">{item.type}</dt>
                <dd class="mt-2 text-sm font-medium text-primary">{item.duration}</dd>
                <dd class="mt-2 leading-7 text-muted-foreground text-sm">{item.description}</dd>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Implementation Process */}
      <section class="py-16 sm:py-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl lg:text-center">
            <h2 class="text-base font-semibold leading-7 text-primary">Implementation</h2>
            <p class="mt-2 text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              HITRUST r2 in 9-12 Months
            </p>
          </div>
          <div class="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-6 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-4 lg:gap-8">
            {[
              {
                phase: "Months 1-2",
                title: "Scoping & Planning",
                description: "Define assessment scope, complete MyCSF intake, and develop implementation roadmap."
              },
              {
                phase: "Months 3-6",
                title: "Control Implementation",
                description: "Implement required controls, policies, and procedures based on organization size and type."
              },
              {
                phase: "Months 7-9",
                title: "Self-Assessment",
                description: "Complete detailed self-assessment questionnaire and gather supporting evidence."
              },
              {
                phase: "Months 10-12",
                title: "Validation & Certification",
                description: "Third-party validation testing, findings remediation, and final certification award."
              }
            ].map((phase) => (
              <div class="flex flex-col items-start">
                <div class="rounded-md bg-primary/10 p-2 ring-1 ring-primary/20">
                  <Clock className="h-6 w-6 text-primary" />
                </div>
                <dt class="mt-4 font-semibold text-foreground">{phase.phase}</dt>
                <dd class="mt-2 text-sm font-medium text-primary">{phase.title}</dd>
                <dd class="mt-2 leading-7 text-muted-foreground text-sm">{phase.description}</dd>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section class="py-16 sm:py-20 bg-muted">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Ready for Healthcare's Highest Security Standard?
            </h2>
            <p class="mx-auto mt-6 max-w-xl text-lg leading-8 text-muted-foreground">
              Get HITRUST CSF certified and unlock trust with the most security-conscious healthcare organizations.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
              
                <a href="/get-started">
                  <Button size="lg">Start HITRUST Assessment
                  </Button>
                </a>                
                <a href="/resources">
                  <Button variant="outline" size="lg">Download HITRUST Guide
                  </Button>
                </a>              
            </div>
          </div>
        </div>
      </section>

</Layout>