import * as rt from "./r4q-runtime.ts";
/**
 * @file identification-authentication.auto.ts
 * @generated This file was auto-generated from the FHIR R4 Questionnaire "Identification & Authentication".
 * Do not edit this file manually; re-run the generator if the source Questionnaire changes.
 * Profiles: http://hl7.org/fhir/4.0/StructureDefinition/Questionnaire
 *
 * Normalizes LHC JSON and FHIR QuestionnaireResponse into the type-safe `IdentificationAuthentication` interface.
 */

// this is the module signature, used by importers to identify the module
// using r4q-runtime.ts `moduleSignature` function
export const identificationAuthenticationModuleSignature: rt.ModuleSignature = {
  title: "Identification & Authentication",
  filename: "identification-authentication.auto.ts",
  titleCamel: "`identificationAuthentication`",
  titlePascal: "`IdentificationAuthentication`",
  titleKebab: "`identification-authentication`",
  lhcFormResponseAdapterFnName: "identificationAuthenticationLhcFormResponseAdapter",
  fhirQuestionnaireResponseAdapterFnName: "identificationAuthenticationFhirQuestionnaireResponseAdapter",
  sourceTextConstName: "identificationAuthenticationSource",
}

// deno-lint-ignore no-explicit-any
type Any = any;
/**
 * Form Help (from display/help controls):
 * - Manage service accounts carefully by assigning minimal privileges and regularly reviewing their usage to prevent misuse.
 * - Maintain an up-to-date list of all devices connected to the network to track and manage authorized hardware
 * - Identify information system users, processes acting on behalf of users, or devices.
 * - Set and enforce strong password rules to ensure users and processes securely verify their identity before accessing systems.
 * - Implement procedures to detect, respond to, and limit the impact of failed authentication attempts to protect against unauthorized access.
 * - Authenticate (or verify) the identities of those users, processes, or devices, as a prerequisite to allowing access to organizational information systems.
 */
/** Map of normalized property names to their source `linkId`. */
export const identificationAuthenticationLinkIds = {
  implementationStatus: "************",
  userIdentificationStandards: "************",
  numberOfServiceAccounts: "************",
  checkAllThatApply: "************",
  notesEvidence: "************",
  doYouHaveADeviceInventorySpreadsheet: "************",
  identityVerificationProcess: "************",
  notesEvidence2: "************",
  workstationsLaptops: "************",
  servers: "************",
  mobileDevices: "************",
  networkDevices: "************",
  deviceIdentification: "************",
  notesEvidence3: "************",
  supportingDocumentation: "************",
  additionalNotes: "************",
  implementationStatus2: "************",
  userAuthenticationMethods: "************",
  notesEvidence4: "************",
  minimumLengthCharacters: "************",
  passwordExpirationDays: "************",
  passwordHistoryPasswordsRemembered: "************",
  clickAllThatApply: "************",
  notesEvidence5: "************",
  multiFactorAuthentication: "************",
  defaultCredentialManagement: "************",
  numberOfFailedAttemptsBeforeLockout: "************",
  accountLockoutDurationMinutes: "************",
  clickAllThatApply2: "************",
  notesEvidence6: "************",
  supportingDocumentation2: "************",
  additionalNotes2: "************"
} as const;

/** Normalized view of "Identification & Authentication" answers. */
export interface IdentificationAuthentication {
  /**
   * Implementation Status
   * linkId: ************
   * FHIR type: choice
   * Section: IA.L1-B.1.V - Identification
   * Options: "Fully Implemented", "Partially Implemented", "Not Implemented"
   * Required: no
   */
  implementationStatus?: "Fully Implemented" | "Partially Implemented" | "Not Implemented";

  /**
   * User Identification Standards
   * linkId: ************
   * FHIR type: choice
   * Section: IA.L1-B.1.V - Identification
   * Options: " First name + last name (john.smith)", " First initial + last name (jsmith)", " Employee ID numbers (EMP001234)", " Department codes + names (IT-jsmith)"
   * Required: no
   */
  userIdentificationStandards?: " First name + last name (john.smith)" | " First initial + last name (jsmith)" | " Employee ID numbers (EMP001234)" | " Department codes + names (IT-jsmith)";

  /**
   * Number of service accounts:
   * linkId: ************
   * FHIR type: integer
   * Entry format: Enter number
   * Section: IA.L1-B.1.V - Identification > Service Account Management
   * Required: no
   */
  numberOfServiceAccounts?: number;

  /**
   * Check all that apply:
   * linkId: ************
   * FHIR type: choice
   * Section: IA.L1-B.1.V - Identification > Service Account Management
   * Options: " Database services", " Web applications", " Backup processes", " Monitoring/logging services", "Security scanning tools"
   * Required: no
   */
  checkAllThatApply?: (" Database services" | " Web applications" | " Backup processes" | " Monitoring/logging services" | "Security scanning tools")[];

  /**
   * Notes / Evidence
   * linkId: ************
   * FHIR type: text
   * Entry format: Type your comments here...
   * Section: IA.L1-B.1.V - Identification > Service Account Management
   * Required: no
   */
  notesEvidence?: string;

  /**
   * Do you have a device inventory spreadsheet?
   * linkId: ************
   * FHIR type: choice
   * Section: IA.L1-B.1.V - Identification > Service Account Management
   * Options: "Yes", "No"
   * Required: no
   */
  doYouHaveADeviceInventorySpreadsheet?: "Yes" | "No";

  /**
   * Identity Verification Process
   * linkId: ************
   * FHIR type: choice
   * Section: IA.L1-B.1.V - Identification > Service Account Management
   * Options: "HR verification with employee records", "Manager approval with written authorization", "Background check completion", "Photo identification verification"
   * Required: no
   */
  identityVerificationProcess?: ("HR verification with employee records" | "Manager approval with written authorization" | "Background check completion" | "Photo identification verification")[];

  /**
   * Notes / Evidence
   * linkId: ************
   * FHIR type: text
   * Entry format: Type your comments here...
   * Section: IA.L1-B.1.V - Identification > Service Account Management
   * Required: no
   */
  notesEvidence2?: string;

  /**
   * Workstations/laptops:
   * linkId: ************
   * FHIR type: integer
   * Section: IA.L1-B.1.V - Identification > Device Inventory
   * Required: no
   */
  workstationsLaptops?: number;

  /**
   * Servers:
   * linkId: ************
   * FHIR type: integer
   * Section: IA.L1-B.1.V - Identification > Device Inventory
   * Required: no
   */
  servers?: number;

  /**
   * Mobile devices:
   * linkId: ************
   * FHIR type: integer
   * Section: IA.L1-B.1.V - Identification > Device Inventory
   * Required: no
   */
  mobileDevices?: number;

  /**
   * Network devices:
   * linkId: ************
   * FHIR type: integer
   * Section: IA.L1-B.1.V - Identification > Device Inventory
   * Required: no
   */
  networkDevices?: number;

  /**
   * Device Identification
   * linkId: ************
   * FHIR type: choice
   * Section: IA.L1-B.1.V - Identification
   * Options: "MAC addresses", "IP addresses (static)", "Computer/device names", "Asset tag numbers", "Serial numbers", "Certificates/digital signatures"
   * Required: no
   */
  deviceIdentification?: ("MAC addresses" | "IP addresses (static)" | "Computer/device names" | "Asset tag numbers" | "Serial numbers" | "Certificates/digital signatures")[];

  /**
   * Notes / Evidence
   * linkId: ************
   * FHIR type: text
   * Entry format: Type your comments here...
   * Section: IA.L1-B.1.V - Identification
   * Required: no
   */
  notesEvidence3?: string;

  /**
   * Supporting Documentation
   * linkId: ************
   * FHIR type: choice
   * Section: IA.L1-B.1.V - Identification
   * Options: "Yes", "No"
   * Required: no
   */
  supportingDocumentation?: "Yes" | "No";

  /**
   * Additional Notes
   * linkId: ************
   * FHIR type: text
   * Entry format: Any additional notes, remediation plans, or implementation challenges...
   * Section: IA.L1-B.1.V - Identification
   * Required: no
   */
  additionalNotes?: string;

  /**
   * Implementation Status
   * linkId: ************
   * FHIR type: choice
   * Section: IA.L1-B.1.VI - Authentication
   * Options: "Fully Implemented", "Partially Implemented", "Not Implemented"
   * Required: no
   */
  implementationStatus2?: "Fully Implemented" | "Partially Implemented" | "Not Implemented";

  /**
   * User Authentication Methods
   * linkId: ************
   * FHIR type: choice
   * Section: IA.L1-B.1.VI - Authentication
   * Options: "Username and password", "Multi-factor authentication (MFA)", "Smart cards/PIV cards", "Biometric authentication", " Digital certificates", "Single sign-on (SSO)"
   * Required: no
   */
  userAuthenticationMethods?: ("Username and password" | "Multi-factor authentication (MFA)" | "Smart cards/PIV cards" | "Biometric authentication" | " Digital certificates" | "Single sign-on (SSO)")[];

  /**
   * Notes / Evidence
   * linkId: ************
   * FHIR type: text
   * Entry format: Type your comments here...
   * Section: IA.L1-B.1.VI - Authentication
   * Required: no
   */
  notesEvidence4?: string;

  /**
   * Minimum length (characters):
   * linkId: ************
   * FHIR type: integer
   * Entry format: 8
   * Section: IA.L1-B.1.VI - Authentication > Password Requirements
   * Required: no
   */
  minimumLengthCharacters?: number;

  /**
   * Password expiration (days):
   * linkId: ************
   * FHIR type: integer
   * Entry format: 90
   * Section: IA.L1-B.1.VI - Authentication > Password Requirements
   * Required: no
   */
  passwordExpirationDays?: number;

  /**
   * Password history (passwords remembered):
   * linkId: ************
   * FHIR type: integer
   * Entry format: 5
   * Section: IA.L1-B.1.VI - Authentication > Password Requirements
   * Required: no
   */
  passwordHistoryPasswordsRemembered?: number;

  /**
   * Click all that apply:
   * linkId: ************
   * FHIR type: choice
   * Section: IA.L1-B.1.VI - Authentication > Password Requirements
   * Options: "Uppercase letters required", "Lowercase letters required", "Numbers required", "Special characters required"
   * Required: no
   */
  clickAllThatApply?: ("Uppercase letters required" | "Lowercase letters required" | "Numbers required" | "Special characters required")[];

  /**
   * Notes / Evidence
   * linkId: ************
   * FHIR type: text
   * Entry format: Type your comments here...
   * Section: IA.L1-B.1.VI - Authentication > Password Requirements
   * Required: no
   */
  notesEvidence5?: string;

  /**
   * Multi-Factor Authentication
   * linkId: ************
   * FHIR type: choice
   * Section: IA.L1-B.1.VI - Authentication
   * Options: "Yes, for all users and systems", "Yes, for privileged accounts only", "Yes, for remote access only", "Yes, for critical systems only", "No, not implemented"
   * Required: no
   */
  multiFactorAuthentication?: "Yes, for all users and systems" | "Yes, for privileged accounts only" | "Yes, for remote access only" | "Yes, for critical systems only" | "No, not implemented";

  /**
   * Default Credential Management
   * linkId: ************
   * FHIR type: choice
   * Section: IA.L1-B.1.VI - Authentication
   * Options: "Always changed before deployment", "Changed during initial configuration", "Users required to change on first login", "No formal process"
   * Required: no
   */
  defaultCredentialManagement?: "Always changed before deployment" | "Changed during initial configuration" | "Users required to change on first login" | "No formal process";

  /**
   * Number of failed attempts before lockout:
   * linkId: ************
   * FHIR type: integer
   * Entry format: 3
   * Section: IA.L1-B.1.VI - Authentication > Authentication Failure Handling
   * Required: no
   */
  numberOfFailedAttemptsBeforeLockout?: number;

  /**
   * Account lockout duration (minutes):
   * linkId: ************
   * FHIR type: integer
   * Entry format: 30
   * Section: IA.L1-B.1.VI - Authentication > Authentication Failure Handling
   * Required: no
   */
  accountLockoutDurationMinutes?: number;

  /**
   * Click all that apply:
   * linkId: ************
   * FHIR type: choice
   * Section: IA.L1-B.1.VI - Authentication > Authentication Failure Handling
   * Options: "Administrator notification sent", " Security team alerted", "Logged for review"
   * Required: no
   */
  clickAllThatApply2?: ("Administrator notification sent" | " Security team alerted" | "Logged for review")[];

  /**
   * Notes / Evidence
   * linkId: ************
   * FHIR type: text
   * Entry format: Type your comments here...
   * Section: IA.L1-B.1.VI - Authentication > Authentication Failure Handling
   * Required: no
   */
  notesEvidence6?: string;

  /**
   * Supporting Documentation
   * linkId: ************
   * FHIR type: choice
   * Section: IA.L1-B.1.VI - Authentication
   * Options: "Yes", "No"
   * Required: no
   */
  supportingDocumentation2?: "Yes" | "No";

  /**
   * Additional Notes
   * linkId: ************
   * FHIR type: text
   * Entry format: Any additional notes, remediation plans, or implementation challenges...
   * Section: IA.L1-B.1.VI - Authentication
   * Required: no
   */
  additionalNotes2?: string;
}

/** Convert an LHC JSON response into a normalized IdentificationAuthentication object. */
export function identificationAuthenticationLhcFormResponseAdapter(input: Any): IdentificationAuthentication {
  return {
    implementationStatus: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")) as IdentificationAuthentication["implementationStatus"],
    userIdentificationStandards: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")) as IdentificationAuthentication["userIdentificationStandards"],
    numberOfServiceAccounts: rt.coerceOptionalNumber(rt.findLhcValueByLinkId(input, "************")),
    checkAllThatApply: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "************")) as IdentificationAuthentication["checkAllThatApply"],
    notesEvidence: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")),
    doYouHaveADeviceInventorySpreadsheet: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")) as IdentificationAuthentication["doYouHaveADeviceInventorySpreadsheet"],
    identityVerificationProcess: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "************")) as IdentificationAuthentication["identityVerificationProcess"],
    notesEvidence2: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")),
    workstationsLaptops: rt.coerceOptionalNumber(rt.findLhcValueByLinkId(input, "************")),
    servers: rt.coerceOptionalNumber(rt.findLhcValueByLinkId(input, "************")),
    mobileDevices: rt.coerceOptionalNumber(rt.findLhcValueByLinkId(input, "************")),
    networkDevices: rt.coerceOptionalNumber(rt.findLhcValueByLinkId(input, "************")),
    deviceIdentification: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "************")) as IdentificationAuthentication["deviceIdentification"],
    notesEvidence3: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")),
    supportingDocumentation: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")) as IdentificationAuthentication["supportingDocumentation"],
    additionalNotes: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")),
    implementationStatus2: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")) as IdentificationAuthentication["implementationStatus2"],
    userAuthenticationMethods: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "************")) as IdentificationAuthentication["userAuthenticationMethods"],
    notesEvidence4: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")),
    minimumLengthCharacters: rt.coerceOptionalNumber(rt.findLhcValueByLinkId(input, "************")),
    passwordExpirationDays: rt.coerceOptionalNumber(rt.findLhcValueByLinkId(input, "************")),
    passwordHistoryPasswordsRemembered: rt.coerceOptionalNumber(rt.findLhcValueByLinkId(input, "************")),
    clickAllThatApply: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "************")) as IdentificationAuthentication["clickAllThatApply"],
    notesEvidence5: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")),
    multiFactorAuthentication: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")) as IdentificationAuthentication["multiFactorAuthentication"],
    defaultCredentialManagement: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")) as IdentificationAuthentication["defaultCredentialManagement"],
    numberOfFailedAttemptsBeforeLockout: rt.coerceOptionalNumber(rt.findLhcValueByLinkId(input, "************")),
    accountLockoutDurationMinutes: rt.coerceOptionalNumber(rt.findLhcValueByLinkId(input, "************")),
    clickAllThatApply2: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "************")) as IdentificationAuthentication["clickAllThatApply2"],
    notesEvidence6: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")),
    supportingDocumentation2: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")) as IdentificationAuthentication["supportingDocumentation2"],
    additionalNotes2: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "************")),
  };
}

/** Convert a FHIR QuestionnaireResponse into a normalized IdentificationAuthentication object. */
export function identificationAuthenticationFhirQuestionnaireResponseAdapter(qr: Any): IdentificationAuthentication {
  return {
    implementationStatus: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")) as IdentificationAuthentication["implementationStatus"],
    userIdentificationStandards: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")) as IdentificationAuthentication["userIdentificationStandards"],
    numberOfServiceAccounts: rt.coerceOptionalNumber(rt.findQrAnswerByLinkId(qr, "************")),
    checkAllThatApply: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "************")) as IdentificationAuthentication["checkAllThatApply"],
    notesEvidence: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")),
    doYouHaveADeviceInventorySpreadsheet: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")) as IdentificationAuthentication["doYouHaveADeviceInventorySpreadsheet"],
    identityVerificationProcess: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "************")) as IdentificationAuthentication["identityVerificationProcess"],
    notesEvidence2: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")),
    workstationsLaptops: rt.coerceOptionalNumber(rt.findQrAnswerByLinkId(qr, "************")),
    servers: rt.coerceOptionalNumber(rt.findQrAnswerByLinkId(qr, "************")),
    mobileDevices: rt.coerceOptionalNumber(rt.findQrAnswerByLinkId(qr, "************")),
    networkDevices: rt.coerceOptionalNumber(rt.findQrAnswerByLinkId(qr, "************")),
    deviceIdentification: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "************")) as IdentificationAuthentication["deviceIdentification"],
    notesEvidence3: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")),
    supportingDocumentation: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")) as IdentificationAuthentication["supportingDocumentation"],
    additionalNotes: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")),
    implementationStatus2: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")) as IdentificationAuthentication["implementationStatus2"],
    userAuthenticationMethods: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "************")) as IdentificationAuthentication["userAuthenticationMethods"],
    notesEvidence4: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")),
    minimumLengthCharacters: rt.coerceOptionalNumber(rt.findQrAnswerByLinkId(qr, "************")),
    passwordExpirationDays: rt.coerceOptionalNumber(rt.findQrAnswerByLinkId(qr, "************")),
    passwordHistoryPasswordsRemembered: rt.coerceOptionalNumber(rt.findQrAnswerByLinkId(qr, "************")),
    clickAllThatApply: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "************")) as IdentificationAuthentication["clickAllThatApply"],
    notesEvidence5: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")),
    multiFactorAuthentication: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")) as IdentificationAuthentication["multiFactorAuthentication"],
    defaultCredentialManagement: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")) as IdentificationAuthentication["defaultCredentialManagement"],
    numberOfFailedAttemptsBeforeLockout: rt.coerceOptionalNumber(rt.findQrAnswerByLinkId(qr, "************")),
    accountLockoutDurationMinutes: rt.coerceOptionalNumber(rt.findQrAnswerByLinkId(qr, "************")),
    clickAllThatApply2: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "************")) as IdentificationAuthentication["clickAllThatApply2"],
    notesEvidence6: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")),
    supportingDocumentation2: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")) as IdentificationAuthentication["supportingDocumentation2"],
    additionalNotes2: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "************")),
  };
}

/**
 * NOTE TO DEVELOPERS:
 * -------------------
 * This Interpreter class is provided only as an EXAMPLE scaffold to demonstrate
 * how to consume the normalized type-safe interface generated for this
 * Questionnaire. It shows minimal factories (`fromLhc`, `fromQuestionnaireResponse`)
 * and convenience methods (`validateRequiredFields`, `assessReadiness`) but it is
 * NOT intended for production use.
 *
 * In real applications:
 * - Treat this class as SAMPLE CODE only.
 * - Replace or extend it with proper business logic, rules engines, or validation
 *   frameworks appropriate to your domain.
 * - Do not rely on the simplistic readiness scoring or validation in production
 *   scenarios; they are illustrative, not authoritative.
 *
 * Best practice: use the generated TypeScript interface (`IdentificationAuthentication`) as your
 * contract for normalized data, then integrate with your own rules processors,
 * compliance engines, or plain TypeScript/JavaScript functions as needed.
 */
export class IdentificationAuthenticationInterpreter {
  constructor(readonly value: IdentificationAuthentication) { }

  /** Factory: build from LHC JSON. */
  static fromLhcFormResponse(input: Any): IdentificationAuthenticationInterpreter {
    return new IdentificationAuthenticationInterpreter(identificationAuthenticationLhcFormResponseAdapter(input));
  }

  /** Factory: build from FHIR QuestionnaireResponse. */
  static fromQuestionnaireResponse(qr: Any): IdentificationAuthenticationInterpreter {
    return new IdentificationAuthenticationInterpreter(identificationAuthenticationFhirQuestionnaireResponseAdapter(qr));
  }

  /** Check required fields and report any missing or blank. */
  validateRequiredFields(): { ok: boolean; missing: Array<keyof IdentificationAuthentication> } {
    const missing: Array<keyof IdentificationAuthentication> = [];
    const req: Array<keyof IdentificationAuthentication> = [];
    for (const k of req) {
      const v = (this.value as Any)[k];
      if (rt.isBlank(v)) missing.push(k);
    }
    return { ok: missing.length === 0, missing };
  }

  /**
   * Assess readiness with a simple completeness score. This is meant to be
   * used to help understand how complete the types are and serves as an
   * example of how to use the generated code.
   * - requiredCovered: percentage of required fields that are non-blank
   * - overallFilled: count of non-blank fields among all known properties
   */
  assessReadiness(): {
    formTitle: string;
    requiredCovered: number; // 0..1
    totalRequired: number;
    totalFilled: number;
    totalFields: number;
    missingRequired: Array<keyof IdentificationAuthentication>;
  } {
    const req: Array<keyof IdentificationAuthentication> = [];
    const all: Array<keyof IdentificationAuthentication> = ["implementationStatus", "userIdentificationStandards", "numberOfServiceAccounts", "checkAllThatApply", "notesEvidence", "doYouHaveADeviceInventorySpreadsheet", "identityVerificationProcess", "notesEvidence2", "workstationsLaptops", "servers", "mobileDevices", "networkDevices", "deviceIdentification", "notesEvidence3", "supportingDocumentation", "additionalNotes", "implementationStatus2", "userAuthenticationMethods", "notesEvidence4", "minimumLengthCharacters", "passwordExpirationDays", "passwordHistoryPasswordsRemembered", "clickAllThatApply", "notesEvidence5", "multiFactorAuthentication", "defaultCredentialManagement", "numberOfFailedAttemptsBeforeLockout", "accountLockoutDurationMinutes", "clickAllThatApply2", "notesEvidence6", "supportingDocumentation2", "additionalNotes2"];

    let reqFilled = 0;
    const missingReq: Array<keyof IdentificationAuthentication> = [];
    for (const k of req) {
      const v = (this.value as Any)[k];
      if (!rt.isBlank(v)) reqFilled++;
      else missingReq.push(k);
    }

    let totalFilled = 0;
    for (const k of all) {
      if (!rt.isBlank((this.value as Any)[k])) totalFilled++;
    }

    return {
      formTitle: "Identification & Authentication",
      requiredCovered: req.length ? reqFilled / req.length : 1,
      totalRequired: req.length,
      totalFilled,
      totalFields: all.length,
      missingRequired: missingReq,
    };
  }
}

/** The original source */
export const identificationAuthenticationSource = `{
  "resourceType": "Questionnaire",
  "meta": {
    "profile": [
      "http://hl7.org/fhir/4.0/StructureDefinition/Questionnaire"
    ],
    "tag": [
      {
        "system": "InternalAssessment",
        "code": "AccessControl",
        "display": "Access Control"
      },
      {
        "system": "InternalAssessment",
        "code": "AccessControl-L1",
        "display": "CMMC Level 1"
      }
    ]
  },
  "extension": [
    {
      "url": "http://hl7.org/fhir/StructureDefinition/variable",
      "valueExpression": {
        "name": "displayOrder",
        "language": "text/fhirpath",
        "expression": "3",
        "extension": [
          {
            "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
            "valueString": "simple"
          },
          {
            "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
            "valueString": "3"
          }
        ]
      }
    }
  ],
  "name": "identification_authentication_verify_identities_of_users_and_processes",
  "title": "Identification & Authentication",
  "status": "draft",
  "date": "2025-08-26",
  "publisher": "Netspective",
  "description": "Identification & Authentication (Verify identities of users and processes)",
  "purpose": "This assessment verifies that all users, devices, and processes accessing the system are properly identified and authenticated. It examines whether unique identifiers are issued, whether identity verification mechanisms (such as passwords, tokens, or certificates) are in place, and whether authentication controls prevent unauthorized access to Federal Contract Information (FCI).",
  "approvalDate": "2025-08-26",
  "item": [
    {
      "item": [
        {
          "item": [
            {
              "type": "text",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "281929303054",
              "text": "Notes / Evidence"
            }
          ],
          "type": "choice",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/variable",
              "valueExpression": {
                "name": "weight",
                "language": "text/fhirpath",
                "expression": "1",
                "extension": [
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                    "valueString": "simple"
                  },
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                    "valueString": "1"
                  }
                ]
              }
            },
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "radio-button",
                    "display": "Radio Button"
                  }
                ]
              }
            }
          ],
          "linkId": "************",
          "text": "Implementation Status",
          "repeats": false,
          "answerOption": [
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 100
                }
              ],
              "valueCoding": {
                "display": "Fully Implemented"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 50
                }
              ],
              "valueCoding": {
                "display": "Partially Implemented"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 0
                }
              ],
              "valueCoding": {
                "display": "Not Implemented"
              }
            }
          ]
        },
        {
          "item": [
            {
              "type": "text",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "243749447566",
              "text": "Notes / Evidence:"
            }
          ],
          "type": "choice",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/variable",
              "valueExpression": {
                "name": "weight",
                "language": "text/fhirpath",
                "expression": "1",
                "extension": [
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                    "valueString": "simple"
                  },
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                    "valueString": "1"
                  }
                ]
              }
            },
            {
              "url": "http://hl7.org/fhir/StructureDefinition/variable",
              "valueExpression": {
                "name": "maxScore",
                "language": "text/fhirpath",
                "expression": "100",
                "extension": [
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                    "valueString": "simple"
                  },
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                    "valueString": "100"
                  }
                ]
              }
            },
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "radio-button",
                    "display": "Radio Button"
                  }
                ]
              }
            }
          ],
          "linkId": "************",
          "text": "User Identification Standards",
          "repeats": false,
          "answerOption": [
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 25
                }
              ],
              "valueCoding": {
                "display": " First name + last name (john.smith)"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 25
                }
              ],
              "valueCoding": {
                "display": " First initial + last name (jsmith)"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 25
                }
              ],
              "valueCoding": {
                "display": " Employee ID numbers (EMP001234)"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 25
                }
              ],
              "valueCoding": {
                "display": " Department codes + names (IT-jsmith)"
              }
            }
          ]
        },
        {
          "item": [
            {
              "type": "integer",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Enter number"
                }
              ],
              "linkId": "************",
              "text": "Number of service accounts:"
            },
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "100",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "100"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "************",
              "text": "Check all that apply:",
              "repeats": true,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": " Database services"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": " Web applications"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": " Backup processes"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": " Monitoring/logging services"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Security scanning tools"
                  }
                }
              ]
            },
            {
              "type": "text",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "************",
              "text": "Notes / Evidence"
            },
            {
              "item": [
                {
                  "type": "text",
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                      "valueString": "Type your comments here..."
                    }
                  ],
                  "linkId": "279054233268",
                  "text": "Notes / Evidence "
                }
              ],
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "radio-button",
                        "display": "Radio Button"
                      }
                    ]
                  }
                }
              ],
              "linkId": "************",
              "text": "Do you have a device inventory spreadsheet?",
              "repeats": false,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 100
                    }
                  ],
                  "valueCoding": {
                    "display": "Yes"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 0
                    }
                  ],
                  "valueCoding": {
                    "display": "No"
                  }
                }
              ]
            },
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "100",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "100"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "************",
              "text": "Identity Verification Process",
              "repeats": true,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 25
                    }
                  ],
                  "valueCoding": {
                    "display": "HR verification with employee records"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 25
                    }
                  ],
                  "valueCoding": {
                    "display": "Manager approval with written authorization"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 25
                    }
                  ],
                  "valueCoding": {
                    "display": "Background check completion"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 25
                    }
                  ],
                  "valueCoding": {
                    "display": "Photo identification verification"
                  }
                }
              ]
            },
            {
              "type": "text",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "************",
              "text": "Notes / Evidence"
            },
            {
              "linkId": "************_helpText",
              "type": "display",
              "text": "Manage service accounts carefully by assigning minimal privileges and regularly reviewing their usage to prevent misuse.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "************",
          "text": "Service Account Management"
        },
        {
          "item": [
            {
              "type": "integer",
              "linkId": "************",
              "text": "Workstations/laptops:"
            },
            {
              "type": "integer",
              "linkId": "************",
              "text": "Servers:"
            },
            {
              "type": "integer",
              "linkId": "************",
              "text": "Mobile devices:"
            },
            {
              "type": "integer",
              "linkId": "************",
              "text": "Network devices:"
            },
            {
              "linkId": "543189099428_helpText",
              "type": "display",
              "text": "Maintain an up-to-date list of all devices connected to the network to track and manage authorized hardware",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "543189099428",
          "text": "Device Inventory"
        },
        {
          "type": "choice",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/variable",
              "valueExpression": {
                "name": "weight",
                "language": "text/fhirpath",
                "expression": "1",
                "extension": [
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                    "valueString": "simple"
                  },
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                    "valueString": "1"
                  }
                ]
              }
            },
            {
              "url": "http://hl7.org/fhir/StructureDefinition/variable",
              "valueExpression": {
                "name": "maxScore",
                "language": "text/fhirpath",
                "expression": "99.96",
                "extension": [
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                    "valueString": "simple"
                  },
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                    "valueString": "99.96"
                  }
                ]
              }
            },
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "check-box",
                    "display": "Check-box"
                  }
                ]
              }
            }
          ],
          "linkId": "************",
          "text": "Device Identification",
          "repeats": true,
          "answerOption": [
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 16.66
                }
              ],
              "valueCoding": {
                "display": "MAC addresses"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 16.66
                }
              ],
              "valueCoding": {
                "display": "IP addresses (static)"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 16.66
                }
              ],
              "valueCoding": {
                "display": "Computer/device names"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 16.66
                }
              ],
              "valueCoding": {
                "display": "Asset tag numbers"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 16.66
                }
              ],
              "valueCoding": {
                "display": "Serial numbers"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 16.66
                }
              ],
              "valueCoding": {
                "display": "Certificates/digital signatures"
              }
            }
          ]
        },
        {
          "type": "text",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
              "valueString": "Type your comments here..."
            }
          ],
          "linkId": "************",
          "text": "Notes / Evidence"
        },
        {
          "item": [
            {
              "type": "text",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "438433871645",
              "text": "Notes / Evidence"
            }
          ],
          "type": "choice",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/variable",
              "valueExpression": {
                "name": "weight",
                "language": "text/fhirpath",
                "expression": "1",
                "extension": [
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                    "valueString": "simple"
                  },
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                    "valueString": "1"
                  }
                ]
              }
            },
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "radio-button",
                    "display": "Radio Button"
                  }
                ]
              }
            }
          ],
          "linkId": "************",
          "text": "Supporting Documentation",
          "repeats": false,
          "answerOption": [
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 100
                }
              ],
              "valueCoding": {
                "display": "Yes"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 0
                }
              ],
              "valueCoding": {
                "display": "No"
              }
            }
          ]
        },
        {
          "type": "text",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
              "valueString": "Any additional notes, remediation plans, or implementation challenges..."
            }
          ],
          "linkId": "************",
          "text": "Additional Notes"
        },
        {
          "linkId": "228228158249_helpText",
          "type": "display",
          "text": "Identify information system users, processes acting on behalf of users, or devices.",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "help",
                    "display": "Help-Button"
                  }
                ],
                "text": "Help-Button"
              }
            }
          ]
        }
      ],
      "type": "group",
      "linkId": "228228158249",
      "text": "IA.L1-B.1.V - Identification"
    },
    {
      "item": [
        {
          "item": [
            {
              "type": "text",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "440326396427",
              "text": "Notes / Evidence"
            }
          ],
          "type": "choice",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/variable",
              "valueExpression": {
                "name": "weight",
                "language": "text/fhirpath",
                "expression": "1",
                "extension": [
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                    "valueString": "simple"
                  },
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                    "valueString": "1"
                  }
                ]
              }
            },
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "radio-button",
                    "display": "Radio Button"
                  }
                ]
              }
            }
          ],
          "linkId": "************",
          "text": "Implementation Status",
          "repeats": false,
          "answerOption": [
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 100
                }
              ],
              "valueCoding": {
                "display": "Fully Implemented"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 50
                }
              ],
              "valueCoding": {
                "display": "Partially Implemented"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 0
                }
              ],
              "valueCoding": {
                "display": "Not Implemented"
              }
            }
          ]
        },
        {
          "type": "choice",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/variable",
              "valueExpression": {
                "name": "weight",
                "language": "text/fhirpath",
                "expression": "1",
                "extension": [
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                    "valueString": "simple"
                  },
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                    "valueString": "1"
                  }
                ]
              }
            },
            {
              "url": "http://hl7.org/fhir/StructureDefinition/variable",
              "valueExpression": {
                "name": "maxScore",
                "language": "text/fhirpath",
                "expression": "99.96",
                "extension": [
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                    "valueString": "simple"
                  },
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                    "valueString": "99.96"
                  }
                ]
              }
            },
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "check-box",
                    "display": "Check-box"
                  }
                ]
              }
            }
          ],
          "linkId": "************",
          "text": "User Authentication Methods",
          "repeats": true,
          "answerOption": [
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 16.66
                }
              ],
              "valueCoding": {
                "display": "Username and password"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 16.66
                }
              ],
              "valueCoding": {
                "display": "Multi-factor authentication (MFA)"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 16.66
                }
              ],
              "valueCoding": {
                "display": "Smart cards/PIV cards"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 16.66
                }
              ],
              "valueCoding": {
                "display": "Biometric authentication"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 16.66
                }
              ],
              "valueCoding": {
                "display": " Digital certificates"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 16.66
                }
              ],
              "valueCoding": {
                "display": "Single sign-on (SSO)"
              }
            }
          ]
        },
        {
          "type": "text",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
              "valueString": "Type your comments here..."
            }
          ],
          "linkId": "************",
          "text": "Notes / Evidence"
        },
        {
          "item": [
            {
              "type": "integer",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "8"
                }
              ],
              "linkId": "************",
              "text": "Minimum length (characters):"
            },
            {
              "type": "integer",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "90"
                }
              ],
              "linkId": "************",
              "text": "Password expiration (days):"
            },
            {
              "type": "integer",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "5"
                }
              ],
              "linkId": "************",
              "text": "Password history (passwords remembered):"
            },
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "100",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "100"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "************",
              "text": "Click all that apply:",
              "repeats": true,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 25
                    }
                  ],
                  "valueCoding": {
                    "display": "Uppercase letters required"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 25
                    }
                  ],
                  "valueCoding": {
                    "display": "Lowercase letters required"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 25
                    }
                  ],
                  "valueCoding": {
                    "display": "Numbers required"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 25
                    }
                  ],
                  "valueCoding": {
                    "display": "Special characters required"
                  }
                }
              ]
            },
            {
              "type": "text",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "************",
              "text": "Notes / Evidence"
            },
            {
              "linkId": "459655669415_helpText",
              "type": "display",
              "text": "Set and enforce strong password rules to ensure users and processes securely verify their identity before accessing systems.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "459655669415",
          "text": "Password Requirements"
        },
        {
          "item": [
            {
              "type": "text",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "496212946934",
              "text": "Notes / Evidence"
            }
          ],
          "type": "choice",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/variable",
              "valueExpression": {
                "name": "weight",
                "language": "text/fhirpath",
                "expression": "1",
                "extension": [
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                    "valueString": "simple"
                  },
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                    "valueString": "1"
                  }
                ]
              }
            },
            {
              "url": "http://hl7.org/fhir/StructureDefinition/variable",
              "valueExpression": {
                "name": "maxScore",
                "language": "text/fhirpath",
                "expression": "100",
                "extension": [
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                    "valueString": "simple"
                  },
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                    "valueString": "100"
                  }
                ]
              }
            },
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "radio-button",
                    "display": "Radio Button"
                  }
                ]
              }
            }
          ],
          "linkId": "************",
          "text": "Multi-Factor Authentication",
          "repeats": false,
          "answerOption": [
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 20
                }
              ],
              "valueCoding": {
                "display": "Yes, for all users and systems"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 20
                }
              ],
              "valueCoding": {
                "display": "Yes, for privileged accounts only"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 20
                }
              ],
              "valueCoding": {
                "display": "Yes, for remote access only"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 20
                }
              ],
              "valueCoding": {
                "display": "Yes, for critical systems only"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 20
                }
              ],
              "valueCoding": {
                "display": "No, not implemented"
              }
            }
          ]
        },
        {
          "item": [
            {
              "type": "text",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "273984680811",
              "text": "Notes / Evidence"
            }
          ],
          "type": "choice",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/variable",
              "valueExpression": {
                "name": "weight",
                "language": "text/fhirpath",
                "expression": "1",
                "extension": [
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                    "valueString": "simple"
                  },
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                    "valueString": "1"
                  }
                ]
              }
            },
            {
              "url": "http://hl7.org/fhir/StructureDefinition/variable",
              "valueExpression": {
                "name": "maxScore",
                "language": "text/fhirpath",
                "expression": "100",
                "extension": [
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                    "valueString": "simple"
                  },
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                    "valueString": "100"
                  }
                ]
              }
            },
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "radio-button",
                    "display": "Radio Button"
                  }
                ]
              }
            }
          ],
          "linkId": "************",
          "text": "Default Credential Management",
          "repeats": false,
          "answerOption": [
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 25
                }
              ],
              "valueCoding": {
                "display": "Always changed before deployment"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 25
                }
              ],
              "valueCoding": {
                "display": "Changed during initial configuration"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 25
                }
              ],
              "valueCoding": {
                "display": "Users required to change on first login"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 25
                }
              ],
              "valueCoding": {
                "display": "No formal process"
              }
            }
          ]
        },
        {
          "item": [
            {
              "type": "integer",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "3"
                }
              ],
              "linkId": "************",
              "text": "Number of failed attempts before lockout:"
            },
            {
              "type": "integer",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "30"
                }
              ],
              "linkId": "************",
              "text": "Account lockout duration (minutes):"
            },
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "100",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "100"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "************",
              "text": "Click all that apply:",
              "repeats": true,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 50
                    }
                  ],
                  "valueCoding": {
                    "display": "Administrator notification sent"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 30
                    }
                  ],
                  "valueCoding": {
                    "display": " Security team alerted"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Logged for review"
                  }
                }
              ]
            },
            {
              "type": "text",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "************",
              "text": "Notes / Evidence"
            },
            {
              "linkId": "341175611920_helpText",
              "type": "display",
              "text": "Implement procedures to detect, respond to, and limit the impact of failed authentication attempts to protect against unauthorized access.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "341175611920",
          "text": "Authentication Failure Handling"
        },
        {
          "item": [
            {
              "type": "text",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "116846260787",
              "text": "Notes / Evidence"
            }
          ],
          "type": "choice",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/variable",
              "valueExpression": {
                "name": "weight",
                "language": "text/fhirpath",
                "expression": "1",
                "extension": [
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                    "valueString": "simple"
                  },
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                    "valueString": "1"
                  }
                ]
              }
            },
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "radio-button",
                    "display": "Radio Button"
                  }
                ]
              }
            }
          ],
          "linkId": "************",
          "text": "Supporting Documentation",
          "repeats": false,
          "answerOption": [
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 100
                }
              ],
              "valueCoding": {
                "display": "Yes"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 0
                }
              ],
              "valueCoding": {
                "display": "No"
              }
            }
          ]
        },
        {
          "type": "text",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
              "valueString": "Any additional notes, remediation plans, or implementation challenges..."
            }
          ],
          "linkId": "************",
          "text": "Additional Notes"
        },
        {
          "linkId": "865372145224_helpText",
          "type": "display",
          "text": "Authenticate (or verify) the identities of those users, processes, or devices, as a prerequisite to allowing access to organizational information systems.",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "help",
                    "display": "Help-Button"
                  }
                ],
                "text": "Help-Button"
              }
            }
          ]
        }
      ],
      "type": "group",
      "linkId": "865372145224",
      "text": "IA.L1-B.1.VI - Authentication"
    }
  ]
}`;