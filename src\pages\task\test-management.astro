---
import Layout from "../../layouts/Layout.astro";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  TestTube, 
  GitBranch, 
  Brain, 
  Database, 
  FileText, 
  Shield, 
  CheckCircle, 
  TrendingUp,
  Users,
  Clock,
  Search,
  Zap
} from "lucide-react";

const features = [
  {
    icon: FileText,
    title: "Markdown-Based Test Authoring",
    description: "Define tests, suites, and plans in Markdown with YAML frontmatter. Use version control for tracking changes and approvals."
  },
  {
    icon: Brain,
    title: "AI-Native Workflows",
    description: "Generate tests from GitHub issues, stories, or code diffs using ChatGPT. Review and revise tests with AI agents."
  },
  {
    icon: Database,
    title: "SQL-First Result Analysis",
    description: "Store test execution events in surveilr tables. Use SQL to audit trends, regressions, and flaky tests."
  },
  {
    icon: Shield,
    title: "Evidence-Grade Traceability",
    description: "Link test cases to external systems via FII. Prove execution of tests tied to specific software releases."
  },
  {
    icon: GitBranch,
    title: "GitOps Compatible",
    description: "Organize tests in folder structures, edited locally or in VS Code. Supports offline mode and Git-based review processes."
  },
  {
    icon: CheckCircle,
    title: "Audit Ready",
    description: "Immutable test result storage with complete audit trails for regulatory compliance requirements."
  }
];

const complianceFrameworks = [
  "SOC 2", "ISO 27001", "HIPAA", "GDPR", "HITRUST", 
  "FDA 21 CFR Part 11", "GxP", "Medical Device Software Assurance"
];

const useCases = [
  {
    title: "SOC 2 Access Control Testing",
    description: "Auditors requested test execution history for access controls. Qualityfolio provided complete traceability from requirements to test results.",
    compliance: "SOC 2"
  },
  {
    title: "FDA Validation Requirements",
    description: "FDA validation required linkages between system requirements and test cases. Used FII to maintain evidence-grade connections.",
    compliance: "FDA 21 CFR Part 11"
  },
  {
    title: "CI/CD Pipeline Integration",
    description: "CI/CD pipelines ingested results to surveilr and auto-generated audit-ready dashboards for continuous compliance monitoring.",
    compliance: "ISO 27001"
  }
];

const comparisonFeatures = [
  { feature: "Evidence Support", qualityfolio: "Native", traditional: "Limited" },
  { feature: "AI Generation", qualityfolio: "Built-in", traditional: "None" },
  { feature: "GitOps Compatible", qualityfolio: "Yes", traditional: "No" },
  { feature: "SQL Analytics", qualityfolio: "Native", traditional: "Limited" },
  { feature: "Audit Ready", qualityfolio: "Yes", traditional: "Manual" },
  { feature: "Markdown Support", qualityfolio: "Native", traditional: "No" },
  { feature: "Offline Mode", qualityfolio: "Yes", traditional: "No" }
];

  const schema = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "Qualityfolio Test Management",
    "description": "AI-native, markdown-based test management platform for compliance-driven organizations",
    "applicationCategory": "TestManagementSoftware",
    "operatingSystem": "Cross-platform",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    }
  };


  const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Task',"aria-disabled": true ,
  },
  {
    index: "2", text: "Test Management","aria-disabled": true 
  },
];
---

<Layout schema={schema}
        breadcrumbLinks={breadcrumbLinks}>

      <main>
          {/* Hero Section */}
          <section class="text-center relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5 pt-16 pb-20">
            <Badge variant="secondary" className="mb-4">
              Test Management
            </Badge>
            <h1 class="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
              Evidence-Driven Test Management for <br />Compliance
            </h1>
            <p class="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
              Transform your testing from a development activity into a compliance cornerstone. 
              Qualityfolio delivers AI-native, markdown-based test management integrated with surveilr 
              for audit-ready evidence collection.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
              <a href="/get-started">
                <Button size="lg">
                  Start Building Test Evidence
                </Button>
             </a>              
              <a href="/contact">
                <Button size="lg" variant="secondary">See Qualityfolio Demo
                </Button>
              </a>
              
            </div>
            
            {/* Demo Video Placeholder */}
            <div class="mt-12 max-w-4xl text-center justify-center mx-auto">
              <img src="/assets/qualityfolio.png" alt="Qualityfolio screenshot" class="rounded-xl shadow-lg">
            </div>
          </section>

          {/* Why Test Management is Essential */}
          <section class="py-16 sm:py-20">
            <div class="mx-auto max-w-7xl px-6 lg:px-8">
             <div class="text-center mb-12">
              <h2 class="text-3xl font-bold mb-4">Why Test Management is Essential in Compliance</h2>
              <p class="text-lg text-muted-foreground max-w-3xl mx-auto">
                Test management isn't just a QA or DevOps function—it's a compliance cornerstone that provides 
                structured test documentation, evidence of execution, and traceability to requirements.
              </p>
             </div>
            
            <div class="grid md:grid-cols-2 gap-8 mb-8">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="w-5 h-5" />
                    Compliance Requirements
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p class="text-muted-foreground mb-4">
                    Modern compliance frameworks require comprehensive test coverage and validation:
                  </p>
                  <div class="flex flex-wrap gap-2">
                    {complianceFrameworks.map((framework) => (
                      <Badge variant="outline">{framework}</Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="w-5 h-5" />
                    Key Concerns
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul class="space-y-2 text-muted-foreground">
                    <li class="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-primary" />
                      Test coverage and validation
                    </li>
                    <li class="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-primary" />
                      Traceability to requirements
                    </li>
                    <li class="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-primary" />
                      Evidence of test execution
                    </li>
                    <li class="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-primary" />
                      Immutable audit trails
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </div>
            </div>            
          </section>

          {/* Compliance Lifecycle */}
          <section class="py-16 sm:py-20 bg-muted">
            <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="text-center mb-12">
              <h2 class="text-3xl font-bold mb-4">Test Management in the Compliance Lifecycle</h2>
              <p class="text-lg text-muted-foreground max-w-3xl mx-auto">
                Understand where test management applies across your compliance program and why 
                auditability and data lineage are critical requirements.
              </p>
            </div>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[
                { icon: Search, title: "Risk-Based Test Planning", description: "Plan tests based on risk assessments and compliance requirements" },
                { icon: Zap, title: "Automated & Manual Execution", description: "Execute tests with full automation support and manual validation" },
                { icon: Database, title: "Result & Log Capture", description: "Capture comprehensive test results and execution logs" },
                { icon: Shield, title: "Evidence Linkage", description: "Link test results to policies and requirements for traceability" },
                { icon: Clock, title: "Audit Trails", description: "Maintain immutable audit trails of all test activities" },
                { icon: TrendingUp, title: "Continuous Monitoring", description: "Monitor test effectiveness and compliance status over time" }
              ].map((phase, index) => (
                <Card key={index}>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <phase.icon className="w-5 h-5 text-primary" />
                      {phase.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p class="text-muted-foreground">{phase.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>

            </div>
          </section>

          {/* Qualityfolio Features */}
          <section class="py-16 sm:py-20">
             <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="text-center mb-12">
              <h2 class="text-3xl font-bold mb-4">Introducing Qualityfolio</h2>
              <p class="text-lg text-muted-foreground max-w-3xl mx-auto">
                Modern test management for compliance-driven organizations. Built on markdown, 
                powered by AI, and integrated with surveilr for evidence-grade results.
              </p>
            </div>
            
            {/* Feature Highlights with Screenshots */}
            <div class="grid lg:grid-cols-2 gap-8 mb-12">
              <div>
                <img src="/assets/qualityfolio-screenshot-1.png" alt="Qualityfolio screenshot" class="rounded-xl w-full shadow-lg">               
              </div>
              <div>
                <img src="/assets/qualityfolio-screenshot-2.jpg" alt="Qualityfolio screenshot" class="rounded-xl w-full shadow-lg">
              </div>
            </div>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {features.map((feature, index) => (
                <Card key={index}>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <feature.icon className="w-5 h-5 text-primary" />
                      {feature.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p class="text-muted-foreground">{feature.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>

             </div>
          </section>

          {/* Comparison Table */}
          <section class="py-16 sm:py-20 bg-muted">
            <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="text-center mb-12">
              <h2 class="text-3xl font-bold mb-4">Qualityfolio vs. Traditional Test Management</h2>
              <p class="text-lg text-muted-foreground max-w-3xl mx-auto">
                See how Qualityfolio compares to traditional tools like TestRail, Xray, and Zephyr 
                across key compliance and modern development dimensions.
              </p>
            </div>
            
            <Card>
              <CardContent className="p-0">
                <div class="overflow-x-auto">
                  <table class="w-full">
                    <thead class="border-b">
                      <tr>
                        <th class="text-left p-4">Feature</th>
                        <th class="text-center p-4">Qualityfolio</th>
                        <th class="text-center p-4">Traditional Tools</th>
                      </tr>
                    </thead>
                    <tbody>
                      {comparisonFeatures.map((item, index) => (
                        <tr class={index % 2 === 0 ? "bg-muted" : ""}>
                          <td class="p-4 font-medium">{item.feature}</td>
                          <td class="p-4 text-center">
                            <Badge variant="default">{item.qualityfolio}</Badge>
                          </td>
                          <td class="p-4 text-center">
                            <Badge variant="outline">{item.traditional}</Badge>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>

            </div>
          </section>

          {/* Use Cases */}
          <section class="py-16 sm:py-20">
            <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="text-center mb-12">
              <h2 class="text-3xl font-bold mb-4">Real-World Use Cases</h2>
              <p class="text-lg text-muted-foreground max-w-3xl mx-auto">
                See how organizations use Qualityfolio to meet compliance requirements 
                and streamline audit processes.
              </p>
            </div>
            
            {/* Case Study Video */}
            <div class="grid lg:grid-cols-2 gap-8 mb-12">
              <div>
                <img src="/assets/qualityfolio-screenshot-3.png" alt="Qualityfolio screenshot" class="rounded-xl w-full shadow-lg">               
              </div>
              <div>
                <img src="/assets/qualityfolio-screenshot-4.png" alt="Qualityfolio screenshot" class="rounded-xl w-full shadow-lg">
              </div>
            </div>
            
            <div class="grid md:grid-cols-1 lg:grid-cols-3 gap-6">
              {useCases.map((useCase, index) => (
                <Card key={index}>
                  <CardHeader>
                    <Badge variant="secondary" className="w-fit mb-2">{useCase.compliance}</Badge>
                    <CardTitle>{useCase.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p class="text-muted-foreground">{useCase.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>

            </div>
          </section>

          {/* CTA Section */}
          <section class="text-center bg-muted rounded-lg p-12">
            <h2 class="text-3xl font-bold mb-4">Start Managing Tests Like Evidence, Not Just Artifacts</h2>
            <p class="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
              Transform your test management approach with Qualityfolio's AI-native, evidence-driven platform. 
              Built for compliance teams who understand that testing is a cornerstone of regulatory readiness.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
              
              <a href="/get-started">
                <Button size="lg">
                  Get Started with Qualityfolio
                </Button>
              </a>
              <a href="/contact">
                <Button size="lg" variant="secondary">See SQL Examples
                </Button>
              </a>
            </div>
          </section>
        </main>

</Layout>
