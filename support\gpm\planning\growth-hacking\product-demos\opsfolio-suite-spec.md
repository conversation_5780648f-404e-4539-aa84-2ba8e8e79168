# Opsfolio Suite – Product Capability Specification

## 1. Core Modules

### 1.1 Controls & Compliance Mapping

* **Domains & Regimes:**

  * Domains group controls by functional area (e.g., cloud security, configuration management).
  * Control regimes represent compliance frameworks (CMMC, SOC 2, ISO 27001, HIPAA, etc.).
* **SCF Integration:**

  * Secure Controls Framework (SCF) provides a unified control set.
  * Controls mapped across multiple regimes for cross-certification leverage.
  * Example: SOC 2 controls overlapping with ISO → % coverage calculated.
* **Custom Controls:**

  * Ability to add and manage customer-specific controls.

### 1.2 Policies & Evidence Management

* **Policy Repository:**

  * Central library for policies (provided by Opsfolio or customer).
  * \~70+ baseline policies available, customizable per client.
* **Evidence Mapping:**

  * Each control linked to supporting evidence.
  * Evidence types: PDF, Word, Excel, SQL query outputs, screenshots, 3rd-party tool exports.
* **Automated Evidence Collection:**

  * Via **Surveyor** agent or manual upload.
  * Supports continuous monitoring and recurring evidence refresh.
* **Audit-Ready Structuring:**

  * Evidence is organized to match control IDs and audit workflows.

### 1.3 Audit & Assessment Management

* **Audit Sessions:**

  * Internal/external audits created with due dates, auditor assignments.
  * Invite auditors via system or email.
* **Audit Dashboard:**

  * Displays compliance % (in/out of compliance, pending, accepted).
  * Tracks machine attestation (e.g., via SQL query) vs. human attestation (docs, images).
* **Workflow:**

  * Auditor reviews control + evidence, marks as accepted/rejected.
  * Comments/requests trigger notifications to stakeholders.
  * Status updated live (accepted, pending, out of compliance).

### 1.4 Test & Quality Management (QualityFolio)

* **Code-First Test Management:**

  * Markdown-driven definition of test plans, test cases, test runs, and test results.
  * Git-style workflow: users upload `.md` files → dashboards auto-generate.
* **Test Suites:**

  * Grouped by function (functional, performance, security).
  * Drill-down into individual test cases with pass/fail, results, execution history.
* **Integration with Automation:**

  * Results ingested from tools (JSON, XML, HTML).
  * CI/CD integration for automated test execution and reporting.
* **Defect Tracking Integration:**

  * JIRA, Bugzilla, Mantis supported.
  * Issues displayed with reporter, assignee, status (open/in-progress/completed).
* **Reporting & Export:**

  * CSV export of test results, defects, and dashboards.

### 1.5 Infrastructure & Observability (FleetFolio / Observability Layer)

* **System Inventory:**

  * Server and application metadata.
* **Observability:**

  * Database connections, performance metrics, application version tracking.
* **Monitoring:**

  * Continuous evidence collection and system logs feed into compliance dashboards.

### 1.6 AI-Enabled Capabilities

* **Policy Automation:**

  * AI assists in drafting and updating policies aligned to frameworks.
* **Audit Assistance:**

  * Natural language queries (e.g., “Show me the org chart evidence”) → system locates and displays evidence.
* **Control Mapping:**

  * AI helps identify control overlaps and evidence gaps.

---

## 2. Cross-Cutting Capabilities

* **Multi-Tenant Dashboards:** Tenant-specific isolation of data and dashboards.
* **Surveillor Integration:**

  * Fetches control/evidence data from multiple sources.
  * Configurable for customer security requirements.
* **Notifications:** Email alerts for auditor comments, evidence requests, or deadline risks.
* **Customizable Plans:** Engagement tiers (Plan 1, 2, 3) based on customer’s policy/evidence maturity.

---

## 3. Example End-to-End Workflow

1. **Onboarding:** Opsfolio provides pre-engagement questionnaire → baseline policies + controls mapped.
2. **Control & Policy Setup:** SCF controls loaded, customer policies linked or Opsfolio templates provided.
3. **Evidence Collection:** Automated (Surveillor agent) or manual document uploads.
4. **Audit Prep:** Evidence reviewed, gaps flagged, remediation guidance issued.
5. **Audit Execution:** Auditors access controls + mapped evidence, mark compliance status, leave comments.
6. **Continuous Monitoring:** For ongoing engagements, evidence refreshed and compliance tracked continuously.

---

✅ This spec positions Opsfolio Suite as an **integrated compliance + quality + observability platform**:

* Compliance mapping (controls, policies, evidence).
* Audit lifecycle management.
* Test & quality management.
* Observability + evidence automation.
* AI-driven acceleration.
