import { z } from "zod";
export interface EvidenceContentType {
  content: string;
  [Symbol.iterator]: () => Iterator<string>;
}

export interface Soc2Type {
  "#": number;
  "Control Identifier": string;
  "Common Criteria": string;
  "Questions Descriptions": string;
  "Status"?: string;
  "Fii ID": string;
}

export interface OriginalControl {
  "#": number;
  "Control Identifier": string;
  "Common Criteria": string;
  "Questions Descriptions": string;
  "FII Id": string;
  "Status"?: string;
}

export interface ControlDataType {
  "#": number;
  "Common Criteria": string;
  "HIPAA Security Rule Reference"?: string;
  "Control Identifier"?: string;
  "Safeguard": string;
  "Handled by nQ": string;
  "Questions Descriptions": string;
  "SCF Control Question"?: string;
  "Name"?: string;
  "FII Id"?: string;
  "Fii ID"?: string;
  "Status"?: string;
  "policies"?: {
    "title": string;
    "path": string;
  }[];
}
export interface ControlType {
  [key: string]: string | number | ({ title: string; path: string }[]);
  policies: {
    title: string;
    path: string;
  }[];
}
export interface PoliciesType {
  frontmatter: {
    satisfies: string | string[];
    title: string;
    weight: number;
    description: string;
    publishDate: string;
    publishBy: string;
    classification: string;
    documentVersion: string;
    documentType: string;
    approvedBy: string;
    category: string[];
    PCII: {
      companyName: string;
    };
  };
  file: string;
  url: string;
  rawContent: string;
  compiledContent: string;
  getHeadings: () => string[];
  Content: unknown;
  default: {
    isAstroComponentFactory: boolean;
    moduleId: string | undefined;
    propagation: unknown;
  };
}
export interface AicpaControl {
  "#": number;
  "Control Identifier": string;
  "Common Criteria": string;
  "Questions Descriptions": string;
  "Fii ID": string;
  Status?: string;
}
export interface AicpaControlType {
  "#": number;
  "Control Identifier": string;
  "Common Criteria": string;
  "Questions Descriptions": string;
  "Fii ID": string;
}
export interface AicpaCommonCriteriaGroup {
  "Common Criteria": string;
  controls: AicpaControl[];
}

export interface HippaControl {
  "#": number;
  "Common Criteria": string;
  "HIPAA Security Rule Reference": string;
  Safeguard: string;
  "Handled by nQ": string;
  "Questions Descriptions": string;
  Status?: string;
  "FII Id": string;
}

export interface HippaCommonCriteriaGroup {
  "Common Criteria": string;
  controls: HippaControl[];
}

export interface HippaOriginalControl {
  "#": number;
  "Common Criteria": string;
  "HIPAA Security Rule Reference": string;
  Safeguard: string;
  "Handled by nQ": string;
  "Questions Descriptions": string;
  "FII Id": string;
  Status?: string;
}
export interface ControlListType {
  "title": string;
  "dataVariable": string;
}
export interface PolicyType {
  "title": string;
  "path": string;
}

export interface HippaControl {
  "#": number;
  "Common Criteria": string;
  "HIPAA Security Rule Reference": string;
  Safeguard: string;
  "Handled by nQ": string;
  "Questions Descriptions": string;
  Status?: string;
  "FII Id": string;
}
export interface AuditSessionUser {
  email: string;
  userId: string;
  username: string;
  assignedOn: Date;
  status: string;
  role: string;
}
export interface AuditSessionType {
  sessionId: number;
  auditType: {
    auditTypeId: number;
    controlRegimeId: number;
    name: string;
    description: string;
    logoURL: string;
    createdAt: Date;
    updatedAt: Date;
    recordStatus: string;
  };
  name: string;
  controlRegimeName?: string;
  tenant: {
    tenantId: number;
    name: string;
    email: string;
    logoURL: string;
    description: string;
  };
  assignedPersons?: AuditSessionUser[];

  contactPerson?: {
    email: string;
    userId: number;
    username: string;
  };
  status: string;
  dueDate: string;
  createdAt: Date;
  updatedAt: Date;
  recordStatus: string;
  controls: {
    controlId: number;
    name: string;
    controls: [];
    createdAt: Date;
    updatedAt: Date;
    recordStatus: string;
  }[];
}

export interface CqiType {
  "#": number;
  Control: string;
  Policy: string;
  "Fii id": string;
  "Status"?: string;
}

export interface NetspectiveControlType {
  "#": number;
  "Control Identifier": string;
  "Common Criteria": string;
  "Questions Descriptions": string;
  "Status"?: string;
}

export interface AicpaReportControlType {
  "#": number;
  "Control Identifier": string;
  "Common Criteria": string;
  "Questions Descriptions": string;
  "Fii ID": string;
  Status: string;
  evidenceCount: number;
  policiesCount: number;
}

export interface AicpaCommonReportCriteriaGroup {
  "Common Criteria": string;
  controls: AicpaReportControlType[];
}

export interface AicpaControlReport {
  "#": number;
  "Control Identifier": string;
  "Common Criteria": string;
  "Questions Descriptions": string;
  "Fii ID": string;
  Status: string;
  evidenceCount: number;
  policiesCount: number;
}

export interface AicpaReport {
  controlData: AicpaCommonReportCriteriaGroup[];
  sessionName: string;
  controlTableData: [{
    title: string;
    dataVariable: string;
  }];
}
export interface Control {
  "#": number;
  "Control Identifier": string;
  "Common Criteria": string;
  "Questions Descriptions": string;
  "Fii ID": string;
  Status: string;
  evidenceCount: number;
  policiesCount: number;
}

export interface DataItem {
  "Common Criteria": string;
  controls: Control[];
}

export interface ConvertedData {
  fields: string[];
  data: (string | number)[][];
}
export interface AuditMember {
  party_name: string;
  invite_email: string;
  state: string;
  user_id: string;
  role?: string | null | undefined;
  email_notifications: boolean;
}
export interface HiTrustControl {
  "#": number;
  "Control Identifier": string;
  "Fii ID": string;
  "Common Criteria": string;
  "Name": string;
  "Description": string;
  Status?: string;
}
export interface ControlLoopData {
  "#": number;
  "Control Identifier": string;
  "Fii ID": string;
  "Common Criteria": string;
  "Name": string;
  "Description": string;
  Status?: string;
  evidenceCount: string;
  policiesCount: number;
}
export interface AssetData {
  "uri": string;
  "device_id": string;
  "content": string;
  "name": string;
}
export interface ThsaControl {
  "#": number;
  "Control Identifier": string;
  "Common Criteria": string;
  Safeguard: string;
  "SCF Control Question": string;
  Status?: string;
}

export interface ControlsType {
  display_order: number;
  control_identifier: string;
  fii: string;
  common_criteria: string;
  expected_evidence: string;
  question: string;
  control_regime: string;
  audit_type: string;
  status?: string;
  control_code: string;
  slug?: string;
}
export interface auditControlType {
  display_order: number;
  control_identifier: string;
  fii: string;
  common_criteria: string;
  expected_evidence: string;
  question: string;
  control_regime: string;
  audit_type: string;
  status: string;
}
export interface TransformedArrayType {
  common_criteria: string;
  controls: ControlsType[];
}
export interface ControlTypeGroup {
  common_criteria: string;
  controls: ControlReport[];
}
export interface ControlReport {
  display_order: number;
  control_id: string;
  fii: string;
  common_criteria: string;
  expected_evidence: string;
  question: string;
  control_regime: string;
  audit_type: string;
  status: string;
  evidenceCount: number;
  policiesCount: number;
  slug?: string;
}

export interface ControlData {
  status?: string;
  controlCode: string;
  controlQuestion: string;
  policyCount: string;
  evidenceCount: number;
  defaultSlug?: string;
  controlIdentifier: string;
}

// Define the structure for the control environment, which includes an array of controls
export interface ControlEnvironment {
  title: string;
  controls: ControlData[];
}
interface ControlTableDataType {
  title: string;
  dataVariable: string;
}
interface AuditStataticType {
  totalControls: number;
  outOfComplianceCount: number;
  evidenceAvailableCount: number;
  humanVerifiedEvidenceCount: number;
  acceptedByExternalAuditorCount: number;
}
export interface ControlListProps {
  controlTableData?: ControlTableDataType[];
  controlData?: ControlEnvironment[];
  sessionId: string;
  controlPageUrl: string;
  auditStatatics: AuditStataticType;
  auditTypeName?: string;
  auditType: string;
}
export interface AuditReportType {
  controlData: ControlTypeGroup[];
  sessionName: string;
  url?: string;
}
export interface ControlDetailType {
  display_order: string;
  control_identifier: string;
  fii: string;
  common_criteria: string;
  expected_evidence: string;
  question: string;
  control_code: string;
  policies: {
    title: string;
    path: string;
  }[];
}

export interface ControlsRegimeType {
  display_order: number;
  control_identifier: string;
  fii: string;
  common_criteria: string;
  expected_evidence: string;
  question: string;
  control_regime: string;
  audit_type: string;
  status?: string;
  control_code: string;
  slug?: string;
}

export interface ControlListRegimProps {
  controlData?: ControlRegimEnvironment[];
  controlDataType2?: ControlRegimEnvironment[];
  controlDataType3?: ControlRegimEnvironment[];
  sessionId: number;
  controlPageUrl?: string;
  auditTypeName?: string;
  auditTypeId?: number;
  controlType?: string;
}

export interface ControlRegimEnvironment {
  title: string;
  controls: ControlRegimData[];
}

export interface ControlRegimData {
  status?: string;
  controlCode: string;
  controlQuestion: string;
  defaultSlug?: string;
  controlIdentifier?: string;
}

export interface ScfDomainData {
  display_order: string;
  scf_domain: string;
  scf_id: string;
  security_privacy_design_principle: string;
  principle_intent: string;
}

export interface ScfData {
  scf_domain: string;
  scf_control: string;
  fii: string;
  scf_description: string;
  question: string;
  methods_to_comply?: string;
}

export interface ControlRegimSideMenuData {
  control_regime_id: string;
  audit_id: string;
  parent_id: string;
  name: string;
  regime_name: string;
  audit_count: number;
}

export interface RegimeMenuTree {
  control_regime_id: string;
  parent_id: string;
  name: string;
}

export interface AuditMenuTree {
  audit_id: string;
  parent_id: string;
  name: string;
}

export interface TreeNodeMenuTree {
  control_regime_id: string;
  parent_id: string;
  name: string;
  audit?: AuditMenuTree[];
}

const AuditSessionInfoSchema = z.object({
  audit_session_id: z.string(),
  title: z.string(),
  due_date: z.string(),
  created_at: z.string(),
  updated_at: z.string(),
  tenant_id: z.string(),
  status: z.string(),
  contact_person: z.string(),
  tenant_name: z.string(),
  audit_type_id: z.string(),
  audit_type: z.string(),
  control_regime_name: z.string(),
  control_regime_id: z.string(),
  percentage_of_completion: z.string()
});
export type AuditSessionInfo = z.infer<typeof AuditSessionInfoSchema>;