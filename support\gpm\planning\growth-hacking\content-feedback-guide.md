# Content Audit & Editing Checklist (For <mark>Ravi</mark> to own and signficantly improve)

## Prioritize Edits by Impact

1. **SEO & GEO Enhancements**

   * Run keyword audit (Google Trends, SEMrush, Ahrefs).
   * Align titles, headers, and body with high-intent search terms.
   * Ensure metadata, alt text, and internal linking support discoverability.

2. **Conversion Enhancers (CTAs)**

   * Every major section should connect to an Opsfolio benefit.
   * Insert clear, action-oriented CTAs (e.g., free tool, demo, case study).
   * Use placement that naturally follows a pain point → solution flow.

3. **Clarity & Usability**

   * Fix layout, typography, and formatting issues.
   * Ensure scannability (short paragraphs, bullets, clear subheads).
   * Add explicit ties between pain points and solutions.

## Evidence-First Editing Rules

* **DO**: Make changes supported by data or established heuristics.
* **DON’T**: Suggest edits based on personal taste or opinions.
* **Anchor decisions in:**

  * **Measurable outcomes** (SEO rankings, conversion rates, engagement).
  * **Nielsen Norman Group (NN/g)** usability & content heuristics (see `ai-context-engineering` docs).
  * **Industry benchmarks** for content performance.

## Structure & Style Heuristics

* Maintain clear narrative arc: *Pain → Broken System → Reimagining → Payoff*.
* For each solution, add a one-liner tying it back to the exact pain.
* Match tone to audience:

  * Executives → clear, authoritative, outcome-focused.
  * Engineers → direct, technical, practical.

## ✅ 4. Final Review Before Publishing

- [ ] Keywords optimized (checked against audit).
- [ ] CTAs added where relevant.
- [ ] Layout polished for readability.
- [ ] All edits tied to goals (SEO, conversion, usability).
- [ ] No “just my opinion” changes.
* [ ] References (NN/g, SEO data) noted where relevant.
Would you like me to also **design this checklist as a 1-page PDF handout** (with simple sections and checkboxes) so Ravi can literally print it or keep it pinned next to his workspace?
