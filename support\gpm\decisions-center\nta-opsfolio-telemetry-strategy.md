# Opsfolio telemetry and never events monitoring strategy 

The absence of proper observability across the platform represents a critical risk. Silent failures and undetected outages must be treated as  *‘Never Events’* —failures that should never occur without immediate detection and notification. To mitigate this, an observability strategy must be prioritized and implemented consistently across all components of the system

## 1. OpenTelemetry Instrumentation

- Since OpenTelemetry SDKs already in some Opsfolio services (frontend, backend, API, DB) we need to expand them everywhere.
- Ensure trace, metric, and log correlation across services so we can pinpoint where failures originate (rate limiting, network errors, backend unavailability, etc.).
- Capture key business events (e.g., "CMMC Assessment Unavailable", "Chat Service Timeout") as structured traces so downtime isn't just "server down" but tied to user-impacting workflows.

## 2. Detection of "Never Events"

- Define and codify a list of "Never Events" (e.g., entire site down > 5 minutes, CMMC Assessment not available > 15 minutes, chat backend not responding to > 10% requests).
- These should automatically trigger alerts via monitoring rules, not wait until a user notices.
- We'll maintain the list in Git alongside product features so engineering knows what's non-negotiable.

## 3. Monitoring & Alerting Pipeline

- Stream OpenTelemetry data into a monitoring backend (Grafana Tempo + Prometheus, or Datadog/New Relic if we choose SaaS).
- Define SLIs/SLOs (e.g., chat availability, assessment uptime, API response latency) and connect them to dashboards and alerting rules.
- Configure immediate alerts via:
  - **Email** – for non-urgent issues and reporting.
  - **SMS / PagerDuty / OpsGenie** – for urgent "Never Events" where downtime or broken features need 24/7 awareness.

## 4. Rate-Limit & Failure Transparency

- **User-facing**: Show clear messages when rate-limits are hit or when a feature is temporarily unavailable.
- **Engineering-facing**: Telemetry must differentiate between expected throttling vs. unexpected outages, so alerts aren't noisy.

## 5. Reliability Engineering Process

- Weekly review of telemetry dashboards to confirm alerts fired as expected.
- Monthly chaos testing of at least one "Never Event" (simulate API downtime or DB unavailability) to validate that alerts are triggered and routed correctly.

## What I need from you and the team:

- Show that status.opsfolio.com is deep and fully inclusive of all components across all ecosystem components
- Propose which OpenTelemetry collectors and monitoring backend you want to use (self-hosted Grafana stack vs. SaaS).
- Draft the initial "Never Events" list by end of week so we can review and approve.
- Estimate effort to instrument chat, assessments, and site availability with OpenTelemetry traces/metrics.

Once this is in place, we'll not only detect outages instantly but also have forensic detail to understand why they occurred. This will raise Opsfolio's reliability to the level we need.

---

## Opsfolio.com Never Events (Initial Draft)

Here's a starter "Never Events" list — the kinds of failures that should never happen without immediate detection and alerts. This is meant to be pragmatic, not exhaustive so you should use it as a starting point; we can expand it once OpenTelemetry instrumentation is in place and we see actual traffic patterns.

### 1. Platform Availability

- Entire site (https://www.opsfolio.com) is unreachable (5xx errors or timeouts) for > 5 minutes.
- Any major sub-application (CMMC Assessment, SOC2 Self-Assessment, Ask AI, etc.) unavailable for > 15 minutes.

### 2. Critical Service Failures

- Chat backend fails to respond to > 10% of requests in any 10-minute rolling window.
- API error rate (5xx) exceeds 5% over 5 minutes.
- Database connection pool exhausted or DB unreachable for > 2 minutes.
- Authentication/login failure rate > 5% over 10 minutes.

### 3. User-Impacting Errors

- Rate limit hit for more than 1% of users in a rolling hour (indicates misconfiguration or abnormal traffic).
- Default questions / assessment flows return empty or no responses for > 2% of requests.
- Any "silent failure" where users see broken UX but no telemetry event logged.

### 4. Infrastructure Health

- CPU utilization > 90% for > 10 minutes on production nodes.
- Memory utilization > 90% for > 10 minutes.
- Disk usage > 80% on primary DB or file storage.
- Collector/agent (OpenTelemetry, Prometheus, etc.) stops reporting.

### 5. Security/Anomaly Detection

- Unusual spike (> 3x baseline) in failed login attempts within 5 minutes.
- Unauthorized access attempts or 4xx/403 spikes.
- Configuration drift (e.g., unapproved change to TLS certs, DNS misconfigurations).

## Alerting Strategy

### Email Alerts

For non-urgent degradations (high CPU, disk > 70%, elevated error rate).

### SMS / PagerDuty Alerts

For Never Events: site down, assessment unavailable, chat backend broken, DB unreachable, auth failures, silent user-impacting failures.

### Daily Digest

System health, top errors, and performance metrics in email form to engineering leads.

## Next Step for the Team

- Add these "Never Events" to a YAML/JSON config that's checked into Git so both engineering and ops can see/edit it.
- Use OpenTelemetry metrics/logs/traces to drive alerting rules in Grafana/Prometheus (or SaaS equivalent).
- Test by simulating one Never Event per week (e.g., stop chat backend for 5 minutes, artificially spike error rates) to verify alerts.
