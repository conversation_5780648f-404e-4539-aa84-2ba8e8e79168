---
import Layout from "../../layouts/Layout.astro";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Shield,
  Eye,
  Activity,
  Target,
  AlertTriangle,
  CheckCircle,
  ArrowRight,
  Database,
  Zap,
  Users,
  BarChart3,
  Clock,
  FileText,
} from "lucide-react";

const benefits = [
  {
    title: "Real-Time Visibility",
    description: "Dashboards show live findings, not stale reports.",
    icon: Eye,
  },
  {
    title: "Faster Remediation",
    description: "Issues can be acted on as soon as they're identified.",
    icon: Zap,
  },
  {
    title: "Standardized Workflows",
    description: "Every finding follows a consistent lifecycle.",
    icon: Activity,
  },
  {
    title: "POA&M Integration",
    description:
      "Findings are automatically translated into POA&M items, tracked through to resolution.",
    icon: FileText,
  },
  {
    title: "Compliance-Ready Evidence",
    description:
      "All evidence is structured, mapped to controls, and tied to compliance obligations.",
    icon: CheckCircle,
  },
  {
    title: "Multi-Tenant Capable",
    description:
      "Opsfolio TEM supports MSPs/MSSPs delivering assessments across multiple customers.",
    icon: Users,
  },
];

const workflow = [
  {
    step: "1",
    title: "Ingest Evidence",
    description:
      "Opsfolio EAA executes authorized penetration testing workflows. All findings are normalized and stored in the evidence warehouse.",
  },
  {
    step: "2",
    title: "Centralize & Correlate",
    description:
      "Surveilr-based ingestion pipelines structure results into SQL. Opsfolio TEM uses SQLPage dashboards to surface evidence consistently.",
  },
  {
    step: "3",
    title: "Deliver & Automate",
    description:
      "TEM provides real-time dashboards showing vulnerabilities and exposures as soon as they're found. Findings are automatically routed into IT workflows.",
  },
  {
    step: "4",
    title: "Remediate & Track",
    description:
      "Issues are tracked through the TEM remediation lifecycle (Open → In Progress → Remediated → Validated → Closed).",
  },
  {
    step: "5",
    title: "Validate & Close the Loop",
    description:
      "When a POA&M item is marked as remediated, TEM automatically triggers Opsfolio EAA retests to confirm closure.",
  },
];

const useCases = [
  {
    title: "CISOs & Security Leaders",
    description:
      "Use TEM dashboards to measure exposure trends, MTTR, and compliance posture across multiple environments.",
    icon: Shield,
  },
  {
    title: "DevOps & IT Operations",
    description:
      "Receive EAA findings directly in Jira or ServiceNow via TEM automation, enabling quick fixes without switching tools.",
    icon: Target,
  },
  {
    title: "Compliance & Audit Teams",
    description:
      "Use TEM's evidence mappings to generate POA&M items automatically, ensuring remediation is tracked in compliance workflows.",
    icon: CheckCircle,
  },
  {
    title: "Service Providers (MSPs/MSSPs)",
    description:
      "Run Opsfolio EAA assessments across multiple customers and deliver standardized TEM dashboards for each tenant.",
    icon: Users,
  },
];

const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1",
    text: "Task",
    "aria-disabled": true,
  },
  {
    index: "2",
    text: "Threat Exposure Management",
    "aria-disabled": true,
  },
];
---

<Layout breadcrumbLinks={breadcrumbLinks}>
  {/* Hero Section */}
  <section
    class="py-20 bg-gradient-to-br from-primary/5 via-background to-accent/5"
  >
    <div class="container mx-auto px-4">
      <div class="max-w-4xl mx-auto text-center">
        <Badge variant="outline" className="mb-4">
          <Shield className="w-4 h-4 mr-2" />
          Enterprise Threat Exposure Management
        </Badge>
        <h1
          class="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent"
        >
          Opsfolio Threat Exposure Management (TEM)
        </h1>
        <p class="text-xl text-muted-foreground mb-8 leading-relaxed">
          Transform technical test results into actionable dashboards,
          workflows, and executive insights. Real-time, evidence-driven
          reporting that connects security assessments to remediation workflows.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a href="/task/tem-assessment">
            <Button size="lg">
              Get TEM Assessment
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </a>
          <a href="/demo">
            <Button variant="outline" size="lg"> View Platform Demo </Button>
          </a>
        </div>
      </div>
    </div>
  </section>

  {/* Problem Statement */}
  <section class="py-16 sm:py-20">
    <div class="container mx-auto px-4">
      <div class="max-w-4xl mx-auto">
        <Card className="border-l-4 border-l-destructive">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-destructive">
              <AlertTriangle className="w-5 h-5" />
              The pen testing and security assessments challenge
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 text-muted-foreground">
            <p>
              Security assessments are only as valuable as the actions they
              drive. Traditional penetration test reports often end up as static
              PDFs, spreadsheets, or siloed documents that are disconnected from
              day-to-day workflows.
            </p>
            <p class="font-medium text-foreground">
              The result: delays in remediation, limited visibility for
              leadership, and compliance gaps when auditors ask for proof.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  </section>

  {/* The Opsfolio Suite */}
  <section class="py-16 sm:py-20 bg-muted">
    <div class="container mx-auto px-4">
      <div class="text-center mb-12">
        <h2 class="text-3xl font-bold mb-4">The Opsfolio Suite</h2>
        <p class="text-lg text-muted-foreground max-w-3xl mx-auto">
          Opsfolio TEM and Opsfolio EAA are part of the Opsfolio Suite, which
          underpins Opsfolio Compliance-as-a-Service (CaaS) offerings.
        </p>
      </div>

      <div class="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
        <Card className="relative overflow-hidden">
          <div
            class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary to-primary/60"
          >
          </div>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="w-5 h-5 text-primary" />
              Opsfolio EAA
            </CardTitle>
            <CardDescription>Enterprise Assets Assessment</CardDescription>
          </CardHeader>
          <CardContent>
            <ul class="space-y-2 text-sm text-muted-foreground">
              <li>
                • Runs technical assessments (authorized penetration testing,
                asset discovery)
              </li>
              <li>
                • Produces structured artifacts stored in evidence warehouse
              </li>
              <li>
                • Ingests outputs from automated scanners and manual pentest
                workflows
              </li>
            </ul>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <div
            class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-accent to-accent/60"
          >
          </div>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5 text-accent" />
              Opsfolio TEM
            </CardTitle>
            <CardDescription>Threat Exposure Management</CardDescription>
          </CardHeader>
          <CardContent>
            <ul class="space-y-2 text-sm text-muted-foreground">
              <li>
                • Customer-facing UI/UX for exposure reporting and dashboards
              </li>
              <li>
                • Automates finding delivery, triage, and ticketing in real time
              </li>
              <li>• Powers executive dashboards and team communications</li>
            </ul>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <div
            class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-secondary to-secondary/60"
          >
          </div>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5 text-primary" />

              Opsfolio CaaS
            </CardTitle>
            <CardDescription>Compliance-as-a-Service</CardDescription>
          </CardHeader>
          <CardContent>
            <ul class="space-y-2 text-sm text-muted-foreground">
              <li>
                • Uses TEM and EAA to generate POA&M for regulated industries
              </li>
              <li>
                • Links threat assessment evidence to compliance frameworks
              </li>
              <li>• Provides continuous compliance pipeline</li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </div>
  </section>

  {/* How It Works */}
  <section class="py-16">
    <div class="container mx-auto px-4">
      <div class="text-center mb-12">
        <h2 class="text-3xl font-bold mb-4">
          How Opsfolio TEM & EAA Work Together
        </h2>
        <p class="text-lg text-muted-foreground max-w-3xl mx-auto">
          A seamless workflow from assessment to remediation validation
        </p>
      </div>

      <div class="max-w-4xl mx-auto">
        <div class="space-y-8">
          {
            workflow.map((item, index) => (
              <div class="flex gap-6 items-start">
                <div class="flex-shrink-0 w-12 h-12 rounded-full bg-primary text-primary-foreground flex items-center justify-center font-bold text-lg">
                  {item.step}
                </div>
                <div class="flex-1">
                  <h3 class="text-xl font-semibold mb-2">{item.title}</h3>
                  <p class="text-muted-foreground">{item.description}</p>
                </div>
              </div>
            ))
          }
        </div>
      </div>
    </div>
  </section>

  {/* Customer Benefits */}
  <section class="py-16 bg-muted">
    <div class="container mx-auto px-4">
      <div class="text-center mb-12">
        <h2 class="text-3xl font-bold mb-4">Customer Benefits</h2>
        <p class="text-lg text-muted-foreground max-w-3xl mx-auto">
          Move from static reports to continuous threat exposure management
        </p>
      </div>

      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
        {
          benefits.map((benefit, index) => (
            <Card key={index} className="h-full">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <benefit.icon className="w-5 h-5 text-primary" />
                  {benefit.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p class="text-muted-foreground">{benefit.description}</p>
              </CardContent>
            </Card>
          ))
        }
      </div>
    </div>
  </section>

  {/* Usage Examples */}
  <section class="py-16">
    <div class="container mx-auto px-4">
      <div class="text-center mb-12">
        <h2 class="text-3xl font-bold mb-4">Usage Examples</h2>
        <p class="text-lg text-muted-foreground max-w-3xl mx-auto">
          How different teams leverage Opsfolio TEM for their specific needs
        </p>
      </div>

      <div class="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto">
        {
          useCases.map((useCase, index) => (
            <Card key={index} className="h-full">
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <useCase.icon className="w-6 h-6 text-primary" />
                  {useCase.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p class="text-muted-foreground">{useCase.description}</p>
              </CardContent>
            </Card>
          ))
        }
      </div>
    </div>
  </section>

  {/* Summary */}
  <section class="py-16 bg-muted">
    <div class="container mx-auto px-4">
      <div class="max-w-4xl mx-auto text-center">
        <h2 class="text-3xl font-bold mb-6">
          Transform Your Security Operations
        </h2>
        <div class="grid md:grid-cols-3 gap-6 mb-8">
          <Card className="text-center">
            <CardContent className="pt-6">
              <Database className="w-12 h-12 text-primary mx-auto mb-4" />
              <h3 class="font-semibold mb-2">Opsfolio EAA</h3>
              <p class="text-sm text-muted-foreground">
                Technical assessment engine
              </p>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="pt-6">
              <BarChart3 className="w-12 h-12 text-accent mx-auto mb-4" />
              <h3 class="font-semibold mb-2">Opsfolio TEM</h3>
              <p class="text-sm text-muted-foreground">
                Reporting and communications experience
              </p>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="pt-6">
              <Shield className="w-12 h-12 text-primary mx-auto mb-4" />
              <h3 class="font-semibold mb-2">Opsfolio CaaS</h3>
              <p class="text-sm text-muted-foreground">
                POA&M workflows and compliance
              </p>
            </CardContent>
          </Card>
        </div>
        <p class="text-lg text-muted-foreground mb-8">
          With TEM and EAA, Opsfolio customers can move from static reports to
          continuous threat exposure management — improving security posture,
          reducing remediation delays, and achieving audit readiness faster.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a href="/contact">
            <Button size="lg">
              Start Your TEM Journey
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </a>
          <a href="/solutions">
            <Button variant="outline" size="lg"> Explore All Solutions </Button>
          </a>
        </div>
      </div>
    </div>
  </section>
</Layout>
