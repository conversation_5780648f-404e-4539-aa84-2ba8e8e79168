#!/usr/bin/env -S deno run --allow-read --allow-write

import { DB } from "https://deno.land/x/sqlite/mod.ts";
import { join, dirname } from "https://deno.land/std@0.224.0/path/mod.ts";
import { ensureDir, exists } from "https://deno.land/std@0.224.0/fs/mod.ts";
import { parse as parseYaml, stringify as stringifyYaml } from "https://deno.land/std@0.224.0/yaml/mod.ts";
import "https://deno.land/std@0.224.0/dotenv/load.ts";

const AI_CONTEXT_ENGINEERING_PATH = Deno.env.get("AI_CONTEXT_ENGINEERING_PATH");
const DB_PATH = Deno.env.get("SURVEILR_DB_PATH");

function splitFrontmatter(content: string): { frontmatter: string | null; body: string } {
  const match = content.match(/^---\s*([\s\S]*?)\s*---\s*([\s\S]*)$/);
  if (match) {
    return {
      frontmatter: match[1],
      body: match[2].trim(),
    };
  }
  return { frontmatter: null, body: content.trim() };
}

async function exportMergedPromptsToFiles(dbFile: string) {
  let db: DB | null = null;

  // Counters for summary
  let total = 0;
  let succeeded = 0;
  let skipped = 0;
  let failed = 0;

  try {
    db = new DB(dbFile, { mode: "read" });

    //  Remove old build folder
    const buildFolderToDelete = join(AI_CONTEXT_ENGINEERING_PATH!, ".build", "anythingllm");
    if (await exists(buildFolderToDelete)) {
      await Deno.remove(buildFolderToDelete, { recursive: true });
      console.log(`🧹 Removed existing folder: ${buildFolderToDelete}`);
    } else {
      console.log(`📁 No existing folder to delete at: ${buildFolderToDelete}`);
    }

    //  Load common prompt bodies
    const commonPromptQuery = `
      SELECT body_text
      FROM ai_ctxe_prompt
      WHERE merge_group = 'common'
      ORDER BY ord ASC;
    `;
    const commonPromptRows = db.query<[string]>(commonPromptQuery);
    const commonBodies: string[] = [];

    for (const [bodyText] of commonPromptRows) {
      const { body } = splitFrontmatter(bodyText);
      commonBodies.push(body);
    }

    const commonPromptBody = commonBodies.join("\n\n").trim();

    //  Get common file paths as dependencies
    const commonFileQuery = `
      SELECT substr(uri, instr(uri, 'opsfolio.com/') + length('opsfolio.com/')) AS file_path
      FROM uniform_resource ur
      JOIN ai_ctxe_prompt a ON a.uniform_resource_id = ur.uniform_resource_id
      WHERE a.merge_group = 'common';
    `;
    const commonFileRows = db.query<[string]>(commonFileQuery);
    const commonFilePaths = commonFileRows.map(([path]) => path).sort();

    //  First, export common prompts as-is (without merging)
    const commonQuery = `
      SELECT urt.content, urf.file_path_rel, urt.uri
      FROM uniform_resource_transform urt
      LEFT JOIN uniform_resource_file urf ON urt.uniform_resource_id = urf.uniform_resource_id
      LEFT JOIN ai_ctxe_prompt acp ON urt.uniform_resource_id = acp.uniform_resource_id
      WHERE urt.nature IN ('system_prompt', 'rag_doc')
        AND urt.content IS NOT NULL
        AND urf.file_path_rel IS NOT NULL
        AND acp.merge_group = 'common';
    `;
    const commonRows = db.query<[string, string, string]>(commonQuery);

    console.log(` Processing ${commonRows.length} common prompts (no merging)...`);
    
    for (const [content, filePathRel, uri] of commonRows) {
      try {
        if (!content || !filePathRel) {
          console.warn(` Skipped common entry due to missing content or file path: ${uri}`);
          skipped++;
          continue;
        }

        // For common prompts, export as-is without merging
        let cleanRelPath = filePathRel;
        const redundantPrefix = ".build/anythingllm/";
        if (filePathRel.startsWith(redundantPrefix)) {
          cleanRelPath = filePathRel.slice(redundantPrefix.length);
        }

        const buildFilePath = join(
          AI_CONTEXT_ENGINEERING_PATH!,
          ".build",
          "anythingllm",
          cleanRelPath,
        );

        const dir = dirname(buildFilePath);
        await ensureDir(dir);
        await Deno.writeTextFile(buildFilePath, content);
        console.log(` Saved common prompt (no merge): ${buildFilePath}`);
        succeeded++;
      } catch (e) {
        console.error(` Error processing common URI: ${uri}\n`, e);
        failed++;
        continue;
      }
    }

    // 🔍 Get transform entries - EXCLUDING common merge group to avoid self-merge
    const query = `
      SELECT urt.content, urf.file_path_rel, urt.uri
      FROM uniform_resource_transform urt
      LEFT JOIN uniform_resource_file urf ON urt.uniform_resource_id = urf.uniform_resource_id
      LEFT JOIN ai_ctxe_prompt acp ON urt.uniform_resource_id = acp.uniform_resource_id
      WHERE urt.nature IN ('system_prompt', 'rag_doc')
        AND urt.content IS NOT NULL
        AND urf.file_path_rel IS NOT NULL
        AND (acp.merge_group IS NULL OR acp.merge_group != 'common');
    `;
    const rows = db.query<[string, string, string]>(query);

    console.log(`🔄 Processing ${rows.length} non-common prompts (with merging)...`);

    if (commonRows.length === 0 && rows.length === 0) {
      console.log(" No transforms found to export. Exiting.");
      return;
    }

    total = commonRows.length + rows.length;

    for (const [content, filePathRel, uri] of rows) {
      try {
        if (!content || !filePathRel) {
          console.warn(` Skipped entry due to missing content or file path: ${uri}`);
          skipped++;
          continue;
        }

        const { frontmatter, body } = splitFrontmatter(content);

        if (!frontmatter) {
          console.warn(` Skipped due to missing frontmatter: ${uri}`);
          skipped++;
          continue;
        }

        let parsedFrontmatter: Record<string, any>;
        try {
          parsedFrontmatter = parseYaml(frontmatter) as Record<string, any>;
        } catch (e) {
          console.error(` Failed to parse YAML frontmatter: ${uri}\n`, e);
          failed++;
          continue;
        }

        // Merge dependencies
        if (!parsedFrontmatter.provenance) {
          parsedFrontmatter.provenance = {};
        }

        const existingDeps = parsedFrontmatter.provenance.dependencies ?? [];
        const allDeps = Array.from(new Set([...existingDeps, ...commonFilePaths])).sort();
        parsedFrontmatter.provenance.dependencies = allDeps;

        const updatedFrontmatter = `---\n${stringifyYaml(parsedFrontmatter).trim()}\n---`;

        const composed = `${updatedFrontmatter}\n\n${commonPromptBody}\n\n${body.trim()}`;

        // Clean path
        let cleanRelPath = filePathRel;
        const redundantPrefix = ".build/anythingllm/";
        if (filePathRel.startsWith(redundantPrefix)) {
          cleanRelPath = filePathRel.slice(redundantPrefix.length);
        }

        const buildFilePath = join(
          AI_CONTEXT_ENGINEERING_PATH!,
          ".build",
          "anythingllm",
          cleanRelPath,
        );

        const dir = dirname(buildFilePath);
        await ensureDir(dir);
        await Deno.writeTextFile(buildFilePath, composed);
        console.log(` Saved merged prompt: ${buildFilePath}`);
        succeeded++;
      } catch (e) {
        console.error(` Error processing URI: ${uri}\n`, e);
        failed++;
        continue;
      }
    }

    // 🎉 Summary
    console.log("\n Export Summary:");
    console.log(`   Total prompts found: ${total}`);
    console.log(`    Successfully exported: ${succeeded}`);
    console.log(`    Skipped (missing data): ${skipped}`);
    console.log(`    Failed: ${failed}`);

  } catch (e) {
    console.error(`Critical error: ${e}`);
    console.error("Ensure the database path and env vars are correct.");
  } finally {
    db?.close();
    console.log(" Database connection closed.");
  }
}

if (import.meta.main) {
  exportMergedPromptsToFiles(DB_PATH!);
}