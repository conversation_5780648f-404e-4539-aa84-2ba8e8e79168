-- index.sql
-- title: Opsfolio Analytics Overview
-- menu: Overview
-- description: High-level KPIs, top pages, top referrers, and small time series.

-- Filters form defaults and helper values
WITH params AS (
  SELECT
    coalesce(:from::timestamp, now() - interval '7 days') AS from_ts,
    coalesce(:to::timestamp, now()) AS to_ts,
    NULLIF(trim(coalesce(:env, '')), '')::text       AS env,
    NULLIF(trim(coalesce(:service, '')), '')::text   AS service,
    NULLIF(trim(coalesce(:path_like, '')), '')::text AS path_like
),
-- Pick appropriate truncation: hour if window <= 21 days, else day
range_meta AS (
  SELECT
    from_ts,
    to_ts,
    env,
    service,
    path_like,
    CASE WHEN (to_ts - from_ts) > interval '21 days' THEN 'day' ELSE 'hour' END AS trunc_unit
  FROM params
)

-- KPI: Sessions
SELECT
  'sessions' AS kpi,
  COUNT(DISTINCT wt.session_id) AS value
FROM opsfolio.web_traffic wt
JOIN range_meta rm ON true
WHERE wt.ts >= rm.from_ts
  AND wt.ts < rm.to_ts
  AND (rm.env IS NULL OR rm.env = wt.env)
  AND (rm.service IS NULL OR rm.service = wt.service)
  AND (rm.path_like IS NULL OR wt.request_path ILIKE '%' || rm.path_like || '%');

-- KPI: Unique users (events view)
SELECT
  'unique_users' AS kpi,
  COUNT(DISTINCT COALESCE(ev.user_id, ev.session_id)) AS value
FROM opsfolio.astro_events ev
JOIN range_meta rm ON true
WHERE ev.ts >= rm.from_ts
  AND ev.ts < rm.to_ts
  AND (rm.env IS NULL OR rm.env = ev.env)
  AND (rm.service IS NULL OR rm.service = ev.service)
  AND (rm.path_like IS NULL OR ev.request_path ILIKE '%' || rm.path_like || '%');

-- KPI: Requests (successful 2xx)
SELECT
  'requests_2xx' AS kpi,
  COUNT(*) FILTER (WHERE wt.status_code BETWEEN 200 AND 299) AS value
FROM opsfolio.web_traffic wt
JOIN range_meta rm ON true
WHERE wt.ts >= rm.from_ts
  AND wt.ts < rm.to_ts
  AND (rm.env IS NULL OR rm.env = wt.env)
  AND (rm.service IS NULL OR rm.service = wt.service)
  AND (rm.path_like IS NULL OR wt.request_path ILIKE '%' || rm.path_like || '%');

-- KPI: Error rate (4xx+5xx share)
SELECT
  'error_rate' AS kpi,
  CASE WHEN COUNT(*) = 0 THEN 0.0
       ELSE SUM(CASE WHEN wt.status_code >= 400 THEN 1 ELSE 0 END)::numeric / COUNT(*)::numeric
  END AS value
FROM opsfolio.web_traffic wt
JOIN range_meta rm ON true
WHERE wt.ts >= rm.from_ts
  AND wt.ts < rm.to_ts
  AND (rm.env IS NULL OR rm.env = wt.env)
  AND (rm.service IS NULL OR rm.service = wt.service)
  AND (rm.path_like IS NULL OR wt.request_path ILIKE '%' || rm.path_like || '%');

-- Top Pages: request_path, unique_sessions, avg_status_2xx_rate (limit 20)
SELECT
  wt.request_path,
  COUNT(DISTINCT wt.session_id) AS unique_sessions,
  COUNT(*) FILTER (WHERE wt.status_code BETWEEN 200 AND 299)::numeric / NULLIF(COUNT(*),0)::numeric AS pct_2xx,
  AVG(wt.body_bytes_sent) AS avg_bytes,
  COUNT(*) AS requests
FROM opsfolio.web_traffic wt
JOIN range_meta rm ON true
WHERE wt.ts >= rm.from_ts
  AND wt.ts < rm.to_ts
  AND (rm.env IS NULL OR rm.env = wt.env)
  AND (rm.service IS NULL OR rm.service = wt.service)
  AND (rm.path_like IS NULL OR wt.request_path ILIKE '%' || rm.path_like || '%')
GROUP BY wt.request_path
ORDER BY unique_sessions DESC
LIMIT 20;

-- Top Referrers: referer, sessions, est_bounce_rate
-- Bounce proxy: session's first hit in window; if no subsequent hit in window -> bounce.
WITH first_hits AS (
  SELECT wt.session_id, MIN(wt.ts) AS first_ts
  FROM opsfolio.web_traffic wt
  JOIN range_meta rm ON true
  WHERE wt.ts >= rm.from_ts
    AND wt.ts < rm.to_ts
    AND (rm.env IS NULL OR rm.env = wt.env)
    AND (rm.service IS NULL OR rm.service = wt.service)
    AND (rm.path_like IS NULL OR wt.request_path ILIKE '%' || rm.path_like || '%')
  GROUP BY wt.session_id
),
session_hits AS (
  SELECT wt.session_id, wt.ts, wt.referer
  FROM opsfolio.web_traffic wt
  JOIN range_meta rm ON true
  WHERE wt.ts >= rm.from_ts
    AND wt.ts < rm.to_ts
    AND (rm.env IS NULL OR rm.env = wt.env)
    AND (rm.service IS NULL OR rm.service = wt.service)
    AND (rm.path_like IS NULL OR wt.request_path ILIKE '%' || rm.path_like || '%')
),
first_hit_with_follow AS (
  SELECT
    fh.session_id,
    sh.referer,
    CASE WHEN EXISTS (
      SELECT 1 FROM session_hits sh2
      WHERE sh2.session_id = fh.session_id
        AND sh2.ts > fh.first_ts
    ) THEN 0 ELSE 1 END AS is_bounce
  FROM first_hits fh
  LEFT JOIN session_hits sh ON sh.session_id = fh.session_id AND sh.ts = fh.first_ts
)
SELECT
  COALESCE(referer, '(direct)') AS referer,
  COUNT(DISTINCT session_id) AS sessions,
  SUM(is_bounce)::numeric / NULLIF(COUNT(DISTINCT session_id),0)::numeric AS est_bounce_rate
FROM first_hit_with_follow
GROUP BY referer
ORDER BY sessions DESC
LIMIT 20;

-- Time series: requests per bucket + unique sessions
-- Bucket by hour/day depending on window size
WITH bounds AS (SELECT * FROM range_meta),
series AS (
  SELECT
    CASE WHEN bounds.trunc_unit = 'day' THEN date_trunc('day', wt.ts)
         ELSE date_trunc('hour', wt.ts) END AS bucket,
    COUNT(*) AS requests,
    COUNT(DISTINCT wt.session_id) AS unique_sessions
  FROM opsfolio.web_traffic wt, bounds
  WHERE wt.ts >= bounds.from_ts
    AND wt.ts < bounds.to_ts
    AND (bounds.env IS NULL OR bounds.env = wt.env)
    AND (bounds.service IS NULL OR bounds.service = wt.service)
    AND (bounds.path_like IS NULL OR wt.request_path ILIKE '%' || bounds.path_like || '%')
  GROUP BY bucket
  ORDER BY bucket
)
SELECT bucket AS ts_bucket, requests, unique_sessions FROM series;
