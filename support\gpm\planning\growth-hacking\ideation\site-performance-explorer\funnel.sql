-- funnel.sql
-- title: Funnel
-- menu: Funnel
-- description: Canonical session funnel: / → /get-started → /contact → signup (events)

WITH params AS (
  SELECT
    coalesce(:from::timestamp, now() - interval '7 days') AS from_ts,
    coalesce(:to::timestamp, now()) AS to_ts,
    NULLIF(trim(coalesce(:env, '')), '')::text       AS env,
    NULLIF(trim(coalesce(:service, '')), '')::text   AS service,
    NULLIF(trim(coalesce(:path_like, '')), '')::text AS path_like
),
-- sessions with which paths they hit in window
session_paths AS (
  SELECT
    wt.session_id,
    wt.request_path,
    wt.ts
  FROM opsfolio.web_traffic wt
  JOIN params p ON true
  WHERE wt.ts >= p.from_ts
    AND wt.ts < p.to_ts
    AND (p.env IS NULL OR p.env = wt.env)
    AND (p.service IS NULL OR p.service = wt.service)
    AND (p.path_like IS NULL OR wt.request_path ILIKE '%' || p.path_like || '%')
),
-- identify sessions that reached specific canonical pages
stage_reached AS (
  SELECT
    session_id,
    MAX(CASE WHEN request_path = '/' THEN 1 ELSE 0 END) AS reached_home,
    MAX(CASE WHEN request_path ILIKE '/get-started%' THEN 1 ELSE 0 END) AS reached_get_started,
    MAX(CASE WHEN request_path ILIKE '/contact%' THEN 1 ELSE 0 END) AS reached_contact
  FROM session_paths
  GROUP BY session_id
),
-- signup reach from events view
signup_sessions AS (
  SELECT DISTINCT ev.session_id
  FROM opsfolio.astro_events ev
  JOIN params p ON true
  WHERE ev.ts >= p.from_ts
    AND ev.ts < p.to_ts
    AND (p.env IS NULL OR p.env = ev.env)
    AND (p.service IS NULL OR p.service = ev.service)
    AND (ev.event ILIKE '%signup%' OR ev.event ILIKE '%registered%' OR ev.event = 'signup')
),
all_sessions AS (
  -- all sessions that have any web_traffic in window (applies request_path filter)
  SELECT DISTINCT wt.session_id
  FROM opsfolio.web_traffic wt
  JOIN params p ON true
  WHERE wt.ts >= p.from_ts
    AND wt.ts < p.to_ts
    AND (p.env IS NULL OR p.env = wt.env)
    AND (p.service IS NULL OR p.service = wt.service)
    AND (p.path_like IS NULL OR wt.request_path ILIKE '%' || p.path_like || '%')
)

-- Single-row funnel counts
SELECT
  (SELECT COUNT(*) FROM all_sessions) AS sessions_total,
  (SELECT COUNT(*) FROM stage_reached WHERE reached_home = 1) AS reached_home,
  (SELECT COUNT(*) FROM stage_reached WHERE reached_get_started = 1) AS reached_get_started,
  (SELECT COUNT(*) FROM stage_reached WHERE reached_contact = 1) AS reached_contact,
  (SELECT COUNT(*) FROM signup_sessions) AS reached_signup;

-- Per-stage conversion table
SELECT
  stage,
  cnt AS sessions,
  ROUND(100.0 * (cnt::numeric / NULLIF(prev_cnt,0)::numeric),2) AS pct_of_prev
FROM (
  SELECT 'sessions_total' AS stage, s_total.cnt, NULL::int AS prev_cnt FROM (SELECT COUNT(*) AS cnt FROM all_sessions) s_total
  UNION ALL
  SELECT 'home', (SELECT COUNT(*) FROM stage_reached WHERE reached_home = 1),
         (SELECT COUNT(*) FROM all_sessions)
  UNION ALL
  SELECT 'get_started', (SELECT COUNT(*) FROM stage_reached WHERE reached_get_started = 1),
         (SELECT COUNT(*) FROM stage_reached WHERE reached_home = 1)
  UNION ALL
  SELECT 'contact', (SELECT COUNT(*) FROM stage_reached WHERE reached_contact = 1),
         (SELECT COUNT(*) FROM stage_reached WHERE reached_get_started = 1)
  UNION ALL
  SELECT 'signup', (SELECT COUNT(*) FROM signup_sessions),
         (SELECT COUNT(*) FROM stage_reached WHERE reached_contact = 1)
) t(stage,cnt,prev_cnt);

-- Top Session Paths: concatenate top 5 ordered by ts per session, then count distinct sequences
WITH seqs AS (
  SELECT
    session_id,
    string_agg(request_path, ' -> ' ORDER BY ts) AS full_seq,
    -- truncated to first 5
    (SELECT string_agg(p.request_path,' -> ')
     FROM (SELECT request_path FROM session_paths sp2 WHERE sp2.session_id = sp.session_id ORDER BY ts LIMIT 5) p
    ) AS seq5
  FROM session_paths sp
  GROUP BY session_id
)
SELECT seq5 AS path_sequence, COUNT(*) AS sessions
FROM seqs
GROUP BY seq5
ORDER BY sessions DESC
LIMIT 10;
