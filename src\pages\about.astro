---
import Layout from "../layouts/Layout.astro";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Shield, 
  Users, 
  Target, 
  Award,
  CheckCircle,
  ArrowRight,
  Heart,
  Zap,
  Clock
} from "lucide-react";


const values = [
  {
    icon: Shield,
    title: "Trust First",
    description: "We build trust through transparency, expertise, and delivering on our promises."
  },
  {
    icon: Target,
    title: "Outcome Focused",
    description: "We measure success by your certifications passed, not hours billed."
  },
  {
    icon: Zap,
    title: "Efficiency Driven",
    description: "We use AI and automation to eliminate busy work and focus on what matters."
  },
  {
    icon: Heart,
    title: "Human Centered",
    description: "Technology amplifies human expertise, but people drive compliance success."
  }
];

const team = [
  {
    name: "<PERSON>",
    role: "CEO & Founder",
    background: "Former CISO at two unicorn startups, led 12+ SOC2 audits",
    credentials: "CISSP, CISA"
  },
  {
    name: "<PERSON>",
    role: "CTO & Co-Founder", 
    background: "Ex-Google security engineer, built compliance automation platforms",
    credentials: "MS Computer Science"
  },
  {
    name: "Dr. <PERSON>",
    role: "Chief Compliance Officer",
    background: "20+ years in GRC, former Big Four compliance partner",
    credentials: "J<PERSON>, CPA, CISA"
  },
  {
    name: "David Kim",
    role: "VP of Engineering",
    background: "Led security teams at fintech unicorns, expert in DevSecOps",
    credentials: "CISSP, AWS Security"
  }
];

const certifications = [
  "SOC2 Type 1 & 2",
  "HIPAA",
  "ISO 27001",
  "CMMC Level 2",
  "FedRAMP",
  "HITRUST"
];
const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'About',"aria-disabled": true ,
  }
];
 
---

<Layout breadcrumbLinks ={breadcrumbLinks}>

    <div class="min-h-screen bg-background">      
      <main>
        {/* Hero Section */}
        <div class="relative isolate px-6 pt-14 lg:px-8 overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5 pb-20">
          <div class="mx-auto max-w-4xl py-24 sm:py-32">
            <div class="text-center">
              <Badge variant="secondary" className="mb-4">About Opsfolio</Badge>
              <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
                Compliance Experts Who{" "}
                <span class="text-primary">Actually Get It Done</span>
              </h1>
              <p class="mt-6 text-lg leading-8 text-muted-foreground max-w-3xl mx-auto">
                We're not just another compliance tool. We're compliance engineers, former CISOs & security engineers, 
                and security experts who understand that getting certified is just the beginning.
              </p>
            </div>
          </div>
        </div>

        {/* Mission Section */}
        <div class="py-24 sm:py-32">
          <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl mb-6">
                  Our Mission
                </h2>
                <p class="text-lg text-muted-foreground mb-6">
                  Make enterprise-grade compliance accessible to every growing company. 
                  We believe that security and compliance should accelerate business growth, 
                  not slow it down.
                </p>
                <p class="text-muted-foreground mb-8">
                  Too many companies spend months or years trying to figure out compliance on their own, 
                  hiring expensive consultants, or buying tools that don't actually help them pass audits. 
                  We built Opsfolio to be different—a true system of record that combines expert guidance 
                  with intelligent automation.
                </p>
                <div class="space-y-3">
                  <div class="flex items-center space-x-3">
                    <CheckCircle className="h-5 w-5 text-success" />
                    <span class="text-foreground">Outcome guarantee on all certifications</span>
                  </div>
                  <div class="flex items-center space-x-3">
                    <CheckCircle className="h-5 w-5 text-success" />
                    <span class="text-foreground">Expert-guided process from day one</span>
                  </div>
                  <div class="flex items-center space-x-3">
                    <CheckCircle className="h-5 w-5 text-success" />
                    <span class="text-foreground">AI-powered automation where it matters</span>
                  </div>
                </div>
              </div>
              <div class="bg-gradient-to-br from-primary/10 to-primary/5 p-8 rounded-2xl">
                <div class="text-center">
                  <div class="grid grid-cols-2 gap-8">
                    <div>
                      <div class="text-3xl font-bold text-primary">2 months</div>
                      <div class="text-sm text-muted-foreground">Average time to SOC2</div>
                    </div>
                    <div>
                      <div class="text-3xl font-bold text-primary">100%</div>
                      <div class="text-sm text-muted-foreground">Audit pass rate</div>
                    </div>
                    <div>
                      <div class="text-3xl font-bold text-primary">50+</div>
                      <div class="text-sm text-muted-foreground">Companies certified</div>
                    </div>
                    <div>
                      <div class="text-3xl font-bold text-primary">6</div>
                      <div class="text-sm text-muted-foreground">Compliance frameworks</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Values Section */}
        <div class="py-24 sm:py-32 bg-muted">
          <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="text-center mb-16">
              <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                Our Values
              </h2>
              <p class="mt-4 text-lg text-muted-foreground">
                The principles that guide how we work with every client
              </p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {values.map((value) => {
                const Icon = value.icon;
                return (
                  <Card key={value.title} className="text-center">
                    <CardContent className="p-6">
                      <div class="flex items-center justify-center w-12 h-12 bg-primary/10 rounded-lg mx-auto mb-4">
                        <Icon className="h-6 w-6 text-primary" />
                      </div>
                      <h3 class="text-lg font-semibold text-foreground mb-3">{value.title}</h3>
                      <p class="text-sm text-muted-foreground">{value.description}</p>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        </div>

        {/* Team Section */}
        <div class="py-24 sm:py-32">
          <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="text-center mb-16">
              <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                Meet the Team
              </h2>
              <p class="mt-4 text-lg text-muted-foreground">
                Compliance experts with real-world experience at scale
              </p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
              {team.map((member) => (
                <Card key={member.name} className="hover:shadow-lg transition-all duration-300">
                  <CardContent className="p-8">
                    <div class="flex items-start space-x-4">
                      <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
                        <Users className="h-8 w-8 text-primary" />
                      </div>
                      <div class="flex-1">
                        <h3 class="text-xl font-semibold text-foreground mb-1">{member.name}</h3>
                        <div class="text-primary font-medium mb-3">{member.role}</div>
                        <p class="text-sm text-muted-foreground mb-3">{member.background}</p>
                        <Badge variant="secondary" className="text-xs">{member.credentials}</Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* Certifications Section */}
        <div class="py-24 sm:py-32 bg-muted">
          <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="text-center mb-12">
              <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                Compliance Frameworks We Support
              </h2>
              <p class="mt-4 text-lg text-muted-foreground">
                From startups to enterprises, we help you achieve the certifications you need
              </p>
            </div>
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
              {certifications.map((cert) => (
                <div
                  class="bg-background p-6 rounded-lg text-center hover:shadow-md transition-all duration-300"
                >
                  <Award className="h-8 w-8 text-primary mx-auto mb-3" />
                  <div class="text-sm font-medium text-foreground">{cert}</div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div>
          <div class="px-6 py-24 sm:px-6 sm:py-32 lg:px-8">
            <div class="mx-auto max-w-2xl text-center">
              <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                Ready to Work Together?
              </h2>
              <p class="mx-auto mt-6 max-w-xl text-lg leading-8 text-muted-foreground/90">
                Let's discuss your compliance goals and create a plan that works for your business.
              </p>
              <div class="mt-10 flex items-center justify-center gap-x-6">
                <a href="/contact">
                  <Button 
                    size="lg" 
                    variant="default"                 
                  >Meet the Team
                      <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </a>
                
                  <a href="/get-started">
                    <Button 
                      size="lg" 
                      variant="outline"
                    >
                        Start Your Journey
                    </Button>
                  </a>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  
</Layout>
