---
import Layout from "../../layouts/Layout.astro";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  CheckCircle,
  Code,
  Shield,
  Clock,
  ArrowRight,
  Users,
} from "lucide-react";

const benefits = [
  {
    icon: Shield,
    title: "Build Trust Into Your Stack",
    description: "Embed compliance directly into your development workflow without slowing down innovation."
  },
  {
    icon: Clock,
    title: "60% Faster Time-to-Market",
    description: "Get enterprise-ready faster with automated compliance that doesn't require a dedicated team."
  },
  {
    icon: Code,
    title: "Developer-Friendly Integration",
    description: "APIs, CLI tools, and CI/CD integrations that your team will actually want to use."
  },
  {
    icon: Users,
    title: "Scale Without Hiring",
    description: "Get fractional CCO expertise without the overhead of a full compliance hire."
  }
];

const outcomes = [
  "SOC2 Type 2 certification in 2 months (vs 12+ months DIY)",
  "Enterprise sales acceleration with trust credentials",
  "Automated evidence collection from existing dev tools",
  "Compliance documentation that passes audits",
  "Real-time compliance posture monitoring"
];

const testimonial = {
  quote: "We’re excited about the future. With Opsfolio CaaS as our partner, we’re not just achieving compliance; we’re building a sustainable future for our clients and the planet.",
  author: "Tara Gupta",
  role: "Founder & CEO",
  company: "Map Collective"
};


const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Role',"aria-disabled": true ,
  },
  {
    index: "2", text: "CTOs & Tech Leaders","aria-disabled": true 
  },
];
---

<Layout 
  breadcrumbLinks={breadcrumbLinks}>
  <main>
      {/* Hero Section */}
      <div class="relative isolate px-6 pt-14 lg:px-8 bg-gradient-to-br from-primary/5 via-background to-secondary/5 ">
        <div class="mx-auto max-w-4xl py-16 sm:py-24 lg:py-28">
          <div class="text-center">
            <Badge variant="secondary" className="mb-4">For CTOs & Tech Leaders</Badge>
            <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
              Build a Trust Layer into Your Stack —{" "}
              <span class="text-primary">Without Building a Compliance Team</span>
            </h1>
            <p class="mt-6 text-lg leading-8 text-muted-foreground max-w-3xl mx-auto">
              Stop losing enterprise deals to compliance requirements. Get SOC2, HIPAA, and ISO certifications
              that integrate seamlessly with your existing development workflow.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
               <a href="/demo">
              <Button size="lg" variant="default">
                See How CTOs & Tech Leaders Use Opsfolio
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button></a>
              <a href="/contact">
                <Button variant="outline" size="lg" >                
                    Talk to Our CTO
                </Button>
              </a>              
            </div>
          </div>
        </div>
      </div>

      {/* Benefits Section */}
      <div class="py-24 sm:py-32">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center mb-16">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Why CTOs & Tech Leaders Choose Opsfolio
            </h2>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Compliance that works with your tech stack, not against it
            </p>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            {benefits.map((benefit) => {
              const Icon = benefit.icon;
              return (
                <Card key={benefit.title} className="border-2 hover:border-primary/20 transition-all">
                  <CardHeader>
                    <div class="flex items-center space-x-3">
                      <div class="flex items-center justify-center w-10 h-10 bg-primary/10 rounded-lg">
                        <Icon className="h-5 w-5 text-primary" />
                      </div>
                      <CardTitle className="text-xl">{benefit.title}</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p class="text-muted-foreground">{benefit.description}</p>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </div>

      {/* Outcomes Section */}
      <div class="py-24 sm:py-32 bg-muted">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl mb-6">
                Proven Outcomes for Technical Leaders
              </h2>
              <div class="space-y-4">
                {outcomes.map((outcome, index) => (
                  <div class="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 text-success mt-0.5 flex-shrink-0" />
                    <span class="text-muted-foreground">{outcome}</span>
                  </div>
                ))}
              </div>
              <div class="mt-8">
                  <a href="/demo">
                    <Button variant="default" >
                    See Technical Case Studies 
                    <ArrowRight className="ml-2 h-4 w-4" />
                   </Button>
                  </a>               
              </div>
            </div>
            <div class="bg-background p-8 rounded-lg shadow-lg">
              <div class="text-center">
                <img
                  src="/assets/compliance-dashboard.png"
                  alt="Opsfolio compliance dashboard showing real-time compliance status, automated evidence collection, and control tracking for CTOs & tech leaders"
                  class="w-full h-64 object-cover rounded-lg mb-6 shadow-lg"
                />
                <p class="text-sm text-muted-foreground">
                  Real-time compliance dashboard with developer-friendly integrations
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Testimonial Section */}
      <div class="py-24 sm:py-32">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <Card className="max-w-4xl mx-auto text-center">
            <CardContent className="p-12">
              <blockquote class="text-xl text-muted-foreground italic mb-8">
                "{testimonial.quote}"
              </blockquote>
              <div>
                <div class="font-semibold text-foreground text-lg">{testimonial.author}</div>
                <div class="text-muted-foreground">{testimonial.role}, {testimonial.company}</div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* CTA Section */}
      <div class="bg-muted">
        <div class="px-6 py-24 sm:px-6 sm:py-32 lg:px-8">
          <div class="mx-auto max-w-2xl text-center">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Ready to Accelerate Enterprise Sales?
            </h2>
            <p class="mx-auto mt-6 max-w-xl text-lg leading-8 text-muted-foreground">
              Get a custom SOC2 implementation plan designed for your tech stack and timeline.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
              <a href="/contact">
                <Button
                size="lg"
                variant="default"
              >Schedule CTO Consultation</Button>
              </a>
             
                <a href="/demo">
                  <Button
                size="lg"
                variant="outline"
              >See Technical Demo</Button>
                </a>              
            </div>
            <div class="mt-8 text-sm text-muted-foreground/80">
              Custom roadmap delivered in 24 hours • No vendor lock-in • Developer-first approach
            </div>
          </div>
        </div>
      </div>
    </main>
</Layout>
