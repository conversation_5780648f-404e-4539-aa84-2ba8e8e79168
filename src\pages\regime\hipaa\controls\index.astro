---
import Layout from "../../../../layouts/Layout.astro";
import { Badge } from "@/components/ui/badge";
import ControlsExplorer from "@/components/ControlsExplorer";
import { Clock } from "lucide-react";
import { getHipaaControlList } from "../../../../services/auditServices";
import type {
  ControlsRegimeType,
  TransformedArrayType,
  ControlRegimEnvironment,
  ControlsType,
} from "../../../../services/auditTypes";
import HippaControls from "@/components/HippaControls"; // static data
import * as HipaaHeroContent from "@/content/control-explorer/hipaa.md";
import fs from "node:fs";
import path from "node:path";

const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1",
    text: "Regime",
    "aria-disabled": true,
  },
  {
    index: "2",
    text: "HIPAA",
    "aria-disabled": true,
  },
];

//Hippa cntrl dynamic
const auditTypeID = 9;
const regimeId = 4;
let tenantId = "239518031485599747";
tenantId = tenantId ? tenantId : "";
let controlAuditTableData: { title: string; dataVariable: string }[] = [];

// const auditControls = await getControlListByRegimeIdAndAuditTypeId(regimeId, auditTypeID , tenantId,);
let auditControls: ControlsType[] | { [x: string]: string }[] = [];
try {
  // auditControls = await getControlListByRegimeIdAndAuditTypeId(
  //   regimeId,
  //   auditTypeID,
  //   tenantId,
  // );
  auditControls = await getHipaaControlList(tenantId);
} catch (err) {
  console.error("Failed to load controls:", err);
  auditControls = []; // fallback to empty array
}

let auditControl = auditControls.map((item) => ({ ...item }));

const transformedArray: TransformedArrayType[] = [];
let currentCommonCriteria: string = "";
auditControl.forEach((control) => {
  const typedControl = control as ControlsRegimeType;
  if (typedControl["common_criteria"] !== currentCommonCriteria) {
    currentCommonCriteria = typedControl["common_criteria"];
    transformedArray.push({
      common_criteria: currentCommonCriteria,
      controls: [typedControl],
    });
  } else {
    transformedArray[transformedArray.length - 1].controls.push(typedControl);
  }
});
const ControlData: ControlRegimEnvironment[] = [];
for (const item of transformedArray) {
  const title = item["common_criteria"];
  const controlsForTitle = await Promise.all(
    item.controls.map(async (control) => ({
      controlCode: control["control_code"],
      controlQuestion: control["question"],
      controlIdentifier: control["control_identifier"],
      defaultSlug: control["slug"] ? control["slug"] : "",
    })),
  );

  ControlData.push({
    title: title,
    controls: controlsForTitle,
  });
}
const hasRealData =
  Array.isArray(ControlData) &&
  ControlData.some(
    (section) => Array.isArray(section.controls) && section.controls.length > 0,
  );


//console.log("auditControls", auditControls,hasRealData);

// Choose a file path for logs
const logFile = path.resolve("logs.txt");

// A function to write logs
function writeLog(message: string) {
  const timestamp = new Date().toISOString();
  
  fs.appendFileSync(logFile, `[${timestamp}] ${message}\n`);
}

// Example: log whenever this page is requested
writeLog("Page loaded.....");
if (!hasRealData) {
  writeLog("Page not loaded.....");
}
---

<Layout
  title="HIPAA Compliance Made Simple - Healthcare Data Protection"
  description="Achieve HIPAA compliance in 90 days with expert guidance, automated controls, and continuous monitoring. Protect patient data with comprehensive healthcare compliance solutions."
  keywords="HIPAA compliance, healthcare data protection, PHI security, healthcare compliance, HIPAA training, healthcare cybersecurity"
  url="/regime/hipaa/"
  breadcrumbLinks={breadcrumbLinks}
>
  {/* Hero Section */}
  <section
    class="relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5 pt-16 pb-20"
  >
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
      <div class="mx-auto max-w-4xl text-center">
        <Badge variant="outline" className="mb-4">
          {HipaaHeroContent.frontmatter.badge}
        </Badge>
        <h1
          class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl"
        >
          {HipaaHeroContent.frontmatter.title}
        </h1>
        <p
          class="mt-6 text-lg leading-8 text-muted-foreground max-w-3xl mx-auto"
        >
          {HipaaHeroContent.frontmatter.description}
        </p>
      </div>
    </div>
  </section>
  {
    hasRealData ? (
      <ControlsExplorer
        controlData={ControlData}
        controlPageUrl=""
        sessionId={Number(regimeId)}
        auditTypeId={Number(auditTypeID)}
        controlType="hipaa"
        client:only="react"
      />
    ) : ( 
      <HippaControls client:only="react" />
     )
  }
    

  {/* Last Updated */}
  <section class="py-8 border-t bg-muted/30">
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
      <div class="mx-auto max-w-4xl text-center">
        <div
          class="flex items-center justify-center gap-2 text-sm text-muted-foreground"
        >
          <Clock className="h-4 w-4" />
          <span>Last Updated: September 3, 2025</span>
        </div>
      </div>
    </div>
  </section>
</Layout>
