{"resourceType": "Questionnaire", "meta": {"profile": ["http://hl7.org/fhir/4.0/StructureDefinition/Questionnaire"]}, "title": "CMMC Level 2 - Configuration Management (CM)", "status": "draft", "item": [{"item": [{"item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "CM.L2-3.4.1_A_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "CM.L2-3.4.1_A_Q", "text": "Has a baseline configuration been formally established and documented for organizational systems?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "CM.L2-3.4.1_A_GRP", "prefix": "(a)", "text": "Baseline Configuration Establishment"}, {"type": "group", "prefix": "(b)", "linkId": "CM.L2-3.4.1_B_GRP", "text": "Baseline Configuration Content", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "CM.L2-3.4.1_B_NOTES", "text": "Notes / Evidence", "required": true, "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "check-box", "display": "Check-box"}]}}], "linkId": "CM.L2-3.4.1_B_Q", "text": "Which of the following are included in the baseline configurations?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Hardware"}}, {"valueCoding": {"display": "Software (including versions)"}}, {"valueCoding": {"display": "Firmware"}}, {"valueCoding": {"display": "System documentation"}}]}]}, {"type": "group", "prefix": "(c)", "linkId": "CM.L2-3.4.1_C_GRP", "text": "Baseline Configuration Maintenance", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "CM.L2-3.4.1_C_NOTES", "text": "Notes / Evidence", "repeats": false}, {"linkId": "CM.L2-3.4.1_C_Q_helpText", "type": "display", "text": "after major change", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "CM.L2-3.4.1_C_Q", "text": "Is the baseline configuration reviewed and updated throughout the system lifecycle?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}, {"type": "group", "prefix": "(d)", "linkId": "CM.L2-3.4.1_D_GRP", "text": "System Inventory Establishment", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "CM.L2-3.4.1_D_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "CM.L2-3.4.1_D_Q", "text": "Has a formal inventory of organizational systems been established?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}, {"type": "group", "prefix": "(e)", "linkId": "CM.L2-3.4.1_E_GRP", "text": "System Inventory Content", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "CM.L2-3.4.1_E_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "check-box", "display": "Check-box"}]}}], "linkId": "CM.L2-3.4.1_E_Q", "text": "Which of the following are included in your system inventory?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Hardware assets"}}, {"valueCoding": {"display": "Software assets and licenses"}}, {"valueCoding": {"display": "Firmware versions"}}, {"valueCoding": {"display": "Relevant documentation"}}]}]}, {"type": "group", "prefix": "(f)", "linkId": "CM.L2-3.4.1_F_GRP", "text": "System Inventory Maintenance", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "CM.L2-3.4.1_F_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "CM.L2-3.4.1_F_Q", "text": "Is the system inventory reviewed and updated throughout the asset lifecycle?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "CM.L2-3.4.1_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "CM.L2-3.4.1_help", "type": "display", "text": "Establish and maintain baseline configurations and inventories of organizational systems (including hardware, software, firmware, and documentation) throughout the respective system development life cycles.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}], "type": "group", "linkId": "CM.L2-3.4.1", "text": "CM.L2-3.4.1 – System Baselining"}, {"type": "group", "linkId": "CM.L2-3.4.2", "text": "CM.L2-3.4.2 – Security Configuration Enforcement", "item": [{"item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "CM.L2-3.4.2_A_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "CM.L2-3.4.2_A_Q", "text": "Are security configuration settings for IT products established and documented as part of the baseline configuration?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "CM.L2-3.4.2_A_GRP", "prefix": "(a)", "text": "Establishment of Security Settings"}, {"type": "group", "prefix": "(b)", "linkId": "CM.L2-3.4.2_B_GRP", "text": "Enforcement of Security Settings", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "CM.L2-3.4.2_B_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "check-box", "display": "Check-box"}]}}], "linkId": "CM.L2-3.4.2_B_Q", "text": "How are these security configuration settings enforced across organizational systems?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Group Policy Objects (GPO)"}}, {"valueCoding": {"display": "Mobile Device Management (MDM) policies"}}, {"valueCoding": {"display": "Automated configuration management tools or scripts"}}, {"valueCoding": {"display": "Periodic compliance scans and manual remediation"}}]}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "CM.L2-3.4.2_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "CM.L2-3.4.2_help", "type": "display", "text": "Establish and enforce security configuration settings for information technology products employed in organizational systems.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}]}, {"type": "group", "linkId": "CM.L2-3.4.3", "text": "CM.L2-3.4.3 – System Change Management", "item": [{"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button"}]}}], "linkId": "CM.L2-3.4.3_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}], "item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "CM.L2-3.4.3_A_NOTES", "text": "Notes / Evidence", "repeats": false}, {"linkId": "CM.L2-3.4.3_A_Q_helpText", "type": "display", "text": "ticketing system, change log", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "CM.L2-3.4.3_A_Q", "text": "Does your organization use a system to track changes to systems?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "CM.L2-3.4.3_A_GRP", "prefix": "(a)", "text": "Tracking Changes"}, {"type": "group", "prefix": "(b)", "linkId": "CM.L2-3.4.3_B_GRP", "text": "Reviewing Changes", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "CM.L2-3.4.3_B_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "CM.L2-3.4.3_B_Q", "text": "Are all system changes formally reviewed?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}, {"type": "group", "prefix": "(c)", "linkId": "CM.L2-3.4.3_C_GRP", "text": "Approving/Disapproving Changes", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "CM.L2-3.4.3_C_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "CM.L2-3.4.3_C_Q", "text": "Is there a formal process for approving or disapproving changes, such as a Change Control Board (CCB)?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}, {"type": "group", "prefix": "(d)", "linkId": "CM.L2-3.4.3_D_GRP", "text": "Logging Changes", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "CM.L2-3.4.3_D_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "CM.L2-3.4.3_D_Q", "text": "Are the results of all implemented system changes documented or logged?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}]}, {"linkId": "CM.L2-3.4.3_help", "type": "display", "text": "Track, review, approve or disapprove, and log changes to organizational systems.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}]}, {"type": "group", "linkId": "CM.L2-3.4.4", "text": "CM.L2-3.4.4 – Security Impact Analysis", "item": [{"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button"}]}}], "linkId": "CM.L2-3.4.4_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}], "item": [{"type": "group", "prefix": "(a)", "linkId": "CM.L2-3.4.4_A_GRP", "text": "Security Impact Analysis", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "CM.L2-3.4.4_A_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "CM.L2-3.4.4_A_Q", "text": "Is a security impact analysis performed as part of the change review process before changes are implemented?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}]}, {"linkId": "CM.L2-3.4.4_help", "type": "display", "text": "Analyze the security impact of changes prior to implementation.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}]}, {"item": [{"item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "CM.L2-3.4.5_ABCD_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "CM.L2-3.4.5_ABCD_Q", "text": "Are physical access restrictions for implementing changes defined, documented, approved, and enforced?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "CM.L2-3.4.5_ABCD_GRP", "prefix": "(a)", "text": "Physical Access Restrictions for Changes"}, {"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "CM.L2-3.4.5_EFGH_NOTES", "text": "Notes / Evidence", "required": true, "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "CM.L2-3.4.5_EFGH_Q", "text": "Are logical access restrictions for implementing changes defined, documented, approved, and enforced?", "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "CM.L2-3.4.5_EFGH_GRP", "prefix": "(b)", "text": "Logical Access Restrictions for Changes"}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "CM.L2-3.4.5_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "CM.L2-3.4.5_help", "type": "display", "text": "Define, document, approve, and enforce physical and logical access restrictions associated with changes to organizational systems.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}], "type": "group", "linkId": "CM.L2-3.4.5", "text": "CM.L2-3.4.5 – Access Restrictions for Change"}, {"type": "group", "linkId": "CM.L2-3.4.6", "text": "CM.L2-3.4.6 – Least Functionality", "item": [{"item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "CM.L2-3.4.6_A_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "CM.L2-3.4.6_A_Q", "text": "Does your organization formally define and document the essential capabilities required for systems to perform their intended function?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "CM.L2-3.4.6_A_GRP", "prefix": "(a)", "text": "Definition of Essential Capabilities"}, {"type": "group", "prefix": "(b)", "linkId": "CM.L2-3.4.6_B_GRP", "text": "Configuration for Least Functionality", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "CM.L2-3.4.6_B_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "CM.L2-3.4.6_B_Q", "text": "Are systems configured to provide only these essential capabilities, with all other functions disabled?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "CM.L2-3.4.6_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "CM.L2-3.4.6_help", "type": "display", "text": "Employ the principle of least functionality by configuring organizational systems to provide only essential capabilities.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}]}, {"item": [{"item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "CM.L2-3.4.7_ABC_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "CM.L2-3.4.7_ABC_Q", "prefix": "(I)", "text": "Does the organization maintain a formal, documented list that defines all software programs considered \"essential\" for operations?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "176897199339", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "158228059219", "prefix": "(II)", "text": "Does organizational policy state that any program not explicitly on the \"essential\" list is, by default, defined as \"nonessential\" and prohibited?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "989854436692", "text": "Notes / Evidence", "required": true, "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "check-box", "display": "Check-box"}]}}], "linkId": "185449565824", "prefix": "(III)", "text": "Which technical control is implemented to restrict or prevent the execution of nonessential programs as defined?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Application allow-listing (e.g., AppLocker, Windows Defender Application Control)"}}, {"valueCoding": {"display": "Network port filtering"}}, {"valueCoding": {"display": "Data Loss Prevention (DLP)"}}, {"valueCoding": {"display": "User awareness training"}}]}], "type": "group", "linkId": "CM.L2-3.4.7_ABC_GRP", "prefix": "(a)", "text": "Programs"}, {"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "CM.L2-3.4.7_DEF_NOTES", "text": "Notes / Evidence", "repeats": false}, {"linkId": "CM.L2-3.4.7_DEF_Q_helpText", "type": "display", "text": "USB access, use of command promp", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "CM.L2-3.4.7_DEF_Q", "prefix": "(I)", "text": "Has the organization defined and documented the system functions that are considered \"essential\" for different user roles?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "759176338437", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "492772941432", "prefix": "(II)", "text": "Is the organizational policy for system functions based on a \"default-deny\" posture, where any function not explicitly defined as essential is considered nonessential?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "360030844580", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "622255824427", "prefix": "(III)", "text": "Are nonessential system functions (like removable media access or script execution) technically restricted or disabled using controls such as Group Policy Objects (GPOs) or MDM profiles?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "CM.L2-3.4.7_DEF_GRP", "prefix": "(b)", "text": "Functions"}, {"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "CM.L2-3.4.7_GHI_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "CM.L2-3.4.7_GHI_Q", "prefix": "(I)", "text": "Does a documented firewall policy or configuration baseline exist that defines all network ports considered \"essential\" for required applications and services?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "801033130760", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "check-box", "display": "Check-box"}]}}], "linkId": "880804823357", "prefix": "(II)", "text": "How does the organization technically enforce the restriction of nonessential network ports on its servers and workstations?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": " By primarily using host-based firewalls (like Windows Defender Firewall) with a \"default-deny\" rule."}}, {"valueCoding": {"display": "By relying solely on the perimeter firewall to block traffic from the internet."}}, {"valueCoding": {"display": "By running antivirus scans to detect unauthorized ports."}}, {"valueCoding": {"display": "By monitoring network bandwidth usage."}}]}, {"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "341832418376", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "663503760434", "prefix": "(III)", "text": "Copy of Are nonessential network ports restricted, disabled, or prevented from use?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "CM.L2-3.4.7_GHI_GRP", "prefix": "(c)", "text": "Ports"}, {"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "CM.L2-3.4.7_JKL_NOTES", "text": "Notes / Evidence", "repeats": false}, {"linkId": "CM.L2-3.4.7_JKL_Q_helpText", "type": "display", "text": "TLS 1.2+, SSHv2", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "CM.L2-3.4.7_JKL_Q", "prefix": "(I)", "text": "Does the organization maintain a documented list of \"essential\" network protocols authorized for use?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "788478736031", "text": "Notes / Evidence", "repeats": false}, {"linkId": "431096477705_helpText", "type": "display", "text": "Telnet, unencrypted FTP, SMBv1", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "431096477705", "prefix": "(II)", "text": "Does organizational policy explicitly define known insecure protocols  as \"nonessential\" and prohibit their use?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "992366305926", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "183168896148", "prefix": "(III)", "text": "Are technical controls implemented to actively restrict, disable, or prevent the use of these nonessential and insecure protocols on systems and the network?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "CM.L2-3.4.7_JKL_GRP", "prefix": "(d)", "text": "Protocols"}, {"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "CM.L2-3.4.7_MNO_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "CM.L2-3.4.7_MNO_Q", "prefix": "(I)", "text": "Are \"essential\" operating system services (those required for the OS to function) defined and documented, typically by adopting a secure configuration baseline (like CIS or DISA STIGs)?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "476924372920", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "826154687762", "prefix": "(II)", "text": "Does the system hardening policy require that any operating system service not on the \"essential\" baseline list is defined as \"nonessential\" and must be set to \"Disabled\"?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "584609458979", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "check-box", "display": "Check-box"}]}}], "linkId": "692181769695", "prefix": "(III)", "text": "Which method provides the strongest evidence that nonessential services are restricted as defined?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "A configuration management script (e.g., PowerShell, GPO, Ansible) that enforces the \"Disabled\" state for all unauthorized services."}}, {"valueCoding": {"display": "A system administrator's verbal confirmation that they periodically check the services."}}, {"valueCoding": {"display": "An inventory asset list showing the operating system version of all servers."}}, {"valueCoding": {"display": "A policy document that only lists services that should be disabled, without proof of enforcement."}}]}], "type": "group", "linkId": "CM.L2-3.4.7_MNO_GRP", "prefix": "(e)", "text": "Services"}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "CM.L2-3.4.7_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "CM.L2-3.4.7_help", "type": "display", "text": "Restrict, disable, or prevent the use of nonessential programs, functions, ports, protocols, and services.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}], "type": "group", "linkId": "CM.L2-3.4.7", "text": "CM.L2-3.4.7 – Nonessential Functionality"}, {"type": "group", "linkId": "CM.L2-3.4.8", "text": "CM.L2-3.4.8 – Application Execution Policy", "item": [{"item": [{"type": "group", "prefix": "(a)", "linkId": "CM.L2-3.4.8_A_GRP", "text": "Application Control Policy", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "CM.L2-3.4.8_A_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "CM.L2-3.4.8_A_Q", "text": "What type of policy is implemented to control software execution?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Deny-all, permit-by-exception (Whitelisting)"}}, {"valueCoding": {"display": "Deny-by-exception (Blacklisting)"}}, {"valueCoding": {"display": "No specific policy"}}]}]}, {"type": "group", "prefix": "(b)", "linkId": "CM.L2-3.4.8_B_GRP", "text": "Application List Maintenance", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "CM.L2-3.4.8_B_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "CM.L2-3.4.8_B_Q", "text": "Is a formal list of allowed (for whitelisting) or disallowed (for blacklisting) software maintained?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}, {"type": "group", "prefix": "(c)", "linkId": "CM.L2-3.4.8_C_GRP", "text": "Implementation of Policy", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "CM.L2-3.4.8_C_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "CM.L2-3.4.8_C_Q", "text": "Is the specified application control policy technically implemented and enforced on organizational systems?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "CM.L2-3.4.8_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "CM.L2-3.4.8_help", "type": "display", "text": "Apply deny-by-exception (blacklisting) policy to prevent the use of unauthorized software or deny-all, permit-by-exception (whitelisting) policy to allow the execution of authorized software.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}]}, {"type": "group", "linkId": "CM.L2-3.4.9", "text": "CM.L2-3.4.9 – User-Installed Software", "item": [{"item": [{"type": "group", "prefix": "(a)", "linkId": "CM.L2-3.4.9_A_GRP", "text": "Policy on User-Installed Software", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "CM.L2-3.4.9_A_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "CM.L2-3.4.9_A_Q", "text": "Does a policy exist for controlling the installation of software by users?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}, {"type": "group", "prefix": "(b)", "linkId": "CM.L2-3.4.9_B_GRP", "text": "Control of Installation", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "CM.L2-3.4.9_B_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "CM.L2-3.4.9_B_Q", "text": "Is the installation of software by users technically controlled in accordance with the policy?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes (e.g., users lack administrative privileges)"}}, {"valueCoding": {"display": "Yes (e.g., via application whitelisting)"}}, {"valueCoding": {"display": "No"}}]}]}, {"type": "group", "prefix": "(c)", "linkId": "CM.L2-3.4.9_C_GRP", "text": "Monitoring of Installation", "item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "CM.L2-3.4.9_C_NOTES", "text": "Notes / Evidence ", "repeats": false, "item": [{"linkId": "CM.L2-3.4.9_C_NOTES_helpText", "type": "display", "text": "software inventory tool scans, SIEM alerts", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "CM.L2-3.4.9_C_Q", "text": "Are user software installations monitored to detect unauthorized software?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "CM.L2-3.4.9_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "CM.L2-3.4.9_help", "type": "display", "text": "Control and monitor user-installed software.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}]}]}