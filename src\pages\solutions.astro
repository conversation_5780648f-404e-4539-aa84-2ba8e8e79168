---

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Users, Bot, Database, ArrowRight, Shield, CheckCircle } from "lucide-react";
import Layout from "../layouts/Layout.astro";

---
<Layout>
      <main>
        {/* Hero Section */}
        <div class="bg-gradient-to-r from-primary to-primary-glow">
          <div class="px-6 py-24 sm:px-6 sm:py-32 lg:px-8">
            <div class="mx-auto max-w-4xl text-center">
              <h1 class="text-4xl font-bold tracking-tight text-primary-foreground sm:text-6xl">
                Complete Compliance Solutions
              </h1>
              <p class="mx-auto mt-6 max-w-2xl text-lg leading-8 text-primary-foreground/90">
                From expert guidance to AI automation, we provide everything you need 
                for successful compliance outcomes.
              </p>
            </div>
          </div>
        </div>

        {/* Solutions Grid */}
        <div class="py-24 sm:py-32">
          <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="grid grid-cols-1 gap-8 lg:grid-cols-3">
              
              {/* Expert-Guided Compliance */}
              <a href="/contact" class="h-full">
                <Card className="border-2 hover:border-primary/20 transition-all duration-300 h-full flex flex-col">
                  <CardHeader>
                    <div class="flex items-center justify-center w-12 h-12 bg-primary/10 rounded-lg mb-4">
                      <Users className="h-6 w-6 text-primary" />
                    </div>
                    <CardTitle>Expert-Guided Compliance</CardTitle>
                  </CardHeader>
                  <CardContent className="flex flex-col flex-grow">
                    <p class="text-muted-foreground mb-6">
                      Work directly with experienced compliance engineers who understand your industry and business needs.
                    </p>
                    <ul class="space-y-3 mb-6">
                      <li class="flex items-center">
                        <CheckCircle className="h-4 w-4 text-primary mr-2" />
                        <span class="text-sm">Fractional CCO services</span>
                      </li>
                      <li class="flex items-center">
                        <CheckCircle className="h-4 w-4 text-primary mr-2" />
                        <span class="text-sm">1-on-1 compliance coaching</span>
                      </li>
                      <li class="flex items-center">
                        <CheckCircle className="h-4 w-4 text-primary mr-2" />
                        <span class="text-sm">Expert policy authoring</span>
                      </li>
                      <li class="flex items-center">
                        <CheckCircle className="h-4 w-4 text-primary mr-2" />
                        <span class="text-sm">Audit preparation & support</span>
                      </li>
                    </ul>
                    <div class="mt-auto">
                      <Button className="w-full">
                        Get Expert Help <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </a>

              {/* AI-Driven Tools */}
              <a href="/demo" class="h-full">
                <Card className="border-2 hover:border-primary/20 transition-all duration-300 h-full flex flex-col">
                  <CardHeader>
                    <div class="flex items-center justify-center w-12 h-12 bg-primary/10 rounded-lg mb-4">
                      <Bot className="h-6 w-6 text-primary" />
                    </div>
                    <CardTitle>AI-Driven Policy & Evidence Tools</CardTitle>
                  </CardHeader>
                  <CardContent className="flex flex-col flex-grow">
                    <p class="text-muted-foreground mb-6">
                      Leverage intelligent automation to streamline your compliance workflows and maintain audit readiness.
                    </p>
                    <ul class="space-y-3 mb-6">
                      <li class="flex items-center">
                        <CheckCircle className="h-4 w-4 text-primary mr-2" />
                        <span class="text-sm">Automated evidence collection</span>
                      </li>
                      <li class="flex items-center">
                        <CheckCircle className="h-4 w-4 text-primary mr-2" />
                        <span class="text-sm">AI-powered policy generation</span>
                      </li>
                      <li class="flex items-center">
                        <CheckCircle className="h-4 w-4 text-primary mr-2" />
                        <span class="text-sm">Continuous gap analysis</span>
                      </li>
                      <li class="flex items-center">
                        <CheckCircle className="h-4 w-4 text-primary mr-2" />
                        <span class="text-sm">Real-time compliance monitoring</span>
                      </li>
                    </ul>
                    <div class="mt-auto">
                      <Button className="w-full">
                        See AI in Action <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </a>

              {/* System of Record */}
              <a href="/get-started" class="h-full">
                <Card className="border-2 hover:border-primary/20 transition-all duration-300 h-full flex flex-col">
                  <CardHeader>
                    <div class="flex items-center justify-center w-12 h-12 bg-primary/10 rounded-lg mb-4">
                      <Database className="h-6 w-6 text-primary" />
                    </div>
                    <CardTitle>One System of Record</CardTitle>
                  </CardHeader>
                  <CardContent className="flex flex-col flex-grow">
                    <p class="text-muted-foreground mb-6">
                      Centralize all your compliance data, policies, and evidence in one secure, auditor-friendly platform.
                    </p>
                    <ul class="space-y-3 mb-6">
                      <li class="flex items-center">
                        <CheckCircle className="h-4 w-4 text-primary mr-2" />
                        <span class="text-sm">Unified compliance dashboard</span>
                      </li>
                      <li class="flex items-center">
                        <CheckCircle className="h-4 w-4 text-primary mr-2" />
                        <span class="text-sm">Centralized policy management</span>
                      </li>
                      <li class="flex items-center">
                        <CheckCircle className="h-4 w-4 text-primary mr-2" />
                        <span class="text-sm">Complete audit trail</span>
                      </li>
                      <li class="flex items-center">
                        <CheckCircle className="h-4 w-4 text-primary mr-2" />
                        <span class="text-sm">Audit readiness scoring</span>
                      </li>
                    </ul>
                    <div class="mt-auto">
                      <Button className="w-full">
                        Start Your Trial <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </a>
              
            </div>
          </div>
        </div>


        {/* CTA Section */}
        <div class="bg-muted">
          <div class="px-6 py-24 sm:px-6 sm:py-32 lg:px-8">
            <div class="mx-auto max-w-2xl text-center">
              <Shield className="mx-auto h-16 w-16 text-primary mb-6" />
              <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                Ready to Transform Your Compliance?
              </h2>
              <p class="mx-auto mt-6 max-w-xl text-lg leading-8 text-muted-foreground">
                Join 500+ companies who trust Opsfolio to deliver their compliance outcomes.
              </p>
              <div class="mt-10 flex items-center justify-center gap-x-6">
                 <a href="/contact">
                    <Button  className="w-full">Talk to an Expert<ArrowRight className="ml-2 h-4 w-4" /></Button>
                  </a>
                   <a href="/get-started">
                    <Button  className="w-full" variant="outline">Start Free Trial<ArrowRight className="ml-2 h-4 w-4" /></Button>
                  </a>
              </div>
            </div>
          </div>
        </div>
      </main>
      
</Layout>