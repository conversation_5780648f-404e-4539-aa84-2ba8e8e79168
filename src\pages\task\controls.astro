---
import Layout from "../../layouts/Layout.astro";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Target, BarChart3, Alert<PERSON>riangle, Clock, CheckCircle, ArrowRight } from "lucide-react";


const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Task',"aria-disabled": true ,
  },
  {
    index: "2", text: "Track Controls","aria-disabled": true 
  },
];
---
<Layout title="Opsfolio - Compliance as a Service | SOC2, HIPAA, ISO Certification"
  description="Get SOC2, HIPAA, ISO certified fast with expert-guided compliance. 98% audit pass rate, 60% faster than DIY. Your Compliance System of Record."
  keywords="Evidence, compliance, SOC2, HIPAA, ISO 27001, cybersecurity, audit, certification"
  url="/task/controls/"
  breadcrumbLinks={breadcrumbLinks}>
    {/* Hero Section */}
  
    <section class="relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5 pt-16 pb-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center">
            <Badge variant="outline" className="mb-4">Control Tracking & Management</Badge>
            <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
              Track Controls Across All Frameworks
            </h1>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Centralize control management across SOC2, HIPAA, ISO, and other frameworks. 
              Monitor effectiveness, track remediation, and maintain continuous compliance.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
                <a href="/get-started">
                  <Button size="lg">Start Control Tracking<ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </a>
            </div>
          </div>
        </div>
    </section>

    <section class="py-16 sm:py-20">
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <div class="mx-auto max-w-2xl lg:text-center">
        <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
            Unified Control Management
        </h2>
        </div>
        <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
        <dl class="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
            <div class="flex flex-col">
            <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                <Target className="h-5 w-5 flex-none text-primary" />
                Cross-Framework Mapping
            </dt>
            <dd class="mt-4 text-base leading-7 text-muted-foreground">
                Map controls across multiple frameworks to eliminate redundant work and ensure comprehensive coverage.
            </dd>
            </div>
            <div class="flex flex-col">
            <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                <BarChart3 className="h-5 w-5 flex-none text-primary" />
                Real-time Monitoring
            </dt>
            <dd class="mt-4 text-base leading-7 text-muted-foreground">
                Continuous monitoring of control effectiveness with automated testing and validation.
            </dd>
            </div>
            <div class="flex flex-col">
            <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-foreground">
                <AlertTriangle className="h-5 w-5 flex-none text-primary" />
                Gap Management
            </dt>
            <dd class="mt-4 text-base leading-7 text-muted-foreground">
                Identify and track remediation of control gaps with automated prioritization and workflows.
            </dd>
            </div>
        </dl>
        </div>
    </div>
    </section>
</Layout>