# Opsfolio Customer Journey (CJ)–Aware Sign-Up and Login Page

The goal of a **Customer Journey–Aware Sign-Up and Login Page** is to dynamically adapt the marketing and contextual content shown alongside the authentication form based on the user’s entry point, campaign, or intent. Instead of every visitor seeing the same static pitch, the left-hand “promo” panel is **personalized** to the journey they’re on.

For example:

* If the visitor comes from a LinkedIn ad offering a free **CMMC Self-Assessment**, the left panel reinforces that offer, explains the benefits, and makes it easy to start the assessment post-login.
* If the visitor is coming from an email inviting them to submit **SOC2 evidence**, the left panel promotes that workflow instead, with relevant trust signals and benefits.

**Why this matters:**

* Increases conversion rates by reducing cognitive friction. Users see continuity between the promise in the ad/email and the first page they land on.
* Minimizes drop-off between marketing and product onboarding.
* Provides measurable attribution and performance data by journey.
* Enables the marketing team to run controlled A/B or multivariate tests without engineering changing page structure.

## Overall Strategy

1. **Journey Identification:**

   * Journey key is derived from URL parameters, UTM tags, campaign IDs, referral domains, or short link routes (e.g., `/signup/cmmc`).
   * Default to a generic journey if no match is found.
2. **Content Mapping:**

   * Each journey key maps to a **content definition** stored in a structured content store (JSON, MDX, CMS).
   * Content includes: title, subhead, bullet points, optional image/video, founder note, trust badges.
3. **Dynamic Rendering:**

   * The sign-up/login shell remains constant (fields, form behavior).
   * The left “promo” panel is populated at runtime based on the journey key.
   * If the journey key is unknown, fall back to the default promo.
4. **Post-Login Routing:**

   * On successful authentication, the journey key determines the immediate landing page (e.g., `/suite/cmmc/self-assessment/start`).
5. **Analytics & Attribution:**

   * Log impressions, submissions, and completions tagged with the journey key for performance tracking.
   * Support A/B testing by splitting journeys into variants (e.g., `cmmc.v1`, `cmmc.v2`).
6. **Content Independence:**

   * Marketing can update promo content without code releases.
   * Engineering’s job is to maintain the rendering logic, journey resolution, and safe fallbacks.

## Example Content Summaries (there will be many more)

### CMMC Self-Assessment Journey

* **Title:** Get instant access to the CMMC Level 1 Self-Assessment
* **Subhead:** Join free to evaluate your readiness and generate an exportable POA\&M starter.
* **Bullets:**

  * Automated control walkthrough with plain-English guidance
  * Evidence checklist with storage suggestions
  * Readiness score and prioritized gaps
  * Exportable summary and remediation plan
  * Optional white-glove help via Opsfolio CaaS
* **Founder Note:** “We built Opsfolio to make compliance practical for small teams. Start free, upgrade if you need hands-on help.”
* **Trust Strip:** “SOC2 and CMMC expertise • Private by default • You control your data”

### B. SOC2 Evidence Intake Journey

* **Title:** Start Your SOC2 Evidence Intake Today
* **Subhead:** Gather, organize, and validate your SOC2 evidence in one secure place.
* **Bullets:**

  * Control-by-control evidence intake mapping
  * Secure file attachments from Drive, S3, Git, or local storage
  * Automated gap analysis for missing evidence
  * Real-time collaboration with team or auditor
  * Exportable auditor-ready packet
* **Founder Note:** “Opsfolio’s SOC2 workflow turns what used to be a 3-month scramble into a guided, trackable process your team can finish in weeks.”
* **Trust Strip:** “SOC2 experts • Secure by design • You own your data”

## CMMC Self‑Assessment Specification

![Generated example](cj-aware-login-spec1.png)

### Left panel (context/pitch)

* Headline: Get instant access to the CMMC Level 1 Self‑Assessment
* Subhead: Join as a free user to evaluate your readiness and generate an exportable POA\&M starter.
* Points:

  * Automated control walkthrough
    Guided questions mapped to the 15 L1 practices with plain‑English help.
  * Evidence checklist
    Per‑practice artifacts to collect and suggested locations to store them.
  * Readiness score and gaps
    Heatmap and prioritized fix list you can share with leadership.
  * Exportable outputs
    Download summary, controls report, and remediation plan.
  * Optional white‑glove help
    Book time with Opsfolio CaaS to close gaps.
* Founder note: “We built Opsfolio to make compliance practical for small teams. Start free, upgrade if you need hands‑on help.”
* Trust strip (small): “SOC2 and CMMC expertise • Private by default • You control your data”

### Right panel (account)

* Title: Create your account
* Fields: Full name, Work email, Company (optional), Password, Confirm password
* Checkbox: I agree to the Terms and Privacy Policy
* Button: Create account
* Below button: Already have an account? Sign in
* Fine print: By creating an account, you consent to receiving essential product emails about your self‑assessment. You can opt out of non‑essential updates anytime.

Dynamic left panel: how it swaps by journey
We’ll use a single LoginShell with a “promo” slot. The promo content is selected by a journey key determined from:

* URL param: ?journey=cmmc-self-assessment, ?journey=soc2-input, etc.
* Campaign/UTM: utm\_campaign or utm\_content to map to a journey
* Referrer rules: linkedin.com ⇒ “linkedin”, ads.google.com ⇒ “search-ads”
* Deep links: /login/cmmc or /signup/cmmc as short routes
* Fallback: “default” welcome to Opsfolio.com

If a user arrives from a cold email to start a SOC2 input form, the journey key resolves to soc2-input and the left panel pulls that copy instead.

Got it — here’s the **SOC2 Intake promo copy** so you can test two journeys end-to-end with the dynamic left panel logic we just outlined.

## SOC2 Customer Journey Example

### Left panel (context/pitch)

* **Headline:** Start Your SOC2 Evidence Intake Today
* **Subhead:** Gather, organize, and validate your SOC2 evidence in one secure place.
* **Points:**

  * Control-by-control evidence intake
    Map each SOC2 Trust Service Criterion to your existing systems and policies.
  * Secure file attachments
    Upload or link evidence from Google Drive, S3, Git, or your local machine.
  * Automated gap analysis
    Get instant feedback on missing evidence or unclear mappings.
  * Collaboration tools
    Invite your compliance team or external auditor to review progress in real time.
  * Exportable audit packet
    Generate an auditor-ready evidence package when you’re done.
* **Founder note:** “Opsfolio’s SOC2 workflow turns what used to be a 3-month scramble into a guided, trackable process your team can finish in weeks.”
* **Trust strip (small):** “SOC2 experts • Secure by design • You own your data”

## Routing after login

* On successful sign-up or login with `journeyKey = soc2-intake`, redirect to:
  `/suite/soc2/intake`
* Maintain the journeyKey in session so users who sign in later still land in the SOC2 evidence workflow without having to navigate manually.

## Implementation notes for devs

### Component model

* \<LoginShell promo={`<PromoPanel journeyKey={journeyKey} />`}>`<AuthForm mode="signup" /></LoginShell>`
* `<PromoPanel />` is dumb; it just renders content from a config map keyed by journeyKey.
* `<AuthForm />` supports both sign‑up and sign‑in modes and emits “onSuccess(user, journeyKey)”.

### Content config (editable without code deploy)

* Store promo copy in JSON or MDX so marketing can edit it (and localize later).
* Example config object:

```json
{
  "default": {
    "title": "Welcome to Opsfolio",
    "subhead": "Compliance made practical.",
    "bullets": ["Fast onboarding", "Evidence tracking", "Team workflows"],
    "cta": null
  },
  "cmmc-self-assessment": {
    "title": "Get instant access to the CMMC Level 1 Self‑Assessment",
    "subhead": "Guided questions, evidence checklist, and exportable plan.",
    "bullets": [
      "Automated control walkthrough",
      "Evidence checklist",
      "Readiness score and gaps",
      "Exportable outputs",
      "Optional white‑glove help"
    ],
    "badge": "Free to start"
  },
  "soc2-input": {
    "title": "Start your SOC2 Evidence Intake",
    "subhead": "Map systems, policies, and controls in under 30 minutes.",
    "bullets": [
      "Control-by-control intake",
      "Attach evidence from Drive, S3, or Git",
      "Gap analysis with next steps"
    ]
  }
}
```

### Journey resolution utility

* Priority: explicit route > query param > UTM mapping > referrer mapping > default.
* Example:

```ts
export function resolveJourneyKey(path: string, search: URLSearchParams, referrer?: string) {
  if (path.startsWith("/login/cmmc") || path.startsWith("/signup/cmmc")) return "cmmc-self-assessment";
  const qp = search.get("journey");
  if (qp) return qp;
  const utm = search.get("utm_campaign") || search.get("utm_content");
  if (utm?.includes("soc2")) return "soc2-input";
  if (referrer?.includes("linkedin.com")) return "linkedin-default";
  return "default";
}
```

### Routing after success

* On successful sign‑up/sign‑in, route users based on journeyKey:

  * cmmc-self-assessment → /suite/cmmc/self-assessment/start
  * soc2-input → /suite/soc2/intake
  * default → /suite/home
* Persist journeyKey in a short‑lived cookie so we can keep context through OAuth redirects.

### Content management and A/B testing

* Allow variant keys: cmmc-self-assessment.v1, .v2 for copy tests.
* Pick variant via feature flags or experiments service; record exposure in analytics.

### Analytics and attribution

* Log an analytics event at render: login\_promo\_impression {journeyKey, variant, utm\_source, utm\_campaign}
* On submit and on success, log: login\_submit and login\_success with same properties.
* Attribute downstream actions (finished assessment, export) back to the journeyKey.

### Security and privacy

* Don’t prefill email from query params unless the link was signed.
* Rate‑limit sign‑ups; require email verification before showing sensitive org data.
* If user is invited to an org, invitation context overrides journey routing.

### Accessibility and i18n

* All promo bullets are list elements with aria‑labels.
* Keep copy in external resource files; support future locales by adding language folders.

### Empty and error states

* If journeyKey not found in config, render default promo.
* If config fails to load, show a minimal safe fallback.

### QA checklist for this page

* Each journey route renders correct left panel copy.
* URL/UTM/referrer precedence works as expected.
* After sign‑up, user lands on the correct assessment start screen.
* A/B variants are served and tracked.
* Responsive behavior: 2‑column on desktop, stacked on mobile.
* Accessibility: tab order, labels, contrasts, screen reader checks.
