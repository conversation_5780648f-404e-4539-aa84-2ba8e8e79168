import type { APIRoute } from 'astro';
import fs from 'fs/promises';
import path from 'path';
import process from 'node:process';

export const POST: APIRoute = async ({ request }) => {
    try {
        const body = await request.json();
        const { tenantID, userID, sessionName } = body;
        if (!tenantID || !userID || !sessionName) {
            return new Response(JSON.stringify({ error: 'Missing parameters' }), { status: 400 });
        }

        // sanitize session name
        const safeName = sessionName.replace(/[\\/:*?"<>|]/g, '').trim();
        if (!safeName) {
            return new Response(JSON.stringify({ error: 'Invalid session name' }), { status: 400 });
        }

        // base data directory for sessions (adjust as needed)

        const targetDir = path.join('./src/data/lforms', safeName ? `${tenantID}` : '', `${userID}`, safeName);

        // NOTE: prefer to check existence first to return 409 when trying to create existing
        try {
            const stat = await fs.stat(targetDir);
            if (stat.isDirectory()) {
                // If directory exists, say conflict
                return new Response(JSON.stringify({ error: 'Session already exists' }), { status: 409 });
            }
        } catch (_err) {
            // stat failed -> directory doesn't exist; mkdir will create it
            await fs.mkdir(targetDir, { recursive: true });
            return new Response(JSON.stringify({ message: 'Session created' }), { status: 200 });
        }

        // Fallback: should never reach here, but return a server error just in case
        return new Response(JSON.stringify({ error: 'Unexpected error' }), { status: 500 });

    } catch (err) {
        console.error(err);
        return new Response(JSON.stringify({ error: 'Server error' }), { status: 500 });
    }
};

