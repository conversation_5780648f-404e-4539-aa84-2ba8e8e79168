{"name": "opsfolio-astro", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro", "astro:check": "astro check", "prepare-qualityfolio-db": "deno run -A https://raw.githubusercontent.com/surveilr/www.surveilr.com/refs/heads/main/lib/service/qualityfolio/qualityfolio-surveilr-prepare-service.ts", "lint": "eslint \"src/**/*.{js,ts,tsx,jsx,astro,md,mdx}\" --quiet --fix", "prepare": "husky install", "lint-staged": "eslint \"src/**/*.{js,ts,tsx,jsx,astro,md,mdx}\" --quiet --fix && prettier \"src/**/*.{js,ts,astro,md,mdx,tsx,jsx}\" --write", "r4-q-autogen": "deno run -A support/r4-q-autogen/r4-q-autogen.ts"}, "dependencies": {"@astrojs/node": "^9.3.3", "@astrojs/react": "^4.3.0", "@astrojs/sitemap": "^3.4.2", "@astrojs/tailwind": "^6.0.2", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@tanstack/react-query": "^5.56.2", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "astro": "^5.12.4", "astro-breadcrumbs": "^3.3.1", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "dotenv": "^17.2.2", "embla-carousel-react": "^8.3.0", "gray-matter": "^4.0.3", "handlebars": "^4.7.8", "input-otp": "^1.2.4", "js-cookie": "^3.0.5", "lucide-react": "^0.462.0", "markdown-it": "^14.1.0", "marked": "^16.1.1", "next-themes": "^0.3.0", "puppeteer": "^24.16.1", "react": "^19.1.0", "react-day-picker": "^8.10.1", "react-dom": "^19.1.0", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "sonner": "^1.5.0", "sqlite3": "^5.1.6", "tailwind-merge": "^2.5.2", "tailwind-variants": "^2.0.1", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.3", "zod": "^3.23.8"}, "devDependencies": {"@astrojs/check": "^0.9.4", "@commitlint/cli": "^17.7.1", "@commitlint/config-conventional": "^17.7.0", "@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@types/js-cookie": "^3.0.6", "@types/node": "^22.5.5", "@types/prettier": "^3.0.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.21", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "husky": "^9.1.7", "lint-staged": "^16.1.5", "lovable-tagger": "^1.1.7", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-astro": "^0.14.1", "tailwindcss": "3.4.1", "typescript": "^5.8.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}, "lint-staged": {"src/**/*.{js,ts,tsx,jsx,astro,md,mdx}": ["eslint --fix", "prettier --write"], "src/**/*.{js,ts,astro,md,mdx,tsx,jsx}": ["prettier --write"]}}