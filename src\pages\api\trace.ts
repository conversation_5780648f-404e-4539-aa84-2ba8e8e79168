import type { APIRoute } from "astro";
import axios from "axios";
 
const OPEN_OBSERVE_URL = import.meta.env.PUBLIC_OPENOBSERVE_URL as string;
const OPEN_OBSERVE_TOKEN = import.meta.env.PUBLIC_OPENOBSERVE_TOKEN as string;
 
const otlpUrl = `${OPEN_OBSERVE_URL}api/default/v1/traces`;
const AUTH_HEADER = `Basic ${btoa(OPEN_OBSERVE_TOKEN)}`;
 
interface RequestBody {
  telemetryData: unknown;
}
 
export const POST: APIRoute = async ({ request }) => {
  try {
    const telemetryData = (await request.json()) as RequestBody;
 
    const response = await axios.post(otlpUrl, telemetryData, {
      headers: {
        "Content-Type": "application/json",
        Authorization: AUTH_HEADER,
        organization: "default",
        "stream-name": "default",
        Accept: "application/json",
      },
    });
 
    console.log("Telemetry sent to OpenObserve");
    console.log("Status:", response.status);
 
    return new Response("Telemetry sent successfully", { status: 200 });
  } catch (error: unknown) {
    if (axios.isAxiosError(error)) {
      console.log(error);
      return new Response("Failed to send telemetry", { status: 502 });
    }
 
    console.error("Unexpected error:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
};