---
title: Opsfolio CaaS Content Engine Activity Log
---

# Purpose of Document

This document serves as the central log and guidance for tracking,
updating, and optimizing all activities related to the Opsfolio
Compliance-as-a-Service (CaaS) content engineering lifecycle. It records
progress by date and provides a template for using AI-based tools to
automatically monitor, report, and optimize ongoing systematic planning,
creation, management, and optimization of content.

This document is a running log of learnings, experiments, tactical
actions, and strategic decisions related to Opsfolio content
engineering. It is organized with the most recent entries at the top to
support rapid situational awareness and effective use of time-ordered
context. The document is intentionally structured to be
machine-ingestible by large language models (LLMs) in the future,
serving as a durable source of truth and training reference.

Nothing should be assumed or left undocumented---every update, status,
or insight should be captured as if explaining to a new engineer or AI
assistant joining the effort later. This is not just a content activity
log---it is a **Content *Engineering* Log**, designed to support
iterative improvement and future reuse of historical knowledge. Past
entries should inform and accelerate future outreach, messaging, and
system optimization.

While this document serves as the central log for tracking the evolution
of the Opsfolio content engineering lifecycle, it is not intended to
replace or duplicate canonical data stored in other systems. Key
metrics, campaign analytics, and source-of-truth assets may reside in
platforms like Google Analytics, LinkedIn campaign reports, CRM systems,
email campaign dashboards, or shared documents. That's not only
expected---it's encouraged. This log should act as the **narrative
thread**, providing context and decision-making history, and may include
**references, summaries, or links** to those external artifacts. Treat
this as the connective tissue that ties together disparate sources into
a coherent, AI-ready timeline of events and learnings.

# Using AI for Activity Tracking and Reporting

AI should be used for the following purposes within this sales campaign:

-   Summarizing daily and weekly updates from campaigns.

-   Providing details about where AI-native content engineering is
    successfully being adopted vs. may be problematic.

-   Evaluating content strategy, information architecture, metadata
    management, and content lifecycle management.

-   Suggesting adjustments to ICPs, blog posts, email copy, and campaign
    timing based on engagement analytics.

-   Generating content variants automatically for A/B testing.

-   Preparing weekly and monthly reports for executive reviews.

# Best Practices for Content Activity Logging

-   Log entries should be updated weekly or more frequently as needed.

-   AI tools such as CRM-integrated bots, GPT-based summaries, and
    campaign analytics dashboards should auto-generate status reports.

-   Use a shared Google Sheet or Airtable to augment this document for
    daily tracking.

-   All team members should input data into a structured form for AI
    summarization.

-   Create a dashboard to visualize activity and engagement metrics.


# Week Ending September 12, 2025 Activities


| Date       | Activity Summary                                                                                   | Status / Next Steps |
|------------|----------------------------------------------------------------------------------------------------|---------------------|
| 2025-09-12 | CMMC requirements blog post  | -visuals <br>-assets <br>-video                  |
| 2025-09-11 | SEO Strategy, JTBD Landing Pages, V&V+TEM Page redo  | -follow up on page schemas <br>-implement JTBD landing pages in lovable <br>-implement v&V and TEM pages in lovable                    |
| 2025-09-10 | CaC blog post finalization, earned media outreach, CaaS page |                     |
| 2025-09-09 | CaC blog post |                     |
| 2025-09-08 | content audit, webinar strategy, content tracking strategy |                     |
| 2025-09-07 | webinar strategy, Github prep for CE log  |                     |
| 2025-07-06 |  |                     |

## Learnings during week of September 5-September 12 and adjustments made

### GA4 data:

| Page title and screen class                                                                                                             |   Views |   Active users |   Views per active user |   Average engagement time per active user |   Event count |   Key events |   Total revenue |
|:----------------------------------------------------------------------------------------------------------------------------------------|--------:|---------------:|------------------------:|------------------------------------------:|--------------:|-------------:|----------------:|
| C3PAO or Self-Assessment? How to Get CMMC Level 1 Compliance Right the First Time \| Opsfolio                                            |     228 |            242 |                0.9421   |                                   3.1281  |           667 |            0 |               0 |
| How to Reduce Compliance Surface Area for CMMC Using Government-Furnished Equipment & Targeted Descoping Strategies \| Opsfolio          |     140 |            142 |                0.9859   |                                   3.4577  |           426 |            0 |               0 |
| 3 CMMC Myths That Could Trigger DoD Contract Losses and Legal Liability \| Opsfolio                                                     |      64 |             70 |                0.9143   |                                   3.0000  |           169 |            0 |               0 |
| How to Set Up Virtual Desktop Infrastructure (VDI) with Azure to reduce Compliance Surface Area for CMMC \| Opsfolio                     |      44 |             44 |                1.0000   |                                   4.2955  |           114 |            0 |               0 |
| The Complete Guide to Compliance-as-Code \| Opsfolio                                                                                    |      15 |              2 |                7.5000   |                                 400.0000  |            43 |            0 |               0 |
| How Map Collective Achieved SOC 2 Certification in Under 2 Months \| Opsfolio                                                           |      10 |              3 |                3.3333   |                                  41.6667  |            25 |            0 |               0 |
| How a Leading Healthcare Engagement Network Secured HIPAA Compliance and Protected Provider and Client Relationships with Opsfolio CaaS |       7 |              1 |                7.0000   |                                  28.0000  |            12 |            0 |               0 |
| How a Global Medical Technology Leader Secured FDA QSR Compliance and Embedded Security by Design with Opsfolio CaaS                     |       5 |              1 |                5.0000   |                                  78.0000  |             9 |            0 |               0 |
| Login - Opsfolio - Compliance as a Service \| SOC2, HIPAA, ISO Certification                                                            |       4 |              4 |                1.0000   |                                   5.7500  |            12 |            0 |               0 |
| CMMC Scoping in the Cloud Era: Three Level 1 Scenarios \| Opsfolio                                                                      |       3 |              2 |                1.5000   |                                   4.0000  |            10 |            0 |               0 |


* drill down into traffic sources with Subhash this week
* ChatGPT recommends the following to understand low engagement time on posts that are getting some traffic:
* Add scroll-depth tracking (25%, 50%, 75%, 100%) to get a truer picture of reading.
* Use heatmaps/session replays (e.g., Hotjar) to see where attention drops.
* Break up text with visuals, CTAs, and jump links.
* Compare conversion rate (e.g., clicks to self-assessment) against engagement time — sometimes short engagement is fine if the CTA fires quickly.

### Content writing

* batch jobs with many similar prompts should be handled prompt by prompt instead of attempting to get it to output multiple similar pieces of output at once. We experienced this on two batch related content tasks: JTBD landing pages where each page used the same prompt applied to a different pain point, and the requirements blog post where each section had the same format across 15 sections. When we run the job as a batch, the output is thin throughout the batch, possibly due to AI maxing out its constraints during the run. We got better results generating section prompts and running one prompt at a time. eg JTBD landing page promts: https://github.com/opsfolio/www.opsfolio.com/blob/main/src/ai-context-engineering/misc-content/jtbd-landing-pages/jtbd-landing-page-prompts.md

* AI drafting still produces thin prose when you constrain it with specific information and citations; it tends to repeat the direct language of the structure/citations. eg. in this week's requirements post https://github.com/opsfolio/www.opsfolio.com/blob/main/src/ai-context-engineering/blog-posts/cmmc-level-1-requirements-smb.md it only provided 1-2 sentences per control until I gave it the specific structure of analogy -> technical explanation. Asking it for "Mckinsey/HBR style analysis" does not help with this point. Best solution so far is still to ask it to explain specific points more. I will try a multi-prompt architecture where I ask it to review for thinness and then ask it to explain more pre-emptively.

### Observations on Argument Construction

**Scope of test:**
This week’s *Compliance-as-Code* blog post required manual intervention to transform AI-generated fragments into a coherent, persuasive argument. The section in question framed the failure of traditional compliance, supported by high-profile examples (Target, Simpson Thacher, Morgan Stanley), and then escalated to executive-level stakes before pivoting to compliance-as-code as solution.

**Findings:**

* AI generated data points of failures of conventional compliance successfully.
* However, it did not clearly surface how it related to the compliance-as-code thesis, just that they were failures of traditional compliance.
* AI can create the logical bridge when you point out the specific argument and how you want it to relate to the data points well. 
* Aligning business purpose -> clear argument -> data points and exploring to find the best argument still requires human massaging currently. 






# Week Ending September 5, 2025 Activities


| Date       | Activity Summary                                                                                   | Status / Next Steps |
|------------|----------------------------------------------------------------------------------------------------|---------------------|
| 2025-09-05 | CIVCO case study, cold email scripts , scoping blog post  |                     |
| 2025-09-04 | Content remediation tool testing, POCN case study  | diagnose the malformed .md file issue on remediation tool                    |
| 2025-09-03 | Content remediation tool testing , CMMC myths post (draft + review) |                     |
| 2025-09-02 | Scoping post research (SERP, competitor scan, knowledge bank construction), newsletter outreach (venues+contacts research, script development), product scripts (drawing details from blog post) | follow up with newsletter reply, continue outreach                    |
| 2025-09-01 | SEO strategy (CMMC serp analysis, new pillar post ideas), content engine refinement |  update compliance as code page and other pages based on SEO results                   |
| 2025-08-31 | Newsletter strategy (segmentation, list cleanup, amplification ideas),  |                     |
| 2025-08-30 |  |                     |




## Learnings during week of August 30-September 5 and adjustments made

* Developed prompt for initial rewrite of ODC case studies: https://github.com/opsfolio/www.opsfolio.com/blob/main/src/ai-context-engineering/case-studies/improve-odc-case-studies.md

### Observations on Outlining and Composition Scripts

**Scope of test:**
Over the past few week I ran multiple attempts to generate articles using AI scripting workflows. The focus was specifically on complex, citation-dependent blog posts. I tested three phases: outlining, composition from outline, and editing.

**Findings:**

* Editing scripts perform consistently well. [Argumentation check](https://github.com/opsfolio/www.opsfolio.com/blob/main/src/ai-context-engineering/content-engineering-workflow/argument-clarity-check.prompt), [fact-checks](https://github.com/opsfolio/www.opsfolio.com/blob/main/src/ai-context-engineering/content-engineering-workflow/editing%20-%20fact%20check.md), and [style refinements](https://github.com/opsfolio/www.opsfolio.com/blob/main/src/ai-context-engineering/content-engineering-workflow/editing%20-%20style%20v2.md) yield reliable improvements. 

* Our composition scripts remain weak for complex, citation-based work.

[Latest version of first approach](https://github.com/opsfolio/www.opsfolio.com/blob/main/src/ai-context-engineering/content-engineering-workflow/rhetorical-construction-prompts.md)

[Latest version of second approach](https://github.com/opsfolio/www.opsfolio.com/blob/main/src/ai-context-engineering/content-engineering-workflow/knowledge-outline-draft.md)

  * Outlining: AI struggles to automatically sequence heterogeneous knowledge into a coherent structure. Generated outlines are serviceable but do not integrate all available knowledge/citations.
  * *Expansion: When provided with a citation-based outline, AI tends to regurgitate outline with a small amount of padding.
  * Idea completion: If given a fully-formed article concept without the constraint of citations, AI can run effectively, producing fluent draft prose (eg this week's [scoping post](https://opsfolio.com/blog/cmmc-scoping-scenarios/) after I came up with the final viable article concept). Also, AI provides workable first drafts of citation-based content. But citation-based content still requires lots of massaging, asking it to explain more
* Overall pattern: AI does well at editing, polishing, and drafting. But “massage” work (human curation, sequencing, and contextual framing) remains essential during the outlining-drafting phase.
* Strategic note: We believe robust outlining-drafting workflows should be possible with more engineering. However, our recent focus has been on increasing throughput of publishable content rather than cracking the outlining→drafting problem directly.







# Week Ending August 29, 2025 Activities
| Date       | Activity Summary                                                                 | Status / Next Steps                                                                 |
|------------|----------------------------------------------------------------------------------|-------------------------------------------------------------------------------------|
| 2025-08-29 | VDI Azure blog post                                                              | - Promote blog posts via outreach, press channels, newsletter ads, etc              |
| 2025-08-28 | Self-assessment tool testing, audit, planning                                    | - ODC to score assessment following rubric, add report improvements                 |
| 2025-08-27 | CSA blog post, SEO tool spec                                                     |                                                                                     |
| 2025-08-26 | Self-assessment report planning, content engine testing, CSA blog post           |                                                                                     |
| 2025-08-25 | Market research, offer development, sales page copy, sales page design, content audit tool spec |                                                                                     |
| 2025-08-24 | Login page copy, GRC blog edits, email policy                                    |                                                                                     |

## Learnings during week of August 23-29 and adjustments made

-   Lovable lets you easily iterate on design and make specified copy
    edits; ChatGPT lets you iterate on copy and concepts but doesn't do
    full design. That points to a potential tool that lets you play with
    copy concepts and design at the same time.

-   Technical blog posts without subject matter expertise on the part of
    writer still require extensive manual checking for due diligence.
    Will test citation checkers this week.

-   Reviewed knowledge engineering approaches for technical content
    writing with ChatGPT. Question is where a marketing writer is able
    to ground technical knowledge when they are not an engineer
    themselves. ChatGPT can of course output steps fluently but
    knowledge needs to be grounded somewhere to be verified. Main
    approaches at leading content shops: 1. SME interviews translating
    SME knowledge into writing 2. Hybrid practitioner engineers/writers
    (used especially at large companies like MSFT, AWS, etc) 3. Reliance
    on vendor documentation is still a major source for technical
    content writing. We can use all 3, with #3 as the most readily
    available knowledge, #2 possible when I can spin up testing
    environments, #1 being the slowest but still potentially useful for
    some pieces.

# Week Ending August 22, 2025 Activities
| Date       | Activity Summary                                                                 | Status / Next Steps             |
|------------|----------------------------------------------------------------------------------|---------------------------------|
| 2025-08-22 | C3PAO blog post                                                                  |                                 |
| 2025-08-21 | Write content audit prompts, CRA content audit, CRA page rewrite, CMMC tool teardown | - Add next edits suggested by CRA audit |
| 2025-08-20 | SPRS blog post                                                                   |                                 |
| 2025-08-19 | Self-assessment report planning, content engine testing                          |                                 |
| 2025-08-18 | Self-assessment tool question review                                             |                                 |

## Learnings during week of August 18-22 and adjustments made

-   Developed Content Audit prompts that deliver accurate and actionable
    feedback
    ([www.opsfolio.com/support/gpm/planning/growth-hacking/compliance-as-code-content-audit.md
    at main ·
    opsfolio/www.opsfolio.com](https://github.com/opsfolio/www.opsfolio.com/blob/main/support/gpm/planning/growth-hacking/compliance-as-code-content-audit.md),
    [www.opsfolio.com/support/gpm/planning/growth-hacking/cra-content-audit.md
    at main ·
    opsfolio/www.opsfolio.com](https://github.com/opsfolio/www.opsfolio.com/blob/main/support/gpm/planning/growth-hacking/cra-content-audit.md)).
    These are ready to be deployed for a sitewide content audit as soon
    as this week.

-   Citation checking takes more than half the time required to write or
    edit a blog post. AI is still capable of hallucinating quotes even
    when asked directly to use verbatim text from a document, even after
    re-prompting (this happened for several quotes in this week's blog
    post). Currently due diligence requires checking every fact by hand,
    especially for regulated industries. Long term, tools for
    citation/verification will be valuable for a content engineering
    workflow.

-   Rewrote editing and content audit prompts in line with guidance that
    they must be grounded in authoritative sources to be useful. Found
    authoritative sources including Minto, Toulmin, Strunk and White,
    etc.

-   SEO research and ideation still requires going back and forth
    between AI+human ideation and SEO analyzer. However, this workflow
    can be automated (generate keyword ideas, research in SEO tool,
    review SERP and analyze results, generate brief to discover
    whitespace) - spec forthcoming.

# 

# 

# Week Ending August 15, 2025 Activities

| Date       | Activity Summary                                                                 | Status / Next Steps                                                                                   |
|------------|----------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------|
| 2025-08-15 | Per-persona landing pages, self-assessment audit for report generation           | - Deliver self-assessment audit and recommendations <br> - Add social proof to landing pages and refine offer <br> - Develop offer structure and write sales page <br> - Produce a set of per-persona blog posts by running content engine |
| 2025-08-14 | Per-persona landing pages, self-assessment audit for report generation, content engine testing |                                                                                                       |
| 2025-08-13 | Email funnel design, self-assessment report planning, content engine testing     | - Milestone: piece run through content engine end-to-end via manual prompting                         |
| 2025-08-12 | Self-assessment report planning, content engine testing                          |                                                                                                       |
| 2025-08-11 | Review documents for CMMC self assessment report, report planning, content engine testing |                                                                                                       |

## Learnings during week of August 11-15 and adjustments made

-   Significant improvement seen in generated blog writing as a result
    of the multi-layered editing pass -- goes from very dry to engaging
    B2B tone. But the result is still not truly outstanding and
    attention grabbing yet because the idea becomes generic.

-   If you prompt LLM to generate articles that don't require SME input
    it is much more able to produce articles that pass the knowledge
    gate. Best two optimize two separate workflows depending on whether
    we are aiming at lightweight SME free articles or deeper articles
    where we are able to get input

-   Current prompts derived from CJM don't have enough information for
    ChatGPT to create truly great landing pages. Either go with stripped
    down landing page format (which is used by many products) or need to
    get much more in the way of testimonials, expected benefits, etc for
    long-style landing page

-   It will be difficult to generate a reliable self-assessment report
    unless the questions are aligned more closely with the source
    material so that the POAMs can be generated from the answers+sources

-   Knowledge audit phase tuning: needed to get it not to "pass" (score
    8+) unless there were no critical questions. But for pieces run
    through the standard ideation prompt, all of them had blockers on
    this phase. Hence the need for a separate prompt looking
    specifically for non-SME pieces

# Week Ending August 8, 2025 Activities
| Date       | Activity Summary                                                                 | Status / Next Steps                                                                                   |
|------------|----------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------|
| 2025-08-08 | User interview, content prompt refining/testing, opsfolio CMMC tool testing, customer journey map, CMMC funnel Plan | - Plan for initial set of blog articles next week <br> - Further interviews with Shuab's friends <br> - Create CJM in Mermaid JS <br> - Execute funnel plan next week, building key funnel artifacts <br> - Currently testing drafting and editing stages of prompt pipeline <br> - Start stitching prompts together via DSPy |
| 2025-08-07 | AM Team sync, content prompt refining/testing, Tour Deck review                  | - Create V2 Tour deck                                                                                 |
| 2025-08-06 | Content prompt creation from roadmap, lead magnet planning, interview agenda      |                                                                                                       |
| 2025-08-05 | Content prompt creation from roadmap, user journey map review                     |                                                                                                       |
| 2025-08-04 | Overwatch tool teardown, Lead Magnet differentiation strategy                     |                                                                                                       |
## Learnings during week of August 4-8 and adjustments made

-Assessment should **filter relevance early** --- avoid asking
non-applicable technical questions to small orgs. Incorporating
**persona-aware flows** improves user satisfaction and trust.

\-**Knowledge arbitrage** (public info + webinar insights delivered
directly) can win attention without SEO competition. For direct
outreach, Tier 1 (knowledge arbitrage) content works for early-stage,
less-informed buyers; Tier 2 (SME-informed) needed for advanced
prospects.

-There is enough initial knowledge from webinar+public search for a
first wave of articles

-CMMC reddit is a great source of detailed pain points, especially IT
related (vs management related)

-Lead magnet's differentiation: **AI-generated, role-personalized
readiness reports** with action plans. Our offer can be differentiated
by **personalization + AI-generated insights**; competitors tend to be
generic checklists.

-Without guardrails, AI will tend to come up with article ideas that
require extensive knowledge gathering. Need to run a 2 track system to
prompt AI for articles that can be addressed via existing knowledge vs
what may need SME input

# 
