{"resourceType": "Questionnaire", "meta": {"profile": ["http://hl7.org/fhir/4.0/StructureDefinition/Questionnaire"]}, "title": "Information and instructions to the user", "status": "draft", "item": [{"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "check-box", "display": "Check-box"}]}}], "linkId": "399719335829", "text": "Identifying details of the software developer provided with the product. (Select all that apply)", "repeats": true, "answerOption": [{"valueString": "Name"}, {"valueString": "Trade name"}, {"valueString": "Trademark"}, {"valueString": "Postal address"}, {"valueString": "Email / digital contact"}, {"valueString": "Website"}]}, {"type": "string", "linkId": "322969903102", "text": "Notes/ Evidence"}, {"type": "string", "linkId": "503755869764", "text": "Single point of contact provided for reporting and receiving information about vulnerabilities in the product"}, {"item": [{"type": "string", "linkId": "502874667212", "text": "Notes/ Evidence"}], "type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "835508308907", "text": "Is the software developer’s coordinated vulnerability disclosure (CVD) policy accessible at this contact point?  ", "answerOption": [{"valueString": "Yes"}, {"valueString": "No"}]}, {"item": [{"type": "string", "linkId": "140749411440", "text": "Notes/ Evidence"}], "type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "121597378321", "text": "Does the product documentation include the name, type, and any additional information necessary for unique identification of the product?  ", "answerOption": [{"valueString": "Yes"}, {"valueString": "No"}]}, {"item": [{"type": "string", "linkId": "482964292841", "text": "Notes/ Evidence"}], "type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "870788560273", "text": "Does the documentation clearly state the intended purpose of the product, including the security environment provided by the developer?  ", "answerOption": [{"valueString": "Yes"}, {"valueString": "No"}]}, {"item": [{"type": "string", "linkId": "119101734605", "text": "Notes/ Evidence"}], "type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "367376720668", "text": "Are the essential functionalities and security properties of the product described for the user?  ", "answerOption": [{"valueString": "Yes"}, {"valueString": "No"}]}, {"item": [{"type": "string", "linkId": "374535761824", "text": "Notes/ Evidence"}], "type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "731345469776", "text": "Does the documentation include any known or foreseeable circumstances, whether from intended use or reasonably foreseeable misuse, that could lead to significant cybersecurity risks?  ", "answerOption": [{"valueString": "Yes"}, {"valueString": "No"}]}, {"item": [{"type": "string", "linkId": "472133035815", "text": "Notes/ Evidence"}], "type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "965337654836", "text": "Where applicable, does the documentation provide the internet address where the EU declaration of conformity can be accessed?  ", "answerOption": [{"valueString": "Yes"}, {"valueString": "No"}]}, {"item": [{"type": "string", "linkId": "597288608758", "text": "Notes/ Evidence"}], "type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "909900123705", "text": "Does the documentation specify the type of technical security support offered by the software developer?  ", "answerOption": [{"valueString": "Yes"}, {"valueString": "No"}]}, {"item": [{"type": "string", "linkId": "770501903653", "text": "Notes/ Evidence"}], "type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "653135313606", "text": "Is the end-date of the support period, during which vulnerabilities will be handled and security updates provided, clearly stated?  ", "answerOption": [{"valueString": "Yes"}, {"valueString": "No"}]}, {"item": [{"type": "string", "linkId": "508821143554", "text": "Notes/ Evidence"}], "type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "593423501537", "text": "Are detailed instructions provided on the necessary measures during initial commissioning and throughout the product’s lifetime to ensure secure use?  ", "answerOption": [{"valueString": "Yes"}, {"valueString": "No"}]}, {"item": [{"type": "string", "linkId": "908219908339", "text": "Notes/ Evidence"}], "type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "277156167672", "text": "Do the instructions explain how changes to the product may affect the security of data?  ", "answerOption": [{"valueString": "Yes"}, {"valueString": "No"}]}, {"item": [{"type": "string", "linkId": "************", "text": "Notes/ Evidence"}], "type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "************", "text": "Do the instructions provide clear guidance on how to install security-relevant updates?  ", "answerOption": [{"valueString": "Yes"}, {"valueString": "No"}]}, {"item": [{"type": "string", "linkId": "************", "text": "Notes/ Evidence"}], "type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "************", "text": "Do the instructions explain how to securely decommission the product, including the secure removal of user data?  ", "answerOption": [{"valueString": "Yes"}, {"valueString": "No"}]}, {"item": [{"type": "string", "linkId": "************", "text": "Notes/ Evidence"}], "type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "905997855907", "text": "Do the instructions explain how users can turn off the default setting for automatic installation of security updates?  ", "answerOption": [{"valueString": "Yes"}, {"valueString": "No"}]}, {"item": [{"type": "string", "linkId": "656393060333", "text": "Notes/ Evidence"}], "type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "587879995982", "text": "If the product is intended for integration into other products, does the documentation provide the necessary information for the integrator to meet the essential cybersecurity and documentation requirements? ", "answerOption": [{"valueString": "Yes"}, {"valueString": "No"}]}, {"item": [{"type": "string", "linkId": "280150474128", "text": "Notes/ Evidence"}], "type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "247970875270", "text": "If a Software Bill of Materials (SBOM) is made available, does the documentation specify where users can access it?  ", "answerOption": [{"valueString": "Yes"}, {"valueString": "No"}]}, {"item": [{"type": "string", "linkId": "197185531753", "text": "Notes/ Evidence"}], "type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "497672905239", "text": "Are all the above user information and instructions included in the Technical Documentation?  ", "answerOption": [{"valueString": "Yes"}, {"valueString": "No"}]}]}