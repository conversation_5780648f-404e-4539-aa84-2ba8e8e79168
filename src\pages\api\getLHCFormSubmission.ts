// src/pages/api/getLHCFormSubmission.ts
import { readdir, readFile } from "fs/promises";
import { readFileSync, existsSync } from "fs";
import type { APIContext } from "astro";
import path from "path";

export async function POST({ request }: APIContext) {
    try {
        const { fileName, userID, sessionName, tenantID } = await request.json();
        const baseDir = `src/data/lforms/${tenantID}/${userID}`;
        const possibleFiles = [
            `.submit.lform-submission.json`,
            `.save.lform-submission.json`,
        ];

        let foundFilePath: string | null = null;

        // --- Step 1: Try ONLY the given session folder ---
        const currentSessionDir = path.join(baseDir, sessionName);
        try {
            const files = await readdir(currentSessionDir);
            const match = files.find(
                (f) =>
                    f.startsWith(fileName) &&
                    possibleFiles.some((ext) => f.endsWith(ext))
            );
            if (match) {
                foundFilePath = path.join(currentSessionDir, match);
            }
        } catch {
            // If no folder or error, we'll fallback to questionnaire
        }

        // --- Step 2: If not found, use master questionnaire ---
        if (!foundFilePath) {
            const masterPath = `src/content/cmmc-self-assessment/questionnaires/${fileName}.json`;
            if (!existsSync(masterPath)) {
                return new Response(
                    JSON.stringify({ error: `File not found: ${masterPath}` }),
                    { status: 404, headers: { "Content-Type": "application/json" } }
                );
            }

            try {
                const fileContents = readFileSync(masterPath, "utf8");
                const data = JSON.parse(fileContents);
                return new Response(
                    JSON.stringify({ data, sessionName: null }),
                    { status: 200, headers: { "Content-Type": "application/json" } }
                );
            } catch {
                return new Response(
                    JSON.stringify({ error: "Error reading master file" }),
                    { status: 500, headers: { "Content-Type": "application/json" } }
                );
            }
        }

        // --- Step 3: Return the found submission ---
        const fileContent = await readFile(foundFilePath, "utf-8");
        const json = JSON.parse(fileContent);
        return new Response(
            JSON.stringify({ data: json, sessionName }),
            { status: 200, headers: { "Content-Type": "application/json" } }
        );
    } catch (err) {
        console.error("Error loading submission:", err);
        return new Response(
            JSON.stringify({ error: "Failed to load form submission" }),
            { status: 500, headers: { "Content-Type": "application/json" } }
        );
    }
}
