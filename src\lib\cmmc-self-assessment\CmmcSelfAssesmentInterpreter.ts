// src/lib/CmmcSelfAssesmentInterpreter.ts
import fs from "fs";
import {
  type CompanyInformation,
  type MediaProtectionProtectInformationOnDigitalAndNonDigitalMedia,
  type PhysicalProtection,
  type AccessControl,
  type IdentificationAuthentication,
  type PolicyFrameworkAssessment,
  type SystemCommunicationsProtection,
  type SystemInformationIntegrity,
  adapters,
  LHCFormSource
} from "./mod.ts";
import { flattenItems } from "./r4q-runtime.ts";

// deno-lint-ignore no-explicit-any
type Any = any;

export type ReadinessStatus = "Fully" | "Partially" | "Not";
export type FamilySummary = {
  key: string;
  name: string;
  status: ReadinessStatus;
  notes: string;
  meta: {
    title: string;
    status: string;
    date: string;
    publisher: string;
    description: string;
    purpose: string;
    approvalDate: string;
    lastReviewDate: string;
  };
  evidence?: { question: string; answer: string; expected: string; result: "Pass" | "Fail" }[];
};

export class CmmcSelfAssesmentInterpreter {
  readonly ci: CompanyInformation;
  readonly mp: MediaProtectionProtectInformationOnDigitalAndNonDigitalMedia;
  readonly pp: PhysicalProtection;
  readonly ac: AccessControl;
  readonly iu: IdentificationAuthentication;
  readonly pf: PolicyFrameworkAssessment;
  readonly sc: SystemCommunicationsProtection;
  readonly si: SystemInformationIntegrity;
  private readonly responseRootPath: string;

  constructor(init: {
    readonly responseRootPath: string;
    companyInfoFile?: string;
    mediaProtectionFile?: string;
    physicalProtectionFile?: string;
    acessControlFile?: string;
    identificationAuthenticationFile?: string;
    policyFrameworkFile?: string;
    systemCommunicationFile?: string;
    systemInformationFile?: string;
  }) {
    const {
      responseRootPath = "",
      companyInfoFile = "",
      mediaProtectionFile = "",
      physicalProtectionFile = "",
      acessControlFile = "",
      identificationAuthenticationFile = "",
      policyFrameworkFile = "",
      systemCommunicationFile = "",
      systemInformationFile = "",
    } = init;

    this.responseRootPath = responseRootPath;
    this.ci = adapters.ci(this.readResponseAsJSON(companyInfoFile));
    this.mp = adapters.mp(this.readResponseAsJSON(mediaProtectionFile));
    this.pp = adapters.pp(this.readResponseAsJSON(physicalProtectionFile));
    this.ac = adapters.ac(this.readResponseAsJSON(acessControlFile));
    this.iu = adapters.ia(this.readResponseAsJSON(identificationAuthenticationFile));
    this.pf = adapters.pf(this.readResponseAsJSON(policyFrameworkFile));
    this.sc = adapters.sc(this.readResponseAsJSON(systemCommunicationFile));
    this.si = adapters.si(this.readResponseAsJSON(systemInformationFile));
  }

  private readResponseAsJSON(fileName: string) {
    try {
      const responseFileName = this.responseRootPath + fileName;
      if (!fileName) return {};
      if (!fs.existsSync(responseFileName)) return {};
      const content = fs.readFileSync(responseFileName, "utf-8");
      return JSON.parse(content);
    } catch {
      return {};
    }
  }

  private extractMeta(form: Any) {
    try {
      const lform = typeof form === "string" ? JSON.parse(form) : form;
      return {
        title: lform.title ?? "",
        status: lform.status ?? "",
        description: lform.description ?? "",
        version: lform.version ?? "",
        publisher: lform.publisher ?? "",
        date: lform.date ?? "",
        purpose: lform.purpose ?? "",
        approvalDate: lform.approvalDate ?? "",
        lastReviewDate: lform.lastReviewDate ?? "",
      };
    } catch (err) {
      console.error("❌ Failed to extract metadata:", err);
      return {
        title: "",
        status: "",
        description: "",
        version: "",
        publisher: "",
        date: "",
        purpose: "",
        approvalDate: "",
        lastReviewDate: "",
      };
    }
  }

  get meta() {
    return {
      ci: this.extractMeta(LHCFormSource.ci),
      mp: this.extractMeta(LHCFormSource.mp),
      pp: this.extractMeta(LHCFormSource.pp),
      ac: this.extractMeta(LHCFormSource.ac),
      iu: this.extractMeta(LHCFormSource.ia),
      pf: this.extractMeta(LHCFormSource.pf),
      sc: this.extractMeta(LHCFormSource.sc),
      si: this.extractMeta(LHCFormSource.si),
    };
  }

  /** ------------ Helpers ------------- */
  private normalizeStatus(status: string | undefined | null): ReadinessStatus {
    if (!status) return "Not";
    const s = String(status).toLowerCase();
    if (s.includes("fully")) return "Fully";
    if (s.includes("partial")) return "Partially";
    if (s.includes("not")) return "Not";
    return "Not";
  }

  private aggregateStatuses(statuses: Array<string | undefined | null>): ReadinessStatus {
    const mapped = statuses
      .filter((s) => s != null && String(s).trim() !== "")
      .map((s) => this.normalizeStatus(s));
    if (mapped.length === 0) return "Not";
    // All fully?
    if (mapped.every((x) => x === "Fully")) return "Fully";
    // Any Not -> Not (unless some Full + some Not: treat as Partial to reflect mixed evidence)
    const anyNot = mapped.includes("Not");
    const anyFully = mapped.includes("Fully");
    const anyPartial = mapped.includes("Partially");
    if (anyFully && (anyNot || anyPartial)) return "Partially";
    if (anyPartial && !anyNot) return "Partially";
    return anyNot ? "Not" : "Partially";
  }

  private pct(n: number, d: number): number {
    return d > 0 ? Math.round((n / d) * 100) : 0;
  }

  private truthyCount(vals: unknown[]): number {
    return vals.filter((v) => {
      if (Array.isArray(v)) return v.length > 0;
      return Boolean(v);
    }).length;
  }

  private nonEmptyArray(v?: unknown): boolean {
    return Array.isArray(v) && v.length > 0;
  }

  private hasAnyData<T extends object>(obj: T | undefined | null): boolean {
    if (!obj) return false;
    return Object.keys(obj as object).length > 0;
  }

  private formatAssessmentDate(value?: Date | string): string {
    try {
      const d = value
        ? value instanceof Date
          ? value
          : new Date(value)
        : new Date(); // default to current date if no value

      return isNaN(d.getTime()) ? new Date().toDateString() : d.toDateString();
    } catch {
      return new Date().toDateString();
    }
  }

  private buildQuestionMeta(
    titleCamel: string,
    titlePascal: string,
    source: Any
  ): Record<string, string> {
    const lFormData = JSON.parse(source);
    const { fields } = flattenItems(titleCamel, titlePascal, lFormData.item, new Set());
    const meta: Record<string, string> = {};
    for (const f of fields) {
      meta[f.propName] = f.text ?? f.linkId;
    }
    return meta;
  }

  private statusForMediaProtection(
    mp: MediaProtectionProtectInformationOnDigitalAndNonDigitalMedia
  ): ReadinessStatus {
    return this.normalizeStatus(mp.implementationStatus);
  }

  private statusForPhysicalProtection(
    pp: PhysicalProtection
  ): ReadinessStatus {
    return this.aggregateStatuses([
      pp.implementationStatus,
      pp.implementationStatus2,
      pp.implementationStatus3,
      pp.implementationStatus4,
    ]);
  }

  private statusForAccessControl(
    ac: AccessControl
  ): ReadinessStatus {
    return this.aggregateStatuses([
      ac.implementationStatus,
      ac.implementationStatus2,
      ac.implementationStatus3,
      ac.howIsThePrincipleOfLeastPrivilegeImplemented,
    ]);
  }

  private statusForIdentification(
    iu: IdentificationAuthentication
  ): ReadinessStatus {
    return this.aggregateStatuses([iu.implementationStatus, iu.implementationStatus2]);
  }

  private statusForPolicyFramework(
    pf: PolicyFrameworkAssessment
  ): ReadinessStatus {
    // No explicit implementationStatus in this model. Use coverage heuristic.
    const buckets = [
      pf.whoIsResponsibleForDevelopingAndApprovingCmmcRelatedPolicies,
      pf.howFrequentlyAreCmmcRelatedPoliciesReviewedAndUpdated,
      pf.whatTrainingIsProvidedToEmployeesOnCmmcRelatedPolicies,
      pf.howIsComplianceWithCmmcRelatedPoliciesMonitored,
      pf.howAreExceptionsToCmmcRelatedPoliciesManaged,
    ];
    const total = buckets.length;
    const answered = buckets.filter((b) => this.nonEmptyArray(b)).length;
    // Penalize explicit "No formal ..." options if selected
    const hasNoTraining = this.nonEmptyArray(
      pf.whatTrainingIsProvidedToEmployeesOnCmmcRelatedPolicies
    ) &&
      (pf.whatTrainingIsProvidedToEmployeesOnCmmcRelatedPolicies as string[]).some((x) =>
        String(x).toLowerCase().includes("no formal")
      );
    const hasNoExceptionProcess = this.nonEmptyArray(
      pf.howAreExceptionsToCmmcRelatedPoliciesManaged
    ) &&
      (pf.howAreExceptionsToCmmcRelatedPoliciesManaged as string[]).some((x) =>
        String(x).toLowerCase().includes("no formal")
      );

    const ratio = answered / total;
    if (ratio >= 0.80 && !hasNoTraining && !hasNoExceptionProcess) return "Fully";
    if (ratio >= 0.4) return "Partially";
    return "Not";
  }

  private statusForSystemCommunications(
    sc: SystemCommunicationsProtection
  ): ReadinessStatus {
    // Has explicit implementationStatus fields
    const status = this.aggregateStatuses([sc.implementationStatus, sc.implementationStatus2]);
    return status;
  }

  private statusForSystemInformation(
    si: SystemInformationIntegrity
  ): ReadinessStatus {
    // Heuristic based on key controls being present/affirmative
    const scoreItems: unknown[] = [
      si.howDoesYourOrganizationIdentifySystemFlawsAndVulnerabilities,
      si.howAreIdentifiedFlawsReportedAndTracked,
      si.criticalSeverityFlaws,
      si.highSeverityFlaws,
      si.mediumLowSeverityFlaws,
      si.howAreSecurityPatchesAndUpdatesManaged,
      si.doYouHaveAMaliciousCodeProtectionPolicyDocument === "Yes",
      si.selectAllLocationsWhereMaliciousCodeProtectionIsImplemented,
      si.realTimeProtectionEnabled === "Yes",
      si.centrallyManaged === "Yes",
      si.howFrequentlyAreMaliciousCodeProtectionMechanismsUpdated,
      si.howAreMaliciousCodeProtectionUpdatesManaged,
      si.doYouHaveASystemScanningPolicyDocumentationFileScanningPolicyAndScanningProcedureDocumentation === "Yes",
      si.doesYourOrganizationHaveAntivirusAntiMalwareSoftwareInstalledOnAllSystems === "Yes",
      si.howFrequentlyAreFullSystemScansConducted,
      si.areFilesFromExternalSourcesScannedInRealTime,
      si.doYouEmployFileIntegrityMonitoringForCriticalSystemFiles,
      si.hasScanningEffectivenessBeenTested === "Yes",
    ];
    const total = scoreItems.length;
    const score = this.truthyCount(scoreItems);
    const ratio = total ? score / total : 0;
    if (ratio >= 0.8) return "Fully";
    if (ratio >= 0.4) return "Partially";
    return "Not";
  }

  accessControlQuestionMeta = this.buildQuestionMeta(
    "accessControl",
    "AccessControl",
    LHCFormSource.ac
  );

  mediaProtectionQuestionMeta = this.buildQuestionMeta(
    "mediaProtection",
    "MediaProtection",
    LHCFormSource.mp
  );

  identificationAuthenticationQuestionMeta = this.buildQuestionMeta(
    "identificationAuthentication",
    "IdentificationAuthentication",
    LHCFormSource.ia
  );

  physicalProtectionQuestionMeta = this.buildQuestionMeta(
    "physicalProtection",
    "PhysicalProtection",
    LHCFormSource.pp
  );

  policyFrameworkQuestionMeta = this.buildQuestionMeta(
    "physicalProtection",
    "PhysicalProtection",
    LHCFormSource.pf
  );

  systemInformationIntegrityQuestionMeta = this.buildQuestionMeta(
    "systemInformationIntegrity",
    "SystemInformationIntegrity",
    LHCFormSource.si
  );

  systemCommunicationsProtectionIntegrityQuestionMeta = this.buildQuestionMeta(
    "systemCommunicationsProtection",
    "SystemCommunicationsProtection",
    LHCFormSource.sc
  );

  /**
   * Return key questions + answers + reason why they passed/failed
   * for a given control family.
   */
  getControlReasons(
    control: "AC" | "IA" | "SC" | "MP" | "PF" | "SI" | "PP"
  ): { question: string; answer: string; expected: string; result: "Pass" | "Fail" }[] {
    const reasons: { question: string; answer: string; expected: string; result: "Pass" | "Fail" }[] = [];

    switch (control) {
      case "AC":
        if (!this.hasAnyData(this.ac)) break;

        // Example: MFA question
        if (this.ac.implementationStatus) {
          reasons.push({
            question: this.accessControlQuestionMeta["implementationStatus"],
            answer: this.ac.implementationStatus,
            expected: "Fully Implemented",
            result: this.ac.implementationStatus.trim() === "Fully Implemented" ? "Pass" : "Fail",
          });
        } else {
          reasons.push({
            question: this.accessControlQuestionMeta["implementationStatus"],
            answer: "Not provided",
            expected: "Fully Implemented",
            result: "Fail",
          });
        }

        if (this.ac.implementationStatus2) {
          reasons.push({
            question: this.accessControlQuestionMeta["implementationStatus2"],
            answer: this.ac.implementationStatus2,
            expected: "Fully Implemented",
            result: this.ac.implementationStatus2.trim() === "Fully Implemented" ? "Pass" : "Fail",
          });
        } else {
          reasons.push({
            question: this.accessControlQuestionMeta["implementationStatus2"],
            answer: "Not provided",
            expected: "Fully Implemented",
            result: "Fail",
          });
        }

        if (this.ac.implementationStatus3) {
          reasons.push({
            question: this.accessControlQuestionMeta["implementationStatus3"],
            answer: this.ac.implementationStatus3,
            expected: "Fully Implemented",
            result: this.ac.implementationStatus3.trim() === "Fully Implemented" ? "Pass" : "Fail",
          });
        } else {
          reasons.push({
            question: this.accessControlQuestionMeta["implementationStatus3"],
            answer: "Not provided",
            expected: "Fully Implemented",
            result: "Fail",
          });
        }

        if (this.ac.howIsThePrincipleOfLeastPrivilegeImplemented) {
          reasons.push({
            question: this.accessControlQuestionMeta["howIsThePrincipleOfLeastPrivilegeImplemented"],
            answer: this.ac.howIsThePrincipleOfLeastPrivilegeImplemented,
            expected: "Fully implemented across all systems",
            result: (this.ac.howIsThePrincipleOfLeastPrivilegeImplemented).trim().includes("Fully implemented") ? "Pass" : "Fail",
          });
        }
        else {
          reasons.push({
            question: this.accessControlQuestionMeta["howIsThePrincipleOfLeastPrivilegeImplemented"],
            answer: "Not provided",
            expected: "Fully implemented across all systems",
            result: "Fail",
          });
        }

        break;
      case "MP":
        if (!this.hasAnyData(this.mp)) break;

        // Example: MFA question
        if (this.mp.implementationStatus) {
          reasons.push({
            question: this.mediaProtectionQuestionMeta["implementationStatus"],
            answer: this.mp.implementationStatus,
            expected: "Fully Implemented",
            result: this.mp.implementationStatus.trim() === "Fully Implemented" ? "Pass" : "Fail",
          });
        }

        break;
      case "IA":
        if (!this.hasAnyData(this.iu)) break;

        // Example: MFA question
        if (this.iu.implementationStatus) {
          reasons.push({
            question: this.identificationAuthenticationQuestionMeta["implementationStatus"],
            answer: this.iu.implementationStatus,
            expected: "Fully Implemented",
            result: this.iu.implementationStatus.trim() === "Fully Implemented" ? "Pass" : "Fail",
          });
        }

        if (this.iu.implementationStatus2) {
          reasons.push({
            question: this.identificationAuthenticationQuestionMeta["implementationStatus2"],
            answer: this.iu.implementationStatus2,
            expected: "Fully Implemented",
            result: this.iu.implementationStatus2.trim() === "Fully Implemented" ? "Pass" : "Fail",
          });
        }
        break;
      case "PP":
        if (!this.hasAnyData(this.pp)) break;

        // Example: MFA question
        if (this.pp.implementationStatus) {
          reasons.push({
            question: this.physicalProtectionQuestionMeta["implementationStatus"],
            answer: this.pp.implementationStatus,
            expected: "Fully Implemented",
            result: this.pp.implementationStatus.trim() === "Fully Implemented" ? "Pass" : "Fail",
          });
        }

        if (this.pp.implementationStatus2) {
          reasons.push({
            question: this.physicalProtectionQuestionMeta["implementationStatus2"],
            answer: this.pp.implementationStatus2,
            expected: "Fully Implemented",
            result: this.pp.implementationStatus2.trim() === "Fully Implemented" ? "Pass" : "Fail",
          });
        }

        if (this.pp.implementationStatus3) {
          reasons.push({
            question: this.physicalProtectionQuestionMeta["implementationStatus3"],
            answer: this.pp.implementationStatus3,
            expected: "Fully Implemented",
            result: this.pp.implementationStatus3.trim() === "Fully Implemented" ? "Pass" : "Fail",
          });
        }
        break;
      case "PF":
        if (!this.hasAnyData(this.pf)) break;
        // Responsible parties

        if (this.pf.whoIsResponsibleForDevelopingAndApprovingCmmcRelatedPolicies?.length) {
          reasons.push({
            question: this.policyFrameworkQuestionMeta["whoIsResponsibleForDevelopingAndApprovingCmmcRelatedPolicies"],
            answer: this.pf.whoIsResponsibleForDevelopingAndApprovingCmmcRelatedPolicies.join(", "),
            expected: "Not an empty answer",
            result: "Pass", // Always a pass if provided
          });
        } else {
          reasons.push({
            question: this.policyFrameworkQuestionMeta["whoIsResponsibleForDevelopingAndApprovingCmmcRelatedPolicies"],
            answer: "Not provided",
            expected: "Not an empty answer",
            result: "Fail", // Always a pass if provided
          });
        }

        // Policy review frequency
        if (this.pf.howFrequentlyAreCmmcRelatedPoliciesReviewedAndUpdated) {
          reasons.push({
            question: this.policyFrameworkQuestionMeta["howFrequentlyAreCmmcRelatedPoliciesReviewedAndUpdated"],
            answer: this.pf.howFrequentlyAreCmmcRelatedPoliciesReviewedAndUpdated,
            expected: "Value should not be 'No formal schedule'",
            result: this.pf.howFrequentlyAreCmmcRelatedPoliciesReviewedAndUpdated.toLowerCase().includes("no formal")
              ? "Fail"
              : "Pass",
          });
        } else {
          reasons.push({
            question: this.policyFrameworkQuestionMeta["howFrequentlyAreCmmcRelatedPoliciesReviewedAndUpdated"],
            answer: "Not provided",
            expected: "Value should not be 'No formal schedule' and should not be empty",
            result: "Fail"
          });
        }

        // Training
        if (this.pf.whatTrainingIsProvidedToEmployeesOnCmmcRelatedPolicies?.length) {
          reasons.push({
            question: this.policyFrameworkQuestionMeta["whatTrainingIsProvidedToEmployeesOnCmmcRelatedPolicies"],
            answer: this.pf.whatTrainingIsProvidedToEmployeesOnCmmcRelatedPolicies.join(", "),
            expected: "Value should not be 'No formal training program'",
            result: this.pf.whatTrainingIsProvidedToEmployeesOnCmmcRelatedPolicies.some(x =>
              x.toLowerCase().includes("no formal")
            )
              ? "Fail"
              : "Pass",
          });
        } else {
          reasons.push({
            question: this.policyFrameworkQuestionMeta["whatTrainingIsProvidedToEmployeesOnCmmcRelatedPolicies"],
            answer: "Not provided",
            expected: "Value should not be 'No formal training program' and should not be empty",
            result: "Fail"
          });
        }

        // Compliance monitoring
        if (this.pf.howIsComplianceWithCmmcRelatedPoliciesMonitored?.length) {
          reasons.push({
            question: this.policyFrameworkQuestionMeta["howIsComplianceWithCmmcRelatedPoliciesMonitored"],
            answer: this.pf.howIsComplianceWithCmmcRelatedPoliciesMonitored.join(", "),
            expected: "Not an empty answer",
            result: "Pass",
          });
        } else {
          reasons.push({
            question: this.policyFrameworkQuestionMeta["howIsComplianceWithCmmcRelatedPoliciesMonitored"],
            answer: "Not provided",
            expected: "Not an empty answer",
            result: "Fail",
          });
        }

        // Exception handling

        if (this.pf.howAreExceptionsToCmmcRelatedPoliciesManaged?.length) {
          reasons.push({
            question: this.policyFrameworkQuestionMeta["howAreExceptionsToCmmcRelatedPoliciesManaged"],
            answer: this.pf.howAreExceptionsToCmmcRelatedPoliciesManaged.join(", "),
            expected: "Value should not be 'No formal exception process'",
            result: this.pf.howAreExceptionsToCmmcRelatedPoliciesManaged.some(x =>
              x.toLowerCase().includes("no formal")
            )
              ? "Fail"
              : "Pass",
          });
        } else {
          reasons.push({
            question: this.policyFrameworkQuestionMeta["howAreExceptionsToCmmcRelatedPoliciesManaged"],
            answer: "Not provided",
            expected: "Value should not be 'No formal exception process' and should not be empty",
            result: "Fail"
          });
        }
        break;
      case "SI":
        if (!this.hasAnyData(this.si)) break;
        // Malicious code protection policy
        if (this.si.doYouHaveAMaliciousCodeProtectionPolicyDocument) {

          reasons.push({
            question: this.systemInformationIntegrityQuestionMeta["doYouHaveAMaliciousCodeProtectionPolicyDocument"],
            answer: this.si.doYouHaveAMaliciousCodeProtectionPolicyDocument,
            expected: "Yes",
            result: this.si.doYouHaveAMaliciousCodeProtectionPolicyDocument === "Yes" ? "Pass" : "Fail",
          });
        }

        // Antivirus deployment
        if (this.si.doesYourOrganizationHaveAntivirusAntiMalwareSoftwareInstalledOnAllSystems) {
          reasons.push({
            question: this.systemInformationIntegrityQuestionMeta["doesYourOrganizationHaveAntivirusAntiMalwareSoftwareInstalledOnAllSystems"],
            answer: this.si.doesYourOrganizationHaveAntivirusAntiMalwareSoftwareInstalledOnAllSystems,
            expected: "Yes",
            result: this.si.doesYourOrganizationHaveAntivirusAntiMalwareSoftwareInstalledOnAllSystems === "Yes" ? "Pass" : "Fail",
          });
        }

        // Real-time protection
        if (this.si.realTimeProtectionEnabled) {
          reasons.push({
            question: this.systemInformationIntegrityQuestionMeta["realTimeProtectionEnabled"],
            answer: this.si.realTimeProtectionEnabled,
            expected: "Yes",
            result: this.si.realTimeProtectionEnabled === "Yes" ? "Pass" : "Fail",
          });
        }

        // Centrally managed
        if (this.si.centrallyManaged) {
          reasons.push({
            question: this.systemInformationIntegrityQuestionMeta["centrallyManaged"],
            answer: this.si.centrallyManaged,
            expected: "Yes",
            result: this.si.centrallyManaged === "Yes" ? "Pass" : "Fail",
          });
        }

        // Scanning effectiveness tested
        if (this.si.hasScanningEffectivenessBeenTested) {
          reasons.push({
            question: this.systemInformationIntegrityQuestionMeta["hasScanningEffectivenessBeenTested"],
            answer: this.si.hasScanningEffectivenessBeenTested,
            expected: "Yes",
            result: this.si.hasScanningEffectivenessBeenTested === "Yes" ? "Pass" : "Fail",
          });
        }

        break;
      case "SC":
        if (!this.hasAnyData(this.sc)) break;
        if (this.sc.implementationStatus) {
          reasons.push({
            question: this.systemCommunicationsProtectionIntegrityQuestionMeta["implementationStatus"],
            answer: this.sc.implementationStatus,
            expected: "Fully Implemented - All boundary protection controls are in place and operational",
            result: (this.sc.implementationStatus).trim().includes("Fully Implemented") ? "Pass" : "Fail",
          });
        }
        if (this.sc.implementationStatus2) {
          reasons.push({
            question: this.systemCommunicationsProtectionIntegrityQuestionMeta["implementationStatus2"],
            answer: this.sc.implementationStatus2,
            expected: "Fully Implemented - DMZ/subnetworks properly isolate public systems",
            result: (this.sc.implementationStatus2).trim().includes("Fully Implemented") ? "Pass" : "Fail",
          });
        }
        break;
    }

    return reasons;
  }

  /** ------------ Public summaries ------------- */
  computeReadinessSummary(): { overall: number; families: FamilySummary[] } {
    const families: FamilySummary[] = [];

    if (this.hasAnyData(this.ac)) {
      const status = this.statusForAccessControl(this.ac);
      families.push({ key: "ac", name: "Access Control", status, notes: this.statusNote(status), meta: this.meta.ac, evidence: this.getControlReasons("AC") });
    }
    if (this.hasAnyData(this.iu)) {
      const status = this.statusForIdentification(this.iu);
      families.push({ key: "iu", name: "Identification & Authentication", status, notes: this.statusNote(status), meta: this.meta.iu, evidence: this.getControlReasons("IA") });
    }
    if (this.hasAnyData(this.mp)) {
      const status = this.statusForMediaProtection(this.mp);
      families.push({ key: "mp", name: "Media Protection", status, notes: this.statusNote(status), meta: this.meta.mp, evidence: this.getControlReasons("MP") });
    }
    if (this.hasAnyData(this.pp)) {
      const status = this.statusForPhysicalProtection(this.pp);
      families.push({ key: "pp", name: "Physical Protection", status, notes: this.statusNote(status), meta: this.meta.pp, evidence: this.getControlReasons("PP") });
    }
    if (this.hasAnyData(this.sc)) {
      const status = this.statusForSystemCommunications(this.sc);
      families.push({ key: "sc", name: "System & Communications Protection", status, notes: this.statusNote(status), meta: this.meta.sc, evidence: this.getControlReasons("SC") });
    }
    if (this.hasAnyData(this.si)) {
      const status = this.statusForSystemInformation(this.si);
      families.push({ key: "si", name: "System Information Integrity", status, notes: this.statusNote(status), meta: this.meta.si, evidence: this.getControlReasons("SI") });
    }
    if (this.hasAnyData(this.pf)) {
      const status = this.statusForPolicyFramework(this.pf);
      families.push({ key: "pf", name: "Policy Framework", status, notes: this.statusNote(status), meta: this.meta.pf, evidence: this.getControlReasons("PF") });
    }


    // Score: Fully = 1, Partially = 0.5, Not = 0
    const scoreMap: Record<ReadinessStatus, number> = { Fully: 1, Partially: 0.5, Not: 0 };
    const scoreSum = families.reduce((acc, f) => acc + scoreMap[f.status], 0);
    const overall = this.pct(scoreSum, families.length);
    return { overall, families };
  }

  private statusNote(status: ReadinessStatus): string {
    return status === "Fully"
      ? "Pass"
      : status === "Partially"
        ? "Partial / inconsistent evidence"
        : "Not implemented";
  }

  extractEvidence(): string[] {
    const ev: string[] = [];

    // System & Communications Protection
    if (this.hasAnyData(this.sc)) {
      if (this.sc.implementationStatus) {
        ev.push(
          `Implementation status: "${this.sc.implementationStatus}" (SC boundary protection).`
        );
      }
      if (
        typeof this.sc.doYouHaveANetworkDiagramShowingSystemBoundariesKeyComponentsAndDataFlows ===
        "string"
      ) {
        const hasDiagram = this.sc.doYouHaveANetworkDiagramShowingSystemBoundariesKeyComponentsAndDataFlows
          .toLowerCase()
          .includes("yes");
        ev.push(`Network diagram present: ${hasDiagram ? "Yes" : "No"}.`);
      }
      if (this.sc.externalBoundaryComponents) {
        ev.push(`External boundary components: ${this.sc.externalBoundaryComponents}.`);
      }
      if (this.sc.keyInternalBoundaryComponents) {
        ev.push(`Internal segmentation components: ${this.sc.keyInternalBoundaryComponents}.`);
      }
      if (this.sc.firewallManufacturerModel || this.sc.firewallSoftwareFirmwareVersion) {
        ev.push(
          `Firewalls: ${this.sc.firewallManufacturerModel ?? "N/A"}; SW/FW: ${this.sc.firewallSoftwareFirmwareVersion ?? "N/A"
          }.`
        );
      }
      if (this.sc.defaultDenyPolicyIsImplementedTrafficIsDeniedByDefaultUnlessExplicitlyPermitted) {
        ev.push(
          `Default-deny policy: ${this.sc.defaultDenyPolicyIsImplementedTrafficIsDeniedByDefaultUnlessExplicitlyPermitted}.`
        );
      }
      if (this.sc.explicitlyAllowedServicesProtocols || this.sc.explicitlyDeniedServicesProtocols) {
        ev.push(
          `Explicitly allowed: ${this.sc.explicitlyAllowedServicesProtocols || "N/A"}; denied: ${this.sc.explicitlyDeniedServicesProtocols || "N/A"
          }.`
        );
      }
      if (this.sc.howDoYouMonitorCommunicationsAtSystemBoundaries && this.sc.howDoYouMonitorCommunicationsAtSystemBoundaries.length > 0) {
        ev.push(
          `Monitoring: ${this.sc.howDoYouMonitorCommunicationsAtSystemBoundaries.join(", ")}.`
        );
      }
    }

    // --- Policy Framework ---
    if (this.hasAnyData(this.pf)) {
      if (this.pf.whoIsResponsibleForDevelopingAndApprovingCmmcRelatedPolicies?.length) {
        ev.push(
          `Policy development/approval responsibility: ${this.pf.whoIsResponsibleForDevelopingAndApprovingCmmcRelatedPolicies.join(", ")}.`
        );
      }
      if (this.pf.howFrequentlyAreCmmcRelatedPoliciesReviewedAndUpdated) {
        ev.push(
          `Policies reviewed/updated: ${this.pf.howFrequentlyAreCmmcRelatedPoliciesReviewedAndUpdated}.`
        );
      }
      if (this.pf.whatTrainingIsProvidedToEmployeesOnCmmcRelatedPolicies?.length) {
        ev.push(
          `Policy training provided: ${this.pf.whatTrainingIsProvidedToEmployeesOnCmmcRelatedPolicies.join(", ")}.`
        );
      }
      if (this.pf.howIsComplianceWithCmmcRelatedPoliciesMonitored?.length) {
        ev.push(
          `Compliance monitored via: ${this.pf.howIsComplianceWithCmmcRelatedPoliciesMonitored.join(", ")}.`
        );
      }
      if (this.pf.howAreExceptionsToCmmcRelatedPoliciesManaged?.length) {
        ev.push(
          `Exceptions managed by: ${this.pf.howAreExceptionsToCmmcRelatedPoliciesManaged.join(", ")}.`
        );
      }

      // Notes / Evidence
      if (this.pf.notesEvidence) ev.push(`Policy notes: ${this.pf.notesEvidence}`);
      if (this.pf.notesEvidence2) ev.push(`Review notes: ${this.pf.notesEvidence2}`);
      if (this.pf.notesEvidence3) ev.push(`Training notes: ${this.pf.notesEvidence3}`);
      if (this.pf.notesEvidence4) ev.push(`Monitoring notes: ${this.pf.notesEvidence4}`);
      if (this.pf.notesEvidence5) ev.push(`Exception notes: ${this.pf.notesEvidence5}`);

      if (this.pf.additionalNotes) {
        ev.push(`Additional policy framework notes: ${this.pf.additionalNotes}`);
      }
    }

    // --- Physical Protection ---
    if (this.hasAnyData(this.pp)) {
      // Authorized personnel inventory
      if (this.pp.fullTimeEmployees || this.pp.contractors || this.pp.partTimeEmployees || this.pp.visitorsWithEscort) {
        ev.push(
          `Authorized personnel - Full-time: ${this.pp.fullTimeEmployees ?? "N/A"}, Contractors: ${this.pp.contractors ?? "N/A"}, Part-time: ${this.pp.partTimeEmployees ?? "N/A"}, Visitors (with escort): ${this.pp.visitorsWithEscort ?? "N/A"}.`
        );
      }

      if (this.pp.whatAreasRequireControlledPhysicalAccess?.length) {
        ev.push(`Controlled access areas: ${this.pp.whatAreasRequireControlledPhysicalAccess.join(", ")}.`);
      }
      if (this.pp.whoAuthorizesPhysicalAccessToControlledAreas?.length) {
        ev.push(`Access authorized by: ${this.pp.whoAuthorizesPhysicalAccessToControlledAreas.join(", ")}.`);
      }
      if (this.pp.whatTypesOfPhysicalAccessCredentialsAreIssued?.length) {
        ev.push(`Access credentials issued: ${this.pp.whatTypesOfPhysicalAccessCredentialsAreIssued.join(", ")}.`);
      }
      if (this.pp.areThereTimeBasedRestrictionsOnPhysicalAccess?.length) {
        ev.push(`Time-based restrictions: ${this.pp.areThereTimeBasedRestrictionsOnPhysicalAccess.join(", ")}.`);
      }
      if (this.pp.implementationStatus) {
        ev.push(`Physical access implementation status: ${this.pp.implementationStatus}.`);
      }

      // Visitor management
      if (this.pp.doesYourOrganizationRequireAllVisitorsToBeEscorted) {
        ev.push(`Visitor escort requirement: ${this.pp.doesYourOrganizationRequireAllVisitorsToBeEscorted}.`);
      }
      if (this.pp.howAreVisitorsIdentifiedAndDistinguishedFromEmployees?.length) {
        ev.push(`Visitor identification methods: ${this.pp.howAreVisitorsIdentifiedAndDistinguishedFromEmployees.join(", ")}.`);
      }
      if (this.pp.howIsVisitorActivityMonitoredWhileOnPremises?.length) {
        ev.push(`Visitor monitoring: ${this.pp.howIsVisitorActivityMonitoredWhileOnPremises.join(", ")}.`);
      }
      if (this.pp.whoIsAuthorizedToEscortVisitors) {
        ev.push(`Visitor escorts: ${this.pp.whoIsAuthorizedToEscortVisitors}.`);
      }
      if (this.pp.implementationStatus2) {
        ev.push(`Visitor access implementation status: ${this.pp.implementationStatus2}.`);
      }

      // Access logs
      if (this.pp.howDoYouLogPhysicalAccessToYourFacilities?.length) {
        ev.push(`Access logging methods: ${this.pp.howDoYouLogPhysicalAccessToYourFacilities.join(", ")}.`);
      }
      if (this.pp.whatInformationIsCapturedInYourPhysicalAccessLogsInformationCapturedInLogs?.length) {
        ev.push(`Access logs capture: ${this.pp.whatInformationIsCapturedInYourPhysicalAccessLogsInformationCapturedInLogs.join(", ")}.`);
      }
      if (this.pp.howLongArePhysicalAccessLogsRetained) {
        ev.push(`Access logs retained for: ${this.pp.howLongArePhysicalAccessLogsRetained}.`);
      }
      if (this.pp.howFrequentlyAreAccessLogsReviewed) {
        ev.push(`Access logs reviewed: ${this.pp.howFrequentlyAreAccessLogsReviewed}.`);
      }
      if (this.pp.whoReviewsThePhysicalAccessLogs?.length) {
        ev.push(`Access logs reviewed by: ${this.pp.whoReviewsThePhysicalAccessLogs.join(", ")}.`);
      }
      if (this.pp.implementationStatus3) {
        ev.push(`Physical access logs implementation status: ${this.pp.implementationStatus3}.`);
      }

      // Device controls
      if (this.pp.whatTypesOfPhysicalAccessDevicesDoesYourOrganizationUse?.length) {
        ev.push(`Access devices used: ${this.pp.whatTypesOfPhysicalAccessDevicesDoesYourOrganizationUse.join(", ")}.`);
      }
      if (this.pp.howArePhysicalAccessDevicesControlledAndManaged?.length) {
        ev.push(`Access devices managed via: ${this.pp.howArePhysicalAccessDevicesControlledAndManaged.join(", ")}.`);
      }
      if (this.pp.whatSecurityMeasuresProtectPhysicalAccessDevices?.length) {
        ev.push(`Security measures for devices: ${this.pp.whatSecurityMeasuresProtectPhysicalAccessDevices.join(", ")}.`);
      }
      if (this.pp.howFrequentlyAreElectronicAccessSystemsUpdated) {
        ev.push(`Electronic access systems updated: ${this.pp.howFrequentlyAreElectronicAccessSystemsUpdated}.`);
      }
      if (this.pp.implementationStatus4) {
        ev.push(`Physical access devices implementation status: ${this.pp.implementationStatus4}.`);
      }

      // Evidence notes
      [
        this.pp.notesEvidence, this.pp.notesEvidence2, this.pp.notesEvidence3, this.pp.notesEvidence4,
        this.pp.notesEvidence5, this.pp.notesEvidence6, this.pp.notesEvidence7, this.pp.notesEvidence8,
        this.pp.notesEvidence9, this.pp.notesEvidence10, this.pp.notesEvidence11, this.pp.notesEvidence12,
        this.pp.notesEvidence13, this.pp.notesEvidence14, this.pp.notesEvidence15, this.pp.notesEvidence16,
        this.pp.notesEvidence17, this.pp.notesEvidence18, this.pp.notesEvidence19, this.pp.notesEvidence20,
        this.pp.notesEvidence21
      ].forEach((note, idx) => {
        if (note) ev.push(`Physical Protection note${idx + 1}: ${note}`);
      });
    }

    // --- Media Protection ---
    if (this.hasAnyData(this.mp)) {
      if (this.mp.doYouHaveAMediaDisposalPolicy) {
        ev.push(`Media disposal policy exists: ${this.mp.doYouHaveAMediaDisposalPolicy}.`);
      }
      if (this.mp.implementationStatus) {
        ev.push(`Media protection implementation status: ${this.mp.implementationStatus}.`);
      }
      if (this.mp.confirmThatYourMediaDisposalPolicyIncludesTheFollowingElementsClickAllThatApply?.length) {
        ev.push(
          `Media disposal policy elements: ${this.mp.confirmThatYourMediaDisposalPolicyIncludesTheFollowingElementsClickAllThatApply.join(", ")}.`
        );
      }

      // Evidence notes
      [this.mp.notesEvidence, this.mp.notesEvidence2, this.mp.notesEvidence3].forEach((note, idx) => {
        if (note) ev.push(`Media Protection note${idx + 1}: ${note}`);
      });
    }

    // --- Identification & Authentication ---
    if (this.hasAnyData(this.iu)) {
      if (this.iu.implementationStatus) {
        ev.push(`Identification implementation status: ${this.iu.implementationStatus}.`);
      }
      if (this.iu.userIdentificationStandards) {
        ev.push(`User ID standard: ${this.iu.userIdentificationStandards}.`);
      }
      if (typeof this.iu.numberOfServiceAccounts === "number") {
        ev.push(`Service accounts: ${this.iu.numberOfServiceAccounts}.`);
      }
      if (this.iu.checkAllThatApply?.length) {
        ev.push(`Service account usage: ${this.iu.checkAllThatApply.join(", ")}.`);
      }
      if (this.iu.doYouHaveADeviceInventorySpreadsheet) {
        ev.push(`Device inventory spreadsheet: ${this.iu.doYouHaveADeviceInventorySpreadsheet}.`);
      }
      if (this.iu.identityVerificationProcess?.length) {
        ev.push(`Identity verification process: ${this.iu.identityVerificationProcess.join(", ")}.`);
      }
      if (
        this.iu.workstationsLaptops ||
        this.iu.servers ||
        this.iu.mobileDevices ||
        this.iu.networkDevices
      ) {
        ev.push(
          `Devices — Workstations: ${this.iu.workstationsLaptops ?? 0}, Servers: ${this.iu.servers ?? 0}, Mobiles: ${this.iu.mobileDevices ?? 0}, Network: ${this.iu.networkDevices ?? 0}.`
        );
      }
      if (this.iu.deviceIdentification?.length) {
        ev.push(`Device identification methods: ${this.iu.deviceIdentification.join(", ")}.`);
      }
      if (this.iu.implementationStatus2) {
        ev.push(`Authentication implementation status: ${this.iu.implementationStatus2}.`);
      }
      if (this.iu.userAuthenticationMethods?.length) {
        ev.push(`Authentication methods: ${this.iu.userAuthenticationMethods.join(", ")}.`);
      }
      if (
        this.iu.minimumLengthCharacters ||
        this.iu.passwordExpirationDays ||
        this.iu.passwordHistoryPasswordsRemembered
      ) {
        ev.push(
          `Password policy — Min length: ${this.iu.minimumLengthCharacters ?? "N/A"}, Expiration: ${this.iu.passwordExpirationDays ?? "N/A"} days, History: ${this.iu.passwordHistoryPasswordsRemembered ?? "N/A"} remembered.`
        );
      }
      if (this.iu.clickAllThatApply?.length) {
        ev.push(`Password complexity: ${this.iu.clickAllThatApply.join(", ")}.`);
      }
      if (this.iu.multiFactorAuthentication) {
        ev.push(`MFA: ${this.iu.multiFactorAuthentication}.`);
      }
      if (this.iu.defaultCredentialManagement) {
        ev.push(`Default credential management: ${this.iu.defaultCredentialManagement}.`);
      }
      if (
        this.iu.numberOfFailedAttemptsBeforeLockout ||
        this.iu.accountLockoutDurationMinutes
      ) {
        ev.push(
          `Lockout policy — Failed attempts: ${this.iu.numberOfFailedAttemptsBeforeLockout ?? "N/A"}, Duration: ${this.iu.accountLockoutDurationMinutes ?? "N/A"} minutes.`
        );
      }
      if (this.iu.clickAllThatApply2?.length) {
        ev.push(`Failure response: ${this.iu.clickAllThatApply2.join(", ")}.`);
      }

      // Evidence notes
      [
        this.iu.notesEvidence,
        this.iu.notesEvidence2,
        this.iu.notesEvidence3,
        this.iu.notesEvidence4,
        this.iu.notesEvidence5,
        this.iu.notesEvidence6,
      ].forEach((note, idx) => {
        if (note) ev.push(`IA note${idx + 1}: ${note}`);
      });
    }

    // --- Access Control ---
    if (this.hasAnyData(this.ac)) {
      if (this.ac.doYouHaveAnAccessControlPolicy) {
        ev.push(`Access control policy: ${this.ac.doYouHaveAnAccessControlPolicy}.`);
      }
      if (this.ac.doesYourOrganizationHaveADocumentedAccessControlPolicyThatAddresses?.length) {
        ev.push(`Policy elements: ${this.ac.doesYourOrganizationHaveADocumentedAccessControlPolicyThatAddresses.join(", ")}.`);
      }
      if (this.ac.activeUserAccounts !== undefined || this.ac.inactiveDisabledUserAccounts !== undefined) {
        ev.push(`Accounts — Active: ${this.ac.activeUserAccounts ?? "N/A"}, Inactive: ${this.ac.inactiveDisabledUserAccounts ?? "N/A"}, Service: ${this.ac.serviceAccounts ?? "N/A"}, Shared: ${this.ac.sharedAccounts ?? "N/A"}.`);
      }
      if (this.ac.howIsThePrincipleOfLeastPrivilegeImplemented) {
        ev.push(`Least privilege: ${this.ac.howIsThePrincipleOfLeastPrivilegeImplemented}.`);
      }
      if (this.ac.howAreAccountLifecycleProcessesManaged?.length) {
        ev.push(`Account lifecycle management: ${this.ac.howAreAccountLifecycleProcessesManaged.join(", ")}.`);
      }
      if (this.ac.howFrequentlyAreUserAccountsReviewedForValidityAndAppropriateAccess) {
        ev.push(`Account review frequency: ${this.ac.howFrequentlyAreUserAccountsReviewedForValidityAndAppropriateAccess}.`);
      }
      if (this.ac.howDoYouLimitUserAccessToSpecificTransactionsAndFunctions) {
        ev.push(`Transaction controls: ${this.ac.howDoYouLimitUserAccessToSpecificTransactionsAndFunctions}.`);
      }
      if (this.ac.whatTypesOfFunctionsAreRestrictedBasedOnUserRoles?.length) {
        ev.push(`Role restrictions: ${this.ac.whatTypesOfFunctionsAreRestrictedBasedOnUserRoles.join(", ")}.`);
      }
      if (this.ac.howAreHighRiskTransactionsAuthorized?.length) {
        ev.push(`High-risk transaction authorization: ${this.ac.howAreHighRiskTransactionsAuthorized.join(", ")}.`);
      }
      if (this.ac.whatTypesOfExternalSystemsDoesYourOrganizationConnectTo?.length) {
        ev.push(`External connections: ${this.ac.whatTypesOfExternalSystemsDoesYourOrganizationConnectTo.join(", ")}.`);
      }
      if (this.ac.howDoYouVerifyExternalSystemConnections?.length) {
        ev.push(`External verification methods: ${this.ac.howDoYouVerifyExternalSystemConnections.join(", ")}.`);
      }
      if (this.ac.whatLimitationsArePlacedOnExternalConnections?.length) {
        ev.push(`External connection limitations: ${this.ac.whatLimitationsArePlacedOnExternalConnections.join(", ")}.`);
      }
      if (this.ac.whatPubliclyAccessibleSystemsDoesYourOrganizationOperate?.length) {
        ev.push(`Public systems: ${this.ac.whatPubliclyAccessibleSystemsDoesYourOrganizationOperate.join(", ")}.`);
      }
      if (this.ac.howDoYouEnsureFciIsNotPostedOnPublicSystems?.length) {
        ev.push(`Public info controls: ${this.ac.howDoYouEnsureFciIsNotPostedOnPublicSystems.join(", ")}.`);
      }
      if (this.ac.whoIsAuthorizedToPostContentToPublicSystems) {
        ev.push(`Authorized to post: ${this.ac.whoIsAuthorizedToPostContentToPublicSystems} (${this.ac.numberOfAuthorizedPersonnel ?? "N/A"} personnel, roles: ${this.ac.chooseAllThatApply?.join(", ") ?? "N/A"}).`);
      }

      // Evidence notes
      [
        this.ac.notesEvidence,
        this.ac.notesEvidence2,
        this.ac.notesEvidence3,
        this.ac.notesEvidence4,
        this.ac.notesEvidence5,
        this.ac.notesEvidence6,
        this.ac.notesEvidence7,
        this.ac.notesEvidence8,
        this.ac.notesEvidence9,
        this.ac.notesEvidence10,
        this.ac.notesEvidence11,
        this.ac.notesEvidence12,
        this.ac.notesEvidence13,
        this.ac.notesEvidence14,
        this.ac.notesEvidence15,
        this.ac.notesEvidence16,
      ].forEach((note, idx) => {
        if (note) ev.push(`AC note${idx + 1}: ${note}`);
      });
    }
    // --- System & Information Integrity (SI) ---
    if (this.hasAnyData(this.si)) {
      if (this.si.howDoesYourOrganizationIdentifySystemFlawsAndVulnerabilities?.length) {
        ev.push(`Flaw identification: ${this.si.howDoesYourOrganizationIdentifySystemFlawsAndVulnerabilities.join(", ")}.`);
      }
      if (this.si.howAreIdentifiedFlawsReportedAndTracked?.length) {
        ev.push(`Flaw reporting/tracking: ${this.si.howAreIdentifiedFlawsReportedAndTracked.join(", ")}.`);
      }
      if (this.si.criticalSeverityFlaws || this.si.highSeverityFlaws || this.si.mediumLowSeverityFlaws) {
        ev.push(`Remediation timelines — Critical: ${this.si.criticalSeverityFlaws ?? "N/A"}, High: ${this.si.highSeverityFlaws ?? "N/A"}, Medium/Low: ${this.si.mediumLowSeverityFlaws ?? "N/A"}.`);
      }
      if (this.si.howAreSecurityPatchesAndUpdatesManaged?.length) {
        ev.push(`Patch management: ${this.si.howAreSecurityPatchesAndUpdatesManaged.join(", ")}.`);
      }
      if (this.si.doYouHaveAMaliciousCodeProtectionPolicyDocument) {
        ev.push(`Malicious code protection policy: ${this.si.doYouHaveAMaliciousCodeProtectionPolicyDocument}.`);
      }
      if (this.si.selectAllLocationsWhereMaliciousCodeProtectionIsImplemented?.length) {
        ev.push(`Malware protection locations: ${this.si.selectAllLocationsWhereMaliciousCodeProtectionIsImplemented.join(", ")}.`);
      }
      if (this.si.primaryAntiMalwareProductSolutionEGMicrosoftDefenderMcAfeeSymantec) {
        ev.push(`Anti-malware solution: ${this.si.primaryAntiMalwareProductSolutionEGMicrosoftDefenderMcAfeeSymantec} (version: ${this.si.antiMalwareVersionReleaseVersionNumberOrReleaseIdentifier ?? "N/A"}, scope: ${this.si.implementationScopeDescribeTheScopeOfYourAntiMalwareImplementationEGAllCompanyEndpointsSpecificServers ?? "N/A"}, real-time: ${this.si.realTimeProtectionEnabled ?? "N/A"}, centrally managed: ${this.si.centrallyManaged ?? "N/A"}).`);
      }
      if (this.si.howFrequentlyAreMaliciousCodeProtectionMechanismsUpdated) {
        ev.push(`Malware update frequency: ${this.si.howFrequentlyAreMaliciousCodeProtectionMechanismsUpdated}.`);
      }
      if (this.si.howAreMaliciousCodeProtectionUpdatesManaged?.length) {
        ev.push(`Malware update management: ${this.si.howAreMaliciousCodeProtectionUpdatesManaged.join(", ")}.`);
      }
      if (this.si.whichExternalSourcesAreScanned?.length) {
        ev.push(`External sources scanned: ${this.si.whichExternalSourcesAreScanned.join(", ")}.`);
      }
      if (this.si.doYouHaveASystemScanningPolicyDocumentationFileScanningPolicyAndScanningProcedureDocumentation) {
        ev.push(`System scanning policy: ${this.si.doYouHaveASystemScanningPolicyDocumentationFileScanningPolicyAndScanningProcedureDocumentation}.`);
      }
      if (this.si.doesYourOrganizationHaveAntivirusAntiMalwareSoftwareInstalledOnAllSystems) {
        ev.push(`Antivirus coverage: ${this.si.doesYourOrganizationHaveAntivirusAntiMalwareSoftwareInstalledOnAllSystems} (solution: ${this.si.whatAntivirusAntiMalwareSolutionIsCurrentlyDeployedEGMicrosoftDefenderNortonMcAfeeEtc ?? "N/A"}).`);
      }
      if (this.si.howFrequentlyAreFullSystemScansConducted || this.si.whatLevelOfThoroughnessIsUsedForPeriodicScans) {
        ev.push(`Scanning — Frequency: ${this.si.howFrequentlyAreFullSystemScansConducted ?? "N/A"}, Depth: ${this.si.whatLevelOfThoroughnessIsUsedForPeriodicScans ?? "N/A"}.`);
      }
      if (this.si.areFilesFromExternalSourcesScannedInRealTime) {
        ev.push(`External file real-time scanning: ${this.si.areFilesFromExternalSourcesScannedInRealTime}.`);
      }
      if (this.si.doYouEmployFileIntegrityMonitoringForCriticalSystemFiles) {
        ev.push(`File integrity monitoring: ${this.si.doYouEmployFileIntegrityMonitoringForCriticalSystemFiles}.`);
      }
      if (this.si.howAreScanResultsReviewedAndDocumentedDescribeYourProcessForReviewingAndDocumentingScanResults) {
        ev.push(`Scan result review: ${this.si.howAreScanResultsReviewedAndDocumentedDescribeYourProcessForReviewingAndDocumentingScanResults}.`);
      }
      if (this.si.whatIsYourResponseTimeframeWhenMalwareOrVulnerabilitiesAreDetected) {
        ev.push(`Response timeframe: ${this.si.whatIsYourResponseTimeframeWhenMalwareOrVulnerabilitiesAreDetected}.`);
      }
      if (this.si.describeYourRemediationProcessForIdentifiedIssuesDescribeYourProcessForRemediatingIssuesDetectedDuringScanning) {
        ev.push(`Remediation process: ${this.si.describeYourRemediationProcessForIdentifiedIssuesDescribeYourProcessForRemediatingIssuesDetectedDuringScanning}.`);
      }
      if (this.si.hasScanningEffectivenessBeenTested) {
        ev.push(`Scanning effectiveness tested: ${this.si.hasScanningEffectivenessBeenTested}.`);
      }

      // Evidence notes
      [
        this.si.notesEvidence,
        this.si.notesEvidence2,
        this.si.notesEvidence3,
        this.si.notesEvidence4,
        this.si.notesEvidence5,
        this.si.notesEvidence6,
        this.si.notesEvidence7,
      ].forEach((note, idx) => {
        if (note) ev.push(`SI note${idx + 1}: ${note}`);
      });
    }

    return ev;
  }

  identifyTopRisks(): string[] {
    const risks: string[] = [];

    // --- Policy Framework (PF) ---
    if (this.hasAnyData(this.pf)) {
      const pfStatus = this.statusForPolicyFramework(this.pf);
      if (pfStatus !== "Fully") {
        risks.push(
          "Policy framework consistency and formalization (review schedule, exceptions, training)."
        );
      }
    }

    // --- System & Communications Protection (SC) ---
    if (this.hasAnyData(this.sc)) {
      if (!this.sc.supportingDocumentation && !this.sc.supportingDocumentation2) {
        risks.push("Missing supporting documentation for certain SC sections.");
      }
    }

    // --- Access Control (AC) ---
    if (this.hasAnyData(this.ac)) {
      if (this.ac.implementationStatus !== "Fully Implemented") {
        risks.push("Access Control implementation incomplete (transaction & function control).");
      }
      if (this.ac.implementationStatus2?.trim() !== "Fully Implemented") {
        risks.push("Access Control implementation incomplete (external connections).");
      }
      if (this.ac.implementationStatus3 !== "Fully Implemented") {
        risks.push("Access Control implementation incomplete (public information controls).");
      }
    }

    // --- Identification & Authentication (IA) ---
    if (this.hasAnyData(this.iu)) {
      if (this.iu.implementationStatus !== "Fully Implemented") {
        risks.push("Identification controls not fully implemented.");
      }
      if (this.iu.implementationStatus2 !== "Fully Implemented") {
        risks.push("Authentication controls not fully implemented.");
      }
      if (!this.iu.supportingDocumentation) {
        risks.push("Missing supporting documentation for Identification & Authentication.");
      }
    }

    // --- Media Protection (MP) ---
    if (this.hasAnyData(this.mp)) {
      if (this.mp.doYouHaveAMediaDisposalPolicy !== "Yes") {
        risks.push("No formal Media Disposal Policy in place.");
      }
      if (!this.mp.implementationStatus || (this.mp.implementationStatus).trim() === "Not Implemented") {
        risks.push("Media protection practice not fully implemented.");
      }
      if (!this.mp.confirmThatYourMediaDisposalPolicyIncludesTheFollowingElementsClickAllThatApply?.length) {
        risks.push("Media disposal policy lacks required elements.");
      }
      if (!this.mp.notesEvidence && !this.mp.notesEvidence2 && !this.mp.notesEvidence3) {
        risks.push("Missing evidence for Media Protection implementation.");
      }
    }

    // --- System & Information Integrity (SI) ---
    if (this.hasAnyData(this.si)) {
      if (!this.si.howDoesYourOrganizationIdentifySystemFlawsAndVulnerabilities?.length) {
        risks.push("No defined process to identify system flaws and vulnerabilities.");
      }
      if (!this.si.howAreSecurityPatchesAndUpdatesManaged?.length) {
        risks.push("No structured patch management process.");
      }
      if (this.si.doYouHaveAMaliciousCodeProtectionPolicyDocument === "No") {
        risks.push("Missing malicious code protection policy.");
      }
      if (this.si.realTimeProtectionEnabled === "No") {
        risks.push("Real-time malware protection not enabled.");
      }
      if (this.si.doesYourOrganizationHaveAntivirusAntiMalwareSoftwareInstalledOnAllSystems !== "Yes") {
        risks.push("Antivirus not deployed on all systems.");
      }
      if (this.si.hasScanningEffectivenessBeenTested !== "Yes") {
        risks.push("Scanning effectiveness has not been tested.");
      }
      if (
        !this.si.supportingDocumentation &&
        !this.si.supportingDocumentation2 &&
        !this.si.supportingDocumentation3 &&
        !this.si.supportingDocumentation4
      ) {
        risks.push("Missing supporting documentation for System & Information Integrity.");
      }
    }

    // --- Company Information (CI) ---
    if (this.hasAnyData(this.ci)) {
      if (!this.ci.organizationName || !this.ci.emailAddress) {
        risks.push("Company Information form incomplete (basic identity fields).");
      }
    } else {
      risks.push("Company Information form not submitted.");
    }

    // Return top 3 risks
    return risks;
  }


  generateReportData() {
    const { overall, families } = this.computeReadinessSummary();
    const evidence = this.extractEvidence();
    const risks = this.identifyTopRisks();

    const assessmentDate = this.formatAssessmentDate(
      this.ci.assessmentDate as string | Date | undefined
    );

    return { overall, families, evidence, risks, assessmentDate };
  }

  getCompanyInformation() {
    return this.ci;
  }
  getMediaProtectionInformation() {
    return this.mp;
  }
  getPhysicalProtectionInformation() {
    return this.pp;
  }
  getAccessControlInformation() {
    return this.ac;
  }
  getIdentificationAuthenticationInformation() {
    return this.iu;
  }
  getPolicyFrameworkInformation() {
    return this.pf;
  }
  getSystemCommunicationInformation() {
    return this.sc;
  }
  getSystemInformation() {
    return this.si;
  }
}
