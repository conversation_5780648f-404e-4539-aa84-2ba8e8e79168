---
import Layout from "../../layouts/Layout.astro";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  MapPin, 
  Eye, 
  Trash2, 
  Download, 
  UserX, 
  CheckCircle,
  Shield,
  Users,
  Scale
} from "lucide-react";


const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Regime',"aria-disabled": true ,
  },
  {
    index: "1", text: "CCPA","aria-disabled": true 
  },
];
---
<Layout title="Opsfolio - Compliance as a Service | SOC2, HIPAA, ISO Certification"
  description="Get SOC2, HIPAA, ISO certified fast with expert-guided compliance. 98% audit pass rate, 60% faster than DIY. Your Compliance System of Record."
  keywords="CCPA, compliance, SOC2, HIPAA, ISO 27001, cybersecurity, audit, certification"
  url="/regime/ccpa/"
  breadcrumbLinks={breadcrumbLinks}>

      {/* Hero Section */}
      <section class="py-24 sm:py-32 bg-gradient-to-b from-primary/5 to-background">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-3xl text-center">
            <Badge variant="outline" className="mb-4">
              <MapPin className="w-4 h-4 mr-2" />
              California Privacy Law
            </Badge>
            <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
              CCPA Compliance
            </h1>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Navigate California's Consumer Privacy Act with confidence. Implement consumer rights, 
              manage data processing disclosures, and ensure ongoing compliance monitoring.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
                <a href="/get-started">
                  <Button size="lg">Start CCPA Assessment
                  </Button>
                </a>

                <a href="/contact">
                  <Button variant="outline" size="lg">Legal Consultation
                  </Button>
                </a>
            </div>
          </div>
        </div>
      </section>

      {/* Consumer Rights */}
      <section class="py-24 sm:py-32">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center mb-16">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Consumer Privacy Rights
            </h2>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Core rights that California consumers have regarding their personal information
            </p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="border-border/50">
              <CardHeader>
                <Eye className="h-8 w-8 text-primary mb-2" />
                <CardTitle>Right to Know</CardTitle>
              </CardHeader>
              <CardContent>
                <p class="text-muted-foreground mb-4">
                  Consumers can request disclosure of personal information collected, 
                  sources, purposes, and third-party sharing details.
                </p>
                <div class="space-y-2">
                  <div class="flex items-center text-sm">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Data categories disclosed
                  </div>
                  <div class="flex items-center text-sm">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Collection sources
                  </div>
                  <div class="flex items-center text-sm">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Business purposes
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-border/50">
              <CardHeader>
                <Trash2 className="h-8 w-8 text-primary mb-2" />
                <CardTitle>Right to Delete</CardTitle>
              </CardHeader>
              <CardContent>
                <p class="text-muted-foreground mb-4">
                  Consumers can request deletion of personal information, 
                  with certain exceptions for legal compliance.
                </p>
                <div class="space-y-2">
                  <div class="flex items-center text-sm">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Deletion requests
                  </div>
                  <div class="flex items-center text-sm">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Exception handling
                  </div>
                  <div class="flex items-center text-sm">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Verification process
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-border/50">
              <CardHeader>
                <Download className="h-8 w-8 text-primary mb-2" />
                <CardTitle>Right to Portability</CardTitle>
              </CardHeader>
              <CardContent>
                <p class="text-muted-foreground mb-4">
                  Consumers can receive their personal information in a portable, 
                  readily usable format for transfer to another entity.
                </p>
                <div class="space-y-2">
                  <div class="flex items-center text-sm">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Data export formats
                  </div>
                  <div class="flex items-center text-sm">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Secure transmission
                  </div>
                  <div class="flex items-center text-sm">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Identity verification
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-border/50">
              <CardHeader>
                <UserX className="h-8 w-8 text-primary mb-2" />
                <CardTitle>Right to Opt-Out</CardTitle>
              </CardHeader>
              <CardContent>
                <p class="text-muted-foreground mb-4">
                  Consumers can opt-out of the sale of personal information 
                  and sharing for cross-context behavioral advertising.
                </p>
                <div class="space-y-2">
                  <div class="flex items-center text-sm">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Sale opt-out mechanisms
                  </div>
                  <div class="flex items-center text-sm">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Sharing restrictions
                  </div>
                  <div class="flex items-center text-sm">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Clear disclosure
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-border/50">
              <CardHeader>
                <Shield className="h-8 w-8 text-primary mb-2" />
                <CardTitle>Non-Discrimination</CardTitle>
              </CardHeader>
              <CardContent>
                <p class="text-muted-foreground mb-4">
                  Businesses cannot discriminate against consumers 
                  for exercising their CCPA rights.
                </p>
                <div class="space-y-2">
                  <div class="flex items-center text-sm">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Equal service quality
                  </div>
                  <div class="flex items-center text-sm">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    No price discrimination
                  </div>
                  <div class="flex items-center text-sm">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Service level parity
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-border/50">
              <CardHeader>
                <Scale className="h-8 w-8 text-primary mb-2" />
                <CardTitle>Right to Correct</CardTitle>
              </CardHeader>
              <CardContent>
                <p class="text-muted-foreground mb-4">
                  Consumers can request correction of inaccurate personal information 
                  maintained by businesses about them.
                </p>
                <div class="space-y-2">
                  <div class="flex items-center text-sm">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Correction requests
                  </div>
                  <div class="flex items-center text-sm">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Data accuracy verification
                  </div>
                  <div class="flex items-center text-sm">
                    <CheckCircle className="h-4 w-4 text-primary mr-2" />
                    Update notifications
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Business Obligations */}
      <section class="py-24 sm:py-32 bg-muted">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center mb-16">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Business Compliance Requirements
            </h2>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Key obligations for businesses processing California consumer data
            </p>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div class="space-y-8">
                <div class="flex items-start space-x-4">
                  <CheckCircle className="h-6 w-6 text-primary mt-1 flex-shrink-0" />
                  <div>
                    <h3 class="font-semibold text-foreground">Privacy Policy Disclosures</h3>
                    <p class="text-muted-foreground mt-1">
                      Comprehensive privacy notices detailing data collection, use, and sharing practices 
                      with consumer rights information.
                    </p>
                  </div>
                </div>

                <div class="flex items-start space-x-4">
                  <CheckCircle className="h-6 w-6 text-primary mt-1 flex-shrink-0" />
                  <div>
                    <h3 class="font-semibold text-foreground">Data Minimization</h3>
                    <p class="text-muted-foreground mt-1">
                      Collect and process only personal information necessary for disclosed business purposes, 
                      with retention limits.
                    </p>
                  </div>
                </div>

                <div class="flex items-start space-x-4">
                  <CheckCircle className="h-6 w-6 text-primary mt-1 flex-shrink-0" />
                  <div>
                    <h3 class="font-semibold text-foreground">Sensitive Data Protection</h3>
                    <p class="text-muted-foreground mt-1">
                      Enhanced protections for sensitive personal information with explicit consent requirements 
                      and limited processing.
                    </p>
                  </div>
                </div>

                <div class="flex items-start space-x-4">
                  <CheckCircle className="h-6 w-6 text-primary mt-1 flex-shrink-0" />
                  <div>
                    <h3 class="font-semibold text-foreground">Third-Party Contracts</h3>
                    <p class="text-muted-foreground mt-1">
                      Contractual obligations for service providers and contractors handling personal information 
                      on behalf of businesses.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div class="lg:text-center">
              <Card className="p-8 bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20">
                <div class="space-y-4">
                  <MapPin className="h-12 w-12 text-primary mx-auto" />
                  <h3 class="text-xl font-semibold text-foreground">California Consumer Protection</h3>
                  <p class="text-muted-foreground">
                    Our platform automates CCPA compliance with consumer request management, 
                    data mapping, and real-time privacy controls.
                  </p>
                  <div class="grid grid-cols-2 gap-4 mt-6">
                    <div class="text-center">
                      <div class="text-2xl font-bold text-primary">45 Days</div>
                      <div class="text-sm text-muted-foreground">Response Time</div>
                    </div>
                    <div class="text-center">
                      <div class="text-2xl font-bold text-primary">100%</div>
                      <div class="text-sm text-muted-foreground">Request Tracking</div>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Implementation Features */}
      <section class="py-24 sm:py-32">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center mb-16">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Automated CCPA Management
            </h2>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Streamline consumer rights management with intelligent automation
            </p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="text-center p-6">
              <Users className="h-12 w-12 text-primary mx-auto mb-4" />
              <h3 class="text-lg font-semibold text-foreground mb-2">Consumer Portal</h3>
              <p class="text-muted-foreground">
                Self-service portal for consumers to submit requests, verify identity, 
                and track processing status in real-time.
              </p>
            </Card>

            <Card className="text-center p-6">
              <Eye className="h-12 w-12 text-primary mx-auto mb-4" />
              <h3 class="text-lg font-semibold text-foreground mb-2">Data Discovery</h3>
              <p class="text-muted-foreground">
                Automated personal information discovery and mapping across systems 
                for complete visibility and compliance.
              </p>
            </Card>

            <Card className="text-center p-6">
              <Shield className="h-12 w-12 text-primary mx-auto mb-4" />
              <h3 class="text-lg font-semibold text-foreground mb-2">Risk Assessment</h3>
              <p class="text-muted-foreground">
                Continuous risk monitoring with privacy impact assessments 
                and compliance gap identification.
              </p>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section class="py-24 sm:py-32 bg-muted">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Ready for CCPA Compliance?
            </h2>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Protect California consumer privacy rights with our comprehensive automation platform. 
              Ensure compliance and build consumer trust.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
              
                <a href="/get-started">
                   <Button size="lg">Start Implementation
                   </Button>
                </a>
                <a href="/contact">
                   <Button variant="outline" size="lg">Privacy Consultation
                   </Button>
                </a>
            </div>
          </div>
        </div>
      </section>
</Layout>