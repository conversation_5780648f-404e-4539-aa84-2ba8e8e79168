## Long-Term Forms Strategy for Opsfolio & Web Apps

TODO: Move to the respective section Once GPM/WIKI folder structure finalized

Forms are one of the most challenging parts of any application—both in terms of
user experience and developer effort. To ensure we have a consistent,
maintainable, and scalable approach across www.Opsfolio.com, EOH, Opsfolio
Suite, and related projects, we are adopting the following long-term strategy:

1. **Start with [LHC Forms](https://lhncbc.github.io/lforms/) whenever
   possible.** LHC Forms provides a powerful, standards-based form framework and
   comes with a built-in form editor for non-technical users. This allows
   subject-matter experts to create, edit, and maintain forms without developer
   intervention.

   - **Reminder:** The main reason to use LHC Forms is that it has the
     presentation and data model combined so storing and querying in SQLite
     RSSDs or PostgreSQL through JSON is easier.

2. **Convert LHC Forms via AI if appearance or functionality needs
   improvement.** If an LHC Form works functionally but doesn’t meet our visual
   or advanced interaction requirements, we will use AI to convert it into our
   preferred shadcn/ui-based form implementation.
   - **Reminder:** If you start with LHC Forms and later decide to move to
     shadcn/ui, always use AI to assist with the conversion. This makes the
     process faster and more accurate—unless you start directly with the
     [shadcn-form.com editor](https://www.shadcn-form.com/), in which case the
     design can go straight to developers for implementation.
   - ChatGPT and other AI engines know about LHC Form and shadcn/ui forms or you
     can train them through prompts to know them.

3. **Fallback to shadcn/ui when LHC Forms isn’t a good fit.** If LHC Forms isn’t
   the right starting point—due to domain-specific requirements, limitations in
   data handling, or technical constraints—non-technical users can use the
   [shadcn-form.com online editor](https://www.shadcn-form.com/) to design the
   full form.
   - Developers will then implement the form using
     [shadcn/ui core components](https://basecoatui.com/components/form/) and
     follow best practices for enterprise-scale forms as outlined in Using
     [shadcn forms in big projects](https://javascript.plainenglish.io/using-shadcn-forms-in-big-projects-a75f86ad442b).

Decision Flow:

![Decision Flow](form-strategry-opsfolio-web-apps.png "Decision Flow")

**Links for Reference:**

- LHC Forms: https://lhncbc.github.io/lforms/
- shadcn-form.com online editor: https://www.shadcn-form.com/
- Enterprise form guidance for shadcn/ui:
  https://javascript.plainenglish.io/using-shadcn-forms-in-big-projects-a75f86ad442b
- Basecoat UI shadcn/ui forms without React:
  https://basecoatui.com/components/form/

** Why this strategy works:**

- Non-technical form authoring is always available
- We maximize reuse of open standards (LHC) where possible
- We retain flexibility to deliver enterprise-grade forms via shadcn/ui
- We leverage AI to accelerate conversion and development

By standardizing this approach, we reduce one-off custom form development, speed
up iteration cycles, and empower both technical and non-technical team members
to contribute effectively to form design.

```
```
