module.exports = {
  plugins: [require.resolve("prettier-plugin-astro")],
  overrides: [
    {
      files: "*.astro",
      options: {
        parser: "astro",
      },
    },
    {
      files: "*.mdx",
      options: {
        // Custom parser or additional options for MDX files can be specified here.
        // You can keep the options you want to customize for MDX files.
        bracketSameLine: true, // This ensures the closing tag '>' stays on the same line.
      },
    },
  ],
  astroAllowShorthand: false,
  semi: true,
  useTabs: false,
  trailingComma: "all",
  singleQuote: false,
  printWidth: 80,
  tabWidth: 2,
};
