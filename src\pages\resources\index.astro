---
import Layout from "../../layouts/Layout.astro";
import { <PERSON>, <PERSON>Content, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  BookOpen, 
  Download, 
  ExternalLink,
  Calendar,
  Shield,  
} from "lucide-react";
const Icon = BookOpen;
import { getCollection } from 'astro:content';


const allResourcePosts = (await getCollection('resources'));
//console.log(allResourcePosts);
const allUpcomingEventsPosts = (await getCollection('upcomingevents'));

// Grouping logic
const groupedResources = allResourcePosts.reduce((acc, post) => {
  const type = post.data.category || "Other";
  if (!acc[type]) acc[type] = [];
  acc[type].push(post);
  return acc;
}, {});

const sortedGroupedResources = Object.fromEntries(
  Object.entries(groupedResources)
    .sort(([a], [b]) => b.localeCompare(a)) // descending key name
);

const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Resources',"aria-disabled": true ,
  }
];
 
---

<Layout title="Compliance Knowledge Center - Free Resources"
        description="Free guides, templates, webinars, and case studies to help you navigate your compliance journey. SOC2 checklists, HIPAA kits, and expert insights."
        keywords="compliance resources, SOC2 checklist, HIPAA guide, compliance templates, compliance webinars, compliance case studies"
        url="/resources/"
        breadcrumbLinks={breadcrumbLinks}
  >
  <main>
        {/* Hero Section */}
        <div class="relative isolate px-6 pt-14 lg:px-8 overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5">
          <div class="mx-auto max-w-4xl py-24 sm:py-32">
            <div class="text-center">
              <Badge variant="secondary" className="mb-4">Resources</Badge>
              <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
                Compliance Knowledge Center
              </h1>
              <p class="mt-6 text-lg leading-8 text-muted-foreground max-w-3xl mx-auto">
                Free guides, templates, webinars, and case studies to help you navigate your compliance journey
              </p>
            </div>
          </div>
        </div>

       <div class="py-24 sm:py-32">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
              {Object.entries(sortedGroupedResources).map(([categoryTitle, resources]) => (
                <div class="flex items-center space-x-3 mb-8">
                  { <Icon className="h-6 w-6 text-primary" /> }
                          <h2 class="text-2xl font-bold text-foreground">{categoryTitle}</h2>
                </div>
            <section class="mb-16">
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {resources.map(resource => {
                  const isCaseStudy = resource.data.type === "Case Study";
                  const cardContent = (
                    <Card
                      className={`flex flex-col h-full hover:shadow-lg transition-all duration-300 ${
                        isCaseStudy ? "cursor-pointer" : ""
                      }`}
                    >
                      <CardHeader className="pb-3 flex-grow">
                        <div class="flex items-start justify-between">
                          <CardTitle className="font-semibold tracking-tight text-lg leading-tight">
                            {resource.data.title}
                          </CardTitle>
                        </div>
                      </CardHeader>
                      <CardContent className="flex flex-col flex-grow justify-between">
                        <div>
                          <p class="text-muted-foreground text-sm mb-4">
                            {resource.data.shortdescription}
                          </p>
                        </div>
                        <div class="flex items-center justify-between mt-auto">
                          <span class="text-xs text-muted-foreground">
                            {resource.data.type}
                          </span>
                          {isCaseStudy ? (
                            <Button size="sm" variant="ghost">
                              Read Story <ExternalLink className="ml-1 h-3 w-3" />
                            </Button>
                          ) : (
                            <a
                              href={resource.data.downloadurl}
                              download
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              <Button size="sm" variant="ghost">
                                Download <Download className="ml-1 h-3 w-3" />
                              </Button>
                            </a>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  );

                  return isCaseStudy ? (
                    <a
                      href={`/${resource.collection}/${resource.id}`}
                      class="block h-full"
                    >
                      {cardContent}
                    </a>
                  ) : (
                    <div class="h-full">{cardContent}</div>
                  );
                })}
              </div>
            </section>

          ))}
      </div>
      </div>

        {/* Upcoming Events */}
        <div class="py-24 sm:py-32 bg-muted">
          <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="text-center mb-12">
              <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                Upcoming Events
              </h2>
              <p class="mt-4 text-lg text-muted-foreground">
                Join our experts for live discussions and Q&A sessions
              </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto items-stretch">
              {allUpcomingEventsPosts.map((event, index) => (
                <a  href="/contact" class="flex">
                  <Card className="text-center flex flex-col w-full h-full hover:shadow-lg transition-shadow duration-300">
                    <CardContent className="p-8 flex flex-col flex-grow">
                      <Calendar className="h-10 w-10 text-primary mx-auto mb-4" />
                      <h3 class="text-xl font-semibold mb-2">{event.data.title}</h3>

                      <div class="text-muted-foreground mb-4">
                        <div class="font-medium">{event.data.time}</div>
                        <div class="text-sm">
                          {event.data.repeat?.day
                            ? event.data.repeat.day
                            : event.data.repeat?.frequency
                            ? event.data.repeat.frequency
                            : null}
                        </div>
                      </div>

                      <p class="text-sm text-muted-foreground mb-6 flex-grow">
                        {event.data.shortdescription}
                      </p>

                      <Button size="lg">Register Now</Button>
                    </CardContent>
                  </Card>
                </a>
              ))}
            </div>

          </div>
        </div>

        {/* CTA Section */}
        <div class="py-24 sm:py-32">
          <div class="mx-auto max-w-4xl px-6 lg:px-8 text-center">
            <Shield className="h-16 w-16 text-primary mx-auto mb-6" />
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl mb-6">
              Need Personalized Guidance?
            </h2>
            <p class="text-lg text-muted-foreground mb-8">
              Get expert advice tailored to your specific compliance needs
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/contact">
                  <Button size="lg">
                    Talk to an Expert                
                  </Button>                
                </a>  
                <a href="/get-started">
                    <Button size="lg" variant="outline">
                      Get Your Compliance Plan                
                    </Button>
                </a>              
            </div>
          </div>
        </div>
      </main>
</Layout>