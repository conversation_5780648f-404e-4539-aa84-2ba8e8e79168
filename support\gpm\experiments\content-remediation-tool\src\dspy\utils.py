import hashlib
import os
from datetime import datetime

REPORT_FOLDER = "audit_reports"

if not os.path.exists(REPORT_FOLDER):
    os.makedirs(REPORT_FOLDER)

def generate_report_filename(url: str, page_type: str) -> str:
    url_str = str(url)
    url_hash = hashlib.md5(url_str.encode()).hexdigest()[:8]
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"{page_type}-{url_hash}-{timestamp}.md"
    return os.path.join(REPORT_FOLDER, filename)

def save_audit_report(result, filename: str):
    with open(filename, "w", encoding="utf-8") as f:
        f.write("# Content Audit Report and Remediated Content\n\n")
        f.write("## Executive Summary\n")
        f.write(result.executive_summary + "\n\n")
        f.write("## Remediated Contents\n")
        f.write(result.remediated_content + "\n\n")
        f.write("## Strengths\n")
        f.write(result.strengths + "\n\n")
        f.write("## Weaknesses\n")
        f.write(result.weaknesses + "\n\n")
        f.write("## Recommendations\n")
        f.write(result.recommendations + "\n\n")
        f.write("## Observation vs Hypothesis\n")
        f.write(result.observation_vs_hypothesis + "\n")
