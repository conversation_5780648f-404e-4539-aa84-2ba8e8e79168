import * as rt from "./r4q-runtime.ts";
/**
 * @file physical-protection.auto.ts
 * @generated This file was auto-generated from the FHIR R4 Questionnaire "Physical Protection".
 * Do not edit this file manually; re-run the generator if the source Questionnaire changes.
 * Profiles: http://hl7.org/fhir/4.0/StructureDefinition/Questionnaire
 *
 * Normalizes LHC JSON and FHIR QuestionnaireResponse into the type-safe `PhysicalProtection` interface.
 */

// this is the module signature, used by importers to identify the module
// using r4q-runtime.ts `moduleSignature` function
export const physicalProtectionModuleSignature: rt.ModuleSignature = {
  title: "Physical Protection",
  filename: "physical-protection.auto.ts",
  titleCamel: "`physicalProtection`",
  titlePascal: "`PhysicalProtection`",
  titleKebab: "`physical-protection`",
  lhcFormResponseAdapterFnName: "physicalProtectionLhcFormResponseAdapter",
  fhirQuestionnaireResponseAdapterFnName: "physicalProtectionFhirQuestionnaireResponseAdapter",
  sourceTextConstName: "physicalProtectionSource",
}

// deno-lint-ignore no-explicit-any
type Any = any;
/**
 * Form Help (from display/help controls):
 * - Maintain an updated list of individuals authorized to access secure physical areas to ensure proper access control.
 * - Control and restrict access to sensitive physical locations to prevent unauthorized entry and protect assets.
 * - Establish formal procedures to grant, review, and revoke physical access permissions for personnel.
 * - Issue and manage secure access credentials to verify and control entry to restricted physical areas.
 * - Limit physical access to authorized areas during specific times to reduce security risks outside business hours.
 * - Limit physical access to organizational information systems, equipment, and the respective operating environments to authorized individuals
 * - Require authorized personnel to accompany visitors while they are in secure areas to ensure safety and security.
 * - Implement procedures to verify and record visitor identities before granting physical access to facilities.
 * - Track and record visitor movements within facilities to detect and prevent unauthorized activities.
 * - Define who is permitted to escort visitors and ensure they understand their responsibilities for security and supervision.
 * - Escort visitors and monitor visitor activity
 * - Implement methods to accurately record entry and exit activities in secure areas for audit and investigation purposes.
 * - Record key details such as date, time, personnel identity, and access points to ensure comprehensive tracking of physical access events.
 * - Maintain and regularly review access logs to detect anomalies and support security investigations.
 * - Keep an up-to-date inventory of all devices used to control physical access, such as card readers and locks, to ensure proper management and security.
 * - Implement procedures to configure, monitor, and maintain physical access devices to prevent unauthorized use or tampering.
 * - Apply security controls to protect physical access devices from damage, tampering, or unauthorized modification.
 * - Regularly perform maintenance and apply updates to physical access devices to ensure their reliability and security.
 */
/** Map of normalized property names to their source `linkId`. */
export const physicalProtectionLinkIds = {
  howManyIndividualsAreAuthorizedForPhysicalAccessToFciAreasFillInTheTotalForEach: "324592389560",
  fullTimeEmployees: "436045572485",
  contractors: "857782926958",
  partTimeEmployees: "944400994758",
  visitorsWithEscort: "571574306369",
  whatAreasRequireControlledPhysicalAccess: "702794466613",
  notesEvidence: "279391143609",
  whoAuthorizesPhysicalAccessToControlledAreas: "784352573703",
  notesEvidence2: "159961192967",
  whatTypesOfPhysicalAccessCredentialsAreIssued: "773851219827",
  notesEvidence3: "614664633852",
  areThereTimeBasedRestrictionsOnPhysicalAccess: "208747627440",
  notesEvidence4: "864878261078",
  implementationStatus: "660777712272",
  notesEvidence5: "158505675327",
  doesYourOrganizationRequireAllVisitorsToBeEscorted: "684131391577",
  notesEvidence6: "372121837424",
  howAreVisitorsIdentifiedAndDistinguishedFromEmployees: "400470675855",
  notesEvidence7: "739299710732",
  howIsVisitorActivityMonitoredWhileOnPremises: "829474009766",
  notesEvidence8: "398473749950",
  whoIsAuthorizedToEscortVisitors: "422650784362",
  notesEvidence9: "766282850057",
  implementationStatus2: "231843690847",
  notesEvidence10: "972038317766",
  howDoYouLogPhysicalAccessToYourFacilities: "734633292283",
  notesEvidence11: "325061856971",
  whatInformationIsCapturedInYourPhysicalAccessLogsInformationCapturedInLogs: "174905707594",
  notesEvidence12: "750143252884",
  howLongArePhysicalAccessLogsRetained: "245305278102",
  notesEvidence13: "571727427731",
  howFrequentlyAreAccessLogsReviewed: "741567851452",
  notesEvidence14: "910408738855",
  whoReviewsThePhysicalAccessLogs: "745836226925",
  notesEvidence15: "361446942388",
  implementationStatus3: "320438032270",
  notesEvidence16: "724862600014",
  whatTypesOfPhysicalAccessDevicesDoesYourOrganizationUse: "903629274308",
  notesEvidence17: "896964575016",
  howArePhysicalAccessDevicesControlledAndManaged: "173451266066",
  notesEvidence18: "164071724457",
  whatSecurityMeasuresProtectPhysicalAccessDevices: "911514884520",
  notesEvidence19: "653480882123",
  howFrequentlyAreElectronicAccessSystemsUpdated: "466342459779",
  notesEvidence20: "951698714660",
  implementationStatus4: "294892506040",
  notesEvidence21: "140603351800"
} as const;

/** Normalized view of "Physical Protection" answers. */
export interface PhysicalProtection {
  /**
   * How many individuals are authorized for physical access to FCI areas (fill in the total for each)?
   * linkId: 324592389560
   * FHIR type: display
   * Section: PE.L1-B.1.VIII - Physical Access Authorization > 1. Authorized Personnel Inventory
   * Required: no
   */
  howManyIndividualsAreAuthorizedForPhysicalAccessToFciAreasFillInTheTotalForEach?: string;

  /**
   * Full-time employees:
   * linkId: 436045572485
   * FHIR type: integer
   * Section: PE.L1-B.1.VIII - Physical Access Authorization > 1. Authorized Personnel Inventory
   * Required: no
   */
  fullTimeEmployees?: number;

  /**
   * Contractors:
   * linkId: 857782926958
   * FHIR type: integer
   * Section: PE.L1-B.1.VIII - Physical Access Authorization > 1. Authorized Personnel Inventory
   * Required: no
   */
  contractors?: number;

  /**
   * Part-time employees:
   * linkId: 944400994758
   * FHIR type: integer
   * Section: PE.L1-B.1.VIII - Physical Access Authorization > 1. Authorized Personnel Inventory
   * Required: no
   */
  partTimeEmployees?: number;

  /**
   * Visitors (with escort):
   * linkId: 571574306369
   * FHIR type: integer
   * Section: PE.L1-B.1.VIII - Physical Access Authorization > 1. Authorized Personnel Inventory
   * Required: no
   */
  visitorsWithEscort?: number;

  /**
   * What areas require controlled physical access?
   * linkId: 702794466613
   * FHIR type: choice
   * Section: PE.L1-B.1.VIII - Physical Access Authorization > 2. Physical Access Areas
   * Options: " Server rooms/data centers", "Workstation areas processing FCI", "Executive offices", "Mail/shipping areas", "Network equipment rooms", "Storage areas for FCI media", "Conference rooms used for FCI discussions"
   * Required: no
   */
  whatAreasRequireControlledPhysicalAccess?: (" Server rooms/data centers" | "Workstation areas processing FCI" | "Executive offices" | "Mail/shipping areas" | "Network equipment rooms" | "Storage areas for FCI media" | "Conference rooms used for FCI discussions")[];

  /**
   * Notes / Evidence
   * linkId: 279391143609
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: PE.L1-B.1.VIII - Physical Access Authorization > 2. Physical Access Areas
   * Required: no
   */
  notesEvidence?: string;

  /**
   * Who authorizes physical access to controlled areas?
   * linkId: 784352573703
   * FHIR type: choice
   * Section: PE.L1-B.1.VIII - Physical Access Authorization > 3. Authorization Process
   * Options: "Facility manager", "Department supervisor", "IT security team", "Security manager", "HR department"
   * Required: no
   */
  whoAuthorizesPhysicalAccessToControlledAreas?: ("Facility manager" | "Department supervisor" | "IT security team" | "Security manager" | "HR department")[];

  /**
   * Notes / Evidence
   * linkId: 159961192967
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: PE.L1-B.1.VIII - Physical Access Authorization > 3. Authorization Process
   * Required: no
   */
  notesEvidence2?: string;

  /**
   * What types of physical access credentials are issued?
   * linkId: 773851219827
   * FHIR type: choice
   * Section: PE.L1-B.1.VIII - Physical Access Authorization > 4. Access Credentials
   * Options: "Photo ID badges", "Physical keys", "Biometric scanners", "Proximity cards/key fobs", "PIN codes", "Visitor badges"
   * Required: no
   */
  whatTypesOfPhysicalAccessCredentialsAreIssued?: ("Photo ID badges" | "Physical keys" | "Biometric scanners" | "Proximity cards/key fobs" | "PIN codes" | "Visitor badges")[];

  /**
   * Notes / Evidence
   * linkId: 614664633852
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: PE.L1-B.1.VIII - Physical Access Authorization > 4. Access Credentials
   * Required: no
   */
  notesEvidence3?: string;

  /**
   * Are there time-based restrictions on physical access?
   * linkId: 208747627440
   * FHIR type: choice
   * Section: PE.L1-B.1.VIII - Physical Access Authorization > 5. Time-Based Access Restrictions
   * Options: "Yes, business hours only", "Yes, specific hours by role", "Yes, weekdays only", "No time restrictions"
   * Required: no
   */
  areThereTimeBasedRestrictionsOnPhysicalAccess?: ("Yes, business hours only" | "Yes, specific hours by role" | "Yes, weekdays only" | "No time restrictions")[];

  /**
   * Notes / Evidence
   * linkId: 864878261078
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: PE.L1-B.1.VIII - Physical Access Authorization > 5. Time-Based Access Restrictions
   * Required: no
   */
  notesEvidence4?: string;

  /**
   * Implementation Status
   * linkId: 660777712272
   * FHIR type: choice
   * Section: PE.L1-B.1.VIII - Physical Access Authorization
   * Options: "Fully Implemented", "Partially Implemented", "Not Implemented"
   * Required: no
   */
  implementationStatus?: "Fully Implemented" | "Partially Implemented" | "Not Implemented";

  /**
   * Notes / Evidence
   * linkId: 158505675327
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: PE.L1-B.1.VIII - Physical Access Authorization
   * Required: no
   */
  notesEvidence5?: string;

  /**
   * Does your organization require all visitors to be escorted?
   * linkId: 684131391577
   * FHIR type: choice
   * Section: PE.L1-B.1.IX – Manage Visitors & Physical Access > 1. Visitor Escort Policy
   * Options: "Yes, all visitors must be escorted at all times", "Yes, but only in restricted areas", "No formal escort requirement"
   * Required: no
   */
  doesYourOrganizationRequireAllVisitorsToBeEscorted?: "Yes, all visitors must be escorted at all times" | "Yes, but only in restricted areas" | "No formal escort requirement";

  /**
   * Notes / Evidence
   * linkId: 372121837424
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: PE.L1-B.1.IX – Manage Visitors & Physical Access > 1. Visitor Escort Policy
   * Required: no
   */
  notesEvidence6?: string;

  /**
   * How are visitors identified and distinguished from employees?
   * linkId: 400470675855
   * FHIR type: choice
   * Section: PE.L1-B.1.IX – Manage Visitors & Physical Access > 2. Visitor Identification
   * Options: "Distinctive visitor badges or lanyards", "Visitor sign-in log at reception", "Photo identification requirement", "Advance visitor approval and notification", "Temporary access cards or badges"
   * Required: no
   */
  howAreVisitorsIdentifiedAndDistinguishedFromEmployees?: ("Distinctive visitor badges or lanyards" | "Visitor sign-in log at reception" | "Photo identification requirement" | "Advance visitor approval and notification" | "Temporary access cards or badges")[];

  /**
   * Notes / Evidence
   * linkId: 739299710732
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: PE.L1-B.1.IX – Manage Visitors & Physical Access > 2. Visitor Identification
   * Required: no
   */
  notesEvidence7?: string;

  /**
   * How is visitor activity monitored while on premises?
   * linkId: 829474009766
   * FHIR type: choice
   * Section: PE.L1-B.1.IX – Manage Visitors & Physical Access > 3. Visitor Activity Monitoring
   * Options: "Continuous escort by authorized personnel", "Security camera surveillance", "Physical access restrictions to sensitive areas", "Time limits on visitor access", "Activity logs maintained by escorts"
   * Required: no
   */
  howIsVisitorActivityMonitoredWhileOnPremises?: ("Continuous escort by authorized personnel" | "Security camera surveillance" | "Physical access restrictions to sensitive areas" | "Time limits on visitor access" | "Activity logs maintained by escorts")[];

  /**
   * Notes / Evidence
   * linkId: 398473749950
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: PE.L1-B.1.IX – Manage Visitors & Physical Access > 3. Visitor Activity Monitoring
   * Required: no
   */
  notesEvidence8?: string;

  /**
   * Who is authorized to escort visitors?
   * linkId: 422650784362
   * FHIR type: choice
   * Section: PE.L1-B.1.IX – Manage Visitors & Physical Access > 4. Escort Authorization
   * Options: "Any employee", "Security staff only", "Trained escort personnel", "Designated personnel only", "Managers and supervisors only"
   * Required: no
   */
  whoIsAuthorizedToEscortVisitors?: "Any employee" | "Security staff only" | "Trained escort personnel" | "Designated personnel only" | "Managers and supervisors only";

  /**
   * Notes / Evidence 
   * linkId: 766282850057
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: PE.L1-B.1.IX – Manage Visitors & Physical Access > 4. Escort Authorization
   * Required: no
   */
  notesEvidence9?: string;

  /**
   * Implementation Status
   * linkId: 231843690847
   * FHIR type: choice
   * Section: PE.L1-B.1.IX – Manage Visitors & Physical Access
   * Options: "Fully Implemented", "Partially Implemented", "Not Implemented"
   * Required: no
   */
  implementationStatus2?: "Fully Implemented" | "Partially Implemented" | "Not Implemented";

  /**
   * Notes / Evidence
   * linkId: 972038317766
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: PE.L1-B.1.IX – Manage Visitors & Physical Access
   * Required: no
   */
  notesEvidence10?: string;

  /**
   *  How do you log physical access to your facilities?
   * linkId: 734633292283
   * FHIR type: choice
   * Section: PE.L1-B.1.IX –Physical Access Logs > 1. Access Logging Methods
   * Options: "Electronic badge readers with automatic logging", "Manual sign-in/sign-out sheets", "Security camera recordings", "Security guard logs and reports", "Physical key assignment and tracking logs"
   * Required: no
   */
  howDoYouLogPhysicalAccessToYourFacilities?: ("Electronic badge readers with automatic logging" | "Manual sign-in/sign-out sheets" | "Security camera recordings" | "Security guard logs and reports" | "Physical key assignment and tracking logs")[];

  /**
   * Notes / Evidence 
   * linkId: 325061856971
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: PE.L1-B.1.IX –Physical Access Logs > 1. Access Logging Methods
   * Required: no
   */
  notesEvidence11?: string;

  /**
   *  What information is captured in your physical access logs Information Captured in Logs?
   * linkId: 174905707594
   * FHIR type: choice
   * Section: PE.L1-B.1.IX –Physical Access Logs > 2. Information Captured in Logs
   * Options: "Person's identity (name, employee ID, visitor ID)", "Date and time of access", "Entry and exit times", "Specific location or area accessed", "Purpose of visit or access", "Escort information (if applicable)", "Failed access attempts"
   * Required: no
   */
  whatInformationIsCapturedInYourPhysicalAccessLogsInformationCapturedInLogs?: ("Person's identity (name, employee ID, visitor ID)" | "Date and time of access" | "Entry and exit times" | "Specific location or area accessed" | "Purpose of visit or access" | "Escort information (if applicable)" | "Failed access attempts")[];

  /**
   * Notes / Evidence 
   * linkId: 750143252884
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: PE.L1-B.1.IX –Physical Access Logs > 2. Information Captured in Logs
   * Required: no
   */
  notesEvidence12?: string;

  /**
   * How long are physical access logs retained?
   * linkId: 245305278102
   * FHIR type: choice
   * Section: PE.L1-B.1.IX –Physical Access Logs > 3. Log Retention and Review 
   * Options: "30 Days", "90 Days", "6 Months", "1 Year", "Longer than 1 year"
   * Required: no
   */
  howLongArePhysicalAccessLogsRetained?: "30 Days" | "90 Days" | "6 Months" | "1 Year" | "Longer than 1 year";

  /**
   * Notes / Evidence 
   * linkId: 571727427731
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: PE.L1-B.1.IX –Physical Access Logs > 3. Log Retention and Review 
   * Required: no
   */
  notesEvidence13?: string;

  /**
   * How frequently are access logs reviewed?
   * linkId: 741567851452
   * FHIR type: choice
   * Section: PE.L1-B.1.IX –Physical Access Logs > 3. Log Retention and Review 
   * Options: "Daily", "Weekly", "Monthly", "Quaterly", "Only when incidents occur", "Never formally reviewed"
   * Required: no
   */
  howFrequentlyAreAccessLogsReviewed?: "Daily" | "Weekly" | "Monthly" | "Quaterly" | "Only when incidents occur" | "Never formally reviewed";

  /**
   * Notes / Evidence 
   * linkId: 910408738855
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: PE.L1-B.1.IX –Physical Access Logs > 3. Log Retention and Review 
   * Required: no
   */
  notesEvidence14?: string;

  /**
   * Who reviews the physical access logs?
   * linkId: 745836226925
   * FHIR type: choice
   * Section: PE.L1-B.1.IX –Physical Access Logs > 3. Log Retention and Review 
   * Options: "Security manager", "IT security team", "Facility manager", "HR department"
   * Required: no
   */
  whoReviewsThePhysicalAccessLogs?: ("Security manager" | "IT security team" | "Facility manager" | "HR department")[];

  /**
   * Notes / Evidence 
   * linkId: 361446942388
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: PE.L1-B.1.IX –Physical Access Logs > 3. Log Retention and Review 
   * Required: no
   */
  notesEvidence15?: string;

  /**
   * Implementation Status
   * linkId: 320438032270
   * FHIR type: choice
   * Section: PE.L1-B.1.IX –Physical Access Logs
   * Options: "Fully Implemented", "Partially Implemented", "Not Implemented"
   * Required: no
   */
  implementationStatus3?: "Fully Implemented" | "Partially Implemented" | "Not Implemented";

  /**
   * Notes / Evidence
   * linkId: 724862600014
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: PE.L1-B.1.IX –Physical Access Logs
   * Required: no
   */
  notesEvidence16?: string;

  /**
   * What types of physical access devices does your organization use?
   * linkId: 903629274308
   * FHIR type: string
   * Section: PE.L1-B.1.IX –Manage Physical Access Devices > 1. Physical Access Device Inventory 
   * Required: no
   */
  whatTypesOfPhysicalAccessDevicesDoesYourOrganizationUse?: string[];

  /**
   * Notes / Evidence
   * linkId: 896964575016
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: PE.L1-B.1.IX –Manage Physical Access Devices > 1. Physical Access Device Inventory 
   * Required: no
   */
  notesEvidence17?: string;

  /**
   * How are physical access devices controlled and managed?
   * linkId: 173451266066
   * FHIR type: string
   * Section: PE.L1-B.1.IX –Manage Physical Access Devices > 2. Device Control and Management 
   * Required: no
   */
  howArePhysicalAccessDevicesControlledAndManaged?: string[];

  /**
   * Notes / Evidence
   * linkId: 164071724457
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: PE.L1-B.1.IX –Manage Physical Access Devices > 2. Device Control and Management 
   * Required: no
   */
  notesEvidence18?: string;

  /**
   * What security measures protect physical access devices?
   * linkId: 911514884520
   * FHIR type: string
   * Section: PE.L1-B.1.IX –Manage Physical Access Devices > 3. Device Security Measures 
   * Required: no
   */
  whatSecurityMeasuresProtectPhysicalAccessDevices?: string[];

  /**
   * Notes / Evidence
   * linkId: 653480882123
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: PE.L1-B.1.IX –Manage Physical Access Devices > 3. Device Security Measures 
   * Required: no
   */
  notesEvidence19?: string;

  /**
   * How frequently are electronic access systems updated?
   * linkId: 466342459779
   * FHIR type: string
   * Section: PE.L1-B.1.IX –Manage Physical Access Devices > 4. Device Maintenance and Updates
   * Required: no
   */
  howFrequentlyAreElectronicAccessSystemsUpdated?: string;

  /**
   * Notes / Evidence
   * linkId: 951698714660
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: PE.L1-B.1.IX –Manage Physical Access Devices > 4. Device Maintenance and Updates
   * Required: no
   */
  notesEvidence20?: string;

  /**
   * Implementation Status
   * linkId: 294892506040
   * FHIR type: string
   * Section: PE.L1-B.1.IX –Manage Physical Access Devices
   * Required: no
   */
  implementationStatus4?: string;

  /**
   * Notes / Evidence 
   * linkId: 140603351800
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: PE.L1-B.1.IX –Manage Physical Access Devices
   * Required: no
   */
  notesEvidence21?: string;
}

/** Convert an LHC JSON response into a normalized PhysicalProtection object. */
export function physicalProtectionLhcFormResponseAdapter(input: Any): PhysicalProtection {
  return {
    howManyIndividualsAreAuthorizedForPhysicalAccessToFciAreasFillInTheTotalForEach: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "324592389560")),
    fullTimeEmployees: rt.coerceOptionalNumber(rt.findLhcValueByLinkId(input, "436045572485")),
    contractors: rt.coerceOptionalNumber(rt.findLhcValueByLinkId(input, "857782926958")),
    partTimeEmployees: rt.coerceOptionalNumber(rt.findLhcValueByLinkId(input, "944400994758")),
    visitorsWithEscort: rt.coerceOptionalNumber(rt.findLhcValueByLinkId(input, "571574306369")),
    whatAreasRequireControlledPhysicalAccess: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "702794466613")) as PhysicalProtection["whatAreasRequireControlledPhysicalAccess"],
    notesEvidence: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "279391143609")),
    whoAuthorizesPhysicalAccessToControlledAreas: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "784352573703")) as PhysicalProtection["whoAuthorizesPhysicalAccessToControlledAreas"],
    notesEvidence2: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "159961192967")),
    whatTypesOfPhysicalAccessCredentialsAreIssued: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "773851219827")) as PhysicalProtection["whatTypesOfPhysicalAccessCredentialsAreIssued"],
    notesEvidence3: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "614664633852")),
    areThereTimeBasedRestrictionsOnPhysicalAccess: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "208747627440")) as PhysicalProtection["areThereTimeBasedRestrictionsOnPhysicalAccess"],
    notesEvidence4: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "864878261078")),
    implementationStatus: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "660777712272")) as PhysicalProtection["implementationStatus"],
    notesEvidence5: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "158505675327")),
    doesYourOrganizationRequireAllVisitorsToBeEscorted: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "684131391577")) as PhysicalProtection["doesYourOrganizationRequireAllVisitorsToBeEscorted"],
    notesEvidence6: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "372121837424")),
    howAreVisitorsIdentifiedAndDistinguishedFromEmployees: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "400470675855")) as PhysicalProtection["howAreVisitorsIdentifiedAndDistinguishedFromEmployees"],
    notesEvidence7: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "739299710732")),
    howIsVisitorActivityMonitoredWhileOnPremises: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "829474009766")) as PhysicalProtection["howIsVisitorActivityMonitoredWhileOnPremises"],
    notesEvidence8: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "398473749950")),
    whoIsAuthorizedToEscortVisitors: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "422650784362")) as PhysicalProtection["whoIsAuthorizedToEscortVisitors"],
    notesEvidence9: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "766282850057")),
    implementationStatus2: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "231843690847")) as PhysicalProtection["implementationStatus2"],
    notesEvidence10: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "972038317766")),
    howDoYouLogPhysicalAccessToYourFacilities: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "734633292283")) as PhysicalProtection["howDoYouLogPhysicalAccessToYourFacilities"],
    notesEvidence11: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "325061856971")),
    whatInformationIsCapturedInYourPhysicalAccessLogsInformationCapturedInLogs: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "174905707594")) as PhysicalProtection["whatInformationIsCapturedInYourPhysicalAccessLogsInformationCapturedInLogs"],
    notesEvidence12: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "750143252884")),
    howLongArePhysicalAccessLogsRetained: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "245305278102")) as PhysicalProtection["howLongArePhysicalAccessLogsRetained"],
    notesEvidence13: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "571727427731")),
    howFrequentlyAreAccessLogsReviewed: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "741567851452")) as PhysicalProtection["howFrequentlyAreAccessLogsReviewed"],
    notesEvidence14: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "910408738855")),
    whoReviewsThePhysicalAccessLogs: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "745836226925")) as PhysicalProtection["whoReviewsThePhysicalAccessLogs"],
    notesEvidence15: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "361446942388")),
    implementationStatus3: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "320438032270")) as PhysicalProtection["implementationStatus3"],
    notesEvidence16: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "724862600014")),
    whatTypesOfPhysicalAccessDevicesDoesYourOrganizationUse: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "903629274308")) as PhysicalProtection["whatTypesOfPhysicalAccessDevicesDoesYourOrganizationUse"],
    notesEvidence17: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "896964575016")),
    howArePhysicalAccessDevicesControlledAndManaged: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "173451266066")) as PhysicalProtection["howArePhysicalAccessDevicesControlledAndManaged"],
    notesEvidence18: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "164071724457")),
    whatSecurityMeasuresProtectPhysicalAccessDevices: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "911514884520")) as PhysicalProtection["whatSecurityMeasuresProtectPhysicalAccessDevices"],
    notesEvidence19: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "653480882123")),
    howFrequentlyAreElectronicAccessSystemsUpdated: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "466342459779")),
    notesEvidence20: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "951698714660")),
    implementationStatus4: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "294892506040")),
    notesEvidence21: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "140603351800")),
  };
}

/** Convert a FHIR QuestionnaireResponse into a normalized PhysicalProtection object. */
export function physicalProtectionFhirQuestionnaireResponseAdapter(qr: Any): PhysicalProtection {
  return {
    howManyIndividualsAreAuthorizedForPhysicalAccessToFciAreasFillInTheTotalForEach: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "324592389560")),
    fullTimeEmployees: rt.coerceOptionalNumber(rt.findQrAnswerByLinkId(qr, "436045572485")),
    contractors: rt.coerceOptionalNumber(rt.findQrAnswerByLinkId(qr, "857782926958")),
    partTimeEmployees: rt.coerceOptionalNumber(rt.findQrAnswerByLinkId(qr, "944400994758")),
    visitorsWithEscort: rt.coerceOptionalNumber(rt.findQrAnswerByLinkId(qr, "571574306369")),
    whatAreasRequireControlledPhysicalAccess: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "702794466613")) as PhysicalProtection["whatAreasRequireControlledPhysicalAccess"],
    notesEvidence: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "279391143609")),
    whoAuthorizesPhysicalAccessToControlledAreas: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "784352573703")) as PhysicalProtection["whoAuthorizesPhysicalAccessToControlledAreas"],
    notesEvidence2: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "159961192967")),
    whatTypesOfPhysicalAccessCredentialsAreIssued: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "773851219827")) as PhysicalProtection["whatTypesOfPhysicalAccessCredentialsAreIssued"],
    notesEvidence3: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "614664633852")),
    areThereTimeBasedRestrictionsOnPhysicalAccess: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "208747627440")) as PhysicalProtection["areThereTimeBasedRestrictionsOnPhysicalAccess"],
    notesEvidence4: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "864878261078")),
    implementationStatus: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "660777712272")) as PhysicalProtection["implementationStatus"],
    notesEvidence5: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "158505675327")),
    doesYourOrganizationRequireAllVisitorsToBeEscorted: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "684131391577")) as PhysicalProtection["doesYourOrganizationRequireAllVisitorsToBeEscorted"],
    notesEvidence6: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "372121837424")),
    howAreVisitorsIdentifiedAndDistinguishedFromEmployees: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "400470675855")) as PhysicalProtection["howAreVisitorsIdentifiedAndDistinguishedFromEmployees"],
    notesEvidence7: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "739299710732")),
    howIsVisitorActivityMonitoredWhileOnPremises: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "829474009766")) as PhysicalProtection["howIsVisitorActivityMonitoredWhileOnPremises"],
    notesEvidence8: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "398473749950")),
    whoIsAuthorizedToEscortVisitors: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "422650784362")) as PhysicalProtection["whoIsAuthorizedToEscortVisitors"],
    notesEvidence9: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "766282850057")),
    implementationStatus2: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "231843690847")) as PhysicalProtection["implementationStatus2"],
    notesEvidence10: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "972038317766")),
    howDoYouLogPhysicalAccessToYourFacilities: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "734633292283")) as PhysicalProtection["howDoYouLogPhysicalAccessToYourFacilities"],
    notesEvidence11: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "325061856971")),
    whatInformationIsCapturedInYourPhysicalAccessLogsInformationCapturedInLogs: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "174905707594")) as PhysicalProtection["whatInformationIsCapturedInYourPhysicalAccessLogsInformationCapturedInLogs"],
    notesEvidence12: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "750143252884")),
    howLongArePhysicalAccessLogsRetained: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "245305278102")) as PhysicalProtection["howLongArePhysicalAccessLogsRetained"],
    notesEvidence13: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "571727427731")),
    howFrequentlyAreAccessLogsReviewed: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "741567851452")) as PhysicalProtection["howFrequentlyAreAccessLogsReviewed"],
    notesEvidence14: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "910408738855")),
    whoReviewsThePhysicalAccessLogs: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "745836226925")) as PhysicalProtection["whoReviewsThePhysicalAccessLogs"],
    notesEvidence15: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "361446942388")),
    implementationStatus3: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "320438032270")) as PhysicalProtection["implementationStatus3"],
    notesEvidence16: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "724862600014")),
    whatTypesOfPhysicalAccessDevicesDoesYourOrganizationUse: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "903629274308")) as PhysicalProtection["whatTypesOfPhysicalAccessDevicesDoesYourOrganizationUse"],
    notesEvidence17: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "896964575016")),
    howArePhysicalAccessDevicesControlledAndManaged: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "173451266066")) as PhysicalProtection["howArePhysicalAccessDevicesControlledAndManaged"],
    notesEvidence18: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "164071724457")),
    whatSecurityMeasuresProtectPhysicalAccessDevices: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "911514884520")) as PhysicalProtection["whatSecurityMeasuresProtectPhysicalAccessDevices"],
    notesEvidence19: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "653480882123")),
    howFrequentlyAreElectronicAccessSystemsUpdated: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "466342459779")),
    notesEvidence20: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "951698714660")),
    implementationStatus4: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "294892506040")),
    notesEvidence21: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "140603351800")),
  };
}

/**
 * NOTE TO DEVELOPERS:
 * -------------------
 * This Interpreter class is provided only as an EXAMPLE scaffold to demonstrate
 * how to consume the normalized type-safe interface generated for this
 * Questionnaire. It shows minimal factories (`fromLhc`, `fromQuestionnaireResponse`)
 * and convenience methods (`validateRequiredFields`, `assessReadiness`) but it is
 * NOT intended for production use.
 *
 * In real applications:
 * - Treat this class as SAMPLE CODE only.
 * - Replace or extend it with proper business logic, rules engines, or validation
 *   frameworks appropriate to your domain.
 * - Do not rely on the simplistic readiness scoring or validation in production
 *   scenarios; they are illustrative, not authoritative.
 *
 * Best practice: use the generated TypeScript interface (`PhysicalProtection`) as your
 * contract for normalized data, then integrate with your own rules processors,
 * compliance engines, or plain TypeScript/JavaScript functions as needed.
 */
export class PhysicalProtectionInterpreter {
  constructor(readonly value: PhysicalProtection) { }

  /** Factory: build from LHC JSON. */
  static fromLhcFormResponse(input: Any): PhysicalProtectionInterpreter {
    return new PhysicalProtectionInterpreter(physicalProtectionLhcFormResponseAdapter(input));
  }

  /** Factory: build from FHIR QuestionnaireResponse. */
  static fromQuestionnaireResponse(qr: Any): PhysicalProtectionInterpreter {
    return new PhysicalProtectionInterpreter(physicalProtectionFhirQuestionnaireResponseAdapter(qr));
  }

  /** Check required fields and report any missing or blank. */
  validateRequiredFields(): { ok: boolean; missing: Array<keyof PhysicalProtection> } {
    const missing: Array<keyof PhysicalProtection> = [];
    const req: Array<keyof PhysicalProtection> = [];
    for (const k of req) {
      const v = (this.value as Any)[k];
      if (rt.isBlank(v)) missing.push(k);
    }
    return { ok: missing.length === 0, missing };
  }

  /**
   * Assess readiness with a simple completeness score. This is meant to be
   * used to help understand how complete the types are and serves as an
   * example of how to use the generated code.
   * - requiredCovered: percentage of required fields that are non-blank
   * - overallFilled: count of non-blank fields among all known properties
   */
  assessReadiness(): {
    formTitle: string;
    requiredCovered: number; // 0..1
    totalRequired: number;
    totalFilled: number;
    totalFields: number;
    missingRequired: Array<keyof PhysicalProtection>;
  } {
    const req: Array<keyof PhysicalProtection> = [];
    const all: Array<keyof PhysicalProtection> = ["howManyIndividualsAreAuthorizedForPhysicalAccessToFciAreasFillInTheTotalForEach", "fullTimeEmployees", "contractors", "partTimeEmployees", "visitorsWithEscort", "whatAreasRequireControlledPhysicalAccess", "notesEvidence", "whoAuthorizesPhysicalAccessToControlledAreas", "notesEvidence2", "whatTypesOfPhysicalAccessCredentialsAreIssued", "notesEvidence3", "areThereTimeBasedRestrictionsOnPhysicalAccess", "notesEvidence4", "implementationStatus", "notesEvidence5", "doesYourOrganizationRequireAllVisitorsToBeEscorted", "notesEvidence6", "howAreVisitorsIdentifiedAndDistinguishedFromEmployees", "notesEvidence7", "howIsVisitorActivityMonitoredWhileOnPremises", "notesEvidence8", "whoIsAuthorizedToEscortVisitors", "notesEvidence9", "implementationStatus2", "notesEvidence10", "howDoYouLogPhysicalAccessToYourFacilities", "notesEvidence11", "whatInformationIsCapturedInYourPhysicalAccessLogsInformationCapturedInLogs", "notesEvidence12", "howLongArePhysicalAccessLogsRetained", "notesEvidence13", "howFrequentlyAreAccessLogsReviewed", "notesEvidence14", "whoReviewsThePhysicalAccessLogs", "notesEvidence15", "implementationStatus3", "notesEvidence16", "whatTypesOfPhysicalAccessDevicesDoesYourOrganizationUse", "notesEvidence17", "howArePhysicalAccessDevicesControlledAndManaged", "notesEvidence18", "whatSecurityMeasuresProtectPhysicalAccessDevices", "notesEvidence19", "howFrequentlyAreElectronicAccessSystemsUpdated", "notesEvidence20", "implementationStatus4", "notesEvidence21"];

    let reqFilled = 0;
    const missingReq: Array<keyof PhysicalProtection> = [];
    for (const k of req) {
      const v = (this.value as Any)[k];
      if (!rt.isBlank(v)) reqFilled++;
      else missingReq.push(k);
    }

    let totalFilled = 0;
    for (const k of all) {
      if (!rt.isBlank((this.value as Any)[k])) totalFilled++;
    }

    return {
      formTitle: "Physical Protection",
      requiredCovered: req.length ? reqFilled / req.length : 1,
      totalRequired: req.length,
      totalFilled,
      totalFields: all.length,
      missingRequired: missingReq,
    };
  }
}

/** The original source */
export const physicalProtectionSource = `{
  "resourceType": "Questionnaire",
  "meta": {
    "profile": [
      "http://hl7.org/fhir/4.0/StructureDefinition/Questionnaire"
    ],
    "tag": [
      {
        "display": "Physical Security"
      }
    ]
  },
  "extension": [
    {
      "url": "http://hl7.org/fhir/StructureDefinition/variable",
      "valueExpression": {
        "name": "displayOrder",
        "language": "text/fhirpath",
        "expression": "5",
        "extension": [
          {
            "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
            "valueString": "simple"
          },
          {
            "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
            "valueString": "5"
          }
        ]
      }
    }
  ],
  "version": "1.0",
  "name": "physical_protection",
  "title": "Physical Protection",
  "status": "draft",
  "publisher": "Netspective",
  "description": "Physical Protection (Limit physical access to information systems and facilities)",
  "purpose": "This assessment examines how the organization controls physical access to information systems, equipment, and the environments where they are housed. It includes ensuring that facilities are secured, visitor access is escorted and logged, and that physical access devices (such as badges or keys) are properly managed. The objective is to reduce the risk of unauthorized physical intrusion into protected spaces.",
  "approvalDate": "2025-08-26",
  "lastReviewDate": "2025-08-26",
  "item": [
    {
      "item": [
        {
          "item": [
            {
              "type": "display",
              "linkId": "324592389560",
              "text": "How many individuals are authorized for physical access to FCI areas (fill in the total for each)?"
            },
            {
              "type": "integer",
              "linkId": "436045572485",
              "text": "Full-time employees:"
            },
            {
              "type": "integer",
              "linkId": "857782926958",
              "text": "Contractors:"
            },
            {
              "type": "integer",
              "linkId": "944400994758",
              "text": "Part-time employees:"
            },
            {
              "type": "integer",
              "linkId": "571574306369",
              "text": "Visitors (with escort):"
            },
            {
              "linkId": "296125947947_helpText",
              "type": "display",
              "text": "Maintain an updated list of individuals authorized to access secure physical areas to ensure proper access control.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "296125947947",
          "text": "1. Authorized Personnel Inventory",
          "required": false,
          "repeats": false
        },
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "100",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "100"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "702794466613",
              "text": "What areas require controlled physical access?",
              "repeats": true,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 10
                    }
                  ],
                  "valueCoding": {
                    "display": " Server rooms/data centers"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 10
                    }
                  ],
                  "valueCoding": {
                    "display": "Workstation areas processing FCI"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 10
                    }
                  ],
                  "valueCoding": {
                    "display": "Executive offices"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 10
                    }
                  ],
                  "valueCoding": {
                    "display": "Mail/shipping areas"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 10
                    }
                  ],
                  "valueCoding": {
                    "display": "Network equipment rooms"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 10
                    }
                  ],
                  "valueCoding": {
                    "display": "Storage areas for FCI media"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 10
                    }
                  ],
                  "valueCoding": {
                    "display": "Conference rooms used for FCI discussions"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "279391143609",
              "text": "Notes / Evidence"
            },
            {
              "linkId": "209389086115_helpText",
              "type": "display",
              "text": "Control and restrict access to sensitive physical locations to prevent unauthorized entry and protect assets.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "209389086115",
          "text": "2. Physical Access Areas"
        },
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "100",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "100"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "784352573703",
              "text": "Who authorizes physical access to controlled areas?",
              "repeats": true,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Facility manager"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Department supervisor"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "IT security team"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Security manager"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "HR department"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "159961192967",
              "text": "Notes / Evidence"
            },
            {
              "linkId": "869992586185_helpText",
              "type": "display",
              "text": "Establish formal procedures to grant, review, and revoke physical access permissions for personnel.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "869992586185",
          "text": "3. Authorization Process"
        },
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "120",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "120"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "773851219827",
              "text": "What types of physical access credentials are issued?",
              "repeats": true,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Photo ID badges"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Physical keys"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Biometric scanners"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Proximity cards/key fobs"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "PIN codes"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Visitor badges"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "614664633852",
              "text": "Notes / Evidence"
            },
            {
              "linkId": "263666472314_helpText",
              "type": "display",
              "text": "Issue and manage secure access credentials to verify and control entry to restricted physical areas.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "263666472314",
          "text": "4. Access Credentials"
        },
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "80",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "80"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "208747627440",
              "text": "Are there time-based restrictions on physical access?",
              "repeats": true,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Yes, business hours only"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Yes, specific hours by role"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Yes, weekdays only"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "No time restrictions"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "864878261078",
              "text": "Notes / Evidence"
            },
            {
              "linkId": "409121643490_helpText",
              "type": "display",
              "text": "Limit physical access to authorized areas during specific times to reduce security risks outside business hours.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "409121643490",
          "text": "5. Time-Based Access Restrictions",
          "repeats": false
        },
        {
          "type": "choice",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/variable",
              "valueExpression": {
                "name": "weight",
                "language": "text/fhirpath",
                "expression": "1",
                "extension": [
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                    "valueString": "simple"
                  },
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                    "valueString": "1"
                  }
                ]
              }
            },
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "radio-button",
                    "display": "Radio Button"
                  }
                ]
              }
            }
          ],
          "linkId": "660777712272",
          "text": "Implementation Status",
          "repeats": false,
          "answerOption": [
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 100
                }
              ],
              "valueCoding": {
                "display": "Fully Implemented"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 50
                }
              ],
              "valueCoding": {
                "display": "Partially Implemented"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 0
                }
              ],
              "valueCoding": {
                "display": "Not Implemented"
              }
            }
          ]
        },
        {
          "type": "string",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
              "valueString": "Type your comments here..."
            }
          ],
          "linkId": "158505675327",
          "text": "Notes / Evidence"
        },
        {
          "linkId": "624769621183_helpText",
          "type": "display",
          "text": "Limit physical access to organizational information systems, equipment, and the respective operating environments to authorized individuals",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "help",
                    "display": "Help-Button"
                  }
                ],
                "text": "Help-Button"
              }
            }
          ]
        }
      ],
      "type": "group",
      "linkId": "624769621183",
      "text": "PE.L1-B.1.VIII - Physical Access Authorization"
    },
    {
      "item": [
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "radio-button",
                        "display": "Radio Button"
                      }
                    ]
                  }
                }
              ],
              "linkId": "684131391577",
              "text": "Does your organization require all visitors to be escorted?",
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 100
                    }
                  ],
                  "valueCoding": {
                    "display": "Yes, all visitors must be escorted at all times"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 50
                    }
                  ],
                  "valueCoding": {
                    "display": "Yes, but only in restricted areas"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 0
                    }
                  ],
                  "valueCoding": {
                    "display": "No formal escort requirement"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "372121837424",
              "text": "Notes / Evidence"
            },
            {
              "linkId": "984680126159_helpText",
              "type": "display",
              "text": "Require authorized personnel to accompany visitors while they are in secure areas to ensure safety and security.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "984680126159",
          "text": "1. Visitor Escort Policy",
          "repeats": false
        },
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "400470675855",
              "text": "How are visitors identified and distinguished from employees?",
              "required": false,
              "repeats": true,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Distinctive visitor badges or lanyards"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Visitor sign-in log at reception"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Photo identification requirement"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Advance visitor approval and notification"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Temporary access cards or badges"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "739299710732",
              "text": "Notes / Evidence"
            },
            {
              "linkId": "896661213301_helpText",
              "type": "display",
              "text": "Implement procedures to verify and record visitor identities before granting physical access to facilities.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "896661213301",
          "text": "2. Visitor Identification",
          "repeats": false
        },
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "829474009766",
              "text": "How is visitor activity monitored while on premises?",
              "repeats": true,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Continuous escort by authorized personnel"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Security camera surveillance"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Physical access restrictions to sensitive areas"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Time limits on visitor access"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Activity logs maintained by escorts"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "398473749950",
              "text": "Notes / Evidence"
            },
            {
              "linkId": "588293653185_helpText",
              "type": "display",
              "text": "Track and record visitor movements within facilities to detect and prevent unauthorized activities.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "588293653185",
          "text": "3. Visitor Activity Monitoring",
          "repeats": false
        },
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "100",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "100"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "radio-button",
                        "display": "Radio Button"
                      }
                    ]
                  }
                }
              ],
              "linkId": "422650784362",
              "text": "Who is authorized to escort visitors?",
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Any employee"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Security staff only"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Trained escort personnel"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Designated personnel only"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Managers and supervisors only"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "766282850057",
              "text": "Notes / Evidence "
            },
            {
              "linkId": "286167746672_helpText",
              "type": "display",
              "text": "Define who is permitted to escort visitors and ensure they understand their responsibilities for security and supervision.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "286167746672",
          "text": "4. Escort Authorization",
          "repeats": false
        },
        {
          "type": "choice",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/variable",
              "valueExpression": {
                "name": "weight",
                "language": "text/fhirpath",
                "expression": "1",
                "extension": [
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                    "valueString": "simple"
                  },
                  {
                    "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                    "valueString": "1"
                  }
                ]
              }
            }
          ],
          "linkId": "231843690847",
          "text": "Implementation Status",
          "repeats": false,
          "answerOption": [
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 100
                }
              ],
              "valueCoding": {
                "display": "Fully Implemented"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 50
                }
              ],
              "valueCoding": {
                "display": "Partially Implemented"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 0
                }
              ],
              "valueCoding": {
                "display": "Not Implemented"
              }
            }
          ]
        },
        {
          "type": "string",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
              "valueString": "Type your comments here..."
            }
          ],
          "linkId": "972038317766",
          "text": "Notes / Evidence"
        },
        {
          "linkId": "197390251867_helpText",
          "type": "display",
          "text": "Escort visitors and monitor visitor activity",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "help",
                    "display": "Help-Button"
                  }
                ],
                "text": "Help-Button"
              }
            }
          ]
        }
      ],
      "type": "group",
      "linkId": "197390251867",
      "text": "PE.L1-B.1.IX – Manage Visitors & Physical Access"
    },
    {
      "item": [
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "100",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "100"
                      }
                    ]
                  }
                }
              ],
              "linkId": "734633292283",
              "text": " How do you log physical access to your facilities?",
              "repeats": true,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Electronic badge readers with automatic logging"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Manual sign-in/sign-out sheets"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Security camera recordings"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Security guard logs and reports"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Physical key assignment and tracking logs"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "325061856971",
              "text": "Notes / Evidence "
            },
            {
              "linkId": "492440543443_helpText",
              "type": "display",
              "text": "Implement methods to accurately record entry and exit activities in secure areas for audit and investigation purposes.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "492440543443",
          "text": "1. Access Logging Methods"
        },
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "70",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "70"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "174905707594",
              "text": " What information is captured in your physical access logs Information Captured in Logs?",
              "repeats": true,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 10
                    }
                  ],
                  "valueCoding": {
                    "display": "Person's identity (name, employee ID, visitor ID)"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 10
                    }
                  ],
                  "valueCoding": {
                    "display": "Date and time of access"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 10
                    }
                  ],
                  "valueCoding": {
                    "display": "Entry and exit times"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 10
                    }
                  ],
                  "valueCoding": {
                    "display": "Specific location or area accessed"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 10
                    }
                  ],
                  "valueCoding": {
                    "display": "Purpose of visit or access"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 10
                    }
                  ],
                  "valueCoding": {
                    "display": "Escort information (if applicable)"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 10
                    }
                  ],
                  "valueCoding": {
                    "display": "Failed access attempts"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "750143252884",
              "text": "Notes / Evidence "
            },
            {
              "linkId": "349759491673_helpText",
              "type": "display",
              "text": "Record key details such as date, time, personnel identity, and access points to ensure comprehensive tracking of physical access events.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "349759491673",
          "text": "2. Information Captured in Logs"
        },
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "radio-button",
                        "display": "Radio Button"
                      }
                    ]
                  }
                }
              ],
              "linkId": "245305278102",
              "text": "How long are physical access logs retained?",
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 100
                    }
                  ],
                  "valueCoding": {
                    "display": "30 Days"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 80
                    }
                  ],
                  "valueCoding": {
                    "display": "90 Days"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 60
                    }
                  ],
                  "valueCoding": {
                    "display": "6 Months"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 40
                    }
                  ],
                  "valueCoding": {
                    "display": "1 Year"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Longer than 1 year"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "571727427731",
              "text": "Notes / Evidence "
            },
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "radio-button",
                        "display": "Radio Button"
                      }
                    ]
                  }
                }
              ],
              "linkId": "741567851452",
              "text": "How frequently are access logs reviewed?",
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 100
                    }
                  ],
                  "valueCoding": {
                    "display": "Daily"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 80
                    }
                  ],
                  "valueCoding": {
                    "display": "Weekly"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 60
                    }
                  ],
                  "valueCoding": {
                    "display": "Monthly"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 40
                    }
                  ],
                  "valueCoding": {
                    "display": "Quaterly"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Only when incidents occur"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 0
                    }
                  ],
                  "valueCoding": {
                    "display": "Never formally reviewed"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "910408738855",
              "text": "Notes / Evidence "
            },
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "80",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "80"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "745836226925",
              "text": "Who reviews the physical access logs?",
              "repeats": true,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Security manager"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "IT security team"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Facility manager"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "HR department"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "361446942388",
              "text": "Notes / Evidence "
            },
            {
              "linkId": "831615420801_helpText",
              "type": "display",
              "text": "Maintain and regularly review access logs to detect anomalies and support security investigations.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "831615420801",
          "text": "3. Log Retention and Review "
        },
        {
          "type": "choice",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "radio-button",
                    "display": "Radio Button"
                  }
                ]
              }
            }
          ],
          "linkId": "320438032270",
          "text": "Implementation Status",
          "repeats": false,
          "answerOption": [
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 100
                }
              ],
              "valueCoding": {
                "display": "Fully Implemented"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 50
                }
              ],
              "valueCoding": {
                "display": "Partially Implemented"
              }
            },
            {
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                  "valueDecimal": 0
                }
              ],
              "valueCoding": {
                "display": "Not Implemented"
              }
            }
          ]
        },
        {
          "type": "string",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
              "valueString": "Type your comments here..."
            }
          ],
          "linkId": "724862600014",
          "text": "Notes / Evidence"
        }
      ],
      "type": "group",
      "linkId": "430398414481",
      "text": "PE.L1-B.1.IX –Physical Access Logs"
    },
    {
      "item": [
        {
          "item": [
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "903629274308",
              "text": "What types of physical access devices does your organization use?",
              "repeats": true,
              "answerOption": [
                {
                  "valueString": "Physical keys"
                },
                {
                  "valueString": "Proximity cards or fobs"
                },
                {
                  "valueString": "Keypad entry systems"
                },
                {
                  "valueString": "Electronic key cards or badges"
                },
                {
                  "valueString": "Biometric scanners (fingerprint, retina, etc.) Smart cards with embedded chips"
                },
                {
                  "valueString": "Mobile phone apps for access control"
                },
                {
                  "valueString": "Smart cards with embedded chips"
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "896964575016",
              "text": "Notes / Evidence"
            },
            {
              "linkId": "621187042559_helpText",
              "type": "display",
              "text": "Keep an up-to-date inventory of all devices used to control physical access, such as card readers and locks, to ensure proper management and security.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "621187042559",
          "text": "1. Physical Access Device Inventory "
        },
        {
          "item": [
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "173451266066",
              "text": "How are physical access devices controlled and managed?",
              "repeats": true,
              "answerOption": [
                {
                  "valueString": "Formal inventory tracking system"
                },
                {
                  "valueString": "Device assignment records maintained"
                },
                {
                  "valueString": "Device return procedures for departing employees"
                },
                {
                  "valueString": "Regular audits of device assignment"
                },
                {
                  "valueString": "Procedures for lost or stolen devices"
                },
                {
                  "valueString": "Ability to quickly revoke device access"
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "164071724457",
              "text": "Notes / Evidence"
            },
            {
              "linkId": "250263340197_helpText",
              "type": "display",
              "text": "Implement procedures to configure, monitor, and maintain physical access devices to prevent unauthorized use or tampering.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "250263340197",
          "text": "2. Device Control and Management "
        },
        {
          "item": [
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "911514884520",
              "text": "What security measures protect physical access devices?",
              "repeats": true,
              "answerOption": [
                {
                  "valueString": "Secure storage for unassigned devices"
                },
                {
                  "valueString": "Encrypted data on electronic devices"
                },
                {
                  "valueString": "Device expiration dates and automatic deactivation"
                },
                {
                  "valueString": "Protection against unauthorized duplication"
                },
                {
                  "valueString": "Tamper-resistant design"
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "653480882123",
              "text": "Notes / Evidence"
            },
            {
              "linkId": "703507215918_helpText",
              "type": "display",
              "text": "Apply security controls to protect physical access devices from damage, tampering, or unauthorized modification.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "703507215918",
          "text": "3. Device Security Measures "
        },
        {
          "item": [
            {
              "item": [
                {
                  "type": "string",
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                      "valueCodeableConcept": {
                        "coding": [
                          {
                            "system": "http://hl7.org/fhir/questionnaire-item-control",
                            "code": "check-box",
                            "display": "Check-box"
                          }
                        ]
                      }
                    }
                  ],
                  "linkId": "944580322601",
                  "text": "Check all that apply:",
                  "repeats": true,
                  "answerOption": [
                    {
                      "valueString": "Regular testing of device functionality"
                    },
                    {
                      "valueString": "Battery monitoring and replacement"
                    },
                    {
                      "valueString": "Regular software/firmware updates"
                    },
                    {
                      "valueString": "Calibration of biometric devices"
                    },
                    {
                      "valueString": "Backup systems for device failures"
                    }
                  ]
                }
              ],
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "radio-button",
                        "display": "Radio Button"
                      }
                    ]
                  }
                }
              ],
              "linkId": "466342459779",
              "text": "How frequently are electronic access systems updated?",
              "answerOption": [
                {
                  "valueString": "Real-time update"
                },
                {
                  "valueString": "Daily"
                },
                {
                  "valueString": "Weekly"
                },
                {
                  "valueString": "Monthly"
                },
                {
                  "valueString": "As Needed Basis"
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "951698714660",
              "text": "Notes / Evidence"
            },
            {
              "linkId": "130535369896_helpText",
              "type": "display",
              "text": "Regularly perform maintenance and apply updates to physical access devices to ensure their reliability and security.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "130535369896",
          "text": "4. Device Maintenance and Updates"
        },
        {
          "type": "string",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "radio-button",
                    "display": "Radio Button"
                  }
                ]
              }
            }
          ],
          "linkId": "294892506040",
          "text": "Implementation Status",
          "repeats": false,
          "answerOption": [
            {
              "valueString": "Fully Implemented"
            },
            {
              "valueString": "Partially Implemented"
            },
            {
              "valueString": " Not Implemented"
            }
          ]
        },
        {
          "type": "string",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
              "valueString": "Type your comments here..."
            }
          ],
          "linkId": "140603351800",
          "text": "Notes / Evidence "
        }
      ],
      "type": "group",
      "linkId": "806534035552",
      "text": "PE.L1-B.1.IX –Manage Physical Access Devices"
    }
  ]
}`;