import { z, defineCollection } from 'astro:content';
import { glob } from 'astro/loaders';
import { time } from 'console';

const blog = defineCollection({
  loader: glob({ pattern: '**/*.md', base: './src/content/blog' }),
  schema: z.object({
    title: z.string(),
    description: z.string().optional(),
    date: z.string().transform((val) => new Date(val)).optional(),
    readingTime: z.string().optional(),
    category: z.string().optional(),
    featured: z.boolean().optional(),
    author: z.string().optional(),
    metaTitle: z.string().optional(),
    metaDescription: z.string().optional(),
    keywords: z.array(z.string()).optional(),
  }),
});

const resources = defineCollection({
  loader: glob({ pattern: '**/*.md', base: './src/content/resources' }),
  schema: z.object({
    title: z.string(),
    description: z.string().optional(),
    shortdescription: z.string().optional(),
    date: z.string().transform((val) => new Date(val)).optional(),
    readingTime: z.string().optional(),
    category: z.string().optional(),//guide,case study
    badge: z.string().optional(), // e.g., "Free", "success storyWebinar", "Guide"
    downloadurl: z.string().optional(), // URL to download the resource
    featured: z.boolean().optional(),
    author: z.string().optional(),
    type: z.string().optional(), // e.g., PDF Guide, Video, Webinar,case study
    company: z.object({
      name: z.string().optional(),    
      stats: z.array(
        z.object({
          label: z.string().optional(),
          value: z.string().optional(),
          image: z.string().optional(), // URL to the image
        }).optional(),
      ).optional(),
  }).optional(),

  }),
});
const upcomingevents = defineCollection({
  loader: glob({ pattern: '**/*.md', base: './src/content/upcoming-events' }),
  schema: z.object({
    title: z.string(),
    description: z.string().optional(),
    shortdescription: z.string().optional(),
    date: z.string().transform((val) => new Date(val)).optional(),
    time: z.string().optional(), // e.g., "2023-10-01T10:00:00Z"
    repeat: z
    .object({
      frequency: z.enum(["daily", "Next Week", "monthly"]).optional(),
      day: z.enum(["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]).optional()
    })
    .optional(),
    readingTime: z.string().optional(),
    category: z.string().optional(),
    featured: z.boolean().optional(),
    author: z.string().optional(),
    type: z.string().optional(), // e.g., PDF Guide, Video, Webinar

  }),
});

const pages = defineCollection({
 type: "content",
 schema: z.object({
    title: z.string(),
    order: z.number().optional(),
    draft: z.boolean().optional(),
    lastUpdated: z.string().optional(),
    description: z.string().optional(),
    metaTitle: z.string().optional(),
    metaDescription: z.string().optional(),
    keywords: z.array(z.string()).optional(),
    layout: z.string().optional(),
  }),
});

// Expose your defined collection to Astro
// with the `collections` export
export const collections = { blog,resources,upcomingevents, pages };