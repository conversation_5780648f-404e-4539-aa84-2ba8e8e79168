import { readFileSync, existsSync } from "fs";
import type { APIRoute } from 'astro';

export const POST: APIRoute = async ({ request }) => {
    const data = await request.json();
    const { filePath } = data;
    if (!filePath) {
        return new Response(JSON.stringify({ error: "Missing filePath or file" }), {
            status: 400,
            headers: { "Content-Type": "application/json" },
        });
    }

    if (!existsSync(filePath)) {
        return new Response(JSON.stringify({ error: `File not found: ${filePath}` }), {
            status: 404,
            headers: { "Content-Type": "application/json" },
        });
    }

    try {
        const fileContents = readFileSync(filePath, "utf8");
        const data = JSON.parse(fileContents);

        return new Response(JSON.stringify({ data }), {
            status: 200,
            headers: { "Content-Type": "application/json" },
        });
    } catch (err) {
        return new Response(JSON.stringify({ error: "Error reading file" }), {
            status: 500,
            headers: { "Content-Type": "application/json" },
        });
    }
};
