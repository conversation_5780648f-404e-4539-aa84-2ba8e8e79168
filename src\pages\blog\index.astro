---
import Layout from "../../layouts/Layout.astro";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Calendar, Clock, ArrowRight, User } from "lucide-react";
import { getCollection } from 'astro:content';


const allBlogPosts = (await getCollection('blog', ({ data }) => {
  return data.featured !== true;
})).sort((a, b) => {
  return new Date(b.data.date ?? '').getTime() - new Date(a.data.date ?? '').getTime();
});

const featuredBlogPosts = await getCollection('blog', ({ data }) => {
  return data.featured === true;
});
const featured = featuredBlogPosts[0];

const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Blog',"aria-disabled": true ,
  }
];
 
---

<Layout title="Blog"
        description="Meet the compliance experts, former CISOs & security engineers who make enterprise-grade compliance accessible to every growing company. 100% audit pass rate."
        keywords="compliance experts, CISO, security engineers, SOC2 experts, compliance team"
        url="/blog/"
        breadcrumbLinks={breadcrumbLinks}
        >
  <main>
        {/* Hero Section */}
        <div class="bg-gradient-to-br from-primary/5 via-background to-secondary/5">
          <div class="px-6 py-24 sm:px-6 sm:py-32 lg:px-8">
            <div class="mx-auto max-w-4xl text-center">
              <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
                Compliance Insights & Best Practices
              </h1>
              <p class="mx-auto mt-6 max-w-2xl text-lg leading-8 text-muted-foreground/90">
                Expert guidance, industry insights, and practical advice from compliance 
                professionals who've helped hundreds of companies achieve their certifications.
              </p>
            </div>
          </div>
        </div>

        {/* Featured Article */}
        {featured && (
            <div class="py-16">
                <div class="mx-auto max-w-7xl px-6 lg:px-8">
                <div class="mx-auto max-w-4xl">
                   <a href={`/blog/${featured.id}`}>
                    <Card className="border-2 border-primary/20 hover:shadow-lg transition-shadow duration-300">
                    <CardContent className="p-8">
                        <div class="flex items-center gap-2 mb-4">
                        <Badge variant="secondary">Featured</Badge>
                        <Badge>{featured.data.category}</Badge>
                        </div>
                        <h2 class="text-3xl font-bold text-foreground mb-4">
                        {featured.data.title}
                        </h2>
                        <p class="text-lg text-muted-foreground mb-6">
                        {featured.data.description}
                        </p>
                        <div class="flex items-center justify-between">
                        <div class="flex items-center gap-4 text-sm text-muted-foreground">
                            <div class="flex items-center gap-1">
                            <User className="h-4 w-4" />
                            {featured.data.author}
                            </div>
                            <div class="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            {featured.data.date 
                            ? new Date(featured.data.date).toLocaleDateString('en-US', { 
                                year: 'numeric', 
                                month: '2-digit', 
                                day: '2-digit' 
                              }) 
                            : ''}
                            </div>
                            <div class="flex items-center gap-1">
                            <Clock className="h-4 w-4" />
                            {featured.data.readingTime}
                            </div>
                        </div>
                        
                       
                          <Button>Read Article
                              <ArrowRight className="ml-2 h-4 w-4" />                        
                          </Button>                        

                        </div>
                    </CardContent>
                    </Card>
                   </a>
                </div>
                </div>
            </div>
            )}

        {/* Blog Grid */}
        <div class="pb-24">
          <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="mx-auto max-w-2xl text-center mb-16">
              <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                Latest Articles
              </h2>
              <p class="mt-4 text-lg text-muted-foreground">
                Stay up to date with the latest compliance trends, regulatory changes, and best practices.
              </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {allBlogPosts.map((post) => (
                <a href={`/blog/${post.id}/`} class="h-full">
                  <Card className="h-full flex flex-col hover:shadow-lg transition-shadow duration-300">
                    <CardHeader>
                      <Badge className="w-fit mb-2">{post.data.category}</Badge>
                      <CardTitle className="text-xl">{post.data.title}</CardTitle>
                    </CardHeader>
                    {/* make content fill remaining space */}
                    <CardContent className="flex flex-col justify-between flex-grow">
                      <div>
                        <p class="text-muted-foreground mb-4">{post.data.description}</p>
                      </div>

                      <div>
                        <div class="flex items-center justify-between text-sm text-muted-foreground mb-4">
                          <div class="flex items-center gap-1">
                            <User className="h-3 w-3" />
                            {post.data.author}
                          </div>
                          <div class="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {post.data.readingTime}
                          </div>
                        </div>
                        <div class="flex items-center justify-between">
                          <span class="text-sm text-muted-foreground">
                            {post.data.date
                              ? new Date(post.data.date).toLocaleDateString('en-US', { 
                                year: 'numeric', 
                                month: '2-digit', 
                                day: '2-digit' 
                              })
                              : ""}
                          </span>
                          <Button variant="ghost" size="sm">
                            Read More
                            <ArrowRight className="ml-2 h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </a>
              ))}
            </div>

          </div>
        </div>

        {/* Newsletter CTA */}
         <div class="bg-muted">
          <div class="px-6 py-24 sm:px-6 sm:py-32 lg:px-8">
            <div class="mx-auto max-w-2xl text-center">
              <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                Stay Informed
              </h2>
              <p class="mx-auto mt-6 max-w-xl text-lg leading-8 text-muted-foreground">
                Get the latest compliance insights, regulatory updates, and best practices 
                delivered to your inbox.
              </p>
              <div class="mt-10 flex items-center justify-center gap-x-6">
                
                <a href="/contact/">                
                  <Button size="lg">
                    Subscribe to Newsletter<ArrowRight className="ml-2 h-4 w-4" />                  
                  </Button>
                </a>
                
              </div>
            </div>
          </div>
        </div> 
      </main>
</Layout>