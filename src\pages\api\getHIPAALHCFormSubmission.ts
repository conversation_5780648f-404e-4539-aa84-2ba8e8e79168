// src/pages/api/getLHCFormSubmission.ts
import { readdir, readFile, stat, access, constants } from 'fs/promises';
import { readFileSync, existsSync } from "fs";
import type { APIContext } from 'astro';
import path from 'path';

export async function POST({ request }: APIContext) {
    try {
        const { fileName, userID, sessionID, tenantID } = await request.json();

        if (!fileName || !userID || !sessionID || !tenantID) {
            return new Response(JSON.stringify({ error: "Missing fileName or user session" }), {
                status: 400,
                headers: { "Content-Type": "application/json" },
            });
        }

        const baseDir = `src/data/lforms/${tenantID}/${userID}`;
        const possibleFiles = [
            `.submit.lform-submission.json`,
            `.save.lform-submission.json`,
        ];

        let foundFilePath: string | null = null;
        let foundSessionId: string | null = null;

        // --- Step 1: Try current session folder ---
        const currentSessionDir = path.join(baseDir, sessionID, 'responses');
        try {
            const files = await readdir(currentSessionDir);
            const match = files.find(f =>
                f.startsWith(fileName) && possibleFiles.some(ext => f.endsWith(ext))
            );
            if (match) {
                foundFilePath = path.join(currentSessionDir, match);
                foundSessionId = sessionID;
            }
        } catch {
            // Current session folder may not exist, skip
        }

        // --- Step 2: If not found, check all other sessions in recency order ---
        if (!foundFilePath) {
            try {
                await access(baseDir, constants.F_OK);
                const sessionDirs = await readdir(baseDir, { withFileTypes: true });
                const otherSessions = sessionDirs
                    .filter(d => d.isDirectory() && d.name !== sessionID)
                    .map(d => path.join(baseDir, d.name, "responses"));

                const sessionWithTimes = await Promise.all(
                    otherSessions.map(async dir => {
                        try {
                            const files = await readdir(dir);
                            let latestTime = 0;
                            for (const f of files) {
                                const stats = await stat(path.join(dir, f));
                                latestTime = Math.max(latestTime, stats.mtimeMs);
                            }
                            return { dir, latestTime };
                        } catch {
                            return { dir, latestTime: 0 };
                        }
                    })
                );

                // Sort all sessions by recency
                const sortedSessions = sessionWithTimes
                    .filter(s => s.latestTime > 0)
                    .sort((a, b) => b.latestTime - a.latestTime);

                for (const session of sortedSessions) {
                    const files = await readdir(session.dir);
                    const match = files.find(f =>
                        f.startsWith(fileName) && possibleFiles.some(ext => f.endsWith(ext))
                    );
                    if (match) {
                        foundFilePath = path.join(session.dir, match);
                        foundSessionId = path.basename(path.dirname(session.dir)); // session folder name
                        break;
                    }
                }
            } catch {
                // baseDir does not exist — skip
            }
        }

        // --- Step 3: If still not found, use master data ---
        if (!foundFilePath) {
            const masterPath = `src/content/hipaa-self-assessment/questionnaires/${fileName}.json`;
            if (!existsSync(masterPath)) {
                return new Response(JSON.stringify({ error: `File not found: ${masterPath}` }), {
                    status: 404,
                    headers: { "Content-Type": "application/json" },
                });
            }
            try {
                const fileContents = readFileSync(masterPath, "utf8");
                const data = JSON.parse(fileContents);
                return new Response(JSON.stringify({ data, sessionId: null }), {
                    status: 200,
                    headers: { "Content-Type": "application/json" },
                });
            } catch {
                return new Response(JSON.stringify({ error: "Error reading master file" }), {
                    status: 500,
                    headers: { "Content-Type": "application/json" },
                });
            }
        }

        // --- Step 4: Return the found submission ---
        const fileContent = await readFile(foundFilePath, 'utf-8');
        const json = JSON.parse(fileContent);
        return new Response(JSON.stringify({ data: json, sessionId: foundSessionId }), {
            status: 200,
            headers: { "Content-Type": "application/json" },
        });

    } catch (err) {
        console.error("Error loading submission:", err);
        return new Response(JSON.stringify({ error: "Failed to load form submission" }), {
            status: 500,
            headers: { "Content-Type": "application/json" },
        });
    }
}
