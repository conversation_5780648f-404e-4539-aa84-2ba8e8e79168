# CJM & UJM Ownership Roadmap + Authoritative Source Priorities

This roadmap clarifies **ownership responsibilities**, **key distinctions**, and the **priority order for authoritative sources** to ensure our outputs are **grounded, defensible, and trustworthy**.

---

## 1. Ownership Responsibilities

### 1.1 Customer Journey Map (CJM)
**Owners:** <PERSON> & <PERSON>/Ajay (joint)
- **Status:** Fully bespoke – no longer aligned with Overwatch Tools format.
- **Focus:** Reflect our unique positioning and understanding of CMMC requirements.
- **Tools:** Use AI to fill gaps and iterate.
- **Maintenance:** Ravi should visualize CJMs regularly and share with the team as customer understanding evolves.

**Definition:**  
A **Customer Journey** describes how prospects discover us and move through our marketing lifecycle—via email campaigns, LinkedIn messages, calls, social media, blogs, partnerships—before landing on lead magnets, landing pages, and sales funnels.  
**Goal:** Attraction & conversion.

---

### 1.2 User Journey Map (UJM)
**Owners:** <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>eo  
**Strategic & Content Direction:** Ravi
- **Status:** Bespoke – not required to match Overwatch Tools.
- **Focus:** Tune for our ideal customer experience.
- **Tools:** Use AI to fill gaps and improve UX.

**Definition:**  
A **User Journey** begins **after** someone engages with our apps. It maps:
- Functional interactions
- Screens and workflows
- Tasks completed
- Points of friction and success  
**Goal:** In-app value delivery and task completion.

---

## 2. Key Distinction: CJM vs. UJM

| **Aspect**       | **Customer Journey (CJ)** | **User Journey (UJ)** |
|------------------|--------------------------|-----------------------|
| **When**         | Before product use        | After product use     |
| **Focus**        | Attraction, conversion   | Usage, value delivery |
| **Scope**        | Marketing lifecycle      | In-app experience     |
| **Goal**         | Bring people in          | Help users succeed    |

---

## 3. Authoritative Source Hierarchy

**Principle:** Follow this strict order to ensure defensibility.

### **Primary – .gov Sources**
- DoD, NIST, CISA, etc.
- Convert to Markdown with **citations in frontmatter** (per AI Interactions Engineering Manifesto).
- Feed into **surveilr AI Context Middleware** for daily use via AnythingLLM or other RAG stacks.

### **Secondary – Secure Controls Framework (SCF)**
- SCF documents and community (Discord, email groups, meetups).
- Use AI grounded in SCF prompts for deliverables.
- Leverage DC-area networking.

### **Tertiary – Certified Assessors**
- C3PAOs (CMMC)
- AICPA auditors (SOC2)
- HITRUST/ISO assessors (other frameworks)
- Capture expert prompts for AI training.

### **Quaternary – External Content**
- Blogs, white papers, commercial tools.
- Use only after exhausting #1–#3.
- Supportive, not definitive.

---

## 4. Treatment of Comparables
- Overwatch Tools and other vendors are **inspiration only**.
- Use for **teardowns and idea capture**, not as design blueprints.
- Discard comparables once requirements are clear and CJM/UJM are documented.

---

## 5. Guiding Principles
1. **AI as Daily Collaborator** – Provide authoritative, structured input.
2. **Iterate Rapidly** – Use AI to close knowledge and design gaps.
3. **Grounded in Sources** – Everything traceable to authoritative documentation.
4. **Confidence & Clarity** – Deliver with discipline and defensibility.

---
