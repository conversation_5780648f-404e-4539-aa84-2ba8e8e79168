# Universal AI Prompt for Content Engineering Ideation

Here’s a **comprehensive AI prompt template** you can give to anyone on the team (Ravi, ODC engineers, or others) so they can quickly generate a **business case for a new content idea** using AI.

It enforces the **Opsfolio rules**: every idea must connect to revenue, be backed by evidence or hypotheses, be mapped to the funnel, and fit into the **customer journey map (CJM)**.


```
You are an AI assistant helping me prepare a **content idea proposal** for Opsfolio.  
I will provide a working title and some initial context, and you will produce a **business-case style document** following the Opsfolio Content Engineering Ideation Guidelines.  

The proposal must include the following sections:  

1. **Idea Title**  
   - A clear, outcome-focused name for the idea.  

2. **Revenue Connection**  
   - Explain how the idea connects to revenue.  
   - Select and justify from: Acquire (new leads), Convert (contracts), Renew (renewals), Retain (customer success), Expand (upsell/cross-sell).  

3. **Evidence Base**  
   - Cite first-party research (Nielsen Norman Group, Forrester, McKinsey, etc.) where relevant.  
   - Reference competitor approaches if applicable.  
   - If no research exists, frame a **hypothesis** and describe why it has merit.  

4. **Funnel Mapping**  
   - Describe how the idea works across stages:  
     - **Top of Funnel (ToFu):** Lead capture.  
     - **Middle of Funnel (MoFu):** Trust building and nurturing.  
     - **Bottom of Funnel (BoFu):** Conversion driver.  
     - **Retention/Expansion:** Support for renewals and ongoing engagement.  

5. **Customer Journey Placement (CJM)**  
   - Map where this idea fits into the Opsfolio CJM.  
   - State which touchpoint it enhances, replaces, or adds.  

6. **Hypothesis & Test Strategy**  
   - Write a testable hypothesis:  
     “If we [do X], then we expect [Y outcome] because [Z reason].”  
   - Define how to test: A/B test, gated vs ungated, nurture sequence split, proposal tracking, etc.  
   - Identify success metrics.  

7. **Competitive/Comparative Insight**  
   - Identify what competitors are doing (if anything similar).  
   - Explain how Opsfolio’s approach is different or better.  

8. **Execution Requirements**  
   - Content: copywriting, design, editorial.  
   - Engineering: automation, landing pages, data handling, integrations.  
   - Marketing/Sales: campaigns, distribution, proposal integration.  

9. **Customer & Executive Talking Points**  
   - Short, persuasive phrases executives and sales can use to describe the value.  

10. **Example Workflow (User Experience)**  
   - Step-by-step: what the prospect sees, clicks, submits, and receives.  

11. **Open Engineering Questions (ODC)**  
   - Identify questions engineers must resolve before implementation.  
   - Examples: scope, automation feasibility, scaling, data security, legal considerations.  

12. **Next Steps**  
   - Draft the action items needed for approval, prototyping, and rollout.  

---

## INPUT PLACEHOLDERS  

- **Idea Title:** <INSERT_TITLE>  
- **Initial Context/Concept:** <INSERT_BRIEF_DESCRIPTION>  
- **Intended Purpose:** <INSERT_PURPOSE>  
- **Suspected Funnel Impact:** <INSERT_FUNNEL_STAGE>  
- **Any Research or Competitor Examples Known:** <INSERT_REFERENCES_OR_LEAVE_BLANK>  
- **Customer Journey Placement (if known):** <INSERT_OR_LEAVE_BLANK>  

---

## OUTPUT FORMAT  

Please generate the response as a **comprehensive ideation proposal document** with headings for each of the 12 sections above, filled out with detailed explanations, business justification, and Opsfolio-specific framing.  
Where placeholders are insufficient, propose assumptions and mark them clearly as [ASSUMED].  
```

---

### Example (mini input for testing the prompt)

```
Idea Title: SOC 2 Self-Assessment Interactive Tool  
Initial Context: An interactive quiz-based tool that gives users a readiness score for SOC 2.  
Intended Purpose: Capture compliance leads in early research phase.  
Suspected Funnel Impact: Top of funnel lead magnet.  
Any Research/Competitor Examples: Drata’s “SOC 2 readiness” quiz.  
Customer Journey Placement: Cold outreach → quiz → nurture email → demo.  
```

AI would then output a **full business-case proposal** in the standard Opsfolio format.
