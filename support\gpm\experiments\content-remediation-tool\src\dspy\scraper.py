import re
import requests
from bs4 import BeautifulSoup
from schemas import Page<PERSON>ontent

def clean_page_text(text: str) -> str:
    text = text.replace("â", "'").replace("â€¢", "•").replace("\xa0", " ")
    text = re.sub(r"[^\x00-\x7F]+", " ", text)
    text = "\n".join([line.strip() for line in text.splitlines() if line.strip()])
    text = re.sub(r"\n{2,}", "\n", text)
    return text

def fetch_page_static(url: str) -> PageContent:
    headers = {"User-Agent": "ContentAuditBot/1.0 (+https://example.com)"}
    resp = requests.get(url, headers=headers, timeout=20)
    resp.raise_for_status()

    soup = BeautifulSoup(resp.text, "lxml")

    for tag in soup(["script", "style", "noscript", "header", "footer", "nav", "svg", "form"]):
        tag.decompose()

    title = soup.title.string.strip() if soup.title else ""
    text = soup.get_text("\n")
    text = "\n".join([line.strip() for line in text.splitlines() if line.strip()])
    return PageContent(title=title, text=text)

def get_page_content(url: str) -> PageContent:
    page = fetch_page_static(url)
    if len(page.text.split()) < 50:
        raise ValueError("Content too short")
    return page
