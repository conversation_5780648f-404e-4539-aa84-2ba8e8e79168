import asyncio
import os
import json
from schemas import AuditRequest, ComplianceAuditResult
from scraper import get_page_content, clean_page_text
from audit import ComplianceAuditProgram
from utils import generate_report_filename, save_audit_report
from pathlib import Path
from playwright.async_api import async_playwright

UX_CAPTURE_FOLDER = "ux_screenshots"
os.makedirs(UX_CAPTURE_FOLDER, exist_ok=True)

PROMPTS_FOLDER = "../../../../../../src/ai-context-engineering/content-audit"

# Set the event loop policy for Windows
if os.name == 'nt':  # Windows
    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

async def get_page_content_with_ux_async(url):
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        try:
            await page.goto(str(url), timeout=60000)
            await page.wait_for_load_state("networkidle")
            
            page_text = await page.inner_text("body")
            
            # Create safe filename
            safe_url = str(url).replace('://', '_').replace('/', '_').replace('?', '_').replace('&', '_')
            filename = os.path.join(UX_CAPTURE_FOLDER, f"{safe_url}.png")
            os.makedirs(UX_CAPTURE_FOLDER, exist_ok=True)
            
            await page.screenshot(path=filename, full_page=True)
            
            return page_text, filename
            
        finally:
            await browser.close()

def get_page_content_with_ux(url):
    """Wrapper function to run async code"""
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    
    return loop.run_until_complete(get_page_content_with_ux_async(url))

def run_audit(url: str, page_type: str):
    """Core function to run audit and save executive summary"""
    # Fetch and clean page content
    page = get_page_content(url)
    page.text = clean_page_text(page.text)
    print(f"\nFetched page: {page.title or url} (length: {len(page.text.split())} words)\n")

    # Load all prompt files
    prompt_files = {
        f: open(os.path.join(PROMPTS_FOLDER, f), "r", encoding="utf-8").read()
        for f in os.listdir(PROMPTS_FOLDER)
        if f.endswith(".prompt.md")
    }

    program = ComplianceAuditProgram()
    content_results_text = ""

    # Step 1 & 2: Run all prompts except summary/UX and aggregate
    for prompt_file, prompt_text in prompt_files.items():
        if "summary" in prompt_file.lower():
            continue
        if "ux" in prompt_file.lower():
            page_text, _screenshot_path = get_page_content_with_ux(url)
        else:
            page_text = page.text

        try:
            print(f"Running prompt: {prompt_file}")
            result = program(page_type=page_type, page_text=page_text, prompt=prompt_text)
            if not isinstance(result, ComplianceAuditResult):
                try:
                    result = ComplianceAuditResult(**json.loads(result))
                except Exception:
                    result = ComplianceAuditResult(
                        executive_summary=str(result),
                        strengths="",
                        weaknesses="",
                        recommendations="",
                        remediated_content="",
                        observation_vs_hypothesis=""
                    )
        except Exception as e:
            result = f"Audit failed for this prompt: {e}"

        prompt_name = os.path.splitext(prompt_file)[0]

        # Aggregate results for summary generation
        if isinstance(result, ComplianceAuditResult):
            content_results_text += (
                f"\n\n=== {prompt_name} ===\n"
                f"{result.executive_summary or ''}\n"
                f"{result.strengths or ''}\n"
                f"{result.weaknesses or ''}\n"
                f"{result.recommendations or ''}\n"
                f"{result.remediated_content or ''}\n"
            )
        else:
            content_results_text += f"\n\n=== {prompt_name} ===\n{result}\n"
        # Save individual prompt result
        filename = generate_report_filename(url, f"{page_type}_{prompt_name}")
        save_audit_report(result, filename)
        print(f"Prompt report saved: {filename}")

    # Step 3: Run Executive Summary prompt on aggregated text
    summary_prompt = None
    for prompt_file, prompt_text in prompt_files.items():
        if "summary-page" in prompt_file.lower():
            summary_prompt = prompt_text
            break

    if summary_prompt:
        try:
            summary_result = program(
                page_type=page_type,
                page_text=content_results_text,
                prompt=summary_prompt,
            )
        except Exception as e:
            summary_result = f"Executive Summary generation failed: {e}"

        # Step 4: Save only executive summary
        filename = generate_report_filename(url, f"{page_type}_executive_summary")
        save_audit_report(summary_result, filename)
        print(f"\nExecutive summary report saved: {filename}\n")

    else:
        print("No executive summary prompt found. Skipping.")

def load_prompts():
    """Load all prompts from prompts folder into a dict."""
    prompts = {}
    for filename in os.listdir(PROMPTS_FOLDER):
        if filename.endswith(".md"):
            prompt_name = filename.replace(".md", "")
            with open(os.path.join(PROMPTS_FOLDER, filename), "r", encoding="utf-8") as f:
                prompts[prompt_name] = f.read()
    return prompts


def merge_prompts(default_prompts: dict, ui_prompts: dict):
    """
    Merge user-updated prompts with defaults.
    - ui_prompts: dict where keys = prompt_name, values = updated text (can be None/empty).
    """
    final_prompts = {}
    for name, text in default_prompts.items():
        if name in ui_prompts and ui_prompts[name].strip():
            # Use UI-updated prompt if provided
            final_prompts[name] = ui_prompts[name]
        else:
            # Otherwise fallback to folder default
            final_prompts[name] = text
    return final_prompts

def run_audit_streamlit(url: str, page_type: str, ui_prompts: dict = None):
    """
    Runs the DSPy content audit and returns results for Streamlit display.
    ui_prompts: dict where key = prompt filename (e.g., "seo.prompt.md"), 
                value = updated text (or None/empty if not updated)
    Returns: dict {prompt_name: result_text}
    """

    req = AuditRequest(url=url.strip(), page_type=page_type.strip())

    # Fetch and clean page content
    page = get_page_content(req.url)
    page.text = clean_page_text(page.text)

    print(f"\nFetched page: {page.title or req.url} (length: {len(page.text.split())} words)\n")

    # Step 1: Load all prompt files from UI
    if ui_prompts:
        prompt_files = ui_prompts
    else:
        prompt_files = {}
        for f in os.listdir(PROMPTS_FOLDER):
            if f.endswith(".prompt.md"):
                with open(os.path.join(PROMPTS_FOLDER, f), "r", encoding="utf-8") as file:
                    prompt_files[f] = file.read()

    program = ComplianceAuditProgram()
    results = {}

    # Step 2: Run all content prompts (skip summary and UX)
    content_results_text = ""
    for prompt_file, prompt_text in prompt_files.items():
        if "summary" in prompt_file.lower():
            continue
        if "ux" in prompt_file.lower():
            page.text, _screenshot_path = get_page_content_with_ux(req.url)

        try:
            print(f"Running prompt: {prompt_file}")
            result = program(page_type=req.page_type, page_text=page.text, prompt=prompt_text)
            # Ensure structured output
            if not isinstance(result, ComplianceAuditResult):
                try:
                    result = ComplianceAuditResult(**json.loads(result))
                except Exception:
                    # Fallback: fill empty fields
                    result = ComplianceAuditResult(
                        executive_summary=str(result),
                        strengths="",
                        weaknesses="",
                        recommendations="",
                        remediated_content="",
                        observation_vs_hypothesis=""
                    )
        except Exception as e:
            result = f"Audit failed for this prompt: {e}"

        prompt_name = os.path.splitext(prompt_file)[0]
        results[prompt_name] = result

        # Aggregate results to feed into executive summary
        if isinstance(result, ComplianceAuditResult):
            content_results_text += (
                f"\n\n=== {prompt_name} ===\n"
                f"{result.executive_summary or ''}\n"
                f"{result.strengths or ''}\n"
                f"{result.weaknesses or ''}\n"
                f"{result.recommendations or ''}\n"
                f"{result.remediated_content or ''}\n"
            )
        else:
            content_results_text += f"\n\n=== {prompt_name} ===\n{result}\n"

    # Step 3: Run Executive Summary prompt
    summary_prompt = None
    for prompt_file, prompt_text in prompt_files.items():
        if "summary-page" in prompt_file.lower():
            summary_prompt = prompt_text
            break

    final_md = None
    if summary_prompt:
        try:
            summary_result = program(
                page_type=req.page_type,
                page_text=content_results_text,
                prompt=summary_prompt
            )
        except Exception as e:
            summary_result = f"**Executive Summary generation failed:** {e}"

        if isinstance(summary_result, ComplianceAuditResult):
            final_md = (
                f"# Final Executive Summary\n\n"
                f"{summary_result.executive_summary or ''}\n\n"
                f"### Strengths\n{summary_result.strengths or ''}\n\n"
                f"### Weaknesses\n{summary_result.weaknesses or ''}\n\n"
                f"### Recommendations\n{summary_result.recommendations or ''}\n\n"
                f"### Remediated Content\n{summary_result.remediated_content or ''}\n\n"
                f"### Observation vs Hypothesis\n{summary_result.observation_vs_hypothesis or ''}\n"
            )
        else:
            final_md = f"# Final Executive Summary\n\n{str(summary_result)}\n"

    return {
        "final_md": final_md,
        "prompt_results": results
    }



if __name__ == "__main__":
    try:
        url = input("Enter the page URL: ").strip()
        page_type = input("Enter the page type (Landing, Case Study, Product Page): ").strip()
        run_audit(url, page_type)
    except Exception as e:

        print(f"Error: {e}")
