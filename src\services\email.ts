import type { ContactFormData, GetStartedFormData, GenericPayload } from "@/lib/schema";

const API_KEY = `<PERSON>piKey ${import.meta.env.PUBLIC_NOVU_API_KEY}`;

type EmailPayload = ContactFormData | GetStartedFormData | GenericPayload;


async function sendEmail(
    templateName: string,
    payload: GenericPayload,
    emailTo?: string,
    otherRecipients?: string[]
): Promise<Response> {
    const now = new Date();
    const submissionDate = now.toISOString().split("T")[0];
    const finalPayload = {
        ...payload,
        submissionDate,
    };

    // Validate environment variables
    if (
        !import.meta.env.PUBLIC_NOVU_API_URL ||
        !import.meta.env.PUBLIC_NOVU_SUBSCRIBER_ID ||
        !import.meta.env.PUBLIC_NOVU_API_KEY
    ) {
        return new Response(
            JSON.stringify({ error: "Missing Novu environment configuration." }),
            { status: 500 }
        );
    }

    const response = await fetch(import.meta.env.PUBLIC_NOVU_API_URL as string, {
        method: "POST",
        headers: {
            Authorization: API_KEY,
            "Content-Type": "application/json",
        },
        body: JSON.stringify({
            name: templateName,
            to: {
                subscriberId: import.meta.env.PUBLIC_NOVU_SUBSCRIBER_ID as string,
                email: emailTo,
            },
            overrides: {
                email: {
                    to: otherRecipients,
                },
            },
            payload: finalPayload,
        }),
    });

    return response;
}

export default sendEmail;
