---
import Layout from "../layouts/Layout.astro";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Database, 
  Shield, 
  Eye, 
  Lock, 
  Search, 
  FileText, 
  GitBranch,
  Server,
  Monitor,
  Laptop,
  ArrowRight,
  CheckCircle
} from "lucide-react";

const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Evidence Warehouse',"aria-disabled": true ,
  }
];
 
---
<Layout breadcrumbLinks={breadcrumbLinks}>
    <main>
        {/* Hero Section */}
        <div class="bg-gradient-to-br from-primary/5 via-background to-secondary/5">
            <div class="px-6 py-24 sm:px-6 sm:py-32 lg:px-8">
            <div class="mx-auto max-w-4xl text-center">
                <Badge className="mb-4 bg-primary-foreground/20 text-foreground border-primary-foreground/30">
                Powered by surveilr
                </Badge>
                <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
                Compliance Evidence Warehouse
                </h1>
                <p class="mx-auto mt-6 max-w-3xl text-lg leading-8 text-muted-foreground/90">
                Your organization's cybersecurity, quality metrics, and regulatory compliance efforts are backed by a SQL queryable private evidence warehouse that can fit on your laptop — fully auditable without IT support and with little to no human intervention.
                </p>
                <div class="mt-10 flex items-center justify-center gap-x-6">
                    
                <a href="/get-started">
                    <Button size="lg" variant="default">
                        Start Building Your Warehouse
                    </Button>
                </a>
                <a href="/contact">
                    <Button size="lg" variant="outline">
                        Talk to Our Experts
                    </Button>
                </a>
                </div>
            </div>
            </div>
        </div>

        {/* Core Concept Section */}
        <div class="py-24">
            <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="mx-auto max-w-2xl text-center mb-16">
                <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                The Foundation of Modern Compliance
                </h2>
                <p class="mt-6 text-lg leading-8 text-muted-foreground">
                Built on surveilr's Resource Surveillance & Integration Engine, designed to address modern challenges by providing a centralized platform for evidence surveillance and data aggregation.
                </p>
            </div>

            <div class="grid lg:grid-cols-3 gap-8 mb-16">
                <Card className="border-primary/20">
                <CardHeader>
                    <Database className="h-12 w-12 text-primary mb-4" />
                    <CardTitle>Stateful & Local-First</CardTitle>
                </CardHeader>
                <CardContent>
                    <p class="text-muted-foreground">
                    Unlike simple data transfer tools, surveilr stores and organizes data in a standardized way, making it easy to query and manage on local devices, laptops, or edge systems.
                    </p>
                </CardContent>
                </Card>

                <Card className="border-primary/20">
                <CardHeader>
                    <Shield className="h-12 w-12 text-primary mb-4" />
                    <CardTitle>Private & Secure</CardTitle>
                </CardHeader>
                <CardContent>
                    <p class="text-muted-foreground">
                    Edge-based approach ensures data stays secure by limiting unnecessary movement of sensitive information. Only what's needed is transferred to central servers.
                    </p>
                </CardContent>
                </Card>

                <Card className="border-primary/20">
                <CardHeader>
                    <Eye className="h-12 w-12 text-primary mb-4" />
                    <CardTitle>Continuous Surveillance</CardTitle>
                </CardHeader>
                <CardContent>
                    <p class="text-muted-foreground">
                    Automatically collects, stores, and analyzes data from various systems, generating evidence for audits and compliance without manual oversight.
                    </p>
                </CardContent>
                </Card>
            </div>
            </div>
        </div>

        {/* How It Works Section */}
        <div class="bg-muted py-24">
            <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="mx-auto max-w-2xl text-center mb-16">
                <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                How Your Evidence Warehouse Works
                </h2>
                <p class="mt-6 text-lg leading-8 text-muted-foreground">
                A single binary that securely runs on Windows, Linux, and macOS in your infrastructure, creating a comprehensive evidence collection system.
                </p>
            </div>

            <div class="grid lg:grid-cols-2 gap-12 items-center">
                <div class="space-y-8">
                <div class="flex items-start gap-4">
                    <div class="bg-primary text-primary-foreground rounded-full p-2 mt-1">
                    <span class="text-sm font-bold">1</span>
                    </div>
                    <div>
                    <h3 class="text-xl font-semibold mb-2">Deploy Locally</h3>
                    <p class="text-muted-foreground">
                        Download and deploy the single surveilr binary on your infrastructure. No cloud dependencies, no data sharing with third parties.
                    </p>
                    </div>
                </div>

                <div class="flex items-start gap-4">
                    <div class="bg-primary text-primary-foreground rounded-full p-2 mt-1">
                    <span class="text-sm font-bold">2</span>
                    </div>
                    <div>
                    <h3 class="text-xl font-semibold mb-2">Automatic Collection</h3>
                    <p class="text-muted-foreground">
                        Continuously collects evidence from your existing systems, code repositories, test results, and operational data without manual intervention.
                    </p>
                    </div>
                </div>

                <div class="flex items-start gap-4">
                    <div class="bg-primary text-primary-foreground rounded-full p-2 mt-1">
                    <span class="text-sm font-bold">3</span>
                    </div>
                    <div>
                    <h3 class="text-xl font-semibold mb-2">SQL Queryable</h3>
                    <p class="text-muted-foreground">
                        All evidence is stored in a standardized SQL database that's fully queryable, auditable, and machine-attestable for compliance reporting.
                    </p>
                    </div>
                </div>

                <div class="flex items-start gap-4">
                    <div class="bg-primary text-primary-foreground rounded-full p-2 mt-1">
                    <span class="text-sm font-bold">4</span>
                    </div>
                    <div>
                    <h3 class="text-xl font-semibold mb-2">Audit Ready</h3>
                    <p class="text-muted-foreground">
                        Generate comprehensive compliance reports that meet regulatory standards with machine-attested evidence that auditors can trust.
                    </p>
                    </div>
                </div>
                </div>

                <div class="grid grid-cols-2 gap-6">
                <Card>
                    <CardContent className="p-6 text-center">
                    <Laptop className="h-12 w-12 text-primary mx-auto mb-4" />
                    <h4 class="font-semibold mb-2">Edge Computing</h4>
                    <p class="text-sm text-muted-foreground">
                        Runs on laptops and edge devices
                    </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6 text-center">
                    <Lock className="h-12 w-12 text-primary mx-auto mb-4" />
                    <h4 class="font-semibold mb-2">Data Privacy</h4>
                    <p class="text-sm text-muted-foreground">
                        Your data never leaves your control
                    </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6 text-center">
                    <Search className="h-12 w-12 text-primary mx-auto mb-4" />
                    <h4 class="font-semibold mb-2">SQL Queries</h4>
                    <p class="text-sm text-muted-foreground">
                        Standard SQL interface for all data
                    </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6 text-center">
                    <FileText className="h-12 w-12 text-primary mx-auto mb-4" />
                    <h4 class="font-semibold mb-2">Audit Reports</h4>
                    <p class="text-sm text-muted-foreground">
                        Automated compliance documentation
                    </p>
                    </CardContent>
                </Card>
                </div>
            </div>
            </div>
        </div>

        {/* Benefits Section */}
        <div class="py-24">
            <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="mx-auto max-w-2xl text-center mb-16">
                <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                Why Choose Evidence Warehouse?
                </h2>
                <p class="mt-6 text-lg leading-8 text-muted-foreground">
                Transform your compliance from a manual burden into an automated advantage.
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="flex items-start gap-4">
                <CheckCircle className="h-6 w-6 text-primary mt-1 flex-shrink-0" />
                <div>
                    <h3 class="font-semibold mb-2">Minimal Human Intervention</h3>
                    <p class="text-muted-foreground">
                    Designed to work with very little manual effort, automatically collecting and organizing evidence.
                    </p>
                </div>
                </div>

                <div class="flex items-start gap-4">
                <CheckCircle className="h-6 w-6 text-primary mt-1 flex-shrink-0" />
                <div>
                    <h3 class="font-semibold mb-2">Machine Attestable</h3>
                    <p class="text-muted-foreground">
                    Generate auditable, queryable evidence that can be reviewed by auditors and decision-makers.
                    </p>
                </div>
                </div>

                <div class="flex items-start gap-4">
                <CheckCircle className="h-6 w-6 text-primary mt-1 flex-shrink-0" />
                <div>
                    <h3 class="font-semibold mb-2">Cross-Industry Support</h3>
                    <p class="text-muted-foreground">
                    Modular patterns allow customization for specific industries and regulatory requirements.
                    </p>
                </div>
                </div>

                <div class="flex items-start gap-4">
                <CheckCircle className="h-6 w-6 text-primary mt-1 flex-shrink-0" />
                <div>
                    <h3 class="font-semibold mb-2">No Vendor Lock-in</h3>
                    <p class="text-muted-foreground">
                    Your data remains in standard SQL format, accessible with any SQL-compatible tools.
                    </p>
                </div>
                </div>

                <div class="flex items-start gap-4">
                <CheckCircle className="h-6 w-6 text-primary mt-1 flex-shrink-0" />
                <div>
                    <h3 class="font-semibold mb-2">Regulatory Ready</h3>
                    <p class="text-muted-foreground">
                    Meet compliance requirements for HIPAA, SOX, GDPR, and other regulatory frameworks.
                    </p>
                </div>
                </div>

                <div class="flex items-start gap-4">
                <CheckCircle className="h-6 w-6 text-primary mt-1 flex-shrink-0" />
                <div>
                    <h3 class="font-semibold mb-2">Cost Effective</h3>
                    <p class="text-muted-foreground">
                    Reduce compliance costs by automating evidence collection and report generation.
                    </p>
                </div>
                </div>
            </div>
            </div>
        </div>

        {/* Integration with Compliance as Code */}
        <div class="bg-muted py-24">
            <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="mx-auto max-w-4xl text-center">
                <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl mb-6">
                The Foundation of Compliance as Code
                </h2>
                <p class="text-lg text-muted-foreground mb-8">
                The Evidence Warehouse is the cornerstone that makes Compliance as Code possible. All controls, policies, procedures, and evidence are stored in surveilr-based highly secure and privacy-preserving SQL data warehouses.
                </p>
                <div class="grid md:grid-cols-2 gap-8 text-left">
                <Card>
                    <CardContent className="p-6">
                    <GitBranch className="h-8 w-8 text-primary mb-4" />
                    <h3 class="font-semibold mb-3">Enables DRY Compliance</h3>
                    <p class="text-muted-foreground">
                        Just like DRY (Don't Repeat Yourself) in software development, compliance evidence is generated once from existing artifacts like code, tests, and operational data.
                    </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-6">
                    <Monitor className="h-8 w-8 text-primary mb-4" />
                    <h3 class="font-semibold mb-3">Individual Contributors Focus</h3>
                    <p class="text-muted-foreground">
                        Engineers, architects, QA specialists, and customer success teams can focus on their day jobs while the warehouse automatically gathers compliance evidence.
                    </p>
                    </CardContent>
                </Card>
                </div>
                <div class="mt-10">                    
                <a href="/compliance-as-code">
                    <Button size="lg">
                        Learn About Compliance as Code
                        <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                </a>                
                </div>
            </div>
            </div>
        </div>

        {/* CTA Section */}
        <div class="py-24">
            <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="mx-auto max-w-2xl text-center">
                <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                Ready to Build Your Evidence Warehouse?
                </h2>
                <p class="mx-auto mt-6 max-w-xl text-lg leading-8 text-muted-foreground">
                Transform your compliance approach with automated evidence collection and SQL-queryable data warehouses.
                </p>
                <div class="mt-10 flex items-center justify-center gap-x-6">

                <a href="/get-started">
                    <Button size="lg">
                        Get Started Today
                    </Button>
                </a>
                <a href="/contact">
                    <Button size="lg" variant="outline">
                        Schedule a Demo
                    </Button>
                </a>
                </div>
            </div>
            </div>
        </div>
    </main>
</Layout>