---
import SEO from "@/components/SEO.astro";
import UserLogin from "../components/zitadel/login";
import UserAttributes from "../content/loginAttributes.json";

let user: string;
if (Astro.cookies.get("User")?.value !== undefined) {
  user = Astro.cookies.get("User")!.value;
} else {
  user = "Unauth";
}
const userName = Astro.cookies.get("zitadel_user_name");
const userEmail = Astro.cookies.get("zitadel_user_email");

let rawJourneyKey = Astro.url.searchParams.get("journey");
const journeyKey = rawJourneyKey?.replace(/^"|"$/g, ""); // remove wrapping quotes

const selectedAttributes =
  journeyKey && journeyKey in UserAttributes
    ? UserAttributes[journeyKey as keyof typeof UserAttributes]
    : UserAttributes["default"];
//console.log(selectedAttributes)
const enableOpenObserve =
  import.meta.env.ENABLE_OPEN_OBSERVE !== undefined
    ? import.meta.env.ENABLE_OPEN_OBSERVE
    : false;
---

<head>
  <!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-NWW38GX7');</script>
<!-- End Google Tag Manager -->
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width" />

  <link rel="stylesheet" href="/assets/ds/tailwind/css/basic.css" />
  <link rel="stylesheet" href="/assets/ds/tailwind/css/tailwind.min.css" />
  <link
    rel="stylesheet"
    href="/assets/css/support-and-feedback/support-and-feedback.css"
  />
  {
    enableOpenObserve == "true" && (
      <script
        type="module"
        crossorigin="anonymous"
        src="/assets/scripts/index.756a5bbf.js"
      />
    )
  }
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width" />
  <link rel="icon" type="image/svg+xml" href="/favicon.png" />
  <SEO
    title="Login - Opsfolio - Compliance as a Service | SOC2, HIPAA, ISO Certification"
    description="Get SOC2, HIPAA, ISO certified fast with expert-guided compliance. 98% audit pass rate, 60% faster than DIY. Your Compliance System of Record."
    keywords="Policies, compliance, SOC2, HIPAA, ISO 27001, cybersecurity, audit, certification"
  />
</head>

<script define:vars={{ userName, userEmail }} is:inline>
  window.widgetConfig = {
    USER_EMAIL: userEmail ? userEmail.value : "Unauth",
    USER_FULL_NAME: userName ? userName.value : "Unauth",
  };
</script>

<!-- Google Tag Manager (noscript) -->
<noscript
  ><iframe
    src="https://www.googletagmanager.com/ns.html?id=GTM-NWW38GX7"
    height="0"
    width="0"
    style="display:none;visibility:hidden"></iframe></noscript
>
<!-- End Google Tag Manager (noscript) -->

<UserLogin
  client:only="react"
  journeyKey={journeyKey}
  attributes={selectedAttributes}
/>
