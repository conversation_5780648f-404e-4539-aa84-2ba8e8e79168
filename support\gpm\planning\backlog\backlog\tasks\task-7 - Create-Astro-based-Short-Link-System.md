---
id: task-7
title: Create Astro-based Short Link System
status: To Do
assignee:
  - Geo
  <PERSON> <PERSON><PERSON><PERSON>
  <PERSON> <PERSON><PERSON>
created_date: '2025-08-29 10:17'
labels: []
dependencies: []
priority: low
---

## Description

We need a **simple short link system** in the `www.opsfolio.com` repo that content team members (e.g., <PERSON>) can manage independently without dev intervention. The goal is to allow clean, memorable URLs (e.g., `/x/cmmc-self-assess`) that redirect to longer internal or external targets.  

If there’s no better solution already in place, we can implement this with less than 15 minutes of coding and testing using Astro.  

## Implementation Plan
Add a new file at:  
`src/pages/x/[slug].astro`  

Inside, implement a strongly typed redirect map (Astro 5 style, with TypeScript):  

```ts
import { AstroGlobal } from "astro";

// Define the shape of our redirect entries
type Redirect = {
  src: string;    // shortlink key
  target: string; // full target URL (internal or external)
};

// Central redirect registry Ravi can edit directly
const redirects: Redirect[] = [
  { src: "cmmc-self-assess", target: "/login?journey=cmmc-self-assessment" },
  { src: "home", target: "/" },
  { src: "docs", target: "/documentation/getting-started" },
  { src: "contact", target: "/contact" },
];

// Build a lookup map for fast access
const redirectMap: Record<string, string> = Object.fromEntries(
  redirects.map(r => [r.src, r.target])
);

export async function GET({ params, redirect }: AstroGlobal) {
  const slug = params.slug;
  const target = redirectMap[slug];

  if (!target) {
    // 404 if no match
    return new Response(`Shortlink not found: ${slug}`, { status: 404 });
  }

  // Permanent redirect
  return redirect(target, 301);
}
````

## How It Works

* Any link of the form `https://www.opsfolio.com/x/<slug>` looks up `<slug>` in the redirects array.
* If found → user is redirected to the full target.
* If not found → 404 returned.

## Examples

* `https://www.opsfolio.com/x/cmmc-self-assess` → `https://www.opsfolio.com/login?journey=cmmc-self-assessment`
* `https://www.opsfolio.com/x/docs` → `https://www.opsfolio.com/documentation/getting-started`
* `https://www.opsfolio.com/x/contact` → `https://www.opsfolio.com/contact`

## Benefits

* Minimal implementation effort
* Type-safe and future-proof
* Easy for non-devs to expand by editing a single redirects list

## Checklist

* [ ] Create `src/pages/x/[slug].astro`
* [ ] Add strongly typed redirect registry
* [ ] Verify `/x/<slug>` resolves correctly
* [ ] Test unauthenticated → login → redirect flow
* [ ] Document for Ravi/content team

@ajaykumaran @AjayKuruvath @shanil-sasikumar
