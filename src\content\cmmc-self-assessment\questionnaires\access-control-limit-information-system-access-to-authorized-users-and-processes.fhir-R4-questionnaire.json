{"resourceType": "Questionnaire", "meta": {"profile": ["http://hl7.org/fhir/4.0/StructureDefinition/Questionnaire"], "tag": [{"system": "InternalAssessment", "code": "AccessControl-L1", "display": "CMMC Level 1"}]}, "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "displayOrder", "language": "text/fhirpath", "expression": "2", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "2"}]}}], "version": "1.0", "name": "access_control_cmmc_l1_assessment", "title": "Access Control", "status": "draft", "date": "2025-08-25", "publisher": "Netspective", "description": "Access Control (Limit information system access to authorized users and processes)", "purpose": "This assessment evaluates how the organization limits system access to authorized users, processes, and devices. It ensures users are granted only the permissions needed to perform their job functions and that access to sensitive transactions and functions is restricted. The focus is on verifying external connections, controlling information on public-facing systems, and enforcing least privilege across the enterprise.", "approvalDate": "2025-08-25", "item": [{"item": [{"item": [{"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "text-box", "display": "Text Box"}]}}], "linkId": "599842914392", "text": "Notes / Evidence"}, {"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "184584712182", "text": "Implementation Status", "repeats": false, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 100}], "valueCoding": {"display": "Fully Implemented"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 50}], "valueCoding": {"display": "Partially Implemented"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 0}], "valueCoding": {"display": "Not Implemented"}}]}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "text-box", "display": "Text Box"}]}}], "linkId": "560290762218", "text": "Notes / Evidence"}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "2", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "2"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "744146359806", "text": "Do you have an Access Control Policy?", "repeats": false, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 100}], "valueCoding": {"display": "Yes"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 0}], "valueCoding": {"display": " No - if no, would you like help creating one for your company?"}}]}, {"item": [{"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "maxScore", "language": "text/fhirpath", "expression": "100", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "100"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "check-box", "display": "Check-box"}]}}], "linkId": "669545773690", "text": "Does your organization have a documented access control policy that addresses:", "repeats": true, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": "Purpose, scope, roles, and responsibilities"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": "Management commitment"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": "Coordination among organizational entities"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": "Compliance requirements"}}]}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "687383539343", "text": "Notes / Evidence"}, {"linkId": "************_helpText", "type": "display", "text": "Establish clear access control rules, including who can access what information, how access is granted, reviewed, and revoked", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "************", "prefix": "1.", "text": "Access Control Policy Elements"}, {"item": [{"type": "display", "linkId": "************", "text": "How many accounts are currently in your systems? "}, {"type": "integer", "linkId": "************", "text": "Active user accounts:", "repeats": false}, {"type": "integer", "linkId": "************", "text": "Inactive/disabled user accounts:", "repeats": false}, {"type": "integer", "linkId": "************", "text": "Service accounts:", "repeats": false}, {"type": "integer", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/maxValue", "valueInteger": 100}], "linkId": "************", "text": "Shared accounts:", "repeats": false}, {"linkId": "************_helpText", "type": "display", "text": "Maintain a detailed and up-to-date record of all user accounts, including their access levels and status", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "************", "prefix": "2.", "text": "User Account Registry"}, {"item": [{"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "************", "text": "How is the principle of least privilege implemented?", "repeats": false, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 100}], "valueCoding": {"display": "Fully implemented across all systems"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 50}], "valueCoding": {"display": "Partially implemented"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 0}], "valueCoding": {"display": "Not implemented"}}]}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "text-box", "display": "Text Box"}]}}], "linkId": "650863308787", "text": "Notes / Evidence"}, {"linkId": "159744780603_helpText", "type": "display", "text": "Grant users and systems only the minimum access necessary to perform their tasks", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "159744780603", "prefix": "3.", "text": "Principle of Least Privilege Implementation"}, {"item": [{"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "maxScore", "language": "text/fhirpath", "expression": "100", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "100"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "check-box", "display": "Check-box"}]}}], "linkId": "************", "text": "How are account lifecycle processes managed?", "repeats": true, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": "Automated identity management system"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": "Manual process with approval workflow"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": "Integration with HR systems"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": " Regular account reviews and recertification"}}]}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "text-box", "display": "Text Box"}]}}], "linkId": "************", "text": "Notes / Evidence"}, {"item": [{"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "************", "text": "How frequently are user accounts reviewed for validity and appropriate access?", "repeats": false, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": "Monthly"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": " Quarterly"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": "Annually"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": "Other (specify):"}}]}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "text-box", "display": "Text Box"}]}}], "linkId": "************", "text": "Notes / Evidence"}, {"linkId": "************_helpText", "type": "display", "text": "Regularly review user accounts to verify access is still appropriate and remove or adjust accounts as needed", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "************", "text": "Account Review Frequency"}, {"linkId": "************_helpText", "type": "display", "text": "Establish and follow formal procedures to manage user accounts throughout their lifecycle", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "************", "prefix": "4.", "text": "Account Management Processes"}, {"linkId": "************_helpText", "type": "display", "text": "Limit information system access to authorized users, processes acting on behalf of authorized users, or devices (including other information systems).", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "************", "text": "AC.L1-B.1.I - Authorized Access Control"}, {"item": [{"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "316234331937", "text": "Implementation Status", "repeats": false, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 100}], "valueCoding": {"display": "Fully Implemented"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 50}], "valueCoding": {"display": "Partially Implemented"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 0}], "valueCoding": {"display": "Not Implemented"}}]}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "text-box", "display": "Text Box"}]}}], "linkId": "983575859757", "text": "Notes / Evidence "}, {"item": [{"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "maxScore", "language": "text/fhirpath", "expression": "100", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "100"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "589002798804", "text": "How do you limit user access to specific transactions and functions?", "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Role-based access control (RBAC)"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Function-based permissions (create, read, update, delete)"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Application-level access controls"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Time-based access restrictions"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Location-based access restrictions"}}]}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "635610218995", "text": "Notes / Evidence "}, {"linkId": "899089109837_helpText", "type": "display", "text": "Implement controls to monitor and regulate transactions, ensuring only authorized actions are performed within systems and applications", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "899089109837", "prefix": "1.", "text": "Transaction Control Implementation"}, {"item": [{"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1.5", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1.5"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "maxScore", "language": "text/fhirpath", "expression": "100", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "100"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "check-box", "display": "Check-box"}]}}], "linkId": "525896610609", "text": "What types of functions are restricted based on user roles?", "repeats": true, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Administrative functions (user management, system configuration)"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Financial transactions and approvals"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Data export and bulk download functions"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Report generation and access"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "System-level commands and utilities"}}]}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "524794183862", "text": "Notes / Evidence "}, {"linkId": "561249826496_helpText", "type": "display", "text": "Limit system functions and capabilities based on user roles to ensure individuals can only perform actions necessary for their job responsibilities", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "561249826496", "prefix": "2.", "text": "Function Restrictions by Role"}, {"item": [{"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "maxScore", "language": "text/fhirpath", "expression": "100", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "100"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "check-box", "display": "Check-box"}]}}], "linkId": "859148329958", "text": "How are high-risk transactions authorized?", "repeats": true, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": "Manager approval required"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": "Two-person authorization"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": "Automated business rules and limits"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": "No special authorization required"}}]}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "988634546235", "text": "Notes / Evidence "}, {"linkId": "338456195634_helpText", "type": "display", "text": "Require formal approval before critical transactions are executed to prevent unauthorized or fraudulent activities.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "338456195634", "prefix": "3.", "text": "Transaction Authorization Requirements"}, {"linkId": "700726342337_helpText", "type": "display", "text": "Limit information system access to the types of transactions and functions that authorized users are permitted to execute.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "700726342337", "text": "AC.L1-B.1.II - Transaction & Function Control"}, {"item": [{"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "2", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "2"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "358071855489", "text": "Implementation Status", "repeats": false, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 100}], "valueCoding": {"display": " Fully Implemented"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 50}], "valueCoding": {"display": " Partially Implemented"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 0}], "valueCoding": {"display": " Not Implemented"}}]}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "108304278260", "text": "Notes / Evidence "}, {"item": [{"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "maxScore", "language": "text/fhirpath", "expression": "100.3", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "100.3"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "check-box", "display": "Check-box"}]}}], "linkId": "261758300502", "text": "What types of external systems does your organization connect to?", "repeats": true, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 14.29}], "valueCoding": {"display": "Cloud services (email, file storage, applications)"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 14.29}], "valueCoding": {"display": "Business partner networks"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 14.29}], "valueCoding": {"display": "Vendor/supplier systems"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 14.29}], "valueCoding": {"display": "Government systems and portals"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 14.29}], "valueCoding": {"display": "Personal devices (BYOD)"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 14.29}], "valueCoding": {"display": "Remote access system"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 14.29}], "valueCoding": {"display": "No external connections"}}]}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "681710464598", "text": "Notes / Evidence "}, {"linkId": "118413869969_helpText", "type": "display", "text": "Manage and secure connections to external systems to protect your network from unauthorized access and data breaches.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "118413869969", "prefix": "1.", "text": " External System Connections"}, {"item": [{"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "maxScore", "language": "text/fhirpath", "expression": "100", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "100"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "check-box", "display": "Check-box"}]}}], "linkId": "495111707033", "text": "How do you verify external system connections?", "repeats": true, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Digital certificates and PKI"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "VPN connections with authentication"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Firewall rules and IP restrictions"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Signed interconnection agreements"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Continuous monitoring and logging"}}]}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "895273053564", "text": "Notes / Evidence"}, {"linkId": "397995568740_helpText", "type": "display", "text": "Use verification techniques to confirm the identity and security of external connections before allowing access to your systems", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "397995568740", "prefix": "2.", "text": "Connection Verification Methods"}, {"item": [{"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "maxScore", "language": "text/fhirpath", "expression": "100", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "100"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "check-box", "display": "Check-box"}]}}], "linkId": "597499672942", "text": "What limitations are placed on external connections?", "repeats": true, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Time-based access restrictions"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Restrictions on data types that can be shared"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Limited to specific user groups"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Management approval required for each connection"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Comprehensive audit trails and logging"}}]}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "197339830339", "text": "Notes / Evidence"}, {"linkId": "354025378477_helpText", "type": "display", "text": "Define and enforce restrictions on external connections to minimize exposure and reduce security risks.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "354025378477", "prefix": "3.", "text": "Connection Control Limitations"}, {"linkId": "293091353060_helpText", "type": "display", "text": "Verify and control/limit connections to and use of external information systems.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "293091353060", "text": "AC.L1-B.1.III - External Connections"}, {"type": "group", "linkId": "942841103790", "text": "AC.L1-B.1.IV - Control Public Information", "item": [{"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "260717222110", "text": "Implementation Status", "repeats": false, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 100}], "valueCoding": {"display": "Fully Implemented"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 50}], "valueCoding": {"display": "Partially Implemented"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 0}], "valueCoding": {"display": "Not Implemented"}}]}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "813842964343", "text": "Notes / Evidence", "item": [{"linkId": "813842964343_helpText", "type": "display", "text": "", "_text": {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/rendering-xhtml", "valueString": "<!-- meta: {...} -->"}]}, "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"item": [{"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "maxScore", "language": "text/fhirpath", "expression": "99.96", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "99.96"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "check-box", "display": "Check-box"}]}}], "linkId": "************", "text": "What publicly accessible systems does your organization operate?", "repeats": true, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 16.66}], "valueCoding": {"display": "Company website"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 16.66}], "valueCoding": {"display": " Social media accounts"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 16.66}], "valueCoding": {"display": "Customer portals or self-service systems"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 16.66}], "valueCoding": {"display": "Corporate blog or news site"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 16.66}], "valueCoding": {"display": "Public forums or discussion boards"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 16.66}], "valueCoding": {"display": "No publicly accessible systems"}}]}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "************", "text": "Notes / Evidence"}, {"linkId": "501427838641_helpText", "type": "display", "text": "Secure and monitor systems that are accessible to the public to prevent unauthorized access and data leakage.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "501427838641", "prefix": "1.", "text": "Publicly Accessible Systems"}, {"item": [{"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "maxScore", "language": "text/fhirpath", "expression": "100", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "100"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "check-box", "display": "Check-box"}]}}], "linkId": "229261839700", "text": "How do you ensure FCI is not posted on public systems?", "repeats": true, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Pre-publication review and approval process"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Designated reviewers trained to identify FCI"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Automated content scanning for sensitive information"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Periodic audits of published content"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 20}], "valueCoding": {"display": "Procedures for rapid removal of inappropriate content"}}]}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "633971923340", "text": "Notes / Evidence"}, {"linkId": "786703783052_helpText", "type": "display", "text": "Establish regular procedures to review and validate information before it is published or shared", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "786703783052", "prefix": "2.", "text": "Content Review Process"}, {"item": [{"type": "display", "linkId": "624223914711", "text": "Who is authorized to post content to public systems?"}, {"type": "integer", "linkId": "374839487767", "text": "Number of authorized personnel:", "repeats": false}, {"type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "weight", "language": "text/fhirpath", "expression": "1", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "1"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/variable", "valueExpression": {"name": "maxScore", "language": "text/fhirpath", "expression": "100", "extension": [{"url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type", "valueString": "simple"}, {"url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax", "valueString": "100"}]}}, {"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "check-box", "display": "Check-box"}]}}], "linkId": "177243885107", "text": "Choose all that apply:", "repeats": true, "answerOption": [{"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": "Marketing department"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": "Communications/PR team"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": "Executive leadership"}}, {"extension": [{"url": "http://hl7.org/fhir/StructureDefinition/itemWeight", "valueDecimal": 25}], "valueCoding": {"display": "IT administrators"}}]}, {"type": "string", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Type your comments here..."}], "linkId": "163760226494", "text": "Notes / Evidence"}, {"linkId": "815496752107_helpText", "type": "display", "text": "Designate and control who is allowed to publish or distribute organizational information", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "group", "linkId": "815496752107", "prefix": "3.", "text": "Authorized Publishing Personnel"}, {"linkId": "942841103790_helpText", "type": "display", "text": "Control information posted or processed on publicly accessible information systems.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}]}