---
import Layout from "../../../layouts/Layout.astro";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

import { 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  Target,
  Cog,
  ShieldCheck,
  ArrowRight,
  Star,
  TrendingUp,
  Building2,
  Users,
  Building
} from "lucide-react";
const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Regime',"aria-disabled": true ,
  },
  {
    index: "2", text: "CMMC","aria-disabled": true, 
  },
  {
    index: "3", text: "CMMC Readiness" 
  },
];
---

<Layout title="Opsfolio - Compliance as a Service | SOC2, HIPAA, ISO Certification"
        description="Get SOC2, HIPAA, ISO certified fast with expert-guided compliance. 98% audit pass rate, 60% faster than DIY. Your Compliance System of Record."
        keywords="CMMC Level 1, CMMC Level 2, CMMC Level 3"
        url="/regime/cmmc/cmmc-readiness/"
        breadcrumbLinks={breadcrumbLinks}>
              
      {/* Hero Section */}
      <section class="relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5 pt-16 pb-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-4xl text-center">
            <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-5xl mb-6">
              Pass Your CMMC Requirements and Secure DoD Contracts — Done for You, With Guaranteed Outcomes
            </h1>
            <p class="text-xl text-muted-foreground mb-8">
              Expert-guided compliance with AI automation delivered by Opsfolio from Netspective.
            </p>
            <a href="/get-started">
              <Button size="lg" className="text-lg px-8 py-6 h-auto">
                  Get CMMC-Ready Now
                  <ArrowRight className="w-5 h-5" />
              </Button>
            </a>
          </div>
        </div>
      </section>

      {/* Risk & Urgency Section */}
      <section class="py-16 sm:py-20 bg-destructive/5">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="text-center mb-12">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl mb-8">
              The Risk of Waiting
            </h2>
          </div>
          <div class="grid gap-8 lg:grid-cols-3">
            <div class="flex items-start gap-4">
              <AlertTriangle className="w-8 h-8 text-destructive flex-shrink-0 mt-1" />
              <p class="text-lg text-muted-foreground">
                Without CMMC Level 1 compliance, you risk losing your DoD contracts.
              </p>
            </div>
            <div class="flex items-start gap-4">
              <AlertTriangle className="w-8 h-8 text-destructive flex-shrink-0 mt-1" />
              <p class="text-lg text-muted-foreground">
                Requirements are complex and confusing, with deadlines and prime contractors already demanding evidence.
              </p>
            </div>
            <div class="flex items-start gap-4">
              <AlertTriangle className="w-8 h-8 text-destructive flex-shrink-0 mt-1" />
              <p class="text-lg text-muted-foreground">
                For Level 1, failure to complete a correct self-assessment and SPRS submission can put your eligibility at risk.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Solution Overview */}
      <section class="py-16 sm:py-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <p class="text-lg leading-8 text-muted-foreground">
                More than software. Better than consultants. Opsfolio combines expert engineers, fractional CCOs, 
                and AI-powered automation to guarantee compliance outcomes. Unlike generic consultants or DIY tools, 
                Opsfolio eliminates duplicate work by generating compliance evidence from the workflows your team already runs.
              </p>
            </div>
            <div class="space-y-6">
              <div class="flex items-center gap-3">
                <Users className="w-6 h-6 text-primary" />
                <span class="font-semibold text-foreground">Expert-Guided Compliance</span>
              </div>
              <div class="flex items-center gap-3">
                <Cog className="w-6 h-6 text-primary" />
                <span class="font-semibold text-foreground">AI-Driven Policy & Evidence Tools</span>
              </div>
              <div class="flex items-center gap-3">
                <Target className="w-6 h-6 text-primary" />
                <span class="font-semibold text-foreground">Unified System of Record</span>
              </div>
              <div class="flex items-center gap-3">
                <Shield className="w-6 h-6 text-primary" />
                <span class="font-semibold text-foreground">Compliance-as-Code (automation, version control, machine attestable)</span>
              </div>
              <div class="flex items-center gap-3">
                <ShieldCheck className="w-6 h-6 text-primary" />
                <span class="font-semibold text-foreground">Powered by Surveilr (local-first, secure evidence warehouse)</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Packages / Pricing */}
      <section class="py-16 sm:py-20 bg-muted">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="text-center mb-12">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Packages for Every Contractor
            </h2>
          </div>
          <div class="grid lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {/* Micro Package */}
            <Card className="relative">
              <CardHeader>
                <CardTitle className="text-center">
                  <Building2 className="w-8 h-8 mx-auto mb-2 text-muted-foreground" />
                  Micro
                </CardTitle>
                <CardDescription className="text-center text-sm text-muted-foreground">
                  &lt;$1M revenue
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div class="text-center">
                  <div class="text-3xl font-bold text-foreground">$5K</div>
                </div>
                <ul class="space-y-2 text-sm">
                  <li class="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-primary mt-0.5 flex-shrink-0" />
                    Essential support to meet CMMC Level 1 requirements
                  </li>
                  <li class="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-primary mt-0.5 flex-shrink-0" />
                    Self-assessment guidance + SPRS submission package
                  </li>
                  <li class="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-primary mt-0.5 flex-shrink-0" />
                    Keeps you eligible to bid without over-investing
                  </li>
                </ul>
                <a href="/get-started" class="block mt-6">
                <Button variant="outline" className="w-full" >
                  Get Started 
                </Button>
                </a>
              </CardContent>
            </Card>

            {/* Mid Package - Highlighted */}
            <Card className="relative border-primary shadow-lg scale-105">
              <Badge className="absolute -top-3 left-1/2 -translate-x-1/2 bg-primary text-primary-foreground">
                Most Popular
              </Badge>
              <CardHeader>
                <CardTitle className="text-center">
                  <Users className="w-8 h-8 mx-auto mb-2 text-primary" />
                  Mid
                </CardTitle>
                <CardDescription className="text-center text-sm text-muted-foreground">
                  $1M–$10M revenue
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div class="text-center">
                  <div class="text-4xl font-bold text-primary">$20K</div>
                </div>
                <ul class="space-y-2 text-sm">
                  <li class="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-primary mt-0.5 flex-shrink-0" />
                    Turnkey CMMC Level 1 readiness designed for growth-oriented contractors
                  </li>
                  <li class="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-primary mt-0.5 flex-shrink-0" />
                    Complete gap analysis, policy authoring, and evidence collection
                  </li>
                  <li class="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-primary mt-0.5 flex-shrink-0" />
                    Positions you as a credible, competitive partner against larger primes
                  </li>
                  <li class="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-primary mt-0.5 flex-shrink-0" />
                    Minimizes disruption by automating evidence from your existing workflows
                  </li>
                </ul>
                <a href="/get-started" class="block mt-6">
                <Button className="w-full" >
                  Get Started
                </Button>
                </a>
              </CardContent>
            </Card>

            {/* Enterprise Package */}
            <Card className="relative">
              <CardHeader>
                <CardTitle className="text-center">
                  <Building className="w-8 h-8 mx-auto mb-2 text-muted-foreground" />
                  Enterprise
                </CardTitle>
                <CardDescription className="text-center text-sm text-muted-foreground">
                  $10M+ revenue
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div class="text-center">
                  <div class="text-lg font-semibold text-foreground">Custom Pricing</div>
                </div>
                <ul class="space-y-2 text-sm">
                  <li class="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-primary mt-0.5 flex-shrink-0" />
                    Custom white-glove engagements for CMMC Level 1 compliance with complex programs
                  </li>
                  <li class="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-primary mt-0.5 flex-shrink-0" />
                    VDI setup, logging/monitoring, access control, and more
                  </li>
                  <li class="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-primary mt-0.5 flex-shrink-0" />
                    Tailored to ensure resilience and compliance across multiple programs
                  </li>
                </ul>
                 <a href="/contact" class="block mt-6">
                <Button variant="outline" className="w-full">
                 Contact Us to Scope Engagement
                </Button>
                </a>
              </CardContent>
            </Card>
          </div>
          <p class="text-center text-base text-muted-foreground mt-8 max-w-4xl mx-auto">
            (Typical market costs for CMMC readiness range $30K–$100K+, and enterprise engagements often exceed $150K. 
            Opsfolio delivers faster, more reliable outcomes at lower total cost.)
          </p>
        </div>
      </section>

      {/* How It Works */}
      <section class="py-16 sm:py-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="text-center mb-12">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Simple, Proven Process
            </h2>
          </div>
          <div class="grid lg:grid-cols-3 gap-8">
            <div class="text-center">
              <div class="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                <CheckCircle className="w-8 h-8 text-primary" />
              </div>
              <h3 class="text-xl font-semibold text-foreground mb-2">
                Step 1: Gap Closure & Policy Development
              </h3>
              <p class="text-muted-foreground">
                We review your self-assessment, perform detailed gap analysis, and remediate compliance requirements.
              </p>
            </div>
            <div class="text-center">
              <div class="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                <Cog className="w-8 h-8 text-primary" />
              </div>
              <h3 class="text-xl font-semibold text-foreground mb-2">
                Step 2: Policy & Evidence Automation
              </h3>
              <p class="text-muted-foreground">
                Opsfolio generates audit-ready policies and collects machine-verifiable evidence.
              </p>
            </div>
            <div class="text-center">
              <div class="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                <Shield className="w-8 h-8 text-primary" />
              </div>
              <h3 class="text-xl font-semibold text-foreground mb-2">
                Step 3: White-Glove Audit Prep
              </h3>
              <p class="text-muted-foreground">
                Our compliance engineers ensure you're ready for DoD submission or certification.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Social Proof */}
      <section class="py-16 sm:py-20 bg-muted">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="text-center space-y-8">
            <div>
              <p class="text-xl font-semibold text-foreground mb-4">
                Built by Netspective, with decades of experience working with leading defense and federal organizations as well as major healthcare and critical infrastructure providers.
              </p>
              <div class="flex flex-wrap justify-center items-center gap-8 text-muted-foreground">
                <div class="flex items-center gap-2">
                  <Star className="w-5 h-5 text-primary" />
                  <span>Netspective's clients include Northrop Grumman, Department of Veterans Affairs, and Office of Management and Budget</span>
                </div>
                <div class="flex items-center gap-2">
                  <ShieldCheck className="w-5 h-5 text-primary" />
                  <span>Backed by Compliance-as-Code technology</span>
                </div>
                <div class="flex items-center gap-2">
                  <TrendingUp className="w-5 h-5 text-primary" />
                  <span>Faster, more reliable outcomes</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* ROI & Guarantee */}
      <section class="py-16 sm:py-20 bg-primary/5">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="text-center space-y-8">
            <div class="max-w-4xl mx-auto">
              <h2 class="text-3xl font-bold text-primary mb-4">
                For a $20K investment, you protect millions in DoD contracts.
              </h2>
              <Card className="inline-block bg-background border-primary">
                <CardContent className="p-6">
                  <div class="flex items-center gap-3">
                    <ShieldCheck className="w-8 h-8 text-primary" />
                    <div>
                      <p class="font-semibold text-foreground">Our Guarantee</p>
                      <p class="text-muted-foreground">If you implement our compliance recommendations and don't achieve CMMC Level 1 readiness, we keep working with you at no extra cost until you do.</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Closing CTA */}
      <section class="py-16 sm:py-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="text-center space-y-8">
            <div class="max-w-4xl mx-auto">
              <h2 class="text-2xl font-bold tracking-tight text-foreground sm:text-3xl mb-6">
                Don't wait until deadlines force you to scramble. Build readiness today with a proven system that combines expert guidance and automation.
              </h2>
              <a href="/get-started" >
                <Button size="lg" className="text-lg px-8 py-6 h-auto">
                    Get CMMC-Ready Now
                    <ArrowRight className="w-5 h-5" />
                </Button>
              </a>
            </div>
          </div>
        </div>
      </section>

</Layout>
