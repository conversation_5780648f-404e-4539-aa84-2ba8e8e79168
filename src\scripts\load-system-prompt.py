import requests
import logging
import argparse
from pathlib import Path
from urllib.parse import urljoin
from typing import Optional


logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s"
)
logger = logging.getLogger(__name__)



def read_prompt_from_file(file_path: Path) -> Optional[str]:
    """Read prompt text from a file."""
    if not file_path.exists():
        logger.error(f"File not found: {file_path}")
        return None
    try:
        content = file_path.read_text(encoding="utf-8").strip()
        logger.info(f"Read prompt from {file_path} ({len(content)} characters).")
        return content
    except Exception as e:
        logger.error(f"Error reading file {file_path}: {e}")
        return None


def update_workspace_system_prompt(workspace_slug: str, prompt: str, api_key: str, workspaces_url: str) -> bool:
    """Update the system prompt for a workspace via AnythingLLM API."""
    try:
        update_url = urljoin(
            workspaces_url.rstrip("/api/workspaces"),
            f"/api/workspace/{workspace_slug}/update"
        )
        payload = {
            "chatProvider": "default",
            "chatMode": "chat",
            "openAiHistory": 20,
            "openAiPrompt": prompt,
            "queryRefusalResponse": "There is no relevant information in this workspace to answer your query.",
            "openAiTemp": 0.7
        }
        headers = {"Content-Type": "application/json"}
        if api_key:
            headers["Authorization"] = f"Bearer {api_key}"

        res = requests.post(update_url, json=payload, headers=headers, timeout=15)
        res.raise_for_status()
        logger.info(f"Updated workspace: {workspace_slug}")
        return True
    except requests.RequestException as e:
        logger.error(f"Failed to update {workspace_slug}: {e}")
        return False

def parse_args():
    parser = argparse.ArgumentParser(
        description="Update AnythingLLM workspace system prompt from a local file."
    )
    parser.add_argument(
        "--slug", required=True, help="Workspace slug to update"
    )
    parser.add_argument(
        "--file", required=True, type=Path, help="Local file path to prompt (e.g., cmmc.prompt.md)"
    )
    parser.add_argument(
        "--api-key", required=False, help="AnythingLLM API key"
    )
    parser.add_argument(
        "--workspaces-url",
        default="http://ask.opsfolio.com/api/workspaces",
        help="AnythingLLM workspaces API URL"
    )
    return parser.parse_args()


def main():
    args = parse_args()

    prompt = read_prompt_from_file(args.file)
    if not prompt:
        logger.error("No prompt loaded. Exiting.")
        return

    updated = update_workspace_system_prompt(
        args.slug, prompt, args.api_key, args.workspaces_url
    )
    if updated:
        logger.info("Workspace prompt update complete.")
    else:
        logger.error("Workspace prompt update failed.")


if __name__ == "__main__":
    main()
