import * as rt from "./r4q-runtime.ts";
/**
 * @file policy-framework-assessment.auto.ts
 * @generated This file was auto-generated from the FHIR R4 Questionnaire "Policy Framework Assessment".
 * Do not edit this file manually; re-run the generator if the source Questionnaire changes.
 * Profiles: http://hl7.org/fhir/4.0/StructureDefinition/Questionnaire
 *
 * Normalizes LHC JSON and FHIR QuestionnaireResponse into the type-safe `PolicyFrameworkAssessment` interface.
 */

// this is the module signature, used by importers to identify the module
// using r4q-runtime.ts `moduleSignature` function
export const policyFrameworkAssessmentModuleSignature: rt.ModuleSignature = {
  title: "Policy Framework Assessment",
  filename: "policy-framework-assessment.auto.ts",
  titleCamel: "`policyFrameworkAssessment`",
  titlePascal: "`PolicyFrameworkAssessment`",
  titleKebab: "`policy-framework-assessment`",
  lhcFormResponseAdapterFnName: "policyFrameworkAssessmentLhcFormResponseAdapter",
  fhirQuestionnaireResponseAdapterFnName: "policyFrameworkAssessmentFhirQuestionnaireResponseAdapter",
  sourceTextConstName: "policyFrameworkAssessmentSource",
}

// deno-lint-ignore no-explicit-any
type Any = any;
/**
 * Form Help (from display/help controls):
 * - Establish a formal process to create, review, and approve policies to ensure they align with organizational goals and compliance requirements.
 * - Implement regular procedures to review and update policies to keep them current and effective.
 * - Provide ongoing training to employees to ensure understanding and compliance with organizational policies.
 * - Regularly monitor and assess adherence to policies to identify gaps and enforce compliance.
 * - Establish a process to document, review, and approve exceptions to policies while managing associated risks.
 * - Comprehensive assessment of your organization's policy management framework covering all CMMC Level 1 practices.
 */
/** Map of normalized property names to their source `linkId`. */
export const policyFrameworkAssessmentLinkIds = {
  whoIsResponsibleForDevelopingAndApprovingCmmcRelatedPolicies: "527949557496",
  notesEvidence: "576726184171",
  howFrequentlyAreCmmcRelatedPoliciesReviewedAndUpdated: "992068463537",
  notesEvidence2: "891438058183",
  whatTrainingIsProvidedToEmployeesOnCmmcRelatedPolicies: "472951321809",
  notesEvidence3: "948893743049",
  howIsComplianceWithCmmcRelatedPoliciesMonitored: "758349008850",
  notesEvidence4: "230314073532",
  howAreExceptionsToCmmcRelatedPoliciesManaged: "255836550808",
  notesEvidence5: "683517806081",
  additionalNotes: "795388091631"
} as const;

/** Normalized view of "Policy Framework Assessment" answers. */
export interface PolicyFrameworkAssessment {
  /**
   * Who is responsible for developing and approving CMMC-related policies?
   * linkId: 527949557496
   * FHIR type: choice
   * Section: Policy Framework Assessment > Policy Development and Approval
   * Options: "Chief Information Officer", "Chief Information Security Officer", "Chief Executive Officer", "Legal/Compliance Department", "IT Security Team"
   * Required: no
   */
  whoIsResponsibleForDevelopingAndApprovingCmmcRelatedPolicies?: ("Chief Information Officer" | "Chief Information Security Officer" | "Chief Executive Officer" | "Legal/Compliance Department" | "IT Security Team")[];

  /**
   * Notes / Evidence 
   * linkId: 576726184171
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: Policy Framework Assessment > Policy Development and Approval
   * Required: no
   */
  notesEvidence?: string;

  /**
   * How frequently are CMMC-related policies reviewed and updated?
   * linkId: 992068463537
   * FHIR type: choice
   * Section: Policy Framework Assessment > Policy Review and Update Procedures
   * Options: "Quarterly", "Bi-annually", "Annually", "When regulations change", "No formal schedule"
   * Required: no
   */
  howFrequentlyAreCmmcRelatedPoliciesReviewedAndUpdated?: "Quarterly" | "Bi-annually" | "Annually" | "When regulations change" | "No formal schedule";

  /**
   * Notes / Evidence 
   * linkId: 891438058183
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: Policy Framework Assessment > Policy Review and Update Procedures
   * Required: no
   */
  notesEvidence2?: string;

  /**
   * What training is provided to employees on CMMC-related policies?
   * linkId: 472951321809
   * FHIR type: choice
   * Section: Policy Framework Assessment > Employee Training on Policies
   * Options: "Initial security awareness training", "Role-specific policy training", "Annual refresher training", "just-in-time training for policy changes", "No formal training program"
   * Required: no
   */
  whatTrainingIsProvidedToEmployeesOnCmmcRelatedPolicies?: ("Initial security awareness training" | "Role-specific policy training" | "Annual refresher training" | "just-in-time training for policy changes" | "No formal training program")[];

  /**
   * Notes / Evidence 
   * linkId: 948893743049
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: Policy Framework Assessment > Employee Training on Policies
   * Required: no
   */
  notesEvidence3?: string;

  /**
   * How is compliance with CMMC-related policies monitored?
   * linkId: 758349008850
   * FHIR type: choice
   * Section: Policy Framework Assessment > Policy Compliance Monitoring
   * Options: " Regular internal audits", "Automated compliance monitoring", "Self-assessment questionnaires", "Manager reviews and attestations", "Third-party assessments"
   * Required: no
   */
  howIsComplianceWithCmmcRelatedPoliciesMonitored?: (" Regular internal audits" | "Automated compliance monitoring" | "Self-assessment questionnaires" | "Manager reviews and attestations" | "Third-party assessments")[];

  /**
   * Notes / Evidence 
   * linkId: 230314073532
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: Policy Framework Assessment > Policy Compliance Monitoring
   * Required: no
   */
  notesEvidence4?: string;

  /**
   * How are exceptions to CMMC-related policies managed?
   * linkId: 255836550808
   * FHIR type: choice
   * Section: Policy Framework Assessment > Policy Exception Management
   * Options: " Formal exception request process", "Risk assessment for exceptions", "Compensating controls for exceptions", "Regular review of approved exceptions", "No formal exception process"
   * Required: no
   */
  howAreExceptionsToCmmcRelatedPoliciesManaged?: (" Formal exception request process" | "Risk assessment for exceptions" | "Compensating controls for exceptions" | "Regular review of approved exceptions" | "No formal exception process")[];

  /**
   * Notes / Evidence 
   * linkId: 683517806081
   * FHIR type: string
   * Entry format: Type your comments here...
   * Section: Policy Framework Assessment > Policy Exception Management
   * Required: no
   */
  notesEvidence5?: string;

  /**
   * Additional Notes
   * linkId: 795388091631
   * FHIR type: text
   * Entry format: Any additional information about your policy framework.
   * Required: no
   */
  additionalNotes?: string;
}

/** Convert an LHC JSON response into a normalized PolicyFrameworkAssessment object. */
export function policyFrameworkAssessmentLhcFormResponseAdapter(input: Any): PolicyFrameworkAssessment {
  return {
    whoIsResponsibleForDevelopingAndApprovingCmmcRelatedPolicies: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "527949557496")) as PolicyFrameworkAssessment["whoIsResponsibleForDevelopingAndApprovingCmmcRelatedPolicies"],
    notesEvidence: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "576726184171")),
    howFrequentlyAreCmmcRelatedPoliciesReviewedAndUpdated: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "992068463537")) as PolicyFrameworkAssessment["howFrequentlyAreCmmcRelatedPoliciesReviewedAndUpdated"],
    notesEvidence2: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "891438058183")),
    whatTrainingIsProvidedToEmployeesOnCmmcRelatedPolicies: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "472951321809")) as PolicyFrameworkAssessment["whatTrainingIsProvidedToEmployeesOnCmmcRelatedPolicies"],
    notesEvidence3: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "948893743049")),
    howIsComplianceWithCmmcRelatedPoliciesMonitored: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "758349008850")) as PolicyFrameworkAssessment["howIsComplianceWithCmmcRelatedPoliciesMonitored"],
    notesEvidence4: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "230314073532")),
    howAreExceptionsToCmmcRelatedPoliciesManaged: rt.coerceOptionalStringArray(rt.findLhcValueByLinkId(input, "255836550808")) as PolicyFrameworkAssessment["howAreExceptionsToCmmcRelatedPoliciesManaged"],
    notesEvidence5: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "683517806081")),
    additionalNotes: rt.coerceOptionalString(rt.findLhcValueByLinkId(input, "795388091631")),
  };
}

/** Convert a FHIR QuestionnaireResponse into a normalized PolicyFrameworkAssessment object. */
export function policyFrameworkAssessmentFhirQuestionnaireResponseAdapter(qr: Any): PolicyFrameworkAssessment {
  return {
    whoIsResponsibleForDevelopingAndApprovingCmmcRelatedPolicies: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "527949557496")) as PolicyFrameworkAssessment["whoIsResponsibleForDevelopingAndApprovingCmmcRelatedPolicies"],
    notesEvidence: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "576726184171")),
    howFrequentlyAreCmmcRelatedPoliciesReviewedAndUpdated: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "992068463537")) as PolicyFrameworkAssessment["howFrequentlyAreCmmcRelatedPoliciesReviewedAndUpdated"],
    notesEvidence2: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "891438058183")),
    whatTrainingIsProvidedToEmployeesOnCmmcRelatedPolicies: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "472951321809")) as PolicyFrameworkAssessment["whatTrainingIsProvidedToEmployeesOnCmmcRelatedPolicies"],
    notesEvidence3: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "948893743049")),
    howIsComplianceWithCmmcRelatedPoliciesMonitored: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "758349008850")) as PolicyFrameworkAssessment["howIsComplianceWithCmmcRelatedPoliciesMonitored"],
    notesEvidence4: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "230314073532")),
    howAreExceptionsToCmmcRelatedPoliciesManaged: rt.coerceOptionalStringArray(rt.findQrAnswersByLinkId(qr, "255836550808")) as PolicyFrameworkAssessment["howAreExceptionsToCmmcRelatedPoliciesManaged"],
    notesEvidence5: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "683517806081")),
    additionalNotes: rt.coerceOptionalString(rt.findQrAnswerByLinkId(qr, "795388091631")),
  };
}

/**
 * NOTE TO DEVELOPERS:
 * -------------------
 * This Interpreter class is provided only as an EXAMPLE scaffold to demonstrate
 * how to consume the normalized type-safe interface generated for this
 * Questionnaire. It shows minimal factories (`fromLhc`, `fromQuestionnaireResponse`)
 * and convenience methods (`validateRequiredFields`, `assessReadiness`) but it is
 * NOT intended for production use.
 *
 * In real applications:
 * - Treat this class as SAMPLE CODE only.
 * - Replace or extend it with proper business logic, rules engines, or validation
 *   frameworks appropriate to your domain.
 * - Do not rely on the simplistic readiness scoring or validation in production
 *   scenarios; they are illustrative, not authoritative.
 *
 * Best practice: use the generated TypeScript interface (`PolicyFrameworkAssessment`) as your
 * contract for normalized data, then integrate with your own rules processors,
 * compliance engines, or plain TypeScript/JavaScript functions as needed.
 */
export class PolicyFrameworkAssessmentInterpreter {
  constructor(readonly value: PolicyFrameworkAssessment) { }

  /** Factory: build from LHC JSON. */
  static fromLhcFormResponse(input: Any): PolicyFrameworkAssessmentInterpreter {
    return new PolicyFrameworkAssessmentInterpreter(policyFrameworkAssessmentLhcFormResponseAdapter(input));
  }

  /** Factory: build from FHIR QuestionnaireResponse. */
  static fromQuestionnaireResponse(qr: Any): PolicyFrameworkAssessmentInterpreter {
    return new PolicyFrameworkAssessmentInterpreter(policyFrameworkAssessmentFhirQuestionnaireResponseAdapter(qr));
  }

  /** Check required fields and report any missing or blank. */
  validateRequiredFields(): { ok: boolean; missing: Array<keyof PolicyFrameworkAssessment> } {
    const missing: Array<keyof PolicyFrameworkAssessment> = [];
    const req: Array<keyof PolicyFrameworkAssessment> = [];
    for (const k of req) {
      const v = (this.value as Any)[k];
      if (rt.isBlank(v)) missing.push(k);
    }
    return { ok: missing.length === 0, missing };
  }

  /**
   * Assess readiness with a simple completeness score. This is meant to be
   * used to help understand how complete the types are and serves as an
   * example of how to use the generated code.
   * - requiredCovered: percentage of required fields that are non-blank
   * - overallFilled: count of non-blank fields among all known properties
   */
  assessReadiness(): {
    formTitle: string;
    requiredCovered: number; // 0..1
    totalRequired: number;
    totalFilled: number;
    totalFields: number;
    missingRequired: Array<keyof PolicyFrameworkAssessment>;
  } {
    const req: Array<keyof PolicyFrameworkAssessment> = [];
    const all: Array<keyof PolicyFrameworkAssessment> = ["whoIsResponsibleForDevelopingAndApprovingCmmcRelatedPolicies", "notesEvidence", "howFrequentlyAreCmmcRelatedPoliciesReviewedAndUpdated", "notesEvidence2", "whatTrainingIsProvidedToEmployeesOnCmmcRelatedPolicies", "notesEvidence3", "howIsComplianceWithCmmcRelatedPoliciesMonitored", "notesEvidence4", "howAreExceptionsToCmmcRelatedPoliciesManaged", "notesEvidence5", "additionalNotes"];

    let reqFilled = 0;
    const missingReq: Array<keyof PolicyFrameworkAssessment> = [];
    for (const k of req) {
      const v = (this.value as Any)[k];
      if (!rt.isBlank(v)) reqFilled++;
      else missingReq.push(k);
    }

    let totalFilled = 0;
    for (const k of all) {
      if (!rt.isBlank((this.value as Any)[k])) totalFilled++;
    }

    return {
      formTitle: "Policy Framework Assessment",
      requiredCovered: req.length ? reqFilled / req.length : 1,
      totalRequired: req.length,
      totalFilled,
      totalFields: all.length,
      missingRequired: missingReq,
    };
  }
}

/** The original source */
export const policyFrameworkAssessmentSource = `{
  "resourceType": "Questionnaire",
  "meta": {
    "profile": [
      "http://hl7.org/fhir/4.0/StructureDefinition/Questionnaire"
    ],
    "tag": [
      {
        "display": "Policy Assessment"
      },
      {
        "display": "CMMC Level 1"
      },
      {
        "display": "Cybersecurity Governance"
      },
      {
        "display": "Policy Compliance"
      }
    ]
  },
  "extension": [
    {
      "url": "http://hl7.org/fhir/StructureDefinition/variable",
      "valueExpression": {
        "name": "displayOrder",
        "language": "text/fhirpath",
        "expression": "8",
        "extension": [
          {
            "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
            "valueString": "simple"
          },
          {
            "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
            "valueString": "8"
          }
        ]
      }
    }
  ],
  "version": "1.0",
  "name": "policy_framework_assessment",
  "title": "Policy Framework Assessment",
  "status": "draft",
  "date": "2025-08-26",
  "publisher": "Netspective",
  "description": "Policy Framework Assessment (Policy Implementation - All CMMC Level 1 Practices)",
  "purpose": "This assessment evaluates whether the organization has established, documented, and enforced safeguarding policies consistent with FAR 52.204-21 requirements. It examines how safeguarding responsibilities are assigned, communicated, and flowed down to subcontractors. The focus is on ensuring that overarching security policies provide the governance needed to enforce access control, authentication, media handling, physical security, communications protection, and system integrity across the organization.",
  "item": [
    {
      "item": [
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "100",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "100"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "527949557496",
              "text": "Who is responsible for developing and approving CMMC-related policies?",
              "repeats": true,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Chief Information Officer"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Chief Information Security Officer"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Chief Executive Officer"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Legal/Compliance Department"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "IT Security Team"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "576726184171",
              "text": "Notes / Evidence "
            },
            {
              "linkId": "590810573907_helpText",
              "type": "display",
              "text": "Establish a formal process to create, review, and approve policies to ensure they align with organizational goals and compliance requirements.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "590810573907",
          "prefix": "1.",
          "text": "Policy Development and Approval"
        },
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "100",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "100"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "radio-button",
                        "display": "Radio Button"
                      }
                    ]
                  }
                }
              ],
              "linkId": "992068463537",
              "text": "How frequently are CMMC-related policies reviewed and updated?",
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Quarterly"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Bi-annually"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Annually"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "When regulations change"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "No formal schedule"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "891438058183",
              "text": "Notes / Evidence "
            },
            {
              "linkId": "441079114846_helpText",
              "type": "display",
              "text": "Implement regular procedures to review and update policies to keep them current and effective.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "441079114846",
          "prefix": "2.",
          "text": "Policy Review and Update Procedures"
        },
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "100",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "100"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "472951321809",
              "text": "What training is provided to employees on CMMC-related policies?",
              "repeats": true,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Initial security awareness training"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Role-specific policy training"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Annual refresher training"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "just-in-time training for policy changes"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "No formal training program"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "948893743049",
              "text": "Notes / Evidence "
            },
            {
              "linkId": "401642968533_helpText",
              "type": "display",
              "text": "Provide ongoing training to employees to ensure understanding and compliance with organizational policies.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "401642968533",
          "prefix": "3.",
          "text": "Employee Training on Policies"
        },
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "100",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "100"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "758349008850",
              "text": "How is compliance with CMMC-related policies monitored?",
              "repeats": true,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": " Regular internal audits"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Automated compliance monitoring"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Self-assessment questionnaires"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Manager reviews and attestations"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Third-party assessments"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "230314073532",
              "text": "Notes / Evidence "
            },
            {
              "linkId": "237023742748_helpText",
              "type": "display",
              "text": "Regularly monitor and assess adherence to policies to identify gaps and enforce compliance.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "237023742748",
          "prefix": "4.",
          "text": "Policy Compliance Monitoring"
        },
        {
          "item": [
            {
              "type": "choice",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "weight",
                    "language": "text/fhirpath",
                    "expression": "1",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "1"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/variable",
                  "valueExpression": {
                    "name": "maxScore",
                    "language": "text/fhirpath",
                    "expression": "100",
                    "extension": [
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/expression-editor-variable-type",
                        "valueString": "simple"
                      },
                      {
                        "url": "http://lhcforms.nlm.nih.gov/fhirExt/simple-syntax",
                        "valueString": "100"
                      }
                    ]
                  }
                },
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "check-box",
                        "display": "Check-box"
                      }
                    ]
                  }
                }
              ],
              "linkId": "255836550808",
              "text": "How are exceptions to CMMC-related policies managed?",
              "repeats": true,
              "answerOption": [
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": " Formal exception request process"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Risk assessment for exceptions"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Compensating controls for exceptions"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "Regular review of approved exceptions"
                  }
                },
                {
                  "extension": [
                    {
                      "url": "http://hl7.org/fhir/StructureDefinition/itemWeight",
                      "valueDecimal": 20
                    }
                  ],
                  "valueCoding": {
                    "display": "No formal exception process"
                  }
                }
              ]
            },
            {
              "type": "string",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
                  "valueString": "Type your comments here..."
                }
              ],
              "linkId": "683517806081",
              "text": "Notes / Evidence "
            },
            {
              "linkId": "260429244098_helpText",
              "type": "display",
              "text": "Establish a process to document, review, and approve exceptions to policies while managing associated risks.",
              "extension": [
                {
                  "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
                  "valueCodeableConcept": {
                    "coding": [
                      {
                        "system": "http://hl7.org/fhir/questionnaire-item-control",
                        "code": "help",
                        "display": "Help-Button"
                      }
                    ],
                    "text": "Help-Button"
                  }
                }
              ]
            }
          ],
          "type": "group",
          "linkId": "260429244098",
          "prefix": "5.",
          "text": "Policy Exception Management"
        },
        {
          "linkId": "364455629781_helpText",
          "type": "display",
          "text": "Comprehensive assessment of your organization's policy management framework covering all CMMC Level 1 practices.",
          "extension": [
            {
              "url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl",
              "valueCodeableConcept": {
                "coding": [
                  {
                    "system": "http://hl7.org/fhir/questionnaire-item-control",
                    "code": "help",
                    "display": "Help-Button"
                  }
                ],
                "text": "Help-Button"
              }
            }
          ]
        }
      ],
      "type": "group",
      "linkId": "364455629781",
      "text": "Policy Framework Assessment"
    },
    {
      "type": "text",
      "extension": [
        {
          "url": "http://hl7.org/fhir/StructureDefinition/entryFormat",
          "valueString": "Any additional information about your policy framework."
        }
      ],
      "linkId": "795388091631",
      "text": "Additional Notes"
    }
  ]
}`;