import { join } from "jsr:@std/path";
import { generateTsCodeForQuestionnaire as generateTsCodeRemote } from "https://raw.githubusercontent.com/netspective-labs/attest/refs/tags/v0.0.6/lib/fhir-r4-questionnaire/r4q-resource-code-gen.ts";

async function main() {
    const fixtures = [
        "access-control-limit-information-system-access-to-authorized-users-and-processes.fhir-R4-questionnaire.json",
        "company-information.fhir-R4-questionnaire.json",
        "identification-authentication-verify-identities-of-users-and-processes.fhir-R4-questionnaire.json",
        "media-protection-protect-information-on-digital-and-non-digital-media.fhir-R4-questionnaire.json",
        "physical-protection-limit-physical-access-to-information-systems-and-facilities.fhir-R4-questionnaire.json",
        "policy-framework-assessment-policy-implementation-all-cmmc-level-1-practices.fhir-R4-questionnaire.json",
        "system-communications-protection-monitor-control-and-protect-organizational-communications.fhir-R4-questionnaire.json",
        "system-information-integrity-identify-report-and-correct-information-system-flaws.fhir-R4-questionnaire.json",
    ];

    const outDir = "./src/lib/cmmc-self-assessment";
    const questionnairesDir = "./src/content/cmmc-self-assessment/questionnaires";

    for (const src of fixtures) {
        const inputPath = join(questionnairesDir, src);
        const stdout: string[] = [];
        try {
            const result = await generateTsCodeRemote(inputPath, {
                outDir,
                force: true,
                includeSrc: true,
            }, stdout);
            if (result) {
                throw result;
            }
        } catch (error) {
            console.error(`Error processing ${src}:`, error);
        }
    }
}

await main();