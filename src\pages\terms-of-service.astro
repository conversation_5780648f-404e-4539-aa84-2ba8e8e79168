---
import { getEntry } from "astro:content";
import Layout from "../layouts/Layout.astro";

const page = await getEntry('pages', 'terms-of-service');

if (!page) {
  throw new Error('Terms of Service content not found.');
}
const { title, 
  lastUpdated,  
  description,
  metaTitle,
  metaDescription,
  keywords = [], } = page.data;

// This gives you the compiled MDX/Markdown content component
const { Content } = await page.render();
const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Terms of Service',"aria-disabled": true ,
  }
];
 
---
<Layout
  breadcrumbLinks={breadcrumbLinks}
  title={metaTitle || title}
  description={metaDescription || description}
  keywords={keywords.join(', ')}
  url="/terms-of-service/">
<main>
  <div class="py-24 sm:py-32">
    <div class="mx-auto max-w-4xl px-6 lg:px-8">
      <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-5xl mb-8">
        {title}
      </h1>
      <div class="text-sm text-muted-foreground mb-8">
        Last updated: {lastUpdated}
      </div>

      <div class="prose prose-lg max-w-none text-muted-foreground markdown-content">
        <Content />
      </div>
    </div>
  </div>
</main>
</Layout>