---
title: "3 CMMC Myths That Could Trigger DoD Contract Losses and Legal Liability"
description: "Defense contractors face real risks and penalties for cybersecurity noncompliance. This article debunks three costly CMMC myths, explains the legal and business risks, and shows how Opsfolio helps safeguard contracts and reputation."
author: "<PERSON>"
date: "2025-09-04"
category: "Strategy & Risk"
readingTime: "8 min read"
featured: false
---

## The Myths That Cost Millions

Defense contractors are no longer dealing with abstract warnings about cybersecurity compliance. They are facing real, multimillion-dollar consequences for failing to comply with federal regulations.

In an settlement dated April 2025, Raytheon and its subcontractor Nightwing Group agreed to pay $84 million to resolve False Claims Act allegations that they misrepresented compliance with federal cybersecurity requirements ([DOJ Press Release](https://www.justice.gov/opa/pr/raytheon-companies-and-nightwing-group-pay-84m-resolve-false-claims-act-allegations-relating)). In a March 2025 settlement, MorseCorp agreed to pay $4.6 million to settle allegations that it falsely claimed adherence to Department of Defense cybersecurity standards ([DOJ Press Release](https://www.justice.gov/usao-ma/pr/defense-contractor-morsecorp-inc-agrees-pay-46-million-settle-cybersecurity-fraud)).

They involved the same core regulations that CMMC is designed to standardize across the defense industrial base: FAR 52.204-21 (basic safeguarding requirements for federal contractors) and NIST SP 800-171 (protection of Controlled Unclassified Information).

### What is CMMC, and why does it matter?

CMMC is the Department of Defense’s comprehensive framework for ensuring that every contractor meets essential cybersecurity obligations. It consolidates requirements from FAR and NIST into a tiered certification model, tying contract eligibility directly to compliance. The Raytheon and MorseCorp cases prove that the government is already willing to pursue contractors who cut corners on these same standards. 

With CMMC enforcement beginning in 2025, the risks of ignoring compliance will only grow more severe. From this fall forward, contractors who ignore CMMC will risk losing contracts and may even face the same legal liability already costing major defense firms millions.

This article breaks down three of the most dangerous CMMC myths, explains why they put your business at risk, and shows how to protect yourself before it’s too late.


## Myth 1: “Level 1 is easy, we don’t need outside help.”

Many executives assume Level 1 is a light lift, little more than basic cyber hygiene. In reality, it covers 15 requirements designed to safeguard Federal Contract Information (FCI), and contractors must self-attest that these practices are in place.

That means not just putting technology in place, but also retaining evidence that each control is being performed. The CMMC Level 1 Assessment Guide makes clear that proof is required for all 15 practices, from written policies to system configuration logs.

The DoD Inspector General’s 2023 report on contractor compliance with NIST SP 800-171—the same framework CMMC Level 2 is based on—shows why even “basic” controls are not easy in practice. The IG identified systemic failures in access control fundamentals, such as weak password enforcement and inactive accounts that were not disabled ([DoD IG Report DODIG-2024-031](https://media.defense.gov/2023/Dec/04/**********/-1/-1/1/DODIG-2024-031%20SECURE.PDF)). These failures in basic identity and account management highlight how contractors often stumble on even straightforward practices.

The lesson is clear: if organizations are struggling with access controls that CMMC considers foundational, no one should assume Level 1 can be handled casually.

Opsfolio combines human experts with AI support to guide you through compliance and evidence collection, so you can self-assess with confidence rather than gamble with contract eligibility.

Even if you’re not ready to engage with our solutions yet, you can still benefit from our expert guidance. Start by exploring our post on [compliance surface area reduction](https://opsfolio.com/blog/reduce-csa-gfe-descoping/) to learn practical techniques for preparing your environment.


## Myth #2: “Self-attestation is a low-stakes box check.”

“Self-attestation” is not a casual form. It’s a formal declaration tied to contract eligibility. Under the DoD’s CMMC program, Level 1 requires an annual self-assessment and an annual affirmation by a senior official that the company meets the 15 basic safeguarding requirements in FAR 52.204-21 ([U.S. Department of Defense CIO](https://dodcio.defense.gov/cmmc/About/)).

Your self-assessment and affirmation directly influence your eligibility for award. And misstatements have consequences. The Department of Justice’s Civil Cyber-Fraud Initiative (CCFI) specifically uses the False Claims Act (FCA) to pursue cybersecurity misrepresentations by federal contractors. The DOJ uses the FCA as its primary civil tool for prosecuting misuse of federal funds due to false claims ([Department of Justice](https://www.justice.gov/archives/opa/pr/deputy-attorney-general-lisa-o-monaco-announces-new-civil-cyber-fraud-initiative)).

We are already seeing FCA outcomes rooted in cybersecurity noncompliance, such as the MORSE and Raytheon cases discussed earlier. These cases show how cybersecurity promises embedded in contracts and reflected in your attestations can translate into real liability when they’re wrong.

Legal practitioners are warning contractors to treat CMMC self-attestations accordingly. WilmerHale notes that the final CMMC rule “will bring both compliance challenges and increased False Claims Act (FCA) risk,” precisely because DOJ is already using the CCFI to enforce cyber representations. The takeaway: self-attestation is high-stakes legal exposure, not clerical paperwork ([WilmerHale](https://www.wilmerhale.com/en/insights/client-alerts/20241024-final-dod-cybersecurity-maturity-model-certification-rule-will-bring-compliance-challenges-increased-false-claims-act-risk)).

### How to de-risk self-attestation
This is where Opsfolio’s human experts, supported by AI, provide leverage. We provide a Self-Assessment that surfaces gaps against the exact controls you’re affirming so your executive can submit a truthful, defensible affirmation in SPRS and you avoid the dual threat of contract ineligibility and FCA liability. 



## Myth #3: “Spreadsheets and email chains are enough to manage compliance.”

Many contractors believe compliance can be managed through basic spreadsheets, emails, and improvised documentation systems. But CMMC compliance demands far more rigor. For CMMC, compliance proof matters just as much as the technology practice.

### Evidence Required, Not Just Implementation

According to the CMMC Assessment Guide—Level 1, achieving compliance requires a result of “MET” or “NOT APPLICABLE” for *every* practice. Crucially, to earn a “MET,” you must back your self‑assessment with actual evidence:

> "All applicable objectives for the security requirement are satisfied based on 
evidence. All evidence must be in final form and not draft. Unacceptable forms of 
evidence include working papers, drafts, and unofficial or unapproved policies. For each 
security requirement marked MET, it is best practice to record statements that indicate 
the response conforms to all objectives and document the appropriate evidence to 
support the response." [CMMC Assessment Guide Level 1](https://dodcio.defense.gov/Portals/0/Documents/CMMC/AssessmentGuideL1.pdf?utm_source=chatgpt.com)

This makes it clear: compliance is not confirmed by telling auditors you did it later; it’s by having a record of the documentation now. It should be complete, official, and audit-ready. That's far more than what spreadsheets and email threads typically deliver, especially under scrutiny.

### Why Manual Tracking Falls Short

* **Fragmented documentation**: Emails and disjointed files make it easy to lose track of which practices have evidence.
* **Lack of structure**: Without a defined system, evidence gets buried in threads or outdated spreadsheets.
* **Audit risk**: Even if control is in place, failure to produce acceptable final evidence per the guide can result in assessment errors and “Not MET” findings.

In other words, compliance means documented and defensible evidence, not just technical implementation or informal notes.

With Opsfolio’s centralized evidence collection hub, all of your compliance evidence flows into one place—organized, finalized, and audit-ready.


## Don’t Let Myths Destroy Your Business

Three myths continue to circulate in the defense industry: that Level 1 is easy, that self-attestation is harmless, and that spreadsheets are enough. These myths have the potential to lead to lost contracts, millions in fines, and reputational damage.

The truth is simple: Level 1 requires a robust compliance posture. Self-attestation carries FCA liability. Manual tracking fails audits. With CMMC requirements starting to appear in contracts in 2025, the window for preparation is closing fast.

CMMC isn’t optional. It’s a survival requirement for your business.

The good news is that you don’t have to face it alone. Start with Opsfolio’s Self-Assessment Tool](https://opsfolio.com/x/cmmc-self-assess) to see exactly where you stand. Then use Opsfolio’s compliance suite to centralize evidence, automate reporting, and stay ahead of auditors before hesitation costs you everything.
