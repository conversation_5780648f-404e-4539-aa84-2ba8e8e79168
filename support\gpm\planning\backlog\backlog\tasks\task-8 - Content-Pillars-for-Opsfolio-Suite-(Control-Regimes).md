---
id: task-8
title: Content Pillars for Opsfolio Suite (Control Regimes)
status: In Progress
assignee: []
created_date: '2025-09-09 09:38'
labels: []
dependencies: []
priority: high
---

## Description

We need to build **content pillars** on Opsfolio.com to attract and inform customers about how Opsfolio Suite aligns with different **security and compliance control regimes**.  

These pillars will highlight Opsfolio’s strengths in compliance automation while dynamically showcasing available controls from the Surveilr and AI Features


## Scope  

### Control Regimes to Cover  
- HIPAA  
- CMMC  
- SOC2  
- ISO 27001  
*(expandable in future as new regimes are added)*  

### Content Pillars (Astro Pages)  
1. **Static Content**  
   - Overview of each control regime.  
   - How Opsfolio Suite helps organizations comply.  
   - Benefits of using Opsfolio vs. traditional GRC tools.  

2. **Dynamic Control Listings**  
   - Pull controls dynamically from **Resource Surveillance Database**.  
   - Display controls mapped to each regime.  
   - Link to **patterns** and **unit test cases** that validate compliance.  


## Requirements  

- [ ] Create an **Astro page** for each control regime under `/controls/`.  
- [ ] Include **static content pillar text** describing the regime + Opsfolio approach.  
- [ ] Add a **dynamic section** that queries and lists regime-specific controls from the Resource Surveillance Database.  
- [ ] Ensure controls link back to patterns and unit test cases already available.  
- [ ] Design with SEO in mind (content pillars should attract organic traffic).  
- [ ] Ensure consistent look & feel with existing Opsfolio.com design.  

---

## Acceptance Criteria  

- [ ] Astro pages live for HIPAA, CMMC, SOC2, ISO 27001.  
- [ ] Each page includes **static content pillar text** + **dynamic control listings**.  
- [ ] Controls correctly load from the Resource Surveillance Database.  
- [ ] Pages are indexed and optimized for SEO.  
- [ ] Customer can navigate from control regimes to Opsfolio Suite solutions.  

---

## Notes  
- Resource Surveillance Database already contains controls structured as **patterns** and **unit test cases**.  
- Future work: Add **case studies and reports** linked to these control regimes.  
- This will serve as the foundation for a **content-led growth strategy** for Opsfolio.com.
