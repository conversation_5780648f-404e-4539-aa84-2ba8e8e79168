## POML Integration with Surveilr

This project provides a workflow for converting POML files into multiple output formats (.md, .json) and ingesting the resulting content into the rssd database.
It uses a capturable executable pipeline designed for seamless integration with Surveilr.

## Directory Structure
```
tools/
├── source/           # Place all .poml files here
│   ├── compliance.poml
│   └── compliance2.poml
├── generated/        # Generated .md files (auto-created)
│   ├── compliance.md
│   └── compliance2.md
├── poml-build.surveilr.ts    # Converts .poml to .md
└── e2e-test.surveilr-SQL.ts  # Generates SQL from .md files
```
## Setup

Grant execute permissions to the build scripts:
```bash
chmod +x ./poml-build.surveilr.ts

chmod +x ./e2e-test.surveilr-SQL.ts
```

## POML Processing Commands

### Execute SQL with surveilr
```bash
# Process with surveilr ingest tasks
deno run -A e2e-test.surveilr-SQL.ts > output.sql | surveilr ingest tasks
```

### Execute SQL directly with SQLite
```bash
# Load SQL into SQLite database
cat output.sql | sqlite3 resource-surveillance.sqlite.db
```
