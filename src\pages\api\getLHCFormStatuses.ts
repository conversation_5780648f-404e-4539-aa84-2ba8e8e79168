import { readdir, stat } from "fs/promises";
import { existsSync } from "fs";
import type { APIContext } from "astro";

const tenantID = import.meta.env.PUBLIC_ZITADEL_ORGANIZATION_ID;

export async function POST({ request }: APIContext) {
    try {
        const { userID, sessionName } = await request.json();

        if (!userID) {
            return new Response(JSON.stringify({ error: "Missing userID" }), {
                status: 400,
                headers: { "Content-Type": "application/json" },
            });
        }

        const userDir = `./src/data/lforms/${tenantID}/${userID}`;

        // Build list of session dirs: either all, or just the requested one
        let sessionDirs: { name: string; isDirectory: () => boolean }[] = [];

        if (sessionName) {
            const sessionPath = `${userDir}/${sessionName}`;
            if (existsSync(sessionPath)) {
                sessionDirs.push({ name: sessionName, isDirectory: () => true });
            } else {
                return new Response(
                    JSON.stringify({ error: `Session ${sessionName} not found` }),
                    { status: 404, headers: { "Content-Type": "application/json" } }
                );
            }
        } else {
            sessionDirs = await readdir(userDir, { withFileTypes: true });
        }

        // Track latest file per form
        const statusMap: Record<string, "submit" | "save" | "none"> = {};
        const latestMap: Record<string, number> = {}; // baseFile -> latest mtime

        for (const session of sessionDirs) {
            if (!session.isDirectory()) continue;
            const responsesDir = `${userDir}/${session.name}`;

            let files: string[];
            try {
                files = await readdir(responsesDir);
            } catch {
                continue; // skip if no responses dir
            }

            for (const file of files) {
                if (file.startsWith("fleetfolio-eaa.fhir-R4-questionnaire")) {
                    continue;
                }
                const parts = file.split(".");
                if (parts.length < 3) continue; // not a valid response file

                const baseFile = `${parts[0]}.${parts[1]}`;
                const type = parts[2] as "submit" | "save";

                const filePath = `${responsesDir}/${file}`;
                const fileStat = await stat(filePath);
                const mtime = fileStat.mtimeMs;

                // Only update if this file is newer
                if (!latestMap[baseFile] || mtime > latestMap[baseFile]) {
                    latestMap[baseFile] = mtime;
                    statusMap[baseFile] = type;
                }
            }
        }

        return new Response(JSON.stringify(statusMap), {
            status: 200,
            headers: { "Content-Type": "application/json" },
        });
    } catch (err) {
        console.error("Error getting response files:", err);
        return new Response(
            JSON.stringify({ error: "Failed to get response files" }),
            {
                status: 500,
                headers: { "Content-Type": "application/json" },
            }
        );
    }
}
