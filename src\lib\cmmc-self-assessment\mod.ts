// Re-export all types and utilities
export * from "./access-control.auto.ts";
export * from "./company-information.auto.ts";
export * from "./identification-authentication.auto.ts";
export * from "./media-protection-protect-information-on-digital-and-non-digital-media.auto.ts";
export * from "./physical-protection.auto.ts";
export * from "./policy-framework-assessment.auto.ts";
export * from "./system-communications-protection.auto.ts";
export * from "./system-information-integrity.auto.ts";

// Import with namespaces for adapters
import * as ac from "./access-control.auto.ts";
import * as ci from "./company-information.auto.ts";
import * as ia from "./identification-authentication.auto.ts";
import * as mp from "./media-protection-protect-information-on-digital-and-non-digital-media.auto.ts";
import * as pp from "./physical-protection.auto.ts";
import * as pf from "./policy-framework-assessment.auto.ts";
import * as sc from "./system-communications-protection.auto.ts";
import * as si from "./system-information-integrity.auto.ts";

// Unified exports for adapters
export const adapters = {
    ac: ac.accessControlLhcFormResponseAdapter,
    ci: ci.companyInformationLhcFormResponseAdapter,
    ia: ia.identificationAuthenticationLhcFormResponseAdapter,
    mp: mp.mediaProtectionProtectInformationOnDigitalAndNonDigitalMediaLhcFormResponseAdapter,
    pp: pp.physicalProtectionLhcFormResponseAdapter,
    pf: pf.policyFrameworkAssessmentLhcFormResponseAdapter,
    sc: sc.systemCommunicationsProtectionLhcFormResponseAdapter,
    si: si.systemInformationIntegrityLhcFormResponseAdapter,
};

export const LHCFormSource = {
    ac: ac.accessControlSource,
    ci: ci.companyInformationSource,
    ia: ia.identificationAuthenticationSource,
    mp: mp.mediaProtectionProtectInformationOnDigitalAndNonDigitalMediaSource,
    pp: pp.physicalProtectionSource,
    pf: pf.policyFrameworkAssessmentSource,
    sc: sc.systemCommunicationsProtectionSource,
    si: si.systemInformationIntegritySource,
}