---
import SEO from "@/components/SEO.astro";
import PasswordReset from "../../components/zitadel/forgotPassword";
const url = Astro.request.url;
const params = url?.includes("?") ? decodeURIComponent(url.split("?")[1]) : "";
const queryParams = new URLSearchParams(params);

const code = queryParams.get("code") ?? undefined;
const userId = queryParams.get("userId") ?? undefined;
const email = queryParams.get("email") ?? undefined;
const enableOpenObserve =
  import.meta.env.ENABLE_OPEN_OBSERVE !== undefined
    ? import.meta.env.ENABLE_OPEN_OBSERVE
    : false;
---

<head>
  <!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-NWW38GX7');</script>
<!-- End Google Tag Manager -->
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width" />
  <link rel="icon" type="image/svg+xml" href="/favicon.png" />
  <link rel="stylesheet" href="/assets/ds/tailwind/css/tailwind.min.css" />
  <SEO
    title="Reset Password - Opsfolio - Compliance as a Service | SOC2, HIPAA, ISO Certification"
    description="Get SOC2, HIPAA, ISO certified fast with expert-guided compliance. 98% audit pass rate, 60% faster than DIY. Your Compliance System of Record."
    keywords="Policies, compliance, SOC2, HIPAA, ISO 27001, cybersecurity, audit, certification"
  />
  {
    enableOpenObserve == "true" && (
      <script
        type="module"
        crossorigin="anonymous"
        src="/assets/scripts/index.756a5bbf.js"
      />
    )
  }
</head>

<!-- <link rel="stylesheet" href="/assets/styles/basic.css" /> -->
<!-- Google Tag Manager (noscript) -->
<noscript
  ><iframe
    src="https://www.googletagmanager.com/ns.html?id=GTM-NWW38GX7"
    height="0"
    width="0"
    style="display:none;visibility:hidden"></iframe></noscript
>
<!-- End Google Tag Manager (noscript) -->
<PasswordReset
  code={code ?? undefined}
  userID={userId ?? undefined}
  email={email ?? undefined}
  client:only="react"
/>
