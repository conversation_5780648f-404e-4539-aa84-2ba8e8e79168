# CMMC Self-Assessment Tool Question Review and Recommendations

## Introduction

This review has been conducted in alignment with Opsfolio’s policy on authoritative sources. In this framework, primary government documents—including the official CMMC Self-Assessment Guide and NIST 800-171—are treated as the highest authority. The Secure Controls Framework (SCF) is considered a secondary reference, used to supplement but not override the government standards.

The benefit of this approach is that it ensures the self-assessment tool remains defensible, consistent, and audit-ready. By grounding all questions in authoritative documents, we reduce the risk of introducing requirements that cannot be traced back to recognized standards. At the same time, selective use of the SCF allows us to enrich the tool with additional clarity and coverage where the primary sources are silent, without compromising compliance integrity. This balance helps organizations prepare confidently for audits while maintaining alignment with official expectations.

The goal of this review is work towards producing an assessment and a valuable and actionable report that stays true to the source documents while also serving as a useful artifact in Opsfolio’s lead generation strategy. By framing compliance guidance in a clear, credible, and digestible format, the resulting report strengthens our position as a trusted authority and enhances the effectiveness of the self-assessment tool as a lead magnet for attracting and engaging prospects.

## Part 1: Recommendations & Discussion

### Problems Identified

#### Over-Specific Questions

The review of the self-assessment questions highlights that many are over-specific. They drill down into technical or procedural details such as account lifecycles, authentication methods, or types of physical access credentials that are not described anywhere in the official source documents, whether the CMMC Self-Assessment Guide, NIST 800-171, or the Secure Controls Framework (SCF). 

By forcing organizations to provide answers at this level of specificity, the tool risks going beyond what the standards actually require. The only way to score such questions effectively would be to have our own set of implementation prescriptions that have no basis in the documentation.

#### Actionability Gaps

Another recurring issue is that many questions lack actionability. In some cases, the standards allow any number of valid answers, so the tool cannot meaningfully assess compliance from the response. For example, when asked “What areas require controlled physical access?” the only requirement in the source material is that equipment must be protected; it does not prescribe which areas must be secured. Questions framed in this way generate data, but not evidence of conformity.

#### Assessment vs. Attestation

A third issue is the confusion between assessment and attestation. A large portion of the tool is structured as if it is drawing out implementation detail, when in fact the self-assessment is primarily meant to confirm the existence of a policy, process, or control. The specific technical implementations belong in the System Security Plan (SSP), not in this assessment tool. By asking questions about implementation specifics, the tool risks becoming a shadow-SSP rather than a verification mechanism. In practice, the tool should be confirming that the SSP exists, that required areas are covered, and that the organization can point to relevant processes, not reproducing those processes in questionnaire form.


### Solution Approach

The Self-Assessment Guide, NIST, and the SCF provide ample material for thorough and defensible questions without the need to extrapolate. Questions should be reframed so they align directly with the text, avoiding references to implementation frequencies, credential types, or technical details that are never specified. Where additional information clearly belongs in the SSP, the tool should simply confirm that the SSP addresses the area in question, rather than extracting details. By phrasing questions in a way that is both general and grounded, the tool can remain authoritative while still being practical. 


### Sample Questions

#### Access Control (AC.L1-b.1.i / 3.1.1)

1. **Authorized Users, Processes, and Devices**

   * *Does your organization maintain a list of all users, processes, and devices authorized to access company computers or log onto the company network?*
   * Yes/No
   * *(Confirms existence of an access control policy and registry of authorized entities.)*

2. **Association of Automated Processes**

   * *Are automated updates, scripts, or background processes associated with the user who initiated or authorized them?*
   * Yes/No
   * *(Processes must be traceable back to an accountable user, per CMMC guidance.)*


#### Media Protection (MP.L1-b.1.vii / 3.8.3)

3. **Reuse of Media**

   * *Before reusing digital media, does your organization remove or purge all FCI so that it cannot be retrieved or reconstructed?*
   * Yes/No
   * *(Confirm use of appropriate sanitization before reuse.)*

4. **Non-Digital Media**

   * *When disposing of paper or other non-digital media containing FCI, does your organization shred, destroy, or redact information so that it is effectively removed?*
   * Yes/No
   * *(Demonstrate appropriate handling of physical media.)*


These questions:

* **Stay close to the source text** 
* **Ask for existence + practice**, not details like “how often” or “which tools”
* **Give compliance signal** — if the org can answer “yes” with evidence, they’re aligned.



### Limitations of This Approach and Mitigation

As the "Potential Assessment Considerations" of the Self-Assessment Guide show, auditors often probe beyond the baseline requirements, asking detailed follow-up questions not specified in the CMMC Level 1 controls. If we plan to keep our self-assessment tool close to the authoritative texts, it will not anticipate every question an auditor might raise. Attempting to do so would risk stepping beyond the authoritative documents into ungrounded assumptions or speculation about what an auditor might want.

However, CMMC Level 1 requires only self-attestation. A C3PAO audit is not needed until Level 2. The proper role of this tool is therefore to give organizations a clear, defensible path for self-attestation grounded in government standards, not to substitute for higher-level audit preparation.

Framed this way, the report could be made a credible, actionable guide for Level 1 compliance and a valuable lead magnet: it helps prospects reduce uncertainty today while positioning Opsfolio as a trusted partner if they later advance to Level 2 and require third-party certification.



## Question Breakdown

### Section: Access Control

**AC.L1-B.1.i – Access Control Policy**
0. *“Do you have an Access Control Policy?”* – AC.L1-B.1.i specifies that access control policy may be assessed.

1. *“Access Control Policy Elements”* – Requires inference to know what the elements are; not specified directly in sources.
2. *“User Account Registry”* – Does not specify how many user accounts are required.

   * SCF requires role-based access control (RBAC).
3. *Least Privilege* – Cited in SCF IAC-01.3, but this corresponds to the identity section, not access controls or the corresponding NIST section.
4. *Account Lifecycle Management* – IAC-08 (corresponding to NIST 3.1.1) mentions RBAC but does not specify account lifecycle management; inference required.

   * *Account review* timeframe is not specified.

**AC.L1-3.1.2 – Transaction & Function Control**

1. Specified by AC.L1-B.1.ii.
2. IAC-15, AC.L1-B.1.ii, and NIST 3.1.2 do not specify a breakdown of functions into categories; requires inference.
3. *Transaction Authorization Requirements* – No specification in the three documents of who should be allowed to authorize high-risk transactions.

**AC.L1-3.1.20 – External Connections**

1. *“What types of external systems does your organization connect to?”* – Not wrong to ask, but not actionable for assessment purposes.

   * SCF DCH-13 provides partial guidance.
2. *“How do you verify external system connections?”* – No specification for acceptable methods in sources.

   * *Limitations on external connections* are also unspecified. 

**AC.L1-3.1.22 – Control Public Information**

1. Question not actionable.
2. *“How do you ensure FCI is not posted on public systems?”* – No specification of how this should be implemented; inference required.
3. *“Who is authorized to post content to public systems?”* – Not an actionable question.


### Section: Identification & Authentication

**IA.L1-3.5.1 – User Identification**

1. *User Identification Standards* – Only requires usernames/unique identifiers; no further specificity.
   
   
   * *Service accounts*: Better phrasing from the CMMC Guide is:

     * *“Are the processes and service accounts that an authorized user initiates identified (e.g., scripts, automatic updates, configuration updates, vulnerability scans)?”*
   * CMMC Guide specifies:
     a. System users are identified.
     b. Processes acting on behalf of users are identified.
     c. Devices accessing the system are identified.
   * Service accounts = processes acting on behalf of users. 

* *Device identification* – Another case of offering choices where sources provide no basis.
* *Identity verification process* – No guidance in sources on background checks, etc.
* *User authentication methods* – No point in asking; answers would not correspond to sources.
* No guidance on password requirements.
* MFA not required at Level 1 (though advisable).
* Credential management not specified.
* *Authentication failure handling* not specified.


### Section: Media Protection (MP.L1-3.8.3 – Media Disposal)

1. May be more user-friendly to break into separate questions


### Section: Physical Protection

**General Physical Protection**
2\. *“What areas require controlled physical access?”* – Sources specify only equipment, not areas.
3\. *“Who authorizes physical access to controlled areas?”* – Not specified; SCF PES-02 suggests facilities maintenance team.
4\. *“What types of physical access credentials are issued?”* – Acceptable credential types not specified.
5\. *“Are there time-based restrictions on physical access?”* – Not specified.

**PE.L1-3.10.3 – Escort Visitors**

1. Requirement is clear from Self-Assessment Guide.
2. *“How are visitors identified and distinguished from employees?”* – Expected answer is special visitor badges.
3. *“How is visitor activity monitored while on premises?”* – Potential Assessment Considerations indicate it should be logged.
4. *“Who is authorized to escort visitors?”* – Not specified.

**PE.L1-3.10.4 – Physical Access Logs**

1. *“How do you log physical access to your facilities?”* – Valid question, but any answer acceptable; must include “none” option in case no logging exists.
2. *“Log Retention and Review”* – Retention required, but duration not specified.

* NIST 3.10.5 notes: *“Physical access devices include keys, locks, combinations, and card readers.”*

**PE.L1-3.10.5 – Manage Physical Access Devices**

* This section does not appear in the latest version of the guide.

### Section: Policy Framework Assessment

* No guidance in documents as to who, when, or how policies are developed.

### Section: System & Communications Protection

* Implementation details not specified in sources.

1. *“System Boundary Definition”* – Asks for setup details; these belong in SSP, not actionable in self-assessment.


### Section: System & Information Integrity

**SI.L1-3.14.1 – Flaw Remediation**

1. *“How does your organization identify system flaws and vulnerabilities?”* – Not specified; SCF VPM-01 mentions use of SIEM.
2. *“How are identified flaws reported and tracked?”* – Not specified.
3. *“What are your target timeframes for correcting identified flaws?”* – Must align with organizational policy; timeframe not specified in sources.
4. *“How are security patches and updates managed?”* – Not specified; questions could reference NIST SP 800-40.

**SI.L1-3.14.2 – Malicious Code Protection**

1. *“Select all locations where malicious code protection is implemented:”* – Valid; supported by guide.

**SI.L1-3.14.4 – Update Malicious Code Protection**

* Sources require updates “frequently,” but do not specify frequency or methods.

**SI.L1-3.14.5 – System & File Scanning**

* Requires periodic and real-time scanning, but no frequencies specified.
* *“Results Handling & Testing”* – Remediation or follow-up not specified.
