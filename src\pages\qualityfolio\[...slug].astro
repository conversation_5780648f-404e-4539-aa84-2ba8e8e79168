---
import Layout from "../../layouts/Layout.astro";
import { Breadcrumbs } from "astro-breadcrumbs";
const site = import.meta.env.PUBLIC_SITE_URL;
const proxiedHtml = (Astro.locals as any).proxiedHtml || "";
const breadcrumbLinks = [
          { index: 2, hidden: true },
          { index: 3, hidden: true },
          { index: 4, hidden: true }
        ];
---

<Layout title="Qualityfolio"
        description="Qualityfolio is a comprehensive compliance and test management platform that streamlines your quality assurance processes, offering real-time dashboards, automated reporting, and seamless collaboration for teams to ensure efficient, reliable, and audit-ready results."
        keywords="quality management, compliance, test management, audit, automation, reporting, dashboards, team collaboration, QA, quality assurance, risk management, workflow, software testing, regulatory compliance, ISO, SOC2, HIPAA, test cases, defect tracking, continuous improvement, enterprise compliance"
        url={`${site}/qualityfolio/`}
        breadcrumbLinks= {breadcrumbLinks}>
        
  <main class="mx-auto px-4 max-w-7xl">
        {proxiedHtml && <div class="proxied-container" set:html={proxiedHtml} />}
      </main>
</Layout>
<script>
       
  document.addEventListener("DOMContentLoaded", function () {
     
    const sourceNav = document.querySelector('nav[aria-label="breadcrumb"]');
    if (!(sourceNav instanceof HTMLElement)) return;
    sourceNav.style.display = "none";
    const breadcrumbItems = Array.from(
      sourceNav.querySelectorAll("li.breadcrumb-item a")
    );
    const itemsToInject = breadcrumbItems.slice(2);
    if (itemsToInject.length === 0) return;

    const targetNav = document.querySelector(
      'nav[aria-label="Site navigation"]'
    );
    if (!targetNav) return;

    const crumbsList = targetNav.querySelector("ol.c-breadcrumbs__crumbs");
    const lastCrumb = crumbsList?.lastElementChild;
    if (!crumbsList || !lastCrumb) return;

    itemsToInject.forEach((item, index) => {
      const text = item.textContent?.trim() || "";
      const href = item.getAttribute("href") ?? "#";

      const li = document.createElement("li");
      li.className = "c-breadcrumbs__crumb";

      const isLast = index === itemsToInject.length - 1;

      if (isLast) {
   
        const span = document.createElement("span");
        span.className = "c-breadcrumbs__link is-current";
        span.textContent = text;
        span.setAttribute("aria-current", "location"); // mark as current
        li.appendChild(span);
      } else {
       
        const a = document.createElement("a");
        a.className = "c-breadcrumbs__link";
        a.href = href;
        a.textContent = text;
        a.setAttribute("aria-current", "false");
        li.appendChild(a);

        const separator = document.createElement("span");
        separator.className = "c-breadcrumbs__separator";
        separator.setAttribute("aria-hidden", "true");
        separator.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
           viewBox="0 0 24 24" fill="none" stroke="currentColor"
           stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <polyline points="9 18 15 12 9 6"></polyline>
      </svg>`;
        li.appendChild(separator);
      }

      crumbsList.insertBefore(li, lastCrumb);
    });
    
  });


  document.addEventListener("DOMContentLoaded", function() {
    // Select all <li> elements inside the breadcrumb nav
    const breadcrumbItems = document.querySelectorAll('.c-breadcrumbs__crumb');

    breadcrumbItems.forEach(item => {
      // Check if the <li> contains an <a> tag with the 'hidden' attribute
      const link = item.querySelector('a[hidden]');
      
      if (link) {
        // Hide the <li> element by setting its display to 'none'
        (item as HTMLElement).style.display = 'none';
      }
    });
  });
</script>
