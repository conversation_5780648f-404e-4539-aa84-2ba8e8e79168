{"resourceType": "Questionnaire", "meta": {"profile": ["http://hl7.org/fhir/4.0/StructureDefinition/Questionnaire"]}, "title": "CMMC Level 2 - Identification and Authentication (IA)", "status": "draft", "item": [{"item": [{"item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "IA.L2-3.5.1_A_NOTES", "text": "Notes / Evidence", "repeats": false}, {"linkId": "IA.L2-3.5.1_A_Q_helpText", "type": "display", "text": "usernames", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "IA.L2-3.5.1_A_Q", "text": "Does your organization issue unique identifiers to all system users?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "IA.L2-3.5.1_A_GRP", "prefix": "(a)", "text": "User Identification"}, {"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "IA.L2-3.5.1_B_NOTES", "text": "Notes / Evidence", "repeats": false}, {"linkId": "IA.L2-3.5.1_B_Q_helpText", "type": "display", "text": "service accounts", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "IA.L2-3.5.1_B_Q", "text": "Are processes acting on behalf of users identified with unique IDs?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "IA.L2-3.5.1_B_GRP", "prefix": "(b)", "text": "Process Identification"}, {"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "IA.L2-3.5.1_C_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "check-box", "display": "Check-box"}]}}], "linkId": "IA.L2-3.5.1_C_Q", "text": "Which methods are used to uniquely identify devices on the network?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "MAC Addresses"}}, {"valueCoding": {"display": "IP Addresses"}}, {"valueCoding": {"display": "Device Certificates"}}, {"valueCoding": {"display": "Device-unique tokens"}}]}], "type": "group", "linkId": "IA.L2-3.5.1_C_GRP", "prefix": "(c)", "text": "Device Identification"}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "IA.L2-3.5.1_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "IA.L2-3.5.1_help", "type": "display", "text": "Identify system users, processes acting on behalf of users, and devices.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}], "type": "group", "linkId": "IA.L2-3.5.1", "text": "IA.L2-3.5.1 – Identification"}, {"type": "group", "linkId": "IA.L2-3.5.2", "text": "IA.L2-3.5.2 – Authentication", "item": [{"item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "IA.L2-3.5.2_A_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "IA.L2-3.5.2_A_Q", "text": "Is the identity of each user authenticated before they are allowed access to the system?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "IA.L2-3.5.2_A_GRP", "prefix": "(a)", "text": "User Authentication"}, {"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "IA.L2-3.5.2_B_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "IA.L2-3.5.2_B_Q", "text": "Is the identity of each process acting on behalf of a user authenticated before access is allowed?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "IA.L2-3.5.2_B_GRP", "prefix": "(b)", "text": "Process Authentication"}, {"item": [{"item": [{"type": "text", "linkId": "IA.L2-3.5.2_C_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "IA.L2-3.5.2_C_Q", "text": "Is the identity of each device authenticated before being allowed to connect to the network?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "IA.L2-3.5.2_C_GRP", "prefix": "(c)", "text": "<PERSON>ce Au<PERSON>cation"}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "IA.L2-3.5.2_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "IA.L2-3.5.2_help", "type": "display", "text": "Authenticate (or verify) the identities of users, processes, or devices, as a prerequisite to allowing access to organizational systems.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}]}, {"type": "group", "linkId": "IA.L2-3.5.3", "text": "IA.L2-3.5.3 – Multifactor Authentication", "item": [{"item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "IA.L2-3.5.3_A_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "IA.L2-3.5.3_A_Q", "text": "Are all privileged accounts identified and documented?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "IA.L2-3.5.3_A_GRP", "prefix": "(a)", "text": "Identification of Privileged Accounts", "required": true}, {"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "IA.L2-3.5.3_B_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "IA.L2-3.5.3_B_Q", "text": "Is multifactor authentication (MFA) required for local access to privileged accounts?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "IA.L2-3.5.3_B_GRP", "prefix": "(b)", "text": "MFA for Local Privileged Access"}, {"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "IA.L2-3.5.3_C_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "IA.L2-3.5.3_C_Q", "text": "Is MFA required for network access to privileged accounts?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "IA.L2-3.5.3_C_GRP", "prefix": "(c)", "text": "MFA for Network Privileged Access"}, {"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "IA.L2-3.5.3_D_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "IA.L2-3.5.3_D_Q", "text": "Is MFA required for network access to non-privileged accounts?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "IA.L2-3.5.3_D_GRP", "prefix": "(d)", "text": "MFA for Network Non-Privileged Access"}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "IA.L2-3.5.3_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "IA.L2-3.5.3_help", "type": "display", "text": "Use multifactor authentication for local and network access to privileged accounts and for network access to non-privileged accounts.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}]}, {"item": [{"item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "IA.L2-3.5.4_A_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "check-box", "display": "Check-box"}]}}], "linkId": "IA.L2-3.5.4_A_Q", "text": "Which replay-resistant authentication mechanisms are employed for network access?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Time-based One-Time Passwords (TOTP)"}}, {"valueCoding": {"display": "Challenge-Response authenticators (e.g., Smart Cards)"}}, {"valueCoding": {"display": "Protocols using nonces or challenges (e.g., Kerberos, TLS)"}}]}], "type": "group", "linkId": "IA.L2-3.5.4_A_GRP", "prefix": "(a)", "text": "Implementation of Replay-Resistant Mechanisms"}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "IA.L2-3.5.4_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "IA.L2-3.5.4_help", "type": "display", "text": "Employ replay-resistant authentication mechanisms for network access to privileged and non-privileged accounts.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}], "type": "group", "linkId": "IA.L2-3.5.4", "text": "IA.L2-3.5.4 – Rep<PERSON>-Resistant Authentication"}, {"item": [{"item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "IA.L2-3.5.5_A_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "IA.L2-3.5.5_A_Q", "text": "Is there a defined period within which user, group, or device identifiers cannot be reused?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "IA.L2-3.5.5_A_GRP", "prefix": "(a)", "text": "Definition of Non-Reuse Period", "required": true}, {"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "IA.L2-3.5.5_B_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "IA.L2-3.5.5_B_Q", "text": "Is the reuse of identifiers prevented for this defined period?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "IA.L2-3.5.5_B_GRP", "prefix": "(b)", "text": "Prevention of Reuse"}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "IA.L2-3.5.5_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "IA.L2-3.5.5_help", "type": "display", "text": "Prevent reuse of identifiers for a defined period.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}], "type": "group", "linkId": "IA.L2-3.5.5", "text": "IA.L2-3.5.5 – Identifier Reuse"}, {"type": "group", "linkId": "IA.L2-3.5.6", "text": "IA.L2-3.5.6 – Identifier Handling", "item": [{"item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "IA.L2-3.5.6_A_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "integer", "linkId": "IA.L2-3.5.6_A_Q", "text": "What is the defined period of inactivity (in days) after which a user identifier is automatically disabled?", "required": true, "repeats": false}], "type": "group", "linkId": "IA.L2-3.5.6_A_GRP", "prefix": "(a)", "text": "Definition of Inactivity Period"}, {"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "IA.L2-3.5.6_B_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "IA.L2-3.5.6_B_Q", "text": "Is this process enforced, either automatically or through a manual review process?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Automated"}}, {"valueCoding": {"display": "Manual"}}]}], "type": "group", "linkId": "IA.L2-3.5.6_B_GRP", "prefix": "(b)", "text": "Disabling Inactive Identifiers"}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "IA.L2-3.5.6_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "IA.L2-3.5.6_help", "type": "display", "text": "Disable identifiers after a defined period of inactivity.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}]}, {"item": [{"item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "IA.L2-3.5.7_BD_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "IA.L2-3.5.7_BD_Q1", "prefix": "(I)", "text": "Are there defined requirements for changing a minimum number of characters when a new password is created?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "952750896926", "text": "Notes / Evidence", "repeats": false}, {"linkId": "IA.L2-3.5.7_AC_Q1_helpText", "type": "display", "text": " minimum length, character types like uppercase, lowercase, numbers, symbols", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "IA.L2-3.5.7_AC_Q1", "prefix": "(II)", "text": "Are password complexity requirements formally documented in a policy or configuration standard?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "101470301039", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "IA.L2-3.5.7_AC_Q2", "prefix": "(III)", "text": " Are the defined password complexity requirements technically enforced by the information system(s) at the time of password creation or change?", "enableWhen": [{"question": "IA.L2-3.5.7_AC_Q1", "operator": "=", "answerCoding": {"display": "Yes"}}], "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "IA.L2-3.5.7_AC_GRP", "prefix": "(a)", "text": "Password Complexity Rules"}, {"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "999142374181", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "568006075787", "prefix": "(I)", "text": "Does a formal policy or standard define password history requirements, specifying that a new password must be different from a certain number of previous passwords?", "required": true, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "959711938096", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "508988555747", "prefix": "(II)", "text": "Does the information system technically enforce the defined password history/reuse policy, preventing users from setting a new password that is identical to one of their recent passwords?", "required": true, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "IA.L2-3.5.7_BD_GRP", "prefix": "(b)", "text": "Password Change Rules"}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "IA.L2-3.5.7_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "IA.L2-3.5.7_help", "type": "display", "text": "Enforce a minimum password complexity and change of characters when new passwords are created.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}], "type": "group", "linkId": "IA.L2-3.5.7", "text": "IA.L2-3.5.7 – Password Complexity"}, {"item": [{"item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "IA.L2-3.5.8_AB_NOTES", "text": "Notes / Evidence ", "repeats": false, "item": [{"linkId": "IA.L2-3.5.8_AB_NOTES_helpText", "type": "display", "text": "provide number, 'password history size", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}, {"linkId": "IA.L2-3.5.8_AB_Q_helpText", "type": "display", "text": "the last 24 passwords cannot be reused", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "IA.L2-3.5.8_AB_Q", "prefix": "(I)", "text": "Does a formal policy or standard specify the exact number of previous passwords that cannot be reused?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}, {"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "353510950184", "text": "Notes / Evidence ", "repeats": false, "item": [{"linkId": "802109510717", "type": "display", "text": "provide number, 'password history size", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "534544790856", "prefix": "(II)", "text": "Does the information system technically enforce the specified password history, preventing users from reusing passwords for the number of generations defined in the policy", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "IA.L2-3.5.8_AB_GRP", "prefix": "(a)", "text": "Password Reuse Policy"}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "IA.L2-3.5.8_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "IA.L2-3.5.8_help", "type": "display", "text": "Prohibit password reuse for a specified number of generations.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}], "type": "group", "linkId": "IA.L2-3.5.8", "text": "IA.L2-3.5.8 – Password Reuse"}, {"type": "group", "linkId": "IA.L2-3.5.9", "text": "IA.L2-3.5.9 – Temporary Passwords", "item": [{"item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "IA.L2-3.5.9_A_NOTES", "text": "Notes / Evidence", "repeats": false}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "IA.L2-3.5.9_A_Q", "text": "Are users technically required to change system-generated temporary passwords immediately upon their first logon?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "IA.L2-3.5.9_A_GRP", "prefix": "(a)", "text": "Immediate Password Change"}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "IA.L2-3.5.9_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "IA.L2-3.5.9_help", "type": "display", "text": "Allow temporary password use for system logons with an immediate change to a permanent password.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}]}, {"type": "group", "linkId": "IA.L2-3.5.10", "text": "IA.L2-3.5.10 – Cryptographically-Protected Passwords", "item": [{"item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "IA.L2-3.5.10_A_NOTES", "text": "Notes / Evidence", "repeats": false}, {"linkId": "IA.L2-3.5.10_A_Q_helpText", "type": "display", "text": "salted one-way hashes", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "IA.L2-3.5.10_A_Q", "text": "Are all passwords stored in a cryptographically-protected format ?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "IA.L2-3.5.10_A_GRP", "prefix": "(a)", "text": "Password Storage"}, {"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "IA.L2-3.5.10_B_NOTES", "text": "Notes / Evidence ", "repeats": false, "item": [{"linkId": "IA.L2-3.5.10_B_NOTES_helpText", "type": "display", "text": "inside a TLS session", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "IA.L2-3.5.10_B_Q", "text": "Are all passwords transmitted over the network only when cryptographically protected?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "IA.L2-3.5.10_B_GRP", "prefix": "(b)", "text": "Password Transmission"}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "IA.L2-3.5.10_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "IA.L2-3.5.10_help", "type": "display", "text": "Store and transmit only cryptographically-protected passwords.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}]}, {"type": "group", "linkId": "IA.L2-3.5.11", "text": "IA.L2-3.5.11 – Obscure Feedback", "item": [{"item": [{"item": [{"item": [{"type": "text", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/entryFormat", "valueString": "Enter notes or supporting evidence, if any"}], "linkId": "IA.L2-3.5.11_A_NOTES", "text": "Notes / Evidence", "repeats": false}, {"linkId": "IA.L2-3.5.11_A_Q_helpText", "type": "display", "text": "displaying asterisks instead of password characters", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help", "display": "Help-<PERSON><PERSON>"}], "text": "Help-<PERSON><PERSON>"}}]}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "IA.L2-3.5.11_A_Q", "text": "Are authentication prompts configured to obscure feedback to prevent shoulder surfing?", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Yes"}}, {"valueCoding": {"display": "No"}}]}], "type": "group", "linkId": "IA.L2-3.5.11_A_GRP", "prefix": "(a)", "text": "Obscuring Authenti<PERSON> <PERSON><PERSON><PERSON>"}], "type": "choice", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "radio-button", "display": "Radio Button"}]}}], "linkId": "IA.L2-3.5.11_STATUS", "text": "Implementation Status", "required": true, "repeats": false, "answerOption": [{"valueCoding": {"display": "Fully Implemented"}}, {"valueCoding": {"display": "Partially Implemented"}}, {"valueCoding": {"display": "Not Implemented"}}, {"valueCoding": {"display": "Not Applicable"}}]}, {"linkId": "IA.L2-3.5.11_help", "type": "display", "text": "Obscure feedback of authentication information.", "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/questionnaire-itemControl", "valueCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/questionnaire-item-control", "code": "help"}]}}]}]}]}