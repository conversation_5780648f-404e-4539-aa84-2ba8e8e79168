---
import Layout from "../layouts/Layout.astro";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircle, Shield, Users, BarChart3, Clock, DollarSign, AlertTriangle, FileCheck, Monitor, Target } from "lucide-react";
import VideoPlaceholder from "@/components/sections/VideoPlaceholder";

  const benefits = [
    {
      icon: Shield,
      title: "Done for You",
      description: "We handle your entire compliance burden while you focus on core business goals. Our experts manage everything from policies to audits, removing operational overhead completely."
    },
    {
      icon: Target,
      title: "Done with You",
      description: "Work alongside our compliance experts as collaborative partners. We provide guidance, training, and support while your team maintains control and builds internal capabilities."
    },
    {
      icon: FileCheck,
      title: "AI + Humans",
      description: "Powerful combination of intelligent automation, advanced software platforms, and expert human oversight. Get the efficiency of technology with the wisdom of experienced professionals."
    },
    {
      icon: DollarSign,
      title: "Reduce Costs",
      description: "Eliminate expensive in-house compliance teams and avoid costly violation penalties. Predictable subscription pricing with expert delivery."
    }
  ];

  const services = [
    {
      title: "Compliance Monitoring",
      description: "24/7 automated monitoring of your compliance posture with real-time alerts and notifications."
    },
    {
      title: "Risk Assessment",
      description: "Continuous compliance risk assessment with AI-powered analytics and predictive insights."
    },
    {
      title: "Audit Preparation",
      description: "Comprehensive due diligence audits with automated evidence collection and audit-ready documentation."
    },
    {
      title: "Staff Training & Coaching",
      description: "Expert coaching and mentoring programs to build internal compliance capabilities."
    },
    {
      title: "Automated Analytics",
      description: "Advanced compliance analytics and automated reporting with customizable dashboards."
    },
    {
      title: "Incident Response",
      description: "Security incident response planning and execution with compliance impact assessment."
    }
  ];

  const teamRoles = [
    {
      role: "Data Protection Officer",
      description: "Ensures organizational data complies with GDPR and global data privacy regulations."
    },
    {
      role: "Compliance Auditor",
      description: "Conducts periodic risk assessments and generates insightful audit reports with actionable recommendations."
    },
    {
      role: "Risk Manager",
      description: "Identifies compliance risks and implements comprehensive risk management strategies."
    },
    {
      role: "Chief Information Security Officer",
      description: "Implements cybersecurity controls to protect sensitive data and ensure regulatory compliance."
    },
    {
      role: "Management Systems Manager",
      description: "Oversees compliance management systems and administers business process adherence."
    }
  ];

  const integrationSteps = [
    {
      step: "1",
      title: "Assess Current Posture",
      description: "Comprehensive evaluation of your regulatory requirements, business processes, and current risk profile."
    },
    {
      step: "2",
      title: "Framework Design",
      description: "Custom compliance framework with specific requirements, policies, procedures, and necessary controls."
    },
    {
      step: "3",
      title: "Implementation",
      description: "Deploy policies, procedures, controls, staff training, and integrate monitoring solutions."
    },
    {
      step: "4",
      title: "Continuous Monitoring",
      description: "Real-time compliance monitoring with performance metrics, regular reporting, and framework updates."
    }
  ];

---
<Layout title="Opsfolio - Compliance as a Service | SOC2, HIPAA, ISO Certification"
  description="Get SOC2, HIPAA, ISO certified fast with expert-guided compliance. 98% audit pass rate, 60% faster than DIY. Your Compliance System of Record."
  keywords="Digital health,CISO solutions, security leadership, security program management, audit readiness, continuous monitoring, security frameworks"
  url="/caas/">
  <main>
          {/* Hero Section */}
          <section class="relative py-24 lg:py-32">
            <div class="container mx-auto px-4">
              <div class="text-center max-w-4xl mx-auto">
                <Badge variant="secondary" className="mb-4">
                  Compliance as a Service
                </Badge>
                <h1 class="text-4xl md:text-6xl font-bold mb-6">
                  Lift the Security & Compliance Burden. So Your Team can Focus on What They do Best.
                </h1>
                <p class="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
                  We can handle your entire security & compliance burden, work alongside your team, or train your staff to manage it themselves. Expert-driven compliance management tailored to your organization's needs.
                </p>

                {/* Proof Bar */}
                <div class="bg-muted/30 rounded-lg p-6 mb-8 max-w-4xl mx-auto">
                  <div class="grid md:grid-cols-3 gap-6 text-center">
                    <div>
                      <h3 class="font-semibold text-primary mb-2 flex items-center justify-center gap-2">
                        <CheckCircle className="h-5 w-5" />
                        SOC 2 I & II in under 2 months
                      </h3>
                      <p class="text-sm text-muted-foreground">(Map Collective)</p>
                    </div>
                    <div>
                      <h3 class="font-semibold text-primary mb-2 flex items-center justify-center gap-2">
                        <CheckCircle className="h-5 w-5" />
                        FDA QSR + ISO 14971 alignment in 30 days
                      </h3>
                      <p class="text-sm text-muted-foreground">(Global Med-Tech)</p>
                    </div>
                    <div>
                      <h3 class="font-semibold text-primary mb-2 flex items-center justify-center gap-2">
                        <CheckCircle className="h-5 w-5" />
                        HIPAA audit passed with clean report
                      </h3>
                      <p class="text-sm text-muted-foreground">(Healthcare Network)</p>
                    </div>
                  </div>
                </div>
                <div class="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                  <Button size="lg" >
                    <a href="/get-started">Get Started Today</a>
                  </Button>
                  <Button variant="outline" size="lg" >
                    <a href="/contact">Schedule Demo</a>
                  </Button>
                </div>
                
                <!-- <VideoPlaceholder 
                  title="Opsfolio CaaS Overview"
                  description="See how Compliance as a Service transforms regulatory management"
                  className="rounded-lg"
                /> -->
              </div>
            </div>
          </section>

          {/* Challenge Section */}
          <section class="py-20 bg-muted/50">
            <div class="container mx-auto px-4">
              <div class="max-w-4xl mx-auto text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold mb-6">
                  The Security Compliance Challenge
                </h2>
                <p class="text-lg text-muted-foreground mb-8">
                  Organizations today face an overwhelming array of security and compliance regulations: GDPR, HIPAA, CCPA, SOC 2, 
                  ISO 27001, and more. While these regulations are complex and subject to regular amendments, 
                  the biggest challenge is often having the staff availability and expertise to handle the work.
                </p>
              </div>

              <div class="grid md:grid-cols-3 gap-8 mb-12">
                <Card>
                  <CardHeader>
                    <AlertTriangle className="h-12 w-12 text-destructive mb-4" />
                    <CardTitle>Complex Requirements</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p class="text-muted-foreground">
                      Traditional approaches are insufficient for today's complex, 
                      multi-framework compliance landscapes.
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <Clock className="h-12 w-12 text-warning mb-4" />
                    <CardTitle>Resource Intensive</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p class="text-muted-foreground">
                      Manual compliance processes consume valuable time and resources 
                      that could be focused on core business operations.
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <Clock className="h-12 w-12 text-warning mb-4" />
                    <CardTitle>Distracts from Priorities</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p class="text-muted-foreground">
                      Security compliance work diverts critical engineering and leadership 
                      resources away from product development and core business objectives.
                    </p>
                  </CardContent>
                </Card>
              </div>

              <div class="text-center">
                <h3 class="text-2xl font-bold mb-4">
                  Opsfolio CaaS: Your Solution
                </h3>
                <p class="text-lg text-muted-foreground max-w-3xl mx-auto">
                  Compliance as a Service automates and centralizes regulatory compliance processes, 
                  reducing manual effort while unifying data collection, evidence generation, 
                  and reporting into a continuous, intelligent workflow.
                </p>
              </div>
            </div>
          </section>

          {/* Benefits Section */}
          <section class="py-20">
            <div class="container mx-auto px-4">
              <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold mb-6">
                  Key Benefits of Opsfolio CaaS
                </h2>
                <p class="text-lg text-muted-foreground max-w-3xl mx-auto">
                  Transform compliance from a burden into a competitive advantage with our 
                  comprehensive Compliance as a Service platform.
                </p>
              </div>

              <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
                {benefits.map((benefit, index) => (
                  <Card key={index} className="text-center">
                    <CardHeader>
                      <div class="mx-auto text-primary mb-4">
                        <benefit.icon className="h-8 w-8" />
                      </div>
                      <CardTitle className="text-lg">{benefit.title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p class="text-muted-foreground text-sm">
                        {benefit.description}
                      </p>
                    </CardContent>
                  </Card>
                ))}
              </div>

              <!-- <div class="text-center">
                <VideoPlaceholder 
                  title="CaaS Benefits in Action"
                  description="Real-world examples of how Opsfolio CaaS delivers value"
                  className="rounded-lg max-w-4xl mx-auto"
                />
              </div> -->
            </div>
          </section>

          {/* Services Section */}
          <section class="py-20 bg-muted/50">
            <div class="container mx-auto px-4">
              <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold mb-6">
                  Comprehensive Compliance Services
                </h2>
                <p class="text-lg text-muted-foreground max-w-3xl mx-auto">
                  Opsfolio CaaS provides end-to-end compliance management across all regulatory frameworks, 
                  ensuring comprehensive coverage of your compliance needs.
                </p>
              </div>

              <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
                {services.map((service, index) => (
                  <Card key={index}>
                    <CardHeader>
                      <CheckCircle className="h-8 w-8 text-primary mb-2" />
                      <CardTitle className="text-lg">{service.title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p class="text-muted-foreground">
                        {service.description}
                      </p>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {/* Case Studies Section */}
              <div class="mt-20">
                <div class="text-center mb-16">
                  <h3 class="text-3xl md:text-4xl font-bold mb-6">
                    Proof in Action: Case Studies
                  </h3>
                </div>

                <div class="space-y-12">
                  {/* Map Collective Case Study */}
                  <Card className="p-8">
                    <div class="mb-6">
                      <h4 class="text-2xl font-bold mb-2">1. Map Collective</h4>
                      <h5 class="text-xl font-semibold text-primary mb-4">SOC 2 I & II in Under 2 Months</h5>
                      <blockquote class="text-lg italic text-muted-foreground mb-4 border-l-4 border-primary pl-4">
                        "With Opsfolio CaaS, we were able to navigate the complexities of SOC 2 compliance, automate our audit preparations, and streamline data security across our platform."
                        <footer class="text-sm font-medium mt-2">— Tara Gupta, Founder & CEO, Map Collective</footer>
                      </blockquote>
                    </div>
                    <div class="mb-6">
                      <h6 class="font-semibold mb-3">What we did:</h6>
                      <ul class="space-y-2 mb-6">
                        <li class="flex items-center gap-3">
                          <CheckCircle className="h-5 w-5 text-primary flex-shrink-0" />
                          <span>Centralized compliance management into Opsfolio CaaS platform</span>
                        </li>
                        <li class="flex items-center gap-3">
                          <CheckCircle className="h-5 w-5 text-primary flex-shrink-0" />
                          <span>Automated audit preparation with real-time monitoring, evidence collection, and control mapping</span>
                        </li>
                        <li class="flex items-center gap-3">
                          <CheckCircle className="h-5 w-5 text-primary flex-shrink-0" />
                          <span>Integrated policy-as-code templates to standardize SOC 2 controls</span>
                        </li>
                      </ul>
                    </div>
                    <div class="mb-6">
                      <h6 class="font-semibold mb-3">Results:</h6>
                      <ul class="space-y-2">
                        <li class="flex items-center gap-3">
                          <CheckCircle className="h-5 w-5 text-primary flex-shrink-0" />
                          <span>SOC 2 Type I & II certified in &lt; 2 months</span>
                        </li>
                        <li class="flex items-center gap-3">
                          <CheckCircle className="h-5 w-5 text-primary flex-shrink-0" />
                          <span>Automated evidence collection and monitoring</span>
                        </li>
                        <li class="flex items-center gap-3">
                          <CheckCircle className="h-5 w-5 text-primary flex-shrink-0" />
                          <span>Improved credibility with enterprise clients</span>
                        </li>
                      </ul>
                    </div>
                    <div class="text-center">
                      <Button >
                        <a href="https://opsfolio.com/resources/case-studies/map-collective" target="_blank" rel="noopener noreferrer">
                          Map Collective Case Study
                        </a>
                      </Button>
                    </div>
                  </Card>

                  {/* Global Medical Technology Case Study */}
                  <Card className="p-8">
                    <div class="mb-6">
                      <h4 class="text-2xl font-bold mb-2">2. Global Medical Technology Leader</h4>
                      <h5 class="text-xl font-semibold text-primary mb-4">FDA QSR + ISO 14971 Compliance in 30 Days</h5>
                      <p class="text-lg text-muted-foreground mb-4">
                        A 200–500 FTE med-tech company partnered with Opsfolio to embed security into its device management platform.
                      </p>
                    </div>
                    <div class="mb-6">
                      <h6 class="font-semibold mb-3">What we did:</h6>
                      <ul class="space-y-2 mb-6">
                        <li class="flex items-center gap-3">
                          <CheckCircle className="h-5 w-5 text-primary flex-shrink-0" />
                          <span>Integrated threat modeling and compliance mapping into SDL</span>
                        </li>
                        <li class="flex items-center gap-3">
                          <CheckCircle className="h-5 w-5 text-primary flex-shrink-0" />
                          <span>Validated APIs across 10 endpoints with Playwright + Xray</span>
                        </li>
                        <li class="flex items-center gap-3">
                          <CheckCircle className="h-5 w-5 text-primary flex-shrink-0" />
                          <span>Centralized evidence hub mapped directly to FDA QSR & ISO 14971</span>
                        </li>
                      </ul>
                    </div>
                    <div class="mb-6">
                      <h6 class="font-semibold mb-3">Results:</h6>
                      <ul class="space-y-2">
                        <li class="flex items-center gap-3">
                          <CheckCircle className="h-5 w-5 text-primary flex-shrink-0" />
                          <span>Audit-ready in 30 working days</span>
                        </li>
                        <li class="flex items-center gap-3">
                          <CheckCircle className="h-5 w-5 text-primary flex-shrink-0" />
                          <span>Reduced risk of vulnerabilities disrupting hospital operations</span>
                        </li>
                        <li class="flex items-center gap-3">
                          <CheckCircle className="h-5 w-5 text-primary flex-shrink-0" />
                          <span>Strengthened trust with healthcare partners</span>
                        </li>
                      </ul>
                    </div>
                    <div class="text-center">
                      <Button >
                        <a href="https://opsfolio.com/resources/case-studies/global-medical-technology-leader" target="_blank" rel="noopener noreferrer">
                          Global Medical Technology Leader Case Study
                        </a>
                      </Button>
                    </div>
                  </Card>

                  {/* Healthcare Engagement Network Case Study */}
                  <Card className="p-8">
                    <div class="mb-6">
                      <h4 class="text-2xl font-bold mb-2">3. Healthcare Engagement Network</h4>
                      <h5 class="text-xl font-semibold text-primary mb-4">HIPAA Audit: Clean Report, No Disruption</h5>
                      <p class="text-lg text-muted-foreground mb-4">
                        A 200–500 FTE healthcare network faced a make-or-break HIPAA audit.
                      </p>
                    </div>
                    <div class="mb-6">
                      <h6 class="font-semibold mb-3">What we did:</h6>
                      <ul class="space-y-2 mb-6">
                        <li class="flex items-center gap-3">
                          <CheckCircle className="h-5 w-5 text-primary flex-shrink-0" />
                          <span>Centralized evidence mapped to HIPAA controls</span>
                        </li>
                        <li class="flex items-center gap-3">
                          <CheckCircle className="h-5 w-5 text-primary flex-shrink-0" />
                          <span>Deployed Surveillr for continuous monitoring</span>
                        </li>
                        <li class="flex items-center gap-3">
                          <CheckCircle className="h-5 w-5 text-primary flex-shrink-0" />
                          <span>Remediated misconfigurations without slowing operations</span>
                        </li>
                      </ul>
                    </div>
                    <div class="mb-6">
                      <h6 class="font-semibold mb-3">Results:</h6>
                      <ul class="space-y-2">
                        <li class="flex items-center gap-3">
                          <CheckCircle className="h-5 w-5 text-primary flex-shrink-0" />
                          <span>Passed HIPAA audit with a clean report</span>
                        </li>
                        <li class="flex items-center gap-3">
                          <CheckCircle className="h-5 w-5 text-primary flex-shrink-0" />
                          <span>Protected multi-million-dollar contracts with life sciences partners</span>
                        </li>
                        <li class="flex items-center gap-3">
                          <CheckCircle className="h-5 w-5 text-primary flex-shrink-0" />
                          <span>Reduced staff burden by automating evidence collection</span>
                        </li>
                      </ul>
                    </div>
                    <div class="text-center">
                      <Button >
                        <a href="https://opsfolio.com/resources/case-studies/healthcare-engagement-network" target="_blank" rel="noopener noreferrer">
                          Healthcare Engagement Network Case Study
                        </a>
                      </Button>
                    </div>
                  </Card>
                </div>
              </div>
            </div>
          </section>


          {/* Expert Team Section */}
          <section class="py-20 bg-muted/50">
            <div class="container mx-auto px-4">
              <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold mb-6">
                  Expert Compliance Team
                </h2>
                <p class="text-lg text-muted-foreground max-w-3xl mx-auto">
                  Our dedicated CaaS team brings specialized expertise across all compliance domains, 
                  ensuring comprehensive coverage and expert guidance.
                </p>
              </div>

              <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                {teamRoles.map((role, index) => (
                  <Card key={index}>
                    <CardHeader>
                      <Users className="h-8 w-8 text-primary mb-2" />
                      <CardTitle className="text-lg">{role.role}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p class="text-muted-foreground">
                        {role.description}
                      </p>
                    </CardContent>
                  </Card>
                ))}
              </div>

              <!-- <div class="text-center">
                <VideoPlaceholder 
                  title="Meet the CaaS Team"
                  description="Expert perspectives on modern compliance challenges"
                  className="rounded-lg max-w-4xl mx-auto"
                />
              </div> -->
            </div>
          </section>

          {/* Integration Process Section */}
          <section class="py-20">
            <div class="container mx-auto px-4">
              <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold mb-6">
                  CaaS Integration Process
                </h2>
                <p class="text-lg text-muted-foreground max-w-3xl mx-auto">
                  Our proven four-step integration process ensures smooth deployment and 
                  immediate value from your Compliance as a Service implementation.
                </p>
              </div>

              <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
                {integrationSteps.map((step, index) => (
                  <Card key={index} className="text-center relative">
                    <CardHeader>
                      <div class="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">
                        {step.step}
                      </div>
                      <CardTitle className="text-lg">{step.title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p class="text-muted-foreground text-sm">
                        {step.description}
                      </p>
                    </CardContent>
                  </Card>
                ))}
              </div>

              <!-- <div class="text-center">
                <VideoPlaceholder 
                  title="CaaS Implementation Journey"
                  description="Step-by-step guide to deploying Opsfolio CaaS"
                  className="rounded-lg max-w-4xl mx-auto"
                />
              </div> -->
            </div>
          </section>

          {/* Technology Section */}
          <section class="py-20 bg-muted/50">
            <div class="container mx-auto px-4">
              <div class="max-w-4xl mx-auto text-center">
                <h2 class="text-3xl md:text-4xl font-bold mb-6">
                  AI-Enhanced Automation
                </h2>
                <p class="text-lg text-muted-foreground mb-12">
                  Opsfolio CaaS leverages advanced artificial intelligence and machine learning 
                  to deliver superior compliance capabilities, automated analytics, and predictive insights.
                </p>

                <div class="grid md:grid-cols-3 gap-8 mb-12">
                  <Card>
                    <CardHeader>
                      <FileCheck className="h-12 w-12 text-primary mb-4 mx-auto" />
                      <CardTitle>Policy Authoring</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p class="text-muted-foreground">
                        AI-powered policy generation using compliance as code principles. 
                        Create, version, and maintain policies programmatically.
                      </p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <Monitor className="h-12 w-12 text-primary mb-4 mx-auto" />
                      <CardTitle>Automated Evidence</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p class="text-muted-foreground">
                        Eliminate manual processes with automated evidence 
                        collection and audit-ready documentation generation.
                      </p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <Shield className="h-12 w-12 text-primary mb-4 mx-auto" />
                      <CardTitle>Continuous Monitoring</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p class="text-muted-foreground">
                        24/7 compliance posture monitoring with automated 
                        alerts for potential violations or risks.
                      </p>
                    </CardContent>
                  </Card>
                </div>

                <!-- <VideoPlaceholder 
                  title="AI-Powered Compliance Technology"
                  description="Behind the scenes of Opsfolio's automated compliance platform"
                  className="rounded-lg"
                /> -->
              </div>
            </div>
          </section>

          {/* CTA Section */}
          <section class="py-20">
            <div class="container mx-auto px-4">
              <div class="max-w-4xl mx-auto text-center">
                <h2 class="text-3xl md:text-4xl font-bold mb-6">
                  Ready to Transform Your Compliance?
                </h2>
                <p class="text-lg text-muted-foreground mb-8">
                  Join leading organizations who trust Opsfolio CaaS to automate their compliance, 
                  reduce risks, and focus on what matters most - growing their business.
                </p>
                
                <div class="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                  <a href="/get-started"><Button size="lg" >
                    Start Your CaaS Journey
                  </Button>
                  </a>
                  <a href="/contact">
                  <Button variant="outline" size="lg" >
                    Schedule Consultation
                  </Button>
                  </a>
                </div>

                <div class="grid md:grid-cols-3 gap-8 text-center">
                  <div>
                    <div class="text-3xl font-bold text-primary mb-2">90%</div>
                    <p class="text-muted-foreground">Reduction in Manual Effort</p>
                  </div>
                  <div>
                    <div class="text-3xl font-bold text-primary mb-2">24/7</div>
                    <p class="text-muted-foreground">Continuous Monitoring</p>
                  </div>
                  <div>
                    <div class="text-3xl font-bold text-primary mb-2">50+</div>
                    <p class="text-muted-foreground">Compliance Frameworks</p>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </main>
</Layout>