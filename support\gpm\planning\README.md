# www.Opsfolio.com August 2025 Roadmap

- [ ] Integrate `assistant-ui` React components for chat using AnythingLLM + MCP
      [#8](https://github.com/opsfolio/www.opsfolio.com/issues/8)

- [ ] Study [inkeep AI Self Service platform](https://inkeep.com/) and perform
      teardowns to help understand how to present all the same functionality on
      Opsfolio.com using `surveilr`, AnythingLLM, etc. Discuss with <PERSON><PERSON> for
      clarity of purpose.

- [ ] Deliver the CMMC Self-Assessment Lead Magnet as our first lead Magnet
      feature [#9](https://github.com/opsfolio/www.opsfolio.com/issues/9)
  - [x] Add CMMC CTA pointing to new landing page with authentication
  - [x] List and embed LHC Forms for submission with proper names
  - [ ] Generate PDF report upon form completion and email to user

- [ ] Visual and Content Enhancements Across www.opsfolio.com
      [#10](https://github.com/opsfolio/www.opsfolio.com/issues/10)
  - [ ] Add screenshots and visuals on every page possible
  - [ ] Review each page and enhance content using AI and available context
        (talk with <PERSON><PERSON> if unclear)
  - [ ] Add suitable screenshots from Opsfolio Suite in the pages of
        Opsfolio.com

  - [ ] Design Wireframes for Videos in opsfolio.com
    - [x] How and where videos will go on the home page
    - [ ] How and where videos will go on the resources page
    - [ ] How videos will be shown on inner pages

- [x] Create detailed `Fleetfolio` product page in "By Compliance Task" called
      "Assets Intelligence" with screenshots
  - [ ] Enhance page with more screenshots and extensive demo videos using
        [Guidde](https://www.guidde.com/) or similar.
  - [ ] Update `Qualityfolio`
        [product page](https://opsfolio.com/task/test-management/) with more
        elaboration and screenshots along with extensive demo videos using
        [Guidde](https://www.guidde.com/) or similar.

- [ ] Under [Resources](https://opsfolio.com/resources/) in Case Studies Section
      add Opsfolio CaaS for CMMC for Netspective and Alim with both PPT and PDF
      [#12](https://github.com/opsfolio/www.opsfolio.com/issues/12)

  - [ ] Automatically fetch **SEO attributes and layout** from frontmatter
  - [ ] Allow custom layout selection if needed via frontmatter

  - [x] Generate **dynamic menus** sepecific folders
- [ ] Dynamic Navigation and Breadcrumb Enhancements in opsfolio.com
      [#11](https://github.com/opsfolio/www.opsfolio.com/issues/11)
  - [x] Enhance the site with Navigations using proper breadcrumbs in Astro
        react based pages and Content collection pages in Astro way
  - [ ] Auto-Generated Navigation
  - [ ] Build dynamic menus, breadcrumbs, and sidebars
- [x] Design Dynamic Content collection for Pages with markdown
      [#13](https://github.com/opsfolio/www.opsfolio.com/issues/13)
  - [x] Add pages collection for adding mark down pages with SEO attributes
  - [x] Set up Auto-Generated Navigation for the pages based on files and
        folders

- [ ] GPM and Internal Wiki Content Migration
      [#34](https://github.com/opsfolio/www.opsfolio.com/issues/34)
  - [ ] Migrate policies, templates, strategies to public/private areas of
        `www.opsfolio.com`
  - [ ] Index for search and easy navigation
- [ ] Integrate QualityFolio into Opsfolio.com with Authentication
      [#35](https://github.com/opsfolio/www.opsfolio.com/issues/35)

### Backlog Tasks

**Goal:** Enhance the opsfolio.com site and add features with Authentication,
more Content Collections\
**Owner:** Astro Team, AI Team and DevOps\
**Due By:** August 14,2025

- [ ] AI-Generated Online Courses as Lead Magnets
  - [ ] Choose a course tool (e.g.[Thea](https://www.thea.study/), GitBook,
        CourseKit, teachable, Kajabi, custom MDX)
  - [ ] Create course using "Tour" docs for major CaaS offerings
  - [ ] Segment by user role and regulatory regime

- [ ] Public Content Migration from Suite
  - [ ] Move Controls Explorer and other lead magnet tools to `www.opsfolio.com`
  - [ ] Add case studies, slide decks, and non-sensitive content to public
        resources

- [ ] Migrate Opsfolio’s Expectations & Outcomes Hub (EOH)
  - [ ] Import EOH as dedicated section within the site
  - [ ] Ensure it supports deliverables tracking and client transparency

---

# Notes & Explanations

TODO: Move all the notes and details into specific GitHub tickets and link the
tickets to the items above. TODO: Create GitHub Projects for different views
(content-focused colleagues, tech-focused colleages, etc.)

---

## Public Content from Suite

Features like **Controls Explorer** and other non-tenant-specific tools will be
migrated from `suite.opsfolio.com` to `www.opsfolio.com`. These tools:

- Act as **lead magnets** for new users
- Provide value without requiring account creation
- Help potential customers evaluate our platform before engaging

> `suite.opsfolio.com` will continue to host **tenant-specific SaaS features**
> tied to actual CaaS project delivery.

---

## GPM and Wiki Content Migration

All **Governance, Planning, and Management (GPM)** content currently in our
internal markdown-based wiki will be integrated into `www.opsfolio.com`:

- Policies
- Templates
- Strategies
- Knowledgebase

This will create **one authoritative source** for internal guidance and
reference materials.

---

## Opsfolio’s Expectations and Outcomes Hub (EOH)

The **EOH functionality**, already built using Astro, will be migrated into
`www.opsfolio.com` as a dedicated section to support:

- Project tracking
- Deliverables monitoring
- Client collaboration

EOH will provide **centralized visibility** into progress for both internal
teams and clients.

---

## Input from ODC Teams

We’re actively seeking input from development and DevOps teams to ensure a
smooth and secure transition.

**Key areas for feedback:**

- Managing authentication and role-based access across both platforms
- Identifying public tools from `suite.opsfolio.com` suitable for migration
- Highlighting risks or improvements for system design and deployment

---

By the end of this unification:

- `www.opsfolio.com` becomes the **central hub** for sales, marketing, planning,
  customer engagement, and technical documentation.
- We eliminate confusion by consolidating access to **public, private, and
  AI-supported** resources in one well-designed experience.

## Final Outcome

As part of our August objectives, we are initiating a strategic unification of
`www.opsfolio.com` and `suite.opsfolio.com` to simplify access, consolidate
public and internal content, and enable AI-driven support for all stakeholders.
This unified approach will improve discoverability, ease of use, and customer
engagement, while reducing duplication and friction across platforms.

## Past: Migrated existing www.opsfolio.com website to new AI defined site model

**Goal:** Archive the existing opsfolio.com site and set up and replace with new
site\
**Owner:** Astro Team & DevOps\
**Due By:** ✅ Completed on August 1

- [x] Archived existing site under Tag
      [www.opsfolio.com-march-2024](https://github.com/opsfolio/www.opsfolio.com/releases/tag/www.opsfolio.com-march-2024)
- [x] Port from Lovable generated SPA to Astro MPA
- [x] Added SEO Checklists, verification of Titles, Meta , Descriptions,
      Sitemaps , etc
- [x] Added and verified the CTAs and its submissions
- [x] Added Blog Section with Content Collections

## Continuous `www.opsfolio.com` Refinement

- [x] Authentication using Netspective Zitadel Authentication IDP which is used
      in Opsfolio Suite, EOH Hub

  - [x] Implement using Netspective Hub's Tenant account as of now
  - [x] Check a Separate Opsfolio.com Tenant is possible for guest features like
        CMMC CTA Report Generation _Created **Anonymous Tenant** and used for
        this_
  - [x] Verify few sample pages under opsfolio.com for this authentication.
        Existing contents which is migrated is available for all without any
        authentication.
- [x] Design Dynamic Content collection for Resources section

  - [x] Add resources under each sections like Tour Documents, Case Studies etc
  - [x] Under [Resources](https://opsfolio.com/resources/) in Tour documents
        Section add Opsfolio CaaS for CMMC with both PPT and PDF
- [x] Astro Content Collection for Static Pages with Dynamic Layout and SEO
      Support [#13](https://github.com/opsfolio/www.opsfolio.com/issues/13)
  - [x] Create a collection schema to support title, slug, description, SEO
        metadata, layout options, and navigation flags

  - [x] Migrate and manage the following static pages using markdown
        files:Privacy Policy, Terms of Service, About etc

  - [x] Enable support for **multiple content pages** using markdown files
    - Markdown files should be stored under `src/content/pages/` or equivalent
    - Each page should include structured frontmatter (e.g., title, description,
      SEO, layout)
