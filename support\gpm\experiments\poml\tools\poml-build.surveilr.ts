#!/usr/bin/env -S deno run --allow-read --allow-write

import { basename, dirname, fromFileUrl, join, resolve } from "jsr:@std/path";
import { walk } from "jsr:@std/fs@^1.0.0";

// Function to patch pomljs reactRender.js for Deno compatibility
async function patchPomljs() {
  const reactRenderPath = join(
    "node_modules", ".deno", "pomljs@0.0.8", "node_modules", "pomljs", "dist", "util", "reactRender.js"
  );

  try {
    const exists = await Deno.stat(reactRenderPath).then(() => true).catch(() => false);
    if (!exists) return;

    const content = await Deno.readTextFile(reactRenderPath);

    // Check if already patched
    if (content.includes("// Patched version of reactRender.js for Deno compatibility")) {
      return;
    }

    // Apply patch
    const patchedContent = `// Patched version of reactRender.js for Deno compatibility
import { renderToString } from 'react-dom/server';

const reactRender = async (element, shellOnly) => {
    try {
        // For Deno, we'll use renderToString as a fallback since renderToPipeableStream is not available
        // This is simpler but less performant than streaming
        const html = renderToString(element);
        return html;
    } catch (error) {
        console.error('Error rendering React element:', error);
        throw error;
    }
};

export { reactRender };
//# sourceMappingURL=reactRender.js.map`;

    await Deno.writeTextFile(reactRenderPath, patchedContent);
    console.log("Applied Deno compatibility patch to pomljs");
  } catch (_error) {
    // Silently continue if patching fails
    console.warn("Warning: Could not apply pomljs patch, but continuing anyway");
  }
}

// Import pomljs with error handling
let read, write;
try {
  await patchPomljs();
  const pomljs = await import("npm:pomljs");
  read = pomljs.read;
  write = pomljs.write;
} catch (error) {
  console.error("Failed to import pomljs:", (error as Error).message);
  console.error("Please ensure React dependencies are properly configured in deno.json");
  Deno.exit(1);
}


// Process all POML files in the source directory
const sourceDir = "source";

try {
  // Resolve repo root (current directory)
  const repoRoot = resolve(dirname(fromFileUrl(import.meta.url)), ".");
  const sourcePath = join(repoRoot, sourceDir);
  const generatedRoot = join(repoRoot, "generated");

  // Create generated directory
  await Deno.mkdir(generatedRoot, { recursive: true });

  let processedCount = 0;

  // Walk through all .poml files in the source directory
  for await (const entry of walk(sourcePath, {
    includeFiles: true,
    includeDirs: false,
    exts: [".poml"]
  })) {
    try {
      console.log(`Processing: ${entry.path}`);

      // Read input POML
      const pomlContent = await Deno.readTextFile(entry.path);

      // Parse into IR
      const ir = await read(pomlContent);

      // Convert to Markdown
      const markdown = write(ir);

      // Derive name from file
      const name = basename(entry.path, ".poml");
      const outputMd = join(generatedRoot, `${name}.md`);

      // Write output
      await Deno.writeTextFile(outputMd, markdown);

      console.log(`Generated: ${outputMd}`);
      processedCount++;
    } catch (fileErr) {
      console.error(`Error processing ${entry.path}:`, (fileErr as Error).message);
    }
  }

  if (processedCount === 0) {
    console.log(`No .poml files found in ${sourcePath}`);
  } else {
    console.log(`Successfully processed ${processedCount} POML files`);
  }

  Deno.exit(0);
} catch (err) {
  console.error("Error:", (err as Error).message);
  Deno.exit(1);
}
