---
import Layout from "../layouts/Layout.astro";
import { CmmcSelfAssesmentInterpreter } from "../lib/cmmc-self-assessment/CmmcSelfAssesmentInterpreter";

// --- Cookie values ---
const tenantId = Astro.cookies.get("zitadel_tenant_id")?.value ?? "";
const userId = Astro.cookies.get("zitadel_user_id")?.value ?? "";
const sessionId = Astro.cookies.get("session_id")?.value ?? "";
const url = new URL(Astro.request.url);
const sessionName = url.searchParams.get("session"); 

// --- Types ---
interface ReportData {
  assessmentDate: string;
  overall: number;
  families: { name: string; status: string; notes: string, meta:{
    title: string;
    status: string;
    date: string;
    publisher: string;
    description: string;
    purpose: string;
    approvalDate: string;
    lastReviewDate: string;
  },
  evidence?: { question: string; answer: string; expected: string; result: "Pass" | "Fail" }[]; 
  }[];
  evidence: string[];
  risks: string[];
}

let errorMessage: string | null = null;
let missingLabels: string[] = [];
let reportData: ReportData | null = null;

// --- File label mapping ---
const FILE_LABELS: Record<string, string> = {
  companyInfoFile: "Company Information",
  mediaProtectionFile: "Media Protection",
  physicalProtectionFile: "Physical Protection",
  accessControlFile: "Access Control", // fixed typo
  identificationAuthenticationFile: "Identification & Authentication",
  policyFrameworkFile: "Policy Framework",
  systemCommunicationFile: "System Communications Protection",
  systemInformationFile: "System Information Integrity",
};

// --- Helpers ---
const getFamilyClasses = (status: string) => {
  switch (status) {
    case "Fully":
      return "bg-green-100 border-green-500";
    case "Partially":
      return "bg-orange-100 border-orange-500";
    default:
      return "bg-red-100 border-red-500";
  }
};

// --- Fetch responses ---
try {
  if(sessionId){
    const res = await fetch(`${Astro.site}api/getLatestResponses`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ tenantId, userId, sessionId, sessionName }),
    });
    
    if (!res.ok) {
      errorMessage = "Failed to fetch latest responses. Please log out and back in, then try again";
    } else {
      const latestFiles = await res.json();
      const missingFiles = Object.entries(latestFiles)
        .filter(([_, v]) => v === null)
        .map(([k]) => FILE_LABELS[k] ?? k);

      if (missingFiles.length > 0) {
        missingLabels = missingFiles;
        errorMessage = "Some questionnaires are missing. To view the report, you need to complete:";
      } else {
        const interpreter = new CmmcSelfAssesmentInterpreter({
          responseRootPath: `src/data/lforms/${tenantId}/${userId}`,
          ...latestFiles,
        });
        reportData = interpreter.generateReportData();
      }
    }
  } else {
    errorMessage = "Session expired. Please log out and back in, then try again";
  }
} catch (err: any) {
  errorMessage = `Unexpected error: ${err.message ?? "Unknown error"}`;
}
---

<Layout>
  <article class="max-w-4xl mx-auto px-7 py-8 lg:p-8">
    {errorMessage ? (
      <section class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-800 p-4 rounded mb-6">
        <h2 class="font-bold">⚠️ Action Required</h2>
        <p>{errorMessage}</p>
        {missingLabels.length > 0 && (
          <ul class="list-disc list-inside mt-2 text-sm">
            {missingLabels.map((f) => <li>{f}</li>)}
          </ul>
        )}
      </section>
    ) : reportData ? (
      <section>
        <!-- Header -->
        <header class="text-center mb-6 pb-5 border-b-4 border-indigo-500">
          <h1 class="text-2xl font-bold text-blue-800 mb-1">
            CMMC Level 1 - Self-Assessment Report
          </h1>
          <p class="text-sm italic text-gray-800">
            Generated from submitted forms
          </p>
        </header>

        <section class="bg-[#f8f9fa] shadow rounded-lg border-l-4 border-green-500">
          <div class="px-6 py-7">
            
            <!-- Executive Summary -->
            <section class="py-6">
              <h2 class="text-xl font-bold text-blue-800 border-l-4 border-sky-600 pl-3 mb-4">
                Executive Summary
              </h2>
              <p>
                <span class="font-bold">Scope:</span> Responses from submitted
                CMMC Level 1 questionnaires.
              </p>
              <p class="mt-2">
                <span class="font-bold">Assessment Date:</span> {reportData.assessmentDate}
              </p>
            </section>

            <!-- Overall Readiness -->
            <section class="py-6 bg-gray-100 border-2 border-black rounded-xl mt-2 mb-2 text-center">
              <p class="text-4xl font-bold text-blue-700 mb-2">
                &asymp; {reportData.overall}%
              </p>
              <p class="font-bold text-[17px]">
                Overall readiness (by families submitted)
              </p>
              <p class="text-sm text-gray-600 mt-1">
                (partial / demonstrable controls - see notes)
              </p>
              <div class="mt-4 p-3 bg-yellow-50 border-l-4 rounded text-sm text-gray-800">
                <span class="font-semibold">ℹ️ Compliance Note:</span>
                CMMC Level 1 requires <span class="font-bold">all 17 practices</span> to be fully implemented.  
                Any score less than <span class="font-bold">100%</span> means you are <span class="text-red-600 font-semibold">not yet compliant</span>.
              </div>
            </section>

           <!-- Family snapshot -->
            <section class="py-6 space-y-4">
              <h3 class="font-bold text-[18px]">Family-level snapshot</h3>
              {reportData.families.map((fam) => (
                <details
                  class={`p-4 rounded-lg shadow border-l-4 ${getFamilyClasses(fam.status)}`}
                >
                  <summary class="cursor-pointer flex justify-between items-center">
                    <span class="font-bold text-lg">
                      {fam.name} - {fam.status} Implemented
                    </span>
                    <span class="text-sm italic text-gray-600">{fam.notes}</span>
                  </summary>

                  <!-- Purpose -->
                  <p class="mt-3 text-sm text-gray-700 italic leading-relaxed border-l-2 border-gray-300 pl-3">
                    {fam.meta.purpose}
                  </p>

                  <!-- Evidence -->
                  {fam.evidence && fam.evidence.length > 0 && (
                    <details class="mt-4 bg-gray-50 rounded-lg p-3 border border-gray-200">
                      <summary class="cursor-pointer text-sm font-semibold text-blue-700">
                        View Evidence ({fam.evidence.length})
                      </summary>
                      <ul class="mt-3 space-y-2">
                        {fam.evidence.map((ev) => (
                          <li class="p-2 bg-white rounded border text-sm">
                            <p class="font-medium text-gray-800">{ev.question}</p>
                            <p class="text-gray-600">
                              <span class="font-semibold">Answer:</span> {ev.answer}
                            </p>
                            {ev.expected && (
                              <p class="text-gray-600">
                                <span class="font-semibold">Expected:</span> {ev.expected}
                              </p>
                            )}
                            <span
                              class={`inline-block mt-1 px-2 py-1 text-xs rounded-full font-bold ${
                                ev.result === "Pass"
                                  ? "bg-green-100 text-green-700"
                                  : "bg-red-100 text-red-700"
                              }`}
                            >
                              {ev.result}
                            </span>
                          </li>
                        ))}
                      </ul>
                    </details>
                  )}
                </details>
              ))}
            </section>

            <!-- Evidence -->
            <section class="mt-8">
              <h4 class="text-gray-700 font-semibold mb-3">
                Evidence (selected key answers):
              </h4>
              <div class="bg-gray-100 border border-gray-300 rounded-lg p-4 font-mono text-sm leading-relaxed text-gray-800">
                <ul class="list-disc pl-5 space-y-1">
                  {reportData.evidence.map((e) => <li>{e}</li>)}
                </ul>
              </div>
            </section>

            <!-- Risks -->
            {reportData.overall < 100 ? <section class="p-4 bg-orange-100 border-l-4 border-orange-500 rounded mt-6">
              <h4 class="font-bold mb-2">Top {reportData.risks.length} risk areas</h4>
              <ol class="list-decimal list-inside space-y-1 ml-6">
                {reportData.risks.map((r) => <li>{r}</li>)}
              </ol>
            </section>:undefined}
            <section class="mt-5 mb-4 text-sm">
              <p>
                For detailed guidance on CMMC Level 1 self-assessment methodology and criteria, see the 
                <a class="text-blue-600 underline" href="https://dodcio.defense.gov/Portals/0/Documents/CMMC/AG_Level1_V2.0_FinalDraft_20211210_508.pdf" target="_blank">
                  CMMC Level 1 Self-Assessment Guide (v2.0, Dec 2021)
                </a>.
              </p>
          </section>
          </div>
        </section>
      </section>
    ) : null}
  </article>
</Layout>
