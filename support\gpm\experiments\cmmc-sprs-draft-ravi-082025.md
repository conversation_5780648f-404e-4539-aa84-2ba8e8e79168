# Supplier Performance Risk System (SPRS) and CMMC: Why Acting Now Protects Your DoD Contracts

Shahid Shah
August 19, 2025
8 min read

For organizations pursuing CMMC Level 1 and non-critical Level 2 compliance, one fact is clear: **without a valid SPRS score, you risk losing eligibility for DoD contracts.** Enforcement begins October 1, 2025, but primes and contracting officers already require scores today.

This article explains the SPRS process, why delays create risk, and how Opsfolio ensures your submissions are complete, accurate, and audit-ready.


## Why SPRS Matters

The Supplier Performance Risk System (SPRS) is the Department of Defense (DoD)’s official web application for collecting and evaluating supplier performance and cybersecurity risk. It is the single repository for:

* Supplier risk assessments and scores
* Supplier performance metrics (e.g., delivery timeliness)
* Cybersecurity reports, including CMMC and NIST SP 800-171 self-assessment results

For contractors, **SPRS is not optional**. It provides the proof of compliance that keeps you eligible for new business.


## The Compliance Requirements

Under CMMC 2.0, contractors must demonstrate protection of Federal Contract Information (FCI) and Controlled Unclassified Information (CUI). SPRS reporting is how the DoD gains this assurance:

* Level 1 and some Level 2 contractors submit self-assessments plus an Affirmation of Compliance signed by an official of the company.
* Level 2 contractors with critical CUI require third-party certification (C3PAO). Results flow from eMASS into SPRS, but the AO still affirms annually.
* Level 3 contractors undergo DoD-led assessments, with results also recorded in SPRS.
* Subcontractors must have SPRS scores available before primes can award contracts (via flowdown requirements).


## Why Act Now

Even though the 48 CFR CMMC Acquisition Rule is still under review, **contract enforcement begins October 1, 2025.** Contractors can already submit self-assessment results in SPRS, and doing so now ensures:

* No delays in contract eligibility
* A clear understanding of your baseline SPRS score
* Time to remediate gaps before the deadline

**Importantly, DFARS 252.204-7019 already requires a current assessment score in SPRS for all covered contractors. Many primes are enforcing this requirement today as a subcontracting condition.**

Industry studies show the average SPRS score is around –12, with only a small fraction of contractors fully prepared. **This gap puts most organizations at real risk.**


## Understanding the Scoring

* Maximum score: 110 (full compliance)
* Range: –203 to 110, depending on requirements met
* POA\&M required if below 110, with remediation within 6 months

Even if your first score isn’t perfect, documenting your plan of action and milestones (POA\&M) is mandatory, and failing to do so will block eligibility.

**Assessments can also be updated in SPRS at any time as gaps are closed. Resubmitting ensures your score reflects your current compliance posture rather than an outdated assessment (Quick Entry Guide v4.0).**


## How Opsfolio Simplifies the Process

With the Opsfolio CMMC Self-Assessment tool, contractors can:

* Run a CMMC Level 1 self-assessment to calculate their starting SPRS score
* Identify gaps through automated evidence mapping to NIST 800-171 and CMMC requirements
* Export results in the exact format required for SPRS entry

From there, Opsfolio CaaS (Compliance-as-a-Service) takes over with:

* Human experts experienced in DoD contracting
* AI-driven workflows to guide remediation, evidence collection, and affirmation prep
* Automation that ensures SSPs, POA\&Ms, and affirmations are always audit-ready

In short: **you see your score, then Opsfolio carries you through every step until you’re fully compliant and contract-ready.**


## The Submission Process

The process in SPRS follows these steps:

1. Access SPRS via PIEE – **new users must log into PIEE and select the “SPRS Cyber Vendor User” role in their account settings (Quick Entry Guide v4.0).**
2. Select your company hierarchy – choose the correct CAGE codes
3. Add your new self-assessment – Level 1 or Level 2
4. Enter details – date, scope, employee count, compliance claims, and CAGE codes
5. Affirmation – the Affirming Official (AO) signs off in SPRS
6. Finalize – SPRS assigns an official status type (e.g., Final Self-Assessment, Conditional, Pending Affirmation)


<p align="center">
  <img src="./sprs-submission.png" alt="SPRS Submission Process" width="250" />
</p>

Opsfolio’s guided workflows ensure you never miss a step and generate all supporting evidence automatically.


## Opsfolio CaaS: Compliance Without the Overwhelm

Defense contractors often underestimate the complexity of SPRS submissions. With Opsfolio CaaS, you don’t have to go it alone:

* Human experts who know DoD contracting
* AI guidance to streamline evidence, remediation, and policy creation
* Automation software to keep your compliance continuous

Whether you’re starting at Level 1 or preparing for a higher-level CMMC assessment, Opsfolio ensures your SPRS submissions are complete, accurate, and audit-ready.


## FAQs

**Why is SPRS relevant for CMMC?**
Because it is the official system for reporting CMMC assessments and affirmations.

**What’s the maximum SPRS score?**
110, indicating full compliance.

**Do I need a perfect score?**
No, but gaps must be tracked in a POA\&M and remediated within 6 months.

**What happens after I self-assess?**
You’ll enter results in SPRS, and your AO will affirm. Opsfolio CaaS can help with everything from POA\&Ms to documentation and affirmation workflows.

**What if my company hierarchy data appear incorrect?**
If company hierarchy or CAGE data appear incorrect, they must be corrected in SAM.gov. Updates flow into SPRS automatically.


**Bottom line:** Without structured reporting in SPRS, your business risks ineligibility for DoD contracts. Opsfolio simplifies the process from first assessment through ongoing compliance—so you’re prepared for October 2025 and beyond.


\[Request a demo of Opsfolio’s CMMC Self-Assessment + CaaS offering]




