module.exports = {
  extends: [
    "eslint:recommended",
    "plugin:@typescript-eslint/eslint-recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:astro/recommended",
    "plugin:mdx/recommended",
  ],
  env: {
    browser: true,
    amd: true,
    node: true,
  },
  overrides: [
    {
      files: ["*.ts", "*.tsx", "*.ts", "*.tsx"],
      parser: "@typescript-eslint/parser",
      parserOptions: {
        parser: "@typescript-eslint/parser",
        extraFileExtensions: [".astro"],
        ecmaVersion: "latest",
        project: ["tsconfig.json"],
      },
      extends: ["plugin:@typescript-eslint/recommended", "prettier"],
      plugins: ["@typescript-eslint", "prettier", "unicorn"],
      env: {
        node: true,
        browser: true,
        es6: true,
      },
      rules: {
        "unicorn/filename-case": [
          "error",
          {
            "cases": {
              "camelCase": true,
              "pascalCase": true,
              "kebabCase": true,
            }
          }
        ],
        "unicorn/better-regex": "error",
        "unicorn/no-unsafe-regex": "error",
        "unicorn/throw-new-error": "error",
        "unicorn/catch-error-name": "error",
        "unicorn/consistent-destructuring": "error",
        "unicorn/consistent-function-scoping": "error",
        "unicorn/no-array-for-each": "error",
        "unicorn/no-for-loop": "error",
        "unicorn/custom-error-definition": "error",
        "unicorn/empty-brace-spaces": "error",
        "unicorn/error-message": "error",
        "unicorn/escape-case": "error",
        "unicorn/expiring-todo-comments": "error",
        "unicorn/explicit-length-check": "error",
        "unicorn/import-style": "error",
        "unicorn/new-for-builtins": "error",
        "unicorn/no-abusive-eslint-disable": "error",
        "unicorn/no-array-callback-reference": "error",
        "unicorn/no-array-method-this-argument": "error",
        "unicorn/no-array-push-push": "error",
        "unicorn/no-array-reduce": "error",
        "unicorn/no-await-expression-member": "error",
        "unicorn/no-console-spaces": "error",
        "unicorn/no-document-cookie": "error",
        "unicorn/no-empty-file": "error",
        "unicorn/no-hex-escape": "error",
        "unicorn/no-instanceof-array": "error",
        "unicorn/no-invalid-remove-event-listener": "error",
        "unicorn/no-keyword-prefix": "error",
        "unicorn/no-lonely-if": "error",
        "unicorn/no-negated-condition": "error",
        "unicorn/no-nested-ternary": "error",
        "unicorn/no-new-array": "error",
        "unicorn/no-new-buffer": "error",
        "unicorn/no-null": "error",
        "unicorn/no-object-as-default-parameter": "error",
        "unicorn/no-process-exit": "error",
        "unicorn/no-static-only-class": "error",
        "unicorn/no-thenable": "error",
        "unicorn/no-this-assignment": "error",
        "unicorn/no-typeof-undefined": "error",
        "unicorn/no-unnecessary-await": "error",
        "unicorn/no-unreadable-array-destructuring": "error",
        "unicorn/no-unreadable-iife": "error",
        "unicorn/no-unused-properties": "error",
        "unicorn/no-useless-fallback-in-spread": "error",
        "unicorn/no-useless-length-check": "error",
        "unicorn/no-useless-promise-resolve-reject": "error",
        "unicorn/no-useless-spread": "error",
        "unicorn/no-useless-switch-case": "error",
        "unicorn/no-useless-undefined": "error",
        "unicorn/no-zero-fractions": "error",
        "unicorn/number-literal-case": "error",
        "unicorn/numeric-separators-style": "error",
        "unicorn/prefer-add-event-listener": "error",
        "unicorn/prefer-array-find": "error",
        "unicorn/prefer-array-flat": "error",
        "unicorn/prefer-array-flat-map": "error",
        "unicorn/prefer-array-index-of": "error",
        "unicorn/prefer-array-some": "error",
        "unicorn/prefer-at": "error",
        "unicorn/prefer-blob-reading-methods": "error",
        "unicorn/prefer-code-point": "error",
        "unicorn/prefer-date-now": "error",
        "unicorn/prefer-default-parameters": "error",
        "unicorn/prefer-dom-node-append": "error",
        "unicorn/prefer-dom-node-dataset": "error",
        "unicorn/prefer-dom-node-remove": "error",
        "unicorn/prefer-dom-node-text-content": "error",
        "unicorn/prefer-event-target": "error",
        "unicorn/prefer-export-from": "error",
        "unicorn/prefer-includes": "error",
        "unicorn/prefer-json-parse-buffer": "error",
        "unicorn/prefer-keyboard-event-key": "error",
        "unicorn/prefer-logical-operator-over-ternary": "error",
        "unicorn/prefer-math-trunc": "error",
        "unicorn/prefer-modern-dom-apis": "error",
        "unicorn/prefer-modern-math-apis": "error",
        "unicorn/prefer-module": "error",
        "unicorn/prefer-native-coercion-functions": "error",
        "unicorn/prefer-negative-index": "error",
        "unicorn/prefer-node-protocol": "error",
        "unicorn/prefer-number-properties": "error",
        "unicorn/prefer-object-from-entries": "error",
        "unicorn/prefer-optional-catch-binding": "error",
        "unicorn/prefer-prototype-methods": "error",
        "unicorn/prefer-query-selector": "error",
        "unicorn/prefer-reflect-apply": "error",
        "unicorn/prefer-regexp-test": "error",
        "unicorn/prefer-set-has": "error",
        "unicorn/prefer-set-size": "error",
        "unicorn/prefer-spread": "error",
        "unicorn/prefer-string-replace-all": "error",
        "unicorn/prefer-string-slice": "error",
        "unicorn/prefer-string-starts-ends-with": "error",
        "unicorn/prefer-string-trim-start-end": "error",
        "unicorn/prefer-switch": "error",
        "unicorn/prefer-ternary": "error",
        "unicorn/prefer-top-level-await": "error",
        "unicorn/prefer-type-error": "error",
        "unicorn/relative-url-style": "error",
        "unicorn/require-array-join-separator": "error",
        "unicorn/require-number-to-fixed-digits-argument": "error",
        "unicorn/require-post-message-target-origin": "error",
        "unicorn/string-content": "error",
        "unicorn/switch-case-braces": "error",
        "unicorn/template-indent": "error",
        "unicorn/text-encoding-identifier-case": "error",
        // Typescript-eslint
        "@typescript-eslint/no-unused-vars": "error",
        "@typescript-eslint/no-explicit-any": "error",
        "@typescript-eslint/adjacent-overload-signatures": "error",
        "@typescript-eslint/array-type": "error",
        "@typescript-eslint/await-thenable": "error",
        "@typescript-eslint/ban-ts-comment": [
          "error",
          {
            "ts-expect-error": "allow-with-description",
            "ts-ignore": true,
            "ts-nocheck": true,
            "ts-check": false,
            minimumDescriptionLength: 5,
          },
        ],
        "@typescript-eslint/ban-tslint-comment": "error",
        "@typescript-eslint/ban-types": [
          "error",
          {
            types: {
              Object: "Use {} instead.",
              String: { message: "Use 'string' instead.", fixWith: "string" },
              Number: { message: "Use 'number' instead.", fixWith: "number" },
              Boolean: {
                message: "Use 'boolean' instead.",
                fixWith: "boolean",
              },
            },
            extendDefaults: false,
          },
        ],
        "@typescript-eslint/consistent-indexed-object-style": [
          "error",
          "record",
        ],
        "@typescript-eslint/consistent-type-assertions": [
          "error",
          {
            assertionStyle: "as",
            objectLiteralTypeAssertions: "allow-as-parameter",
          },
        ],
        "@typescript-eslint/consistent-type-definitions": [
          "error",
          "interface",
        ],
        "@typescript-eslint/consistent-type-imports": [
          "error",
          { prefer: "type-imports" },
        ],
        "@typescript-eslint/explicit-function-return-type": [
          "error",
          {
            allowExpressions: true,
            allowTypedFunctionExpressions: true,
          },
        ],
        "@typescript-eslint/explicit-member-accessibility": [
          "error",
          {
            accessibility: "explicit",
            overrides: {
              accessors: "explicit",
              constructors: "no-public",
              methods: "explicit",
              properties: "off",
              parameterProperties: "explicit",
            },
          },
        ],
        "@typescript-eslint/member-delimiter-style": [
          "error",
          {
            multiline: {
              delimiter: "semi",
              requireLast: true,
            },
            singleline: {
              delimiter: "semi",
              requireLast: false,
            },
          },
        ],
        "@typescript-eslint/member-ordering": [
          "error",
          {
            default: [
              "public-static-field",
              "protected-static-field",
              "private-static-field",
              "public-instance-field",
              "protected-instance-field",
              "private-instance-field",
              "public-constructor",
              "protected-constructor",
              "private-constructor",
              "public-instance-method",
              "protected-instance-method",
              "private-instance-method",
              "public-static-method",
              "protected-static-method",
              "private-static-method",
            ],
          },
        ],
        "@typescript-eslint/method-signature-style": ["error", "property"],
        "@typescript-eslint/no-inferrable-types": "error",
        "@typescript-eslint/explicit-module-boundary-types": "error",
        "@typescript-eslint/no-floating-promises": "error",
        "@typescript-eslint/no-misused-promises": "error",
        "@typescript-eslint/no-unsafe-assignment": "error",
        "@typescript-eslint/no-unsafe-call": "error",
        "@typescript-eslint/no-unsafe-member-access": "error",
        "@typescript-eslint/no-unsafe-return": "error",
        "@typescript-eslint/prefer-nullish-coalescing": "error",
        "@typescript-eslint/prefer-optional-chain": "error",
        "@typescript-eslint/restrict-template-expressions": [
          "error",
          { allowBoolean: true },
        ],
        "@typescript-eslint/strict-boolean-expressions": [
          "error",
          { allowString: false, allowNumber: false },
        ],
        "@typescript-eslint/switch-exhaustiveness-check": "error",
      },
    },
    {
      // Define the configuration for `.astro` file.
      files: ["*.astro"],
      // Allows Astro components to be parsed.
      parser: "astro-eslint-parser",
      // Parse the script in `.astro` as TypeScript by adding the following configuration.
      // It's the setting you need when using TypeScript.
      parserOptions: {
        parser: "@typescript-eslint/parser",
        extraFileExtensions: [".astro"],
        ecmaVersion: "latest",
        project: ["tsconfig.json"],
      },
      rules: {
        // Astro
        "astro/no-conflict-set-directives": "error",
        "astro/no-deprecated-astro-canonicalurl": "error",
        "astro/no-deprecated-astro-fetchcontent": "error",
        "astro/no-deprecated-astro-resolve": "error",
        "astro/no-unused-define-vars-in-style": "error",
        "astro/valid-compile": "error",
        "astro/no-set-text-directive": "error",
        "astro/no-unused-css-selector": "error",

        // Astro - a11y rules
        "astro/jsx-a11y/alt-text": "error",
        "astro/jsx-a11y/anchor-ambiguous-text": "error",
        "astro/jsx-a11y/anchor-has-content": "error",
        "astro/jsx-a11y/anchor-is-valid": "error",
        "astro/jsx-a11y/aria-activedescendant-has-tabindex": "error",
        "astro/jsx-a11y/aria-props": "error",
        "astro/jsx-a11y/aria-proptypes": "error",
        "astro/jsx-a11y/aria-role": "error",
        "astro/jsx-a11y/aria-unsupported-elements": "error",
        "astro/jsx-a11y/autocomplete-valid": "error",
        "astro/jsx-a11y/click-events-have-key-events": "error",
        "astro/jsx-a11y/control-has-associated-label": "error",
        "astro/jsx-a11y/heading-has-content": "error",
        "astro/jsx-a11y/html-has-lang": "error",
        "astro/jsx-a11y/iframe-has-title": "error",
        "astro/jsx-a11y/img-redundant-alt": "error",
        "astro/jsx-a11y/interactive-supports-focus": "error",
        "astro/jsx-a11y/label-has-associated-control": "error",
        "astro/jsx-a11y/lang": "error",
        "astro/jsx-a11y/media-has-caption": "error",
        "astro/jsx-a11y/mouse-events-have-key-events": "error",
        "astro/jsx-a11y/no-access-key": "error",
        "astro/jsx-a11y/no-aria-hidden-on-focusable": "error",
        "astro/jsx-a11y/no-autofocus": "error",
        "astro/jsx-a11y/no-distracting-elements": "error",
        "astro/jsx-a11y/no-interactive-element-to-noninteractive-role": "error",
        "astro/jsx-a11y/no-noninteractive-element-interactions": "error",
        "astro/jsx-a11y/no-noninteractive-element-to-interactive-role": "error",
        "astro/jsx-a11y/no-noninteractive-tabindex": "error",
        "astro/jsx-a11y/no-redundant-roles": "error",
        "astro/jsx-a11y/no-static-element-interactions": "error",
        "astro/jsx-a11y/prefer-tag-over-role": "error",
        "astro/jsx-a11y/role-has-required-aria-props": "error",
        "astro/jsx-a11y/role-supports-aria-props": "error",
        "astro/jsx-a11y/scope": "error",
        "astro/jsx-a11y/tabindex-no-positive": "error",
      },
    },
    {
      files: ["*.mdx", "*.md"],
      rules: {
        "unicorn/filename-case": [
          "error",
          {
            case: "kebabCase",
          },
        ],
        "@typescript-eslint/no-unused-vars": "off",
      },
    },
  ],
};
