import { defineConfig } from "astro/config";
import react from "@astrojs/react";
import tailwind from "@astrojs/tailwind";
import node from "@astrojs/node";
import "dotenv/config";
import sitemap from "@astrojs/sitemap";
import { getDynamicSitemapUrls } from "./src/utils/getDynamicSitemapUrls";

const siteUrl = process.env.PUBLIC_SITE_URL;

export default defineConfig({
  site: "https://opsfolio.com",
  integrations: [
    react(),
    tailwind(),
    sitemap(),
    sitemap({
      customPages: getDynamicSitemapUrls(siteUrl),
    }),
  ],
  output: "server",
  adapter: node({
    mode: "standalone", // or 'middleware'
  }),
  build: {
    assets: "_astro",
  },
});
