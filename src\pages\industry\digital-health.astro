---
import Layout from "../../layouts/Layout.astro";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowRight, Smartphone, Cloud, Shield, Users, Stethoscope, Monitor } from "lucide-react";

  const platforms = [
    {
      icon: Smartphone,
      title: "Telehealth Platforms",
      description: "Comprehensive compliance for virtual care delivery, remote patient monitoring, and digital therapeutics.",
      regulations: ["HIPAA", "HITECH", "FDA 510(k)", "State telemedicine laws"]
    },
    {
      icon: Monitor,
      title: "Health Analytics & AI",
      description: "AI/ML compliance for diagnostic tools, predictive analytics, and clinical decision support systems.",
      regulations: ["FDA AI/ML guidance", "GDPR Article 22", "Clinical validation", "Bias testing"]
    },
    {
      icon: Cloud,
      title: "Health Information Exchanges",
      description: "Secure data sharing platforms enabling interoperability between healthcare systems.",
      regulations: ["HIPAA", "HITECH", "FHIR compliance", "State HIE regulations"]
    },
    {
      icon: Users,
      title: "Patient Engagement Platforms",
      description: "Consumer health apps, patient portals, and wellness platforms with PHI handling.",
      regulations: ["HIPAA", "FTC Health Breach", "App store compliance", "Consumer protection"]
    },
    {
      icon: Stethoscope,
      title: "Clinical Trial Platforms",
      description: "Digital platforms for clinical research, eClinical systems, and patient recruitment.",
      regulations: ["GCP", "21 CFR Part 11", "ICH guidelines", "Clinical data integrity"]
    },
    {
      icon: Shield,
      title: "Health Data Security",
      description: "Specialized security frameworks for protecting sensitive health information at scale.",
      regulations: ["NIST Cybersecurity", "HHS Security Rule", "Incident response", "Risk assessments"]
    }
  ];

  const challenges = [
    {
      challenge: "Regulatory Complexity",
      solution: "AI-powered regulatory mapping across federal, state, and international requirements",
      impact: "90% reduction in compliance gaps"
    },
    {
      challenge: "PHI Data Flows",
      solution: "Automated data flow discovery and classification with real-time monitoring",
      impact: "100% PHI visibility"
    },
    {
      challenge: "Vendor Management",
      solution: "Continuous third-party risk assessment for health technology vendors",
      impact: "75% faster vendor assessments"
    },
    {
      challenge: "Patient Consent",
      solution: "Dynamic consent management with granular control and audit trails",
      impact: "Enhanced patient trust"
    }
  ];


const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Industry',"aria-disabled": true ,
  },
  {
    index: "2", text: "Digital Health","aria-disabled": true ,
  },
];
 
---

<Layout breadcrumbLinks={breadcrumbLinks}>
   <div class="min-h-screen bg-background">
      
      {/* Hero Section */}
      <section class="relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5 pt-16 pb-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-3xl text-center">
            <Badge variant="outline" className="mb-4">Digital Health Compliance</Badge>
            <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
              Next-Gen Healthcare Compliance
            </h1>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Revolutionary compliance platform designed for the digital health ecosystem. 
              Navigate complex healthcare regulations while accelerating innovation.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
            <a href="/get-started">
              <Button size="lg" variant="default">
                Start Health Tech Assessment
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
             </a> 
            </div>
          </div>
        </div>
      </section>

      {/* Platform Types */}
      <section class="py-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center mb-16">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Digital Health Platform Compliance
            </h2>
            <p class="mt-4 text-lg text-muted-foreground">
              Specialized compliance solutions for every type of digital health platform
            </p>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {platforms.map((platform, index) => (
              <Card key={index} className="border-border/50 hover:border-primary/50 transition-colors h-full">
                <CardHeader>
                  <platform.icon className="h-8 w-8 text-primary mb-2" />
                  <CardTitle className="text-xl">{platform.title}</CardTitle>
                  <CardDescription className="text-base">
                    {platform.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="space-y-2">
                    {platform.regulations.map((regulation) => (
                      <Badge variant="secondary" className="text-xs mr-2">
                        {regulation}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Challenges & Solutions */}
      <section class="py-20 bg-muted">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center mb-16">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Digital Health Challenges Solved
            </h2>
            <p class="mt-4 text-lg text-muted-foreground">
              Advanced solutions for the unique compliance challenges in digital health
            </p>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            {challenges.map((item, index) => (
              <Card key={index} className="p-6">
                <div class="mb-4">
                  <h3 class="text-lg font-semibold text-destructive">{item.challenge}</h3>
                </div>
                <div class="mb-4">
                  <p class="text-muted-foreground">{item.solution}</p>
                </div>
                <div class="flex items-center justify-between">
                  <Badge variant="outline" className="text-primary">{item.impact}</Badge>
                  <a href="/get-started">
                    <Button variant="ghost" size="sm" asChild>
                      <ArrowRight className="h-4 w-4" />
                    </Button>
                  </a>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Industry Stats */}
      <section class="py-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center mb-16">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Digital Health by the Numbers
            </h2>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div class="text-center">
              <div class="text-4xl font-bold text-primary">$659B</div>
              <div class="text-muted-foreground">Digital Health Market 2025</div>
            </div>
            <div class="text-center">
              <div class="text-4xl font-bold text-primary">87%</div>
              <div class="text-muted-foreground">Healthcare Orgs Using Telehealth</div>
            </div>
            <div class="text-center">
              <div class="text-4xl font-bold text-primary">350K+</div>
              <div class="text-muted-foreground">Health Apps Available</div>
            </div>
            <div class="text-center">
              <div class="text-4xl font-bold text-primary">$10.9B</div>
              <div class="text-muted-foreground">Healthcare Data Breach Costs</div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA */}
      <section class="py-20 bg-muted">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Ready to Scale Your Digital Health Platform?
            </h2>
            <p class="mt-4 text-lg text-muted-foreground">
              Join leading digital health companies that trust our compliance expertise
            </p>
            <div class="mt-8">
            <a href="/get-started">
              <Button size="lg" variant="default" >
                Get Digital Health Roadmap<ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </a>
            </div>
          </div>
        </div>
      </section>

    </div>
</Layout>
