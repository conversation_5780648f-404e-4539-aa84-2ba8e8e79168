---
import Layout from "../layouts/Layout.astro";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Play,
  Code,
  GitBranch,
  Monitor,
  Clock,
  Users,
  TrendingUp,
  CheckCircle,
  ArrowRight,
  Calendar
} from "lucide-react";

const caseStudies = [
  {
    company: "TechFlow Systems",
    industry: "FinTech",
    timeline: "4 months to SOC2",
    challenge: "Needed SOC2 certification to close $2M enterprise deal",
    solution: "Automated evidence collection from existing GitHub, AWS, and Slack workflows",
    outcome: "Closed enterprise deal, 3x faster compliance process",
    cto: "Sarah Kim",
    teamSize: "15 developers"
  },
  {
    company: "HealthAI Inc",
    industry: "Digital Health",
    timeline: "6 months to HIPAA + SOC2",
    challenge: "Required both HIPAA and SOC2 for hospital partnerships",
    solution: "Integrated compliance monitoring into CI/CD pipeline",
    outcome: "Secured 3 major hospital partnerships worth $5M ARR",
    cto: "<PERSON>",
    teamSize: "25 developers"
  },
  {
    company: "DataVault",
    industry: "SaaS",
    timeline: "3 months to ISO 27001",
    challenge: "European clients required ISO 27001 certification",
    solution: "Leveraged existing security tools and automated documentation",
    outcome: "Expanded into EU market, 40% revenue increase",
    cto: "James Thompson",
    teamSize: "12 developers"
  }
];

const integrations = [
  { name: "GitHub", description: "Automated code review evidence" },
  { name: "AWS CloudTrail", description: "Infrastructure monitoring" },
  { name: "Slack", description: "Communication compliance" },
  { name: "Jira", description: "Change management tracking" },
  { name: "Docker", description: "Container security scanning" },
  { name: "Kubernetes", description: "Runtime security monitoring" }
];
---

<Layout title="Demo Opsfolio - Compliance Experts Who Actually Get It Done"
        description="Meet the compliance experts, former CISOs & security engineers who make enterprise-grade compliance accessible to every growing company. 100% audit pass rate."
        keywords="compliance experts, CISO, security engineers, SOC2 experts, compliance team"
        url="/demo/">

        <main>
        {/* Hero Section */}
        <div class="relative isolate px-6 pt-14 lg:px-8">
          <div class="mx-auto max-w-4xl py-32 sm:py-48 lg:py-56">
            <div class="text-center">
              <Badge variant="secondary" className="mb-4">CTO Success Stories</Badge>
              <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
                How CTOs & Tech Leaders{" "}
                <span class="text-primary">Build Trust at Scale</span>
              </h1>
              <p class="mt-6 text-lg leading-8 text-muted-foreground max-w-3xl mx-auto">
                See how technical leaders at fast-growing companies use Opsfolio to accelerate enterprise sales 
                without building dedicated compliance teams.
              </p>
              <div class="mt-10 flex items-center justify-center gap-x-6">                
                <a href="#demo-video">
                  <Button size="lg">
                  <Play className="mr-2 h-4 w-4" />
                  Watch 5-Minute Demo
                </Button>                
                </a>                  
                <a href="/contact">
                  <Button variant="outline" size="lg">                  
                      <Calendar className="mr-2 h-4 w-4" />Schedule Technical Review
                  </Button>
                </a>             
                
              </div>
            </div>
          </div>
        </div>

        {/* Case Studies Section */}
        <div class="py-24 sm:py-32">
          <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="mx-auto max-w-2xl text-center mb-16">
              <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                Real CTO Success Stories
              </h2>
              <p class="mt-6 text-lg leading-8 text-muted-foreground">
                How technical leaders accelerated enterprise sales with compliance automation
              </p>
            </div>
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {caseStudies.map((study, index) => (
                <Card key={index} className="border-2 hover:border-primary/20 transition-all">
                  <CardHeader>
                    <div class="flex items-center justify-between mb-2">
                      <Badge variant="outline">{study.industry}</Badge>
                      <Badge variant="secondary">{study.teamSize}</Badge>
                    </div>
                    <CardTitle className="text-xl">{study.company}</CardTitle>
                    <p class="text-sm text-muted-foreground">CTO: {study.cto}</p>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h4 class="font-semibold text-primary mb-2">Challenge</h4>
                      <p class="text-sm text-muted-foreground">{study.challenge}</p>
                    </div>
                    <div>
                      <h4 class="font-semibold text-primary mb-2">Solution</h4>
                      <p class="text-sm text-muted-foreground">{study.solution}</p>
                    </div>
                    <div>
                      <h4 class="font-semibold text-success mb-2">Outcome</h4>
                      <p class="text-sm text-muted-foreground">{study.outcome}</p>
                    </div>
                    <div class="pt-2 border-t">
                      <div class="flex items-center text-sm text-primary font-medium">
                        <Clock className="h-4 w-4 mr-2" />
                        {study.timeline}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* Technical Integration Section */}
        <div class="py-24 sm:py-32 bg-muted/30">
          <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl mb-6">
                  Developer-First Compliance
                </h2>
                <p class="text-lg text-muted-foreground mb-8">
                  Integrate compliance into your existing development workflow without disrupting productivity.
                </p>
                <div class="grid grid-cols-2 gap-4 mb-8">
                  {integrations.map((integration) => (
                    <div class="flex items-center space-x-3 p-3 bg-background rounded-lg">
                      <Code className="h-5 w-5 text-primary flex-shrink-0" />
                      <div>
                        <div class="font-medium text-sm">{integration.name}</div>
                        <div class="text-xs text-muted-foreground">{integration.description}</div>
                      </div>
                    </div>
                  ))}
                </div>                
                <a href="/get-started/">
                  <Button>
                      View All Integrations
                      <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </a>                
              </div>
              <div class="bg-background p-8 rounded-lg shadow-lg">
                <img 
                  src="/assets/compliance-workflow.png" 
                  alt="Automated compliance workflow integrated with development tools showing GitHub, CI/CD, and monitoring integrations"
                  class="w-full h-64 object-cover rounded-lg mb-6"
                />
                <p class="text-sm text-muted-foreground text-center">
                  Automated evidence collection from your existing development tools
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Demo Video Section */}
        <div id="demo-video" class="py-24 sm:py-32">
          <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="mx-auto max-w-2xl text-center mb-16">
              <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                See Opsfolio in Action
              </h2>
              <p class="mt-6 text-lg leading-8 text-muted-foreground">
                Watch how CTOs implement compliance automation in under 5 minutes
              </p>
            </div>
            <div class="relative max-w-4xl mx-auto">
              <div class="aspect-video bg-muted/30 rounded-lg border-2 border-dashed border-muted-foreground/20 flex items-center justify-center">
                <div class="text-center">
                  <Play className="h-16 w-16 text-primary mx-auto mb-4" />
                  <p class="text-lg font-medium text-foreground mb-2">Technical Demo Video</p>
                  <p class="text-sm text-muted-foreground">5 minutes • CTO walkthrough</p>
                  <a href="/contact">
                    <Button className="mt-4">
                        Request Live Demo
                    </Button>
                  </a>                  
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* ROI Calculator Section */}
        <div class="py-24 sm:py-32 bg-muted/30">
          <div class="mx-auto max-w-7xl px-6 lg:px-8">
            <div class="mx-auto max-w-2xl text-center mb-16">
              <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
                Calculate Your Compliance ROI
              </h2>
              <p class="mt-6 text-lg leading-8 text-muted-foreground">
                See the financial impact of accelerated enterprise sales
              </p>
            </div>
            <Card className="max-w-4xl mx-auto">
              <CardContent className="p-12">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
                  <div>
                    <div class="text-3xl font-bold text-primary mb-2">60%</div>
                    <div class="text-sm text-muted-foreground">Faster time to certification</div>
                  </div>
                  <div>
                    <div class="text-3xl font-bold text-primary mb-2">$250K</div>
                    <div class="text-sm text-muted-foreground">Average first enterprise deal</div>
                  </div>
                  <div>
                    <div class="text-3xl font-bold text-primary mb-2">3X</div>
                    <div class="text-sm text-muted-foreground">ROI in first year</div>
                  </div>
                </div>
                <div class="mt-8 text-center">
                  <a href="/get-started/">
                    <Button size="lg">
                        Calculate Your ROI
                        <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </a>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* CTA Section */}
        <div class="bg-gradient-to-r from-primary to-primary-glow">
          <div class="px-6 py-24 sm:px-6 sm:py-32 lg:px-8">
            <div class="mx-auto max-w-2xl text-center">
              <h2 class="text-3xl font-bold tracking-tight text-primary-foreground sm:text-4xl">
                Ready to Join These CTOs?
              </h2>
              <p class="mx-auto mt-6 max-w-xl text-lg leading-8 text-primary-foreground/90">
                Get a custom technical roadmap for your compliance automation strategy.
              </p>
              <div class="mt-10 flex items-center justify-center gap-x-6">
                  <a href="/contact">
                    <Button 
                      size="lg" 
                      variant="secondary"
                      className="bg-background text-foreground hover:bg-background/90"
                    >                  
                        <Calendar className="mr-2 h-4 w-4" />
                        Schedule Technical Review
                    </Button>
                  </a>
                
                <a href="/get-started">
                  <Button 
                    size="lg" 
                    variant="secondary"
                    className="border-primary-foreground/20 text-primary-foreground hover:bg-primary-foreground/10"
                  >
                      Start Free Trial
                      <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </a>                
              </div>
              <div class="mt-8 text-sm text-primary-foreground/80">
                Free technical consultation • Custom implementation plan • No vendor lock-in
              </div>
            </div>
          </div>
        </div>
      </main>

</Layout>
