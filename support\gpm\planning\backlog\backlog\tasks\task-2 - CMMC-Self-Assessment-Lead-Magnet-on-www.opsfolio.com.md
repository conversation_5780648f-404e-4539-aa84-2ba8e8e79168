---
id: task-2
title: CMMC Self-Assessment Lead Magnet on www.opsfolio.com
status: In Progress
assignee:
  - <PERSON><PERSON>
  <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>
  - <PERSON><PERSON><PERSON><PERSON>
  - <PERSON>yJames
  - RameesaP
created_date: '2025-08-25 09:02'
updated_date: '2025-08-29 10:11'
labels: []
dependencies: []
priority: high
---

## Description

Deliver the CMMC Self-Assessment Lead Magnet as the first major public-facing content feature on www.opsfolio.com. This will act as a lead capture and awareness generation tool for strategic CMMC positioning.

The feature should:
- Attract visitors through a compelling CTA
- Capture information through authenticated form submissions
- Generate and email a downloadable PDF report based on the submission
- Follow the established Overwatch self-assessment workflow (see attached diagram)

---

### Functional Requirements

- [ ] Deliver the CMMC Self-Assessment Lead Magnet feature on [www.opsfolio.com/regime/CMMC ](https://opsfolio.com/regime/cmmc/)
  - [ ] Add a clear and compelling Call to Action (CTA) on the homepage and relevant pages
    - Example: "GET YOUR STRATEGIC ADVANTAGE"
    - Link to a new dedicated CMMC landing page
  - [ ] On the landing page:
    - [ ] Embed LHC Forms (Phase 1) for user input
    - [ ] Ensure form submission is allowed only for authenticated users
    - [ ] Capture all input fields necessary to support downstream PDF report generation
  - [ ] Implement PDF report generation:
    - [ ] Auto-generate a CMMC self-assessment report upon form submission
    - [ ] Send the report to the user via email
    - [ ] Display confirmation message and PDF download link
    - [ ] Handle failures with appropriate error messages and retry option

---

### Optional Enhancements for Phase 2

- [ ] Replace LHC Forms with custom HTML form implementation
- [ ] Include AI-generated insights or summaries in the report
- [ ] Store user-submitted assessments for future access
- [ ] Allow users to download historical reports from a dashboard (authenticated)

---


---

### Implementation Notes

- Leverage existing Zitadel IDP authentication to protect form submission
- Use the Overwatch implementation as a reference for backend and PDF rendering logic
- Recommended technologies for PDF generation:
  - Puppeteer or PDFKit (Node.js)
  - Use transactional email service like Postmark or SendGrid for delivery

---

### Expected Outcome

Implementing this lead magnet will:
- Deliver immediate value to new visitors
- Establish www.opsfolio.com as a trusted compliance resource
- Capture qualified leads through secure and auditable form submissions


@ajaykumaran @arunqa @shanil-sasikumar
