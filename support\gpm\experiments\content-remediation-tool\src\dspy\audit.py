import dspy
from schemas import ComplianceAuditResult
from config import settings

# Configure DSPy
lm = dspy.LM(settings.LLM_NAME, api_key=settings.LLM_API_KEY, max_tokens=65000, temperature=0.5)
dspy.configure(lm=lm)

INSTRUCTIONS = """
Instructions to the model:
1. Review the following page text.
2. Identify strengths and weaknesses in the content.
3. For each weakness, provide:
   a. The original content snippet that illustrates the issue.
   b. A remediated version or suggestion that improves the content.
4. Output in the following structured format:
   - Executive Summary (1–2 paragraphs)
   - Strengths (with evidence citations)
   - Weaknesses (with evidence citations)
   - Recommendations (each tied explicitly to evidence)
   - Observation vs Hypothesis Table
   - Original Content and Corresponding Remediated Content
5. Ensure that each remediated content directly addresses the identified weaknesses.
"""

def chunk_by_paragraph(text, max_words=500, overlap=50):
    """Yield text chunks split by paragraph with optional overlap."""
    paragraphs = text.split("\n\n")
    chunk = []
    word_count = 0
    last_words = []

    for para in paragraphs:
        wc = len(para.split())
        if word_count + wc > max_words and chunk:
            # include overlap words
            if last_words:
                chunk.insert(0, " ".join(last_words))
            yield "\n\n".join(chunk)
            # start new chunk
            chunk = [para]
            word_count = wc
        else:
            chunk.append(para)
            word_count += wc

        # track last N words for overlap
        last_words = para.split()[-overlap:]

    if chunk:
        yield "\n\n".join(chunk)

# DSPy signature
class ComplianceAuditSig(dspy.Signature):
    context = dspy.InputField(desc="Audit instructions + Page Text")
    page_type = dspy.InputField(desc="Page type (Landing, Case Study, etc.)")

    executive_summary = dspy.OutputField()
    strengths = dspy.OutputField()
    weaknesses = dspy.OutputField()
    recommendations = dspy.OutputField()
    observation_vs_hypothesis = dspy.OutputField()
    remediated_content = dspy.OutputField()

class ComplianceAuditProgram(dspy.Module):
    def __init__(self):
        super().__init__()
        self.predict = dspy.Predict(ComplianceAuditSig)

    def forward(self, page_type: str, page_text: str, prompt: str) -> ComplianceAuditResult:
        full_context = prompt + "\n\nPage Text:\n" + page_text + "\n\nInstructions:\n" + INSTRUCTIONS

        try:
            result = self.predict(context=full_context, page_type=page_type)
        except Exception as e:
            # If LLM fails, return a default result with the error
            result = ComplianceAuditResult(
                executive_summary=f"Failed to process page: {e}",
                strengths="",
                weaknesses="",
                recommendations="",
                observation_vs_hypothesis="",
                remediated_content=""
            )

        # Return result directly
        return ComplianceAuditResult(
            executive_summary=result.executive_summary or "",
            strengths=result.strengths or "",
            weaknesses=result.weaknesses or "",
            recommendations=result.recommendations or "",
            observation_vs_hypothesis=result.observation_vs_hypothesis or "",
            remediated_content=result.remediated_content or "",
        )
