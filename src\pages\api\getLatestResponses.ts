// src/pages/api/getLatestResponses.ts
import { readdir, stat, access, constants } from "fs/promises";
import path from "path";
import process from "node:process";
import type { APIContext } from "astro";

const FILE_KEYS = {
    companyInfoFile:
        "company-information.fhir-R4-questionnaire",
    mediaProtectionFile:
        "media-protection-protect-information-on-digital-and-non-digital-media.fhir-R4-questionnaire",
    physicalProtectionFile:
        "physical-protection-limit-physical-access-to-information-systems-and-facilities.fhir-R4-questionnaire",
    acessControlFile:
        "access-control-limit-information-system-access-to-authorized-users-and-processes.fhir-R4-questionnaire",
    identificationAuthenticationFile:
        "identification-authentication-verify-identities-of-users-and-processes.fhir-R4-questionnaire",
    policyFrameworkFile:
        "policy-framework-assessment-policy-implementation-all-cmmc-level-1-practices.fhir-R4-questionnaire",
    systemCommunicationFile:
        "system-communications-protection-monitor-control-and-protect-organizational-communications.fhir-R4-questionnaire",
    systemInformationFile:
        "system-information-integrity-identify-report-and-correct-information-system-flaws.fhir-R4-questionnaire",
};

async function fileExists(filePath: string) {
    try {
        await access(filePath, constants.F_OK);
        return true;
    } catch {
        return false;
    }
}

export async function POST({ request }: APIContext) {
    try {
        const { tenantId, userId, sessionId, sessionName } = await request.json();
        if (!tenantId || !userId || !sessionId) {
            return new Response(
                JSON.stringify({ error: "tenantId, userId, sessionId are required" }),
                { status: 400 }
            );
        }
        const basePath = path.join(
            process.cwd(),
            "src/data/lforms",
            tenantId,
            userId
        );

        // Get all session folders
        const sessionFolders = (await readdir(basePath)).filter(
            async (f) => (await stat(path.join(basePath, f))).isDirectory()
        );

        // Sort by timestamp (assuming sessionName is timestamp-like numeric/string)
        sessionFolders.sort((a, b) => b.localeCompare(a)); // latest first

        // Ensure current sessionName is first in the search order
        const orderedSessions = [
            sessionName,
            ...sessionFolders.filter((s) => s !== sessionName),
        ];

        const resolvedFiles: Record<string, string | null> = {};

        for (const [key, prefix] of Object.entries(FILE_KEYS)) {
            let found: string | null = null;

            for (const session of orderedSessions) {
                const responseDir = path.join(basePath, session);

                const submitFile = path.join(
                    responseDir,
                    `${prefix}.submit.lform-submission.json`
                );
                const saveFile = path.join(
                    responseDir,
                    `${prefix}.save.lform-submission.json`
                );

                if (await fileExists(submitFile)) {
                    found = `/${session}/${path.basename(submitFile)}`;
                    break;
                }
                if (await fileExists(saveFile)) {
                    found = `/${session}/${path.basename(saveFile)}`;
                    break;
                }
            }

            resolvedFiles[key] = found;
        }
        return new Response(JSON.stringify(resolvedFiles), {
            status: 200,
            headers: { "Content-Type": "application/json" },
        });
    } catch (err: unknown) {
        const errorMessage =
            err && typeof err === "object" && "message" in err
                ? String((err as { message?: string }).message)
                : "Internal error";
        return new Response(
            JSON.stringify({ error: errorMessage }),
            { status: 500 }
        );
    }
}
