---
import Layout from "../../layouts/Layout.astro";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowRight, Building, CreditCard, TrendingUp, Shield, Globe, Banknote } from "lucide-react";
  const segments = [
    {
      icon: Building,
      title: "Traditional Banking",
      description: "Core banking systems, payment processing, and regulatory compliance for traditional financial institutions.",
      frameworks: ["SOX", "Basel III", "FFIEC", "OCC Guidelines", "GDPR"]
    },
    {
      icon: CreditCard,
      title: "Fintech & Digital Payments",
      description: "Innovative payment solutions, digital wallets, and mobile banking platforms with regulatory alignment.",
      frameworks: ["PCI DSS", "PSD2", "Open Banking", "AML/KYC", "Consumer Protection"]
    },
    {
      icon: TrendingUp,
      title: "Investment & Trading",
      description: "Investment platforms, algorithmic trading, and wealth management with fiduciary compliance.",
      frameworks: ["SEC Rules", "FINRA", "MiFID II", "CFTC", "Market Abuse Regulation"]
    },
    {
      icon: Shield,
      title: "Insurance Technology",
      description: "Digital insurance platforms, claims processing, and risk assessment with actuarial compliance.",
      frameworks: ["Solvency II", "NAIC", "GDPR", "State Insurance Laws", "Actuarial Standards"]
    },
    {
      icon: Globe,
      title: "Cryptocurrency & DeFi",
      description: "Digital asset platforms, DeFi protocols, and blockchain compliance in evolving regulatory landscape.",
      frameworks: ["AML/BSA", "FinCEN Guidance", "SEC Digital Assets", "FATF Standards", "State Money Transmission"]
    },
    {
      icon: Banknote,
      title: "Lending & Credit",
      description: "Digital lending platforms, credit scoring, and alternative financing with consumer protection.",
      frameworks: ["TILA", "FCRA", "ECOA", "State Lending Laws", "CFPB Regulations"]
    }
  ];

  const riskAreas = [
    {
      risk: "Regulatory Fragmentation",
      solution: "Unified compliance orchestration across multiple jurisdictions and regulatory bodies",
      impact: "85% reduction in regulatory gaps"
    },
    {
      risk: "Transaction Monitoring",
      solution: "AI-powered transaction surveillance with real-time anomaly detection and reporting",
      impact: "99.7% AML detection accuracy"
    },
    {
      risk: "Data Privacy & Localization",
      solution: "Dynamic data governance with automated classification and residency compliance",
      impact: "100% data location visibility"
    },
    {
      risk: "Third-Party Risk",
      solution: "Continuous vendor monitoring with financial services-specific risk scoring",
      impact: "60% faster vendor assessments"
    }
  ];


const breadcrumbLinks = [
  { index: "0", text: "Home" },
  {
    index: "1", text: 'Industry',"aria-disabled": true ,
  },
  {
    index: "2", text: "Financial Services","aria-disabled": true ,
  },
];
---

<Layout  breadcrumbLinks={breadcrumbLinks}>

    <section class="relative overflow-hidden bg-gradient-to-br from-primary/5 via-background to-secondary/5 pt-16 pb-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-3xl text-center">
            <Badge variant="outline" className="mb-4">Financial Services Compliance</Badge>
            <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl">
              Regulatory Excellence in Finance
            </h1>
            <p class="mt-6 text-lg leading-8 text-muted-foreground">
              Advanced compliance platform for the complex world of financial services. 
              Navigate evolving regulations while driving innovation and growth.
            </p>
            <div class="mt-10 flex items-center justify-center gap-x-6">
              <a href="/get-started">
              <Button size="lg" variant="default">
                Get Financial Services Assessment<ArrowRight className="ml-2 h-4 w-4" />
              </Button>
             </a>
            </div>
          </div>
        </div>
      </section>

      {/* Financial Segments */}
      <section class="py-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center mb-16">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Financial Services Segments
            </h2>
            <p class="mt-4 text-lg text-muted-foreground">
              Specialized compliance solutions across all financial services sectors
            </p>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {segments.map((segment, index) => (
              <Card key={index} className="border-border/50 hover:border-primary/50 transition-colors h-full">
                <CardHeader>
                  <segment.icon className="h-8 w-8 text-primary mb-2" />
                  <CardTitle className="text-xl">{segment.title}</CardTitle>
                  <CardDescription className="text-base">
                    {segment.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="space-y-2">
                    {segment.frameworks.map((framework) => (
                      <Badge variant="secondary" className="text-xs mr-2 mb-1">
                        {framework}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Risk Areas */}
      <section class="py-20 bg-muted">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center mb-16">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Financial Services Risk Management
            </h2>
            <p class="mt-4 text-lg text-muted-foreground">
              Advanced solutions for the critical risk areas in financial services
            </p>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            {riskAreas.map((item, index) => (
              <Card key={index} className="p-6">
                <div class="mb-4">
                  <h3 class="text-lg font-semibold text-destructive">{item.risk}</h3>
                </div>
                <div class="mb-4">
                  <p class="text-muted-foreground">{item.solution}</p>
                </div>
                <div class="flex items-center justify-between">
                  <Badge variant="outline" className="text-primary">{item.impact}</Badge>
                  <a href="/get-started">
                    <Button variant="ghost" size="sm" asChild>
                        <ArrowRight className="h-4 w-4" />
                    </Button>
                  </a>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Financial Services Stats */}
      <section class="py-20">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center mb-16">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Financial Services by the Numbers
            </h2>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div class="text-center">
              <div class="text-4xl font-bold text-primary">$8.3T</div>
              <div class="text-muted-foreground">Global FinTech Market Value</div>
            </div>
            <div class="text-center">
              <div class="text-4xl font-bold text-primary">$5.8B</div>
              <div class="text-muted-foreground">Average Financial Data Breach Cost</div>
            </div>
            <div class="text-center">
              <div class="text-4xl font-bold text-primary">73%</div>
              <div class="text-muted-foreground">Banks Using AI for Compliance</div>
            </div>
            <div class="text-center">
              <div class="text-4xl font-bold text-primary">120+</div>
              <div class="text-muted-foreground">Global Financial Regulators</div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA */}
      <section class="py-20 bg-muted">
        <div class="mx-auto max-w-7xl px-6 lg:px-8">
          <div class="mx-auto max-w-2xl text-center">
            <h2 class="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
              Ready to Transform Financial Compliance?
            </h2>
            <p class="mt-4 text-lg text-muted-foreground">
              Join leading financial institutions that trust our compliance expertise
            </p>
            <div class="mt-8">
              <a href="/get-started">
              <Button size="lg" variant="default">
                Get Financial Services Strategy<ArrowRight className="ml-2 h-4 w-4" />
              </Button>
              </a>
            </div>
          </div>
        </div>
      </section>

    </Layout>